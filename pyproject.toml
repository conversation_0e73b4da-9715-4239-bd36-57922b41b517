[tool.black]
line-length = 120
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | migrations
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | \.reports
  | \*.proto
  | \*_pb2\.py$
  | \*_pb2_grpc\.py$
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 120
default_section = "THIRDPARTY"
skip_glob = "learning_trail/tasks/notifications.py, /.reports, konquest/rest_clients/myaccount.py"
known_first_party = [
  "account", "user", "achievement", "authentication", "category", "config", "custom", "di",
  "goal_mission", "group", "learn_content", "learning_trail", "mission", "notification",
  "pulse", "user_activity", "utils", "rest_clients", "scripts", "tasks"
]
known_third_party = [
  "PIL", "apscheduler", "attr", "auditlog", "boto3", "cachetools", "celery", "conftest",
  "constants", "corsheaders", "dateutil", "discord_webhook", "distutils", "django",
  "django_filters", "django_injector", "django_redis", "drf_multiple_model", "drf_yasg",
  "eventlet", "fitz", "gevent", "history", "htmlmin", "injector", "jinja2", "jose",
  "keycloak", "kombu", "magic", "mock", "model_bakery", "model_mommy", "newrelic",
  "observer", "pandas", "pika", "psutil", "pytest", "pytz", "repository", "requests",
  "rest_framework", "rest_framework_dataclasses", "rules", "shareds", "unidecode", "xlrd"
]

[tool.ruff]
line-length = 120
lint.ignore = ["E402", "FIX001"]
lint.select = [
    # Pyflakes
    "F",
    # Pycodestyle
    "E",
    "W",
    # isort
    "I001"
]
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".github",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "migrations",
    "email_locale",
    "__init__.py",
    "manage.py",
    "settings.py",
    "konquest/pulse/tests/*",
    "config",
    "apps.py",
    "konquest/mission/tests/*",
    "konquest/config/*",
    "konquest/locales/*",
    "konquest/scripts/*",
    "konquest/temp/*",
    "konquest/manage.py",
    "konquest/gunicorn_config.py",
    "konquest/email_locale/msgfmt.py",
    "konquest/tasks/scheduler.py",
    ".proto",
    "konquest/grpc_services/generated/*",
    "konquest/grpc_server.py"
]
