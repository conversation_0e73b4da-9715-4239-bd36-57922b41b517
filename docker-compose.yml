version: '3.8'

services:
  web:
    build:
      context: ./konquest
      dockerfile: Dockerfile
    command: gunicorn --reload --access-logfile=- --config gunicorn_config.py "config.wsgi" --bind 0.0.0.0:8000
    volumes:
      - ./konquest:/app
    ports:
      - 8000:8000
    env_file:
      - ./.env
    depends_on:
      - db
      - rabbitmq

  grpc-server:
    build:
      context: ./konquest
      dockerfile: Dockerfile
    command: python manage.py run_grpc_server
    volumes:
      - ./konquest:/app
    ports:
      - 50051:50051
    env_file:
      - ./.env
    depends_on:
      - db

  worker:
    image: konquest
    build:
      context: ./konquest
      dockerfile: Dockerfile
    command: /usr/bin/supervisord -c supervisord.conf
    volumes:
      - ./konquest:/app
    env_file:
      - ./.env
    depends_on:
      - db
      - rabbitmq
    links:
      - rabbitmq

  worker-celery-beat:
    image: konquest
    build:
      context: ./konquest
      dockerfile: Dockerfile
    command: /usr/bin/supervisord -c supervisord_celery_beat.conf
    volumes:
      - ./konquest:/app
    env_file:
      - ./.env
    depends_on:
      - db
      - rabbitmq
    links:
      - rabbitmq

  notifications-worker:
    image: konquest
    build:
      context: ./konquest
      dockerfile: Dockerfile
    command: /usr/bin/supervisord -c supervisord_notifications_worker.conf
    volumes:
      - ./konquest:/app
    env_file:
      - ./.env
    depends_on:
      - db
      - rabbitmq
    links:
      - rabbitmq
    deploy:
      resources:
        limits:
          cpus: "1.00"
          memory: 1024M

  rabbitmq:
    image: rabbitmq:3-management-alpine
    ports:
        - 5672:5672
        - 15672:15672
    environment:
        - RABBITMQ_DEFAULT_USER=admin
        - RABBITMQ_DEFAULT_PASS=admin

  db:
<<<<<<< Updated upstream
    image: postgres:13.0-alpine
    volumes:
      - postgres_data:/var/lib/postgresql/data/
=======
    image: postgres:16
>>>>>>> Stashed changes
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=konquest_dev_db
    ports:
      - 15432:5432

  redis:
    image: redis:7-alpine
    command: ["redis-server", "--requirepass", "123456"]
    env_file:
      - ./.env
    ports:
      - 6379:6379


volumes:
  postgres_data:
  app_data:
