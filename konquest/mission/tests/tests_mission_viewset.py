import datetime
import uuid
from unittest import mock

from django.conf import settings
from django.test import TestCase, override_settings
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from account.models import User, Workspace
from account.models.language_enum import LanguageEnum
from authentication.keeps_permissions import ADMIN, CONTENT, INSTRUCTOR, USER
from constants import CELERY_SEND_TASK_PATH
from learning_trail.models import LearningTrailStep
from mission.models import (
    ExternalMission,
    LiveMission,
    LiveMissionDates,
    Mission,
    MissionBookmark,
    MissionCategory,
    MissionContentResume,
    MissionContributor,
    MissionStage,
    MissionTypeEnum,
    MissionWorkspace,
    PresentialMission,
    PresentialMissionDates,
)
from mission.models.mission import EXTERNAL_PROVIDER, LIVE, PRESENTIAL
from mission.models.mission_development_status_enum import IN_PROGRESS
from mission.models.mission_evaluation import MissionEvaluation
from mission.models.mission_provider import MissionProvider
from mission.tasks.finish_sync_mission import finish_sync_mission
from user_activity.models import LearningTrailEnrollment, MissionEnrollment
from user_activity.models.mission_enrollment import INACTIVATED

I18N_MISSION_NOT_FOUND = "mission_not_found"
KONTENT_GET_DOCS = "rest_clients.kontent.KontentClient.get_docs"
GET_USER_ROLES = "authentication.keeps_permissions.KeepsBasePermission.get_user_roles"


@mock.patch(GET_USER_ROLES)
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class MissionViewsetTestCase(TestCase):
    fixtures = ["pulse_type", "user", "learning_trail_type", "learning_trail", "mission_type"]

    def setUp(self):
        """
        Workspace: Keeps Test
        Configs:
            allow_list_public_mission = true
            allow_list_paid_mission = true
            allow_create_public_mission = true
            allow_create_paid_mission = true

        Workspace: Bayer Test
        Configs:
            allow_list_public_mission = true
            allow_list_paid_mission = true
            allow_create_public_mission = false
            allow_create_paid_mission = false

        Workspace: Mosaic Test
        Configs:
            allow_list_public_mission = false
            allow_list_paid_mission = false
            allow_create_public_mission = false
            allow_create_paid_mission = false
        """
        self.client = APIClient()

        self.user_creator = mommy.make(User, id=uuid.uuid4(), email="<EMAIL>")
        self.user_consumer = mommy.make(User, id=uuid.uuid4(), email="<EMAIL>")

        self.workspace_keeps = mommy.make(
            Workspace,
            id=uuid.uuid4(),
            name="Keeps",
            allow_list_public_mission=True,
            allow_list_paid_mission=True,
            allow_create_public_mission=True,
            allow_create_paid_mission=True,
            min_performance_certificate=0.8,
        )

        self.workspace_bayer = mommy.make(
            Workspace,
            id=uuid.uuid4(),
            name="Bayer",
            allow_list_public_mission=True,
            allow_list_paid_mission=True,
            allow_create_public_mission=False,
            allow_create_paid_mission=False,
        )

        self.workspace_mosaic = mommy.make(
            Workspace,
            id=uuid.uuid4(),
            name="Mosaic",
            allow_list_public_mission=False,
            allow_list_paid_mission=False,
            allow_create_public_mission=False,
            allow_create_paid_mission=False,
        )

        self.workspace_public_not_paid = mommy.make(
            Workspace,
            id=uuid.uuid4(),
            name="List Public but not list paid",
            allow_list_public_mission=True,
            allow_list_paid_mission=False,
            allow_create_public_mission=False,
            allow_create_paid_mission=False,
        )

        self.workspace_paid_not_public = mommy.make(
            Workspace,
            id=uuid.uuid4(),
            name="List Paid but not list public",
            allow_list_public_mission=False,
            allow_list_paid_mission=True,
            allow_create_public_mission=True,
            allow_create_paid_mission=False,
        )

        self.mission_type_public = MissionTypeEnum.PUBLIC.value
        self.mission_type_open_for_workspace = MissionTypeEnum.OPEN_FOR_COMPANY.value
        self.mission_type_close_for_workspace = MissionTypeEnum.OPEN_FOR_COMPANY.value
        self.mission_type_paid = MissionTypeEnum.PAID.value
        self.mission_type_close_for_workspace_right = MissionTypeEnum.CLOSE_FOR_COMPANY.value

        self.mission_category = mommy.make(MissionCategory, id=uuid.uuid4())
        self.mission_category_2 = mommy.make(MissionCategory, id=uuid.uuid4())
        self.provider = mommy.make(MissionProvider)
        self.provider_2 = mommy.make(MissionProvider)

        self.trail_id = "9bde55d4-2036-4c24-80a5-e5dbf7eaa92d"
        self.mission_external_id = "c8baa509-7637-47a4-a97b-4e717f56df74"

        self.url = reverse("missions-list")
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={
                "sub": str(self.user_consumer.id),
                "role": USER,
                "managed": False,
                "client_id": str(self.workspace_keeps.id),
            }
        )

    def test_mission_workspace_user_not_allowed_for_workspace(self, check_role, get_user_roles):
        """
        User don"t have access allowed for workspace in x-client header.
        """
        check_role.return_value = False

        response = self.client.get(self.url, **self.headers, format="json")
        self.assertEqual(response.status_code, 403)

    def test_mission_workspace_keeps_list(self, check_role, get_user_roles):
        self.create_missions()

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 5)

        _ids = [x["mission_type"]["id"] for x in response["results"]]
        _names = [x["name"] for x in response["results"]]

        self.assertIn(MissionTypeEnum.OPEN_FOR_COMPANY.value, _ids)
        self.assertIn(MissionTypeEnum.PAID.value, _ids)
        self.assertIn(MissionTypeEnum.PUBLIC.value, _ids)

        self.assertIn("Keeps open for workspace mission", _names)
        self.assertIn("Keeps paid mission", _names)
        self.assertIn("Mosaic public mission created in past", _names)
        self.assertIn("Keeps public mission", _names)

    def test_mission_workspace_bayer_list(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_bayer.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_bayer.id), "role": USER}
        )

        self.create_missions()

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 5)
        _type_ids = [x["mission_type"]["id"] for x in response["results"]]
        _names = [x["name"] for x in response["results"]]

        self.assertIn(MissionTypeEnum.PUBLIC.value, _type_ids)
        self.assertIn(MissionTypeEnum.OPEN_FOR_COMPANY.value, _type_ids)
        self.assertIn(MissionTypeEnum.PAID.value, _type_ids)

        self.assertIn("Mosaic public mission created in past", _names)
        self.assertIn("Keeps paid mission", _names)
        self.assertIn("Bayer open for workspace mission", _names)
        self.assertIn("Keeps public mission", _names)

    def test_mission_workspace_mosaic_list(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_mosaic.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_mosaic.id), "role": USER}
        )

        self.create_missions()

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 3)

        _ids = [x["mission_type"]["id"] for x in response["results"]]
        self.assertIn(MissionTypeEnum.PUBLIC.value, _ids)
        self.assertIn(MissionTypeEnum.OPEN_FOR_COMPANY.value, _ids)

    def test_mission_workspace_list_public_not_paid(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_public_not_paid.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_public_not_paid.id), "role": USER}
        )
        self.create_missions()

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 2)

        _ids = [x["mission_type"]["id"] for x in response["results"]]
        _names = [x["name"] for x in response["results"]]

        self.assertIn(MissionTypeEnum.PUBLIC.value, _ids)
        self.assertNotIn(MissionTypeEnum.PAID.value, _ids)
        self.assertIn("Mosaic public mission created in past", _names)
        self.assertIn("Keeps public mission", _names)

    def test_mission_workspace_list_paid_not_public(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_paid_not_public.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_paid_not_public.id), "role": USER}
        )

        self.create_missions()

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)
        self.assertEqual(response["results"][0]["name"], "Keeps paid mission")
        self.assertEqual(response["results"][0]["mission_type"]["id"], MissionTypeEnum.PAID.value)

    def test_mission_list_filter_active(self, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id), "role": ADMIN}
        )
        self.create_missions()

        response = self.client.get(self.url + "?is_active=True", **self.headers, format="json").json()

        self.assertEqual(len(response["results"]), 4)

    def test_mission_list_filter_mission_category_2(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.create_missions()

        response = self.client.get(
            self.url + f"?mission_category={self.mission_category.id}", **self.headers, format="json"
        ).json()
        self.assertEqual(len(response["results"]), 4)

        count_mission_category = 0
        count_public_mission_type_public = 0
        count_public_mission_type_paid = 0

        for result in response["results"]:
            if result["mission_category"]["id"] == str(self.mission_category.id):
                count_mission_category += 1

            if result["mission_type"]["id"] == MissionTypeEnum.PUBLIC.value:
                count_public_mission_type_public += 1

            if result["mission_type"]["id"] == MissionTypeEnum.PAID.value:
                count_public_mission_type_paid += 1

        self.assertEqual(count_mission_category, 4)
        self.assertEqual(count_public_mission_type_public, 2)
        self.assertEqual(count_public_mission_type_paid, 1)

        response = self.client.get(
            self.url + f"?mission_category={self.mission_category_2.id}", **self.headers, format="json"
        ).json()
        self.assertEqual(len(response["results"]), 1)
        self.assertEqual(response["results"][0]["mission_type"]["id"], MissionTypeEnum.OPEN_FOR_COMPANY.value)
        self.assertEqual(response["results"][0]["mission_category"]["id"], str(self.mission_category_2.id))

    def test_mission_list_search_name(self, check_role, get_user_roles):
        self.create_missions()

        response = self.client.get(self.url + "?search=Keeps public mission", **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 2)

        response = self.client.get(self.url + "?search=Keeps", **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 5)

    def test_mission_list_only_enrolled(self, check_role, get_user_roles):
        mission_enrolled = self.create_missions()
        mommy.make(MissionEnrollment, mission=mission_enrolled, user=self.user_consumer, workspace=self.workspace_keeps)

        response = self.client.get(self.url + "?only_enrolled=true", **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)
        self.assertEqual(response["results"][0]["id"], str(mission_enrolled.id))

    def test_mission_list_by_instructor(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id), "role": INSTRUCTOR}
        )

        mission = self.create_missions()
        live = mommy.make(LiveMission, mission=mission)
        live.instructors.add(self.user_consumer)
        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

    def test_mission_get_success(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id), "role": USER}
        )
        mission = self.create_missions()
        mommy.make(MissionStage, mission=mission)

        response = self.client.get(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json["mission_type"]["id"], MissionTypeEnum.PUBLIC.value)
        self.assertEqual(response_json["mission_category"]["name"], self.mission_category.name)

    def test_mission_get_success_internal_code(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id), "role": USER}
        )
        mission = self.create_missions()
        mommy.make(MissionStage, mission=mission)

        response = self.client.get(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json["internal_code"], mission.internal_code)

    def test_mission_get_with_evaluated_enrollment_null(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id), "role": USER}
        )
        mission = self.create_missions()
        mommy.make(MissionEvaluation, enrollment=None)
        response = self.client.get(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json["enrollment"], None)

    def test_mission_delete_with_evaluated_enrollment_null(self, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )
        mission = self.create_missions()
        mommy.make(MissionEnrollment, user=self.user_creator, mission=mission, workspace=self.workspace_keeps)
        mommy.make(MissionEvaluation, enrollment=None)
        response = self.client.delete(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        self.assertEqual(response.status_code, 204)

    def test_mission_get_with_evaluated_enrollment_invalid(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id), "role": USER}
        )
        mission = self.create_missions()
        enrollment = MissionEnrollment()
        mommy.make(MissionEvaluation, enrollment=enrollment)
        response = self.client.get(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json["enrollment"], None)

    def test_mission_delete_with_evaluated_enrollment_invalid(self, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )
        mission = self.create_missions()
        enrollment = MissionEnrollment()
        mommy.make(MissionEvaluation, enrollment=enrollment)
        response = self.client.delete(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        self.assertEqual(response.status_code, 204)

    def test_mission_get_success_with_many_enrollments(self, check_role, get_user_roles):
        """
        When user have many enrollments in the same mission-workspace
        return the recent enrollment in serializer
        """
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id), "role": USER}
        )
        mission = self.create_missions()
        older_enrollment = mommy.make(
            MissionEnrollment,
            mission=mission,
            user=self.user_consumer,
            workspace=self.workspace_keeps,
            status="COMPLETED",
            end_date=datetime.datetime.today(),
        )
        new_enrollment = mommy.make(
            MissionEnrollment,
            mission=older_enrollment.mission,
            user=older_enrollment.user,
            workspace=older_enrollment.workspace,
            status="STARTED",
        )

        response = self.client.get(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json["enrollment"].get("id"), str(new_enrollment.id))
        self.assertEqual(response_json["users_finished"], 1)

    def test_mission_get_not_found(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id), "role": USER}
        )
        self.create_missions()
        response = self.client.get(reverse("missions-detail", args=[str(uuid.uuid4())]), **self.headers, format="json")
        response_json = response.json()

        self.assertEqual(response_json["i18n"], "mission_not_found")
        self.assertEqual(response.status_code, 404)

    def test_get_live_mission(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "role": USER, "client_id": str(self.workspace_keeps.id)}
        )
        mission = self.create_missions()
        live = LiveMission(url="https://url.com", seats=1, mission=mission)
        live.save()
        response = self.client.get(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["live"]["url"], live.url)
        self.assertEqual(response.data["live"]["seats"], live.seats)
        self.assertEqual(response.data["live"]["remaining_seats"], live.remaining_seats)
        self.assertEqual(response.data["live"]["is_finished"], False)

    def test_get_live_mission_is_finished(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "role": USER, "client_id": str(self.workspace_keeps.id)}
        )
        mission = self.create_missions()
        live = LiveMission(url="https://url.com", seats=1, mission=mission)
        live.save()
        date = LiveMissionDates(
            start_at=datetime.datetime.now() - datetime.timedelta(days=2),
            end_at=datetime.datetime.now() - datetime.timedelta(days=1),
            live=live,
        )
        date.save()
        finish_sync_mission(mission.id)
        response = self.client.get(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        self.assertEqual(response.data["live"]["is_finished"], True)

    def test_get_live_mission_not_is_finish(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "role": USER, "client_id": str(self.workspace_keeps.id)}
        )
        mission = self.create_missions()
        live = LiveMission(url="https://url.com", seats=1, mission=mission)
        live.save()
        date = LiveMissionDates(
            start_at=datetime.datetime.now() + datetime.timedelta(days=2),
            end_at=datetime.datetime.now() + datetime.timedelta(days=1),
            live=live,
        )
        date.save()

        response = self.client.get(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        self.assertEqual(response.data["live"]["is_finished"], False)

    def test_get_live_mission_not_add_date(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "role": USER, "client_id": str(self.workspace_keeps.id)}
        )
        mission = self.create_missions()
        live = LiveMission(url="https://url.com", seats=1, mission=mission)
        live.save()

        response = self.client.get(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        self.assertEqual(response.data["live"]["is_finished"], False)

    def test_get_presential_mission_is_finished(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "role": USER, "client_id": str(self.workspace_keeps.id)}
        )
        mission = self.create_missions()
        presential = PresentialMission(seats=1, mission=mission)
        presential.save()
        date = PresentialMissionDates(
            start_at=datetime.datetime.now() - datetime.timedelta(days=2),
            end_at=datetime.datetime.now() - datetime.timedelta(days=1),
            presential=presential,
        )
        date.save()
        finish_sync_mission(mission.id)
        response = self.client.get(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        self.assertEqual(response.data["presential"]["is_finished"], True)

    def test_get_presential_mission_not_is_finish(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "role": USER, "client_id": str(self.workspace_keeps.id)}
        )
        mission = self.create_missions()
        presential = PresentialMission(seats=1, mission=mission)
        presential.save()
        date = PresentialMissionDates(
            start_at=datetime.datetime.now() + datetime.timedelta(days=2),
            end_at=datetime.datetime.now() + datetime.timedelta(days=1),
            presential=presential,
        )
        date.save()

        response = self.client.get(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        self.assertEqual(response.data["presential"]["is_finished"], False)

    def test_get_presential_mission_not_add_date(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "role": USER, "client_id": str(self.workspace_keeps.id)}
        )
        mission = self.create_missions()
        presential = PresentialMission(seats=1, mission=mission)
        presential.save()

        response = self.client.get(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        self.assertEqual(response.data["presential"]["is_finished"], False)

    def test_mission_internal_post_success(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id)}
        )

        data = {
            "name": "Test New mission",
            "description": "Test New mission",
            "mission_type": str(self.mission_type_public),
            "mission_category": str(self.mission_category.id),
            "user_creator": str(self.user_creator.id),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)

    def test_mission_post_internal_code(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id)}
        )

        data = {
            "name": "Test New mission",
            "description": "Test New mission",
            "mission_type": str(self.mission_type_public),
            "mission_category": str(self.mission_category.id),
            "user_creator": str(self.user_creator.id),
            "internal_code": "22b634TE",
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        data = response.json()
        self.assertEqual(response.status_code, 201)
        self.assertEqual(data["internal_code"], "22b634TE")

    def test_mission_external_post_success(self, check_role, get_user_roles):
        course_url = "https://course.com"
        course_type = "FREE"
        data = {
            "name": "Test New mission",
            "description": "Test New mission",
            "mission_type": str(self.mission_type_public),
            "mission_category": str(self.mission_category.id),
            "user_creator": str(self.user_creator.id),
            "duration_time": 1000,
            "external": {"provider": str(self.provider.id), "course_url": course_url, "course_type": course_type},
        }

        response = self.client.post(reverse("missions-external"), **self.headers, data=data, format="json")
        response_data = response.json()

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response_data.get("external").get("provider"), str(self.provider.id))
        self.assertEqual(response_data.get("external").get("course_url"), course_url)
        self.assertEqual(response_data.get("external").get("course_type"), course_type)
        self.assertTrue(response_data.get("points") > 0)
        self.assertTrue(response_data.get("development_status"), "DONE")

    def test_mission_post_required_field(self, check_role, get_user_roles):
        data = {
            "name": "Test New mission",
            "description": "Test New mission",
            "mission_type": str(self.mission_type_public),
            "mission_category": str(self.mission_category.id),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response_json["user_creator"], ["This field is required."])
        self.assertEqual(response.status_code, 400)

    def test_mission_post_invalid_foreign_key(self, check_role, get_user_roles):
        data = {
            "name": "Test New mission",
            "description": "Test New mission",
            "mission_type": str(self.mission_type_public),
            "mission_category": str(self.mission_category.id),
            "user_creator": str(uuid.uuid4()),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertIsNotNone(response_json.get("user_creator"))
        self.assertEqual(response.status_code, 400)

    def test_mission_patch_success(self, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )
        mission = self.create_missions()

        data = {"name": "Test mission"}

        response = self.client.put(
            reverse("missions-detail", args=[str(mission.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["name"], data["name"])
        self.assertEqual(response.status_code, 200)

    def test_mission_patch_summary_success(self, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )
        mission = self.create_missions()

        data = {"summary": "Test mission"}

        response = self.client.put(
            reverse("missions-detail", args=[str(mission.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json["summary"], data["summary"])

    @mock.patch(CELERY_SEND_TASK_PATH)
    def test_inactive_mission(self, inactive, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )
        mission = self.create_missions()

        data = {"is_active": False}

        response = self.client.put(
            reverse("missions-detail", args=[str(mission.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json["is_active"], data["is_active"])
        self.assertEqual(response_json["development_status"], INACTIVATED)

    def test_external_mission_patch_success(self, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )
        mission = mommy.make(
            Mission,
            mission_type_id=str(self.mission_type_open_for_workspace),
            mission_category=self.mission_category,
            user_creator=self.user_creator,
        )
        mommy.make(ExternalMission, mission=mission, provider=self.provider)
        MissionWorkspace(mission=mission, workspace=self.workspace_keeps).save()

        data = {
            "name": "Test mission",
            "external": {"course_url": "https://new_course.com", "provider": str(self.provider_2.id)},
        }

        response = self.client.patch(
            reverse("missions-external-detail", args=[str(mission.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()
        self.assertEqual(response_json["name"], data["name"])
        self.assertEqual(response.status_code, 200)

    @mock.patch(CELERY_SEND_TASK_PATH)
    def test_inactive_external_mission(self, check_role, get_user_roles, send_task):
        mission = mommy.make(Mission, user_creator=self.user_consumer)
        MissionWorkspace(mission=mission, workspace=self.workspace_keeps).save()

        data = {"is_active": False}

        response = self.client.patch(
            reverse("missions-external-detail", args=[str(mission.id)]), **self.headers, data=data, format="json"
        )

        self.assertEqual(response.status_code, 200)
        self.assertFalse(response.json()["is_active"])

    def test_mission_put_success(self, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )
        mission = self.create_missions()

        data = {
            "name": "Test New mission",
            "description": "Test New mission",
            "mission_type": str(self.mission_type_public),
            "mission_category": str(self.mission_category.id),
            "user_creator": str(self.user_creator.id),
        }

        response = self.client.put(
            reverse("missions-detail", args=[str(mission.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["name"], data["name"])
        self.assertEqual(response.status_code, 200)

    def test_mission_put_not_found(self, check_role, get_user_roles):
        data = {
            "name": "Test New mission",
            "description": "Test New mission",
            "mission_type": str(self.mission_type_public),
            "mission_category": str(self.mission_category.id),
            "user_creator": str(self.user_creator.id),
        }

        response = self.client.put(
            reverse("missions-detail", args=[str(uuid.uuid4())]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["i18n"], "mission_not_found")
        self.assertEqual(response.status_code, 404)

    def test_mission_delete_success(self, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )
        mission = self.create_missions()

        response = self.client.delete(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        mission.refresh_from_db()

        self.assertEqual(mission.deleted, True)
        self.assertEqual(response.status_code, 204)

    @override_settings(SUSPEND_SIGNALS=False)
    @mock.patch("django.db.transaction.on_commit")
    def test_mission_delete_live_success(self, on_commit, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )
        mission = self._create_live_mission()

        response = self.client.delete(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        self.assertEqual(response.status_code, 204)

    def test_mission_with_content_resume_delete_success(self, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )
        mission = self.create_missions()
        mommy.make(MissionContentResume, mission=mission)
        response = self.client.delete(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        self.assertEqual(response.status_code, 204)

    def test_mission_delete_not_found(self, check_role, get_user_roles):
        response = self.client.delete(
            reverse("missions-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["i18n"], "mission_not_found")
        self.assertEqual(response.status_code, 404)

    def test_mission_shared_delete_not_access(self, check_role, get_user_roles):
        mission_mosaic_shared = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Mosaic shared mission created in past",
            mission_type_id=str(self.mission_type_public),
            mission_category=self.mission_category,
            user_creator=self.user_creator,
            development_status="DONE",
            is_active=True,
        )
        MissionWorkspace(
            id=uuid.uuid4(), mission=mission_mosaic_shared, workspace=self.workspace_keeps, relationship_type="SHARED"
        ).save()

        response = self.client.delete(
            reverse("missions-detail", args=[str(mission_mosaic_shared.id)]), **self.headers, format="json"
        )

        self.assertEqual(response.data["detail"], "not_permission_to_modify_this_mission")
        self.assertEqual(response.status_code, 403)

    @mock.patch(CELERY_SEND_TASK_PATH)
    def test_mission_publish_success(self, mock_notify, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )
        new_mission = mommy.make(Mission, user_creator=self.user_creator)
        mommy.make(MissionWorkspace, mission=new_mission, workspace=self.workspace_keeps, relationship_type="OWNER")
        url = reverse("missions-publish", args=[str(new_mission.id)])
        response = self.client.post(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {"status": "Mission Content Analyze Started"})

    @mock.patch(CELERY_SEND_TASK_PATH)
    def test_external_mission_publish_success(self, mock_notify, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )
        new_mission = mommy.make(
            Mission, user_creator=self.user_creator, mission_model="EXTERNAL_PROVIDER", duration_time=10
        )
        mommy.make(
            ExternalMission, mission=new_mission, course_type="FREE", course_url="url.com", provider=self.provider
        )
        mommy.make(MissionWorkspace, mission=new_mission, workspace=self.workspace_keeps, relationship_type="OWNER")
        url = reverse("missions-publish", args=[str(new_mission.id)])
        response = self.client.post(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {"status": "Mission Content Analyze Started"})

    def test_detail_closed_mission_enrolled(self, check_role, get_user_roles):
        closed_mission = mommy.make(
            Mission,
            mission_type_id=str(self.mission_type_close_for_workspace),
            development_status="DONE",
            is_active=True,
        )
        mommy.make(MissionWorkspace, mission=closed_mission, workspace=self.workspace_keeps)
        mommy.make(LearningTrailStep, learning_trail_id=self.trail_id, mission=closed_mission)
        mommy.make(LearningTrailEnrollment, learning_trail_id=self.trail_id, user=self.user_consumer)
        mommy.make(MissionEnrollment, mission=closed_mission, user=self.user_consumer, workspace=self.workspace_keeps)

        response = self.client.get(
            reverse("missions-detail", args=[str(closed_mission.id)]),
            **self.headers,
        )
        self.assertEqual(response.status_code, 200)

    def test_external_mission_publish_not_found_error(self, check_role, get_user_roles):
        url = reverse("missions-publish", args=[str(uuid.uuid4())])
        response = self.client.post(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.data.get("i18n"), I18N_MISSION_NOT_FOUND)

    def test_detail_an_inactive_mission_by_admin(self, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "role": ADMIN, "client_id": str(self.workspace_keeps.id)}
        )
        mission = mommy.make(Mission, mission_type_id=str(self.mission_type_open_for_workspace), is_active=False)
        mommy.make(MissionWorkspace, mission=mission, workspace=self.workspace_keeps)

        url = reverse("missions-detail", args=[str(mission.id)])
        response = self.client.get(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)

    def test_detail_an_inactive_mission_by_manager(self, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "role": CONTENT, "client_id": str(self.workspace_keeps.id)}
        )
        mission = mommy.make(
            Mission,
            mission_type_id=str(self.mission_type_open_for_workspace),
            is_active=False,
            user_creator=self.user_creator,
        )
        mommy.make(MissionWorkspace, mission=mission, workspace=self.workspace_keeps)

        url = reverse("missions-detail", args=[str(mission.id)])
        response = self.client.get(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)

    def test_mission_patch_mission_type(self, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )
        mission = self.create_missions()

        data = {
            "name": "Test New mission",
            "description": "Test New mission",
            "mission_type": str(self.mission_type_close_for_workspace),
            "mission_category": str(self.mission_category.id),
            "user_creator": str(self.user_creator.id),
        }

        response = self.client.put(
            reverse("missions-detail", args=[str(mission.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["name"], data["name"])
        self.assertEqual(response_json["mission_type"], self.mission_type_close_for_workspace)
        self.assertEqual(response.status_code, 200)

    def test_mission_detail_fields(self, check_role, get_user_roles):
        mission = self.create_missions()
        mommy.make(MissionStage, mission=mission)
        presential = mommy.make(PresentialMission, mission=mission)
        mommy.make(PresentialMissionDates, presential=presential)
        response = self.client.get(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        data = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(data["assessment_type"], "FULL")
        self.assertEqual(data["bookmark_id"], None)
        self.assertEqual(data["content_resume"], [])
        self.assertEqual(data["contributors"], [])
        self.assertIsNotNone(data["created_date"])
        self.assertEqual(data["description"], None)
        self.assertEqual(data["development_status"], "DONE")
        self.assertEqual(data["duration_time"], None)
        self.assertEqual(data["enrollment"], None)
        self.assertEqual(data["expiration_date"], None)
        self.assertEqual(data["external"], None)
        self.assertEqual(data["external_course_url"], None)
        self.assertEqual(data["holder_image"], None)
        self.assertEqual(data["id"], str(mission.id))
        self.assertEqual(data["is_active"], True)
        self.assertEqual(data["is_contributor"], False)
        self.assertEqual(data["is_owner"], False)
        self.assertEqual(data["language"], "pt-BR")
        self.assertEqual(data["live"], None)
        self.assertTrue("id" in data["mission_category"] and "name" in data["mission_category"])
        self.assertEqual(data["mission_model"], "INTERNAL")
        mission_type = data["mission_type"]
        self.assertTrue("id" in mission_type and "name" in mission_type and "image" in mission_type)
        self.assertEqual(data["name"], "Keeps public mission")
        self.assertEqual(data["points"], None)
        self.assertIsNotNone(data["presential"])
        self.assertEqual(data["provider"], None)
        self.assertEqual(data["rating_avg"], 0)
        self.assertEqual(data["required_evaluation"], True)
        self.assertEqual(data["stages"], 1)
        self.assertEqual(data["summary"], None)
        self.assertEqual(data["tags"], [])
        self.assertEqual(data["thumb_image"], None)
        self.assertIsNotNone(data["updated_date"])
        user_creator = data["user_creator"]
        self.assertTrue("id" in user_creator and "name" in user_creator and "avatar" in user_creator)
        self.assertEqual(data["users_enrolled"], 0)
        self.assertEqual(data["users_finished"], 0)
        self.assertEqual(data["vertical_holder_image"], None)

    def create_missions(self):
        mission_template = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Keeps public mission",
            mission_type_id=str(self.mission_type_public),
            mission_category=self.mission_category,
            user_creator=self.user_creator,
            development_status="DONE",
            minimum_performance=0.2,
            internal_code="22b634TE",
            enrollments_accepted=1,
        )
        MissionWorkspace(mission=mission_template, workspace=self.workspace_keeps).save()

        mission_keeps_open = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Keeps open for workspace mission",
            mission_type_id=str(self.mission_type_open_for_workspace),
            mission_category=self.mission_category_2,
            user_creator=self.user_creator,
            development_status="DONE",
            is_active=True,
        )
        MissionWorkspace(mission=mission_keeps_open, workspace=self.workspace_keeps).save()

        mission_keeps_close = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Keeps close for workspace mission",
            mission_type_id=str(self.mission_type_close_for_workspace),
            mission_category=self.mission_category,
            user_creator=self.user_creator,
            development_status="DONE",
            is_active=True,
        )
        MissionWorkspace(mission=mission_keeps_close, workspace=self.workspace_keeps).save()

        mission_keeps_paid = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Keeps paid mission",
            mission_type_id=str(self.mission_type_paid),
            mission_category=self.mission_category,
            user_creator=self.user_creator,
            development_status="DONE",
            is_active=True,
        )
        MissionWorkspace(mission=mission_keeps_paid, workspace=self.workspace_keeps).save()

        mission_bayer_open = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Bayer open for workspace mission",
            mission_type_id=str(self.mission_type_open_for_workspace),
            mission_category=self.mission_category,
            user_creator=self.user_creator,
            development_status="DONE",
            is_active=True,
        )
        MissionWorkspace(mission=mission_bayer_open, workspace=self.workspace_bayer).save()

        mission_bayer_close = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Bayer close for workspace mission",
            mission_type_id=str(self.mission_type_close_for_workspace),
            mission_category=self.mission_category,
            user_creator=self.user_creator,
            development_status="DONE",
            is_active=True,
        )
        MissionWorkspace(mission=mission_bayer_close, workspace=self.workspace_bayer).save()

        mission_mosaic_open = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Mosaic open for workspace mission",
            mission_type_id=str(self.mission_type_open_for_workspace),
            mission_category=self.mission_category,
            user_creator=self.user_creator,
            development_status="DONE",
            is_active=True,
        )
        MissionWorkspace(mission=mission_mosaic_open, workspace=self.workspace_mosaic).save()

        mission_mosaic_close = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Mosaic close for workspace mission",
            mission_type_id=str(self.mission_type_close_for_workspace),
            mission_category=self.mission_category,
            user_creator=self.user_creator,
            development_status="DONE",
            is_active=True,
        )
        MissionWorkspace(mission=mission_mosaic_close, workspace=self.workspace_mosaic).save()

        mission_mosaic_public = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Mosaic public mission created in past",
            mission_type_id=str(self.mission_type_public),
            mission_category=self.mission_category,
            user_creator=self.user_creator,
            development_status="DONE",
            is_active=True,
        )
        MissionWorkspace(mission=mission_mosaic_public, workspace=self.workspace_mosaic).save()

        return mission_template

    def _create_live_mission(self) -> Mission:
        mission = mommy.make(Mission, user_creator_id=self.user_creator.id)
        live = mommy.make(
            LiveMission, mission=mission, seats=1, remaining_seats=1, url="https://www.google.meet.com/id-da-chamada"
        )
        mommy.make(LiveMissionDates, live=live)
        MissionWorkspace(mission=mission, workspace_id=self.workspace_keeps.id).save()
        mission.refresh_from_db()
        return mission

    def test_mission_detail_users_enrolled(self, check_role, get_user_roles):
        mission = self.create_missions()
        mommy.make(MissionStage, mission=mission)
        live = mommy.make(LiveMission, mission=mission)
        mommy.make(LiveMissionDates, live=live)
        mommy.make(
            MissionEnrollment,
            user=self.user_creator,
            mission=mission,
            goal_date=datetime.datetime.now(),
            status="ENROLLED",
        )
        response = self.client.get(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        data = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(data["enrollments_accepted"], 1)

    def test_mission_list_filter_minimum_performance_range(self, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id), "role": ADMIN}
        )
        self.create_missions()

        response = self.client.get(
            self.url + "?minimum_performance__gte=0.1&minimum_performance__lte=0.2", **self.headers, format="json"
        ).json()
        self.assertEqual(len(response["results"]), 1)

    def test_mission_list_filter_minimum_performance_workspace_range(self, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id), "role": ADMIN}
        )
        self.create_missions()

        response = self.client.get(
            self.url + "?minimum_performance__gte=0.8&minimum_performance__lte=0.8", **self.headers, format="json"
        ).json()

        self.assertEqual(len(response["results"]), 3)

    def test_mission_get_success_workspace_min_performance(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id), "role": USER}
        )
        mission = self.create_missions()
        mommy.make(MissionStage, mission=mission)

        response = self.client.get(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json["workspace_min_performance"], 0.8)

    def test_mission_get_success_workspace_source_id(self, check_role, get_user_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id), "role": USER}
        )
        mission = self.create_missions()
        mommy.make(MissionStage, mission=mission)

        response = self.client.get(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        response_json = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json["workspace_source_id"], str(self.workspace_keeps.id))

    def test_detail_a_mission_with_content_contributor(self, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "role": CONTENT, "client_id": str(self.workspace_keeps.id)}
        )
        mission = mommy.make(
            Mission,
            mission_type_id=str(self.mission_type_close_for_workspace_right),
            is_active=True,
            user_creator=self.user_creator,
        )
        mommy.make(MissionWorkspace, mission=mission, workspace=self.workspace_keeps)
        mommy.make(MissionContributor, mission=mission, user=self.user_consumer)

        url = reverse("missions-detail", args=[str(mission.id)])
        response = self.client.get(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)

    def test_mission_list_only_favorites(self, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id), "role": ADMIN}
        )
        mission = self.create_missions()

        mission_bookmark = mommy.make(MissionBookmark, mission=mission, user=self.user_consumer)

        response = self.client.get(self.url + "?favorites=true", **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)
        self.assertEqual(response["results"][0]["id"], str(mission_bookmark.mission.id))

    def test_mission_list_filter_mission_category(self, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id), "role": ADMIN}
        )
        self.create_missions()
        response = self.client.get(
            self.url + f"?mission_category={str(self.mission_category.id)},{str(self.mission_category_2.id)}",
            **self.headers,
            format="json",
        ).json()
        self.assertEqual(len(response["results"]), 4)

    def test_create_external_mission_with_partial_data(self, check_role, get_user_roles):
        data = {
            "name": "Test New mission",
            "description": "Test New mission",
            "mission_type": str(self.mission_type_public),
            "mission_category": str(self.mission_category.id),
            "user_creator": str(self.user_creator.id),
            "language": "pt-BR",
        }

        response = self.client.post(reverse("missions-external"), **self.headers, data=data, format="json")
        response_data = response.json()

        self.assertEqual(response.status_code, 201)
        self.assertTrue(response_data.get("development_status"), IN_PROGRESS)
        self.assertTrue(response_data.get("mission_model"), EXTERNAL_PROVIDER)

    def test_create_live_mission_with_partial_data(self, check_role, get_user_roles):
        data = {
            "name": "Test New mission",
            "description": "Test New mission",
            "mission_type": str(self.mission_type_public),
            "mission_category": str(self.mission_category.id),
            "user_creator": str(self.user_creator.id),
            "language": "pt-BR",
        }

        response = self.client.post(reverse("missions-live-list"), **self.headers, data=data, format="json")
        response_data = response.json()

        self.assertEqual(response.status_code, 201)
        self.assertTrue(response_data.get("development_status"), IN_PROGRESS)
        self.assertTrue(response_data.get("mission_model"), LIVE)

    def test_create_presential_mission_with_partial_data(self, check_role, get_user_roles):
        data = {
            "name": "Test New mission",
            "description": "Test New mission",
            "mission_type": str(self.mission_type_public),
            "mission_category": str(self.mission_category.id),
            "user_creator": str(self.user_creator.id),
            "language": "pt-BR",
        }

        response = self.client.post(reverse("missions-presential"), **self.headers, data=data, format="json")
        response_data = response.json()

        self.assertEqual(response.status_code, 201)
        self.assertTrue(response_data.get("development_status"), IN_PROGRESS)
        self.assertTrue(response_data.get("mission_model"), PRESENTIAL)

    def test_mission_language_data(self, check_role, get_user_roles):
        data = [{"i18n": lang[0], "name": lang[1]} for lang in LanguageEnum.choices()]
        response = self.client.get(reverse("mission-language-list"), **self.headers, format="json")
        response_data = response.json()

        self.assertEqual(response.status_code, 201)
        self.assertEqual(data, response_data)

    def test_external_mission_integrated_provider_patch_not_allowed(self, check_role, get_user_roles):
        user_creator = mommy.make(User, id=settings.USER_OWNER_ALURA_INTEGRATION_ID)
        self.client.force_authenticate(user={"sub": str(user_creator.id), "client_id": str(self.workspace_keeps.id)})
        mission = mommy.make(
            Mission,
            mission_type_id=str(self.mission_type_open_for_workspace),
            mission_category=self.mission_category,
            user_creator=user_creator,
        )
        mommy.make(ExternalMission, mission=mission, provider=self.provider)
        MissionWorkspace(mission=mission, workspace=self.workspace_keeps).save()

        data = {
            "name": "Test mission",
            "external": {"course_url": "https://new_course.com", "provider": str(self.provider_2.id)},
        }

        response = self.client.patch(
            reverse("missions-external-detail", args=[str(mission.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()
        self.assertEqual(response_json["detail"][0], "External mission integrated cannot be changed")

    def test_integrated_mission_not_allow_delete(self, check_role, get_user_roles):
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )
        mission = self.create_missions()
        mission.user_creator.id = settings.USER_OWNER_ALURA_INTEGRATION_ID
        mission.user_creator.save()
        mission.user_creator_id = settings.USER_OWNER_ALURA_INTEGRATION_ID
        mission.save()
        response = self.client.delete(reverse("missions-detail", args=[str(mission.id)]), **self.headers, format="json")
        mission.refresh_from_db()

        self.assertEqual(mission.deleted, False)
        self.assertEqual(response.status_code, 403)
