import uuid

import mock
from constants import CELERY_SEND_TASK_PATH
from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from account.models import User, Workspace
from authentication.keeps_permissions import C<PERSON>AT<PERSON>, SUPER_ADMIN
from constants import CELERY_SEND_TASK_PATH
from mission.models import (
    Mission,
    MissionCategory,
    MissionStage,
    MissionStageContent,
    MissionType,
    MissionTypeEnum,
    MissionWorkspace,
)
from mission.models.mission_content_type_enum import CONTENT, EXAM
from rest_clients.kontent import KontentClient

CHECK_ROLE = "authentication.keeps_permissions.KeepsBasePermission._check_role"
SEND_TASK = "celery.Celery.send_task"


@mock.patch(CHECK_ROLE)
class MissionPermissionsViewsetTestCase(TestCase):
    def setUp(self) -> None:
        self.user_creator = mommy.make(User, email="<EMAIL>")
        self.user_super_admin = mommy.make(User, email="<EMAIL>")
        self.user_curator = mommy.make(User, email="<EMAIL>")
        self.workspace = mommy.make(Workspace)

        self.mission_detail_url_name = "missions-detail"
        self.mission_publish_url_name = "missions-publish"
        self.mission_stages_url_name = "mission-stages-detail"
        self.mission_stages_list_url_name = "mission-stages-list"
        self.mission_stage_contents_url_name = "mission-stage-contents"
        self.mission_stage_content_url_name = "mission-stage-content"

        self.client = APIClient()
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}

    def test_super_admin_can_update_any_mission_workspace(self, check_role):
        self.client.force_authenticate(
            user={"sub": self.user_super_admin.id, "role": SUPER_ADMIN, "client_id": self.workspace.id}
        )
        mission = self._create_mission()
        mission_url = reverse(self.mission_detail_url_name, args=[str(mission.id)])
        payload = {"name": "New Name"}
        response = self.client.patch(mission_url, data=payload, **self.headers, format="json")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["name"], payload.get("name"))

    def test_super_admin_can_delete_any_mission_workspace(self, check_role):
        self.client.force_authenticate(
            user={"sub": self.user_super_admin.id, "role": SUPER_ADMIN, "client_id": self.workspace.id}
        )
        mission = self._create_mission()
        mission_url = reverse(self.mission_detail_url_name, args=[str(mission.id)])
        response = self.client.delete(mission_url, **self.headers, format="json")
        self.assertEqual(response.status_code, 204)

    @mock.patch(CELERY_SEND_TASK_PATH)
    @mock.patch.object(KontentClient, "create_learn_contents_analyzed_check")
    def test_super_admin_can_publish_any_mission(self, create_check_learn_contents_analyzed, notify, check_role):
        self.client.force_authenticate(
            user={"sub": self.user_super_admin.id, "role": SUPER_ADMIN, "client_id": self.workspace.id}
        )
        mission = self._create_mission()
        mission_url = reverse(self.mission_publish_url_name, args=[str(mission.id)])
        response = self.client.post(mission_url, **self.headers, format="json")
        self.assertEqual(response.status_code, 200)

    def test_super_admin_can_create_any_mission_stage(self, check_role):
        self.client.force_authenticate(
            user={"sub": self.user_super_admin.id, "role": SUPER_ADMIN, "client_id": self.workspace.id}
        )
        mission = self._create_mission()
        data = {"mission": str(mission.id), "name": "mission_stage_test", "order": 1}
        mission_url = reverse(self.mission_stages_list_url_name)

        response = self.client.post(mission_url, data=data, **self.headers, format="json")

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["mission"], mission.id)
        self.assertEqual(response.data["name"], data["name"])

    def test_super_admin_can_update_any_mission_stage_workspace(self, check_role):
        self.client.force_authenticate(
            user={"sub": self.user_super_admin.id, "role": SUPER_ADMIN, "client_id": self.workspace.id}
        )
        mission_stage = self._create_mission_stage()
        mission_url = reverse(self.mission_stages_url_name, args=[str(mission_stage.id)])
        payload = {"name": "New Name"}

        response = self.client.patch(mission_url, data=payload, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["name"], payload.get("name"))

    def test_super_admin_can_delete_any_mission_stage(self, check_role):
        self.client.force_authenticate(
            user={"sub": self.user_super_admin.id, "role": SUPER_ADMIN, "client_id": self.workspace.id}
        )
        mission_stage = self._create_mission_stage()
        mission_url = reverse(self.mission_stages_url_name, args=[str(mission_stage.id)])

        response = self.client.delete(mission_url, **self.headers, format="json")

        self.assertEqual(response.status_code, 204)

    def test_super_admin_can_create_any_mission_stage_content(self, check_role):
        self.client.force_authenticate(
            user={"sub": self.user_super_admin.id, "role": SUPER_ADMIN, "client_id": self.workspace.id}
        )
        stage = self._create_mission_stage()
        data = {
            "stage": stage.id,
            "name": "mission_stage_test",
            "order": 1,
            "learn_content_uuid": uuid.uuid4(),
            "content_type": CONTENT,
        }
        mission_url = reverse(self.mission_stage_contents_url_name)

        with mock.patch(SEND_TASK) as send_task:
            response = self.client.post(mission_url, data=data, **self.headers, format="json")

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["stage"], stage.id)
        self.assertEqual(response.data["name"], data["name"])

    def test_super_admin_can_update_any_mission_stage_content_workspace(self, check_role):
        self.client.force_authenticate(
            user={"sub": self.user_super_admin.id, "role": SUPER_ADMIN, "client_id": self.workspace.id}
        )
        content = self._create_mission_stage_content()
        url = reverse(self.mission_stage_content_url_name, args=[str(content.id)])
        payload = {"name": "New Name"}

        response = self.client.patch(url, data=payload, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["name"], payload.get("name"))

    def test_super_admin_can_delete_any_mission_stage_content(self, check_role):
        self.client.force_authenticate(
            user={"sub": self.user_super_admin.id, "role": SUPER_ADMIN, "client_id": self.workspace.id}
        )
        content = self._create_mission_stage_content()
        url = reverse(self.mission_stage_content_url_name, args=[str(content.id)])
        with mock.patch(SEND_TASK):
            response = self.client.delete(url, **self.headers, format="json")
        self.assertEqual(response.status_code, 204)

    def test_curator_can_update_any_mission_workspace(self, check_role):
        self.client.force_authenticate(
            user={"sub": self.user_curator.id, "role": CURATOR, "client_id": self.workspace.id}
        )
        mission = self._create_mission()
        mission_url = reverse(self.mission_detail_url_name, args=[str(mission.id)])
        payload = {"name": "New Name"}
        response = self.client.patch(mission_url, data=payload, **self.headers, format="json")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["name"], payload.get("name"))

    def test_curator_can_delete_any_mission_workspace(self, check_role):
        self.client.force_authenticate(
            user={"sub": self.user_curator.id, "role": CURATOR, "client_id": self.workspace.id}
        )
        mission = self._create_mission()
        mission_url = reverse(self.mission_detail_url_name, args=[str(mission.id)])
        response = self.client.delete(mission_url, **self.headers, format="json")
        self.assertEqual(response.status_code, 204)

    @mock.patch(CELERY_SEND_TASK_PATH)
    @mock.patch.object(KontentClient, "create_learn_contents_analyzed_check")
    def test_curator_can_publish_any_mission(self, create_check_learn_contents_analyzed, notify, check_role):
        self.client.force_authenticate(
            user={"sub": self.user_curator.id, "role": CURATOR, "client_id": self.workspace.id}
        )
        mission = self._create_mission()
        mission_url = reverse(self.mission_publish_url_name, args=[str(mission.id)])
        response = self.client.post(mission_url, **self.headers, format="json")
        self.assertEqual(response.status_code, 200)

    def test_curator_can_create_any_mission_stage(self, check_role):
        self.client.force_authenticate(
            user={"sub": self.user_curator.id, "role": CURATOR, "client_id": self.workspace.id}
        )
        mission = self._create_mission()
        data = {"mission": str(mission.id), "name": "mission_stage_test", "order": 1}
        mission_url = reverse(self.mission_stages_list_url_name)

        response = self.client.post(mission_url, data=data, **self.headers, format="json")

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["mission"], mission.id)
        self.assertEqual(response.data["name"], data["name"])

    def test_curator_can_update_any_mission_stage_workspace(self, check_role):
        self.client.force_authenticate(
            user={"sub": self.user_curator.id, "role": CURATOR, "client_id": self.workspace.id}
        )
        mission_stage = self._create_mission_stage()
        mission_url = reverse(self.mission_stages_url_name, args=[str(mission_stage.id)])
        payload = {"name": "New Name"}

        response = self.client.patch(mission_url, data=payload, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["name"], payload.get("name"))

    def test_curator_can_delete_any_mission_stage(self, check_role):
        self.client.force_authenticate(
            user={"sub": self.user_curator.id, "role": CURATOR, "client_id": self.workspace.id}
        )
        mission_stage = self._create_mission_stage()
        mission_url = reverse(self.mission_stages_url_name, args=[str(mission_stage.id)])

        response = self.client.delete(mission_url, **self.headers, format="json")

        self.assertEqual(response.status_code, 204)

    def test_curator_can_create_any_mission_stage_content(self, check_role):
        self.client.force_authenticate(
            user={"sub": self.user_curator.id, "role": CURATOR, "client_id": self.workspace.id}
        )
        stage = self._create_mission_stage()
        data = {
            "stage": stage.id,
            "name": "mission_stage_test",
            "order": 1,
            "learn_content_uuid": uuid.uuid4(),
            "content_type": CONTENT,
        }
        mission_url = reverse(self.mission_stage_contents_url_name)

        with mock.patch(SEND_TASK):
            response = self.client.post(mission_url, data=data, **self.headers, format="json")

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["stage"], stage.id)
        self.assertEqual(response.data["name"], data["name"])

    def test_curator_can_update_any_mission_stage_content_workspace(self, check_role):
        self.client.force_authenticate(
            user={"sub": self.user_curator.id, "role": CURATOR, "client_id": self.workspace.id}
        )
        content = self._create_mission_stage_content()
        url = reverse(self.mission_stage_content_url_name, args=[str(content.id)])
        payload = {"name": "New Name"}

        response = self.client.patch(url, data=payload, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["name"], payload.get("name"))

    def test_curator_can_delete_any_mission_stage_content(self, check_role):
        self.client.force_authenticate(
            user={"sub": self.user_curator.id, "role": CURATOR, "client_id": self.workspace.id}
        )
        content = self._create_mission_stage_content()
        url = reverse(self.mission_stage_content_url_name, args=[str(content.id)])
        with mock.patch(SEND_TASK):
            response = self.client.delete(url, **self.headers, format="json")
        self.assertEqual(response.status_code, 204)

    def _create_mission(self):
        mission_type_open = mommy.make(MissionType, id=MissionTypeEnum.OPEN_FOR_COMPANY.value)
        mission_category = mommy.make(MissionCategory)

        mission = mommy.make(
            Mission, mission_type=mission_type_open, user_creator=self.user_creator, mission_category=mission_category
        )
        mommy.make(MissionWorkspace, mission=mission, workspace=self.workspace, relationship_type="OWNER")

        return mission

    def _create_mission_stage(self) -> MissionStage:
        mission = self._create_mission()
        mission_stage = mommy.make(MissionStage, mission=mission)
        return mission_stage

    def _create_mission_stage_content(self) -> MissionStageContent:
        stage = self._create_mission_stage()
        content = mommy.make(
            MissionStageContent, stage=stage, learn_content_uuid=uuid.uuid4(), content_type=EXAM, order=0
        )
        return content
