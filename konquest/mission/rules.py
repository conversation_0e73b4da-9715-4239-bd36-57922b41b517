import rules

from account.models import User
from authentication.keeps_permissions import ADMIN, SUPER_ADMIN, CURATOR
from constants import (
    CAN_CHANGE_CONTRIBUTOR_MISSION,
    CAN_CHANGE_MISSION_USER_CREATOR,
    CAN_DUPLICATE_MISSION,
    CAN_ROLLBACK_TRANSACTION,
    CAN_SHARE_MISSION,
    CAN_TRANSFER_MISSION,
    RULE_CAN_CHANGE_MISSION,
)
from mission.models import Mission, MissionTransactionTracking
from mission.models.mission import LIVE, PRESENTIAL
from mission.models.mission_workspace import OWNER


@rules.predicate
def is_mission_owner(user: User, mission: Mission):
    return mission.user_creator_id == user.id


@rules.predicate
def is_mission_contributor(user: User, mission: Mission):
    return mission.contributors.filter(user_id=user.id).exists()


@rules.predicate
def is_instructor(user: User, mission: Mission):
    if mission.mission_model in [LIVE, PRESENTIAL]:
        return mission.sync.instructors.filter(id=user.id).exists()
    return False


@rules.predicate
def is_super_admin(user: User, mission: Mission):
    is_workspace_owner = mission.missionworkspace_set.filter(
        relationship_type=OWNER, workspace_id=user.workspace_id
    ).exists()
    return user.role == SUPER_ADMIN and is_workspace_owner


@rules.predicate
def is_admin(user: User, mission: Mission):
    is_workspace_owner = mission.missionworkspace_set.filter(
        relationship_type=OWNER, workspace_id=user.workspace_id
    ).exists()
    return user.role == ADMIN and is_workspace_owner

@rules.predicate
def is_super_admin(user: User, mission: Mission):
    is_workspace_owner = mission.missionworkspace_set.filter(
        relationship_type=CURATOR, workspace_id=user.workspace_id
    ).exists()
    return user.role == SUPER_ADMIN and is_workspace_owner


@rules.predicate
def is_super_admin_or_admin_transaction(user: User, transaction_tracking: MissionTransactionTracking):
    is_workspace_source = str(transaction_tracking.source_id) == str(user.workspace_id)
    is_workspace_receiver = str(transaction_tracking.receiver_id) == str(user.workspace_id)
    return (user.role == SUPER_ADMIN or user.role == ADMIN) and (is_workspace_source or is_workspace_receiver)


rules.add_rule(
    RULE_CAN_CHANGE_MISSION, is_mission_owner | is_mission_contributor | is_super_admin | is_instructor | is_admin
)
rules.add_rule("can_delete_mission", is_mission_owner | is_super_admin)
rules.add_rule(CAN_CHANGE_MISSION_USER_CREATOR, is_mission_owner | is_super_admin)
rules.add_rule(CAN_TRANSFER_MISSION, is_mission_owner | is_super_admin)
rules.add_rule(CAN_DUPLICATE_MISSION, is_mission_owner | is_super_admin)
rules.add_rule(CAN_SHARE_MISSION, is_mission_owner | is_super_admin | is_admin)
rules.add_rule(CAN_ROLLBACK_TRANSACTION, is_super_admin_or_admin_transaction)

rules.add_rule(CAN_CHANGE_CONTRIBUTOR_MISSION, is_mission_owner | is_super_admin | is_admin | is_instructor)
