from account.models import User
from authentication.keeps_permissions import ADMIN, USER
from django.test import TestCase
from mission.models import Mission
from mock import mock
from model_mommy import mommy
from repository import Repository
from repository.repository_manager import RepositoryManager


class StubMissionByUserRepository(Repository):
    roles = (USER,)
    model = Mission


class StubMissionByAdminRepository(Repository):
    roles = (ADMIN,)
    model = Mission


class ManagerTest(TestCase):
    def setUp(self) -> None:
        pass

    @mock.patch.object(StubMissionByUserRepository, "list")
    @mock.patch.object(StubMissionByAdminRepository, "list")
    def test_mapping_user(
        self,
        mission_by_admin_repository_list: mock.MagicMock,
        mission_by_user_repository_list: mock.MagicMock,
    ):
        manager = RepositoryManager([StubMissionByUserRepository(), StubMissionByAdminRepository()])
        user = mommy.make(User)
        user.role = USER

        manager.list(Mission, user, "workspace_id")

        mission_by_user_repository_list.assert_called_with(user.id, "workspace_id")

        user.role = ADMIN

        manager.list(Mission, user, "workspace_id")

        mission_by_admin_repository_list.assert_called_once_with(user.id, "workspace_id")
