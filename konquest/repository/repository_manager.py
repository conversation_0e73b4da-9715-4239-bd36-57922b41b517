import typing
from typing import List, Optional

from account.models import User
from authentication.keeps_permissions import USER
from custom.keeps_exception_handler import KeepsRuntimeError
from django.db.models import QuerySet
from django.db.models.base import ModelBase
from repository import Repository
from repository.model_mapping import ModelMapping


class RepositoryManager:
    def __init__(self, selectors: List[Repository], default_role: str = USER):
        self._models_selectors: typing.Dict[ModelBase, ModelMapping] = {}
        self._model_default_selectors = {}
        self.default_role = default_role
        for selector in selectors:
            self._map(selector)
        self._check_defaults()

    def list(self, model: ModelBase, user: User, workspace_id: str) -> QuerySet:
        role = user.role
        model_mapping = self.get_model_mapping(model)
        if not model_mapping:
            raise KeepsRuntimeError(f"{model.__name__} not mapped")
        role_mapped = role in model_mapping.selectors
        selectors = self._models_selectors[model].selectors
        role_selector = selectors[user.role] if role_mapped else model_mapping.default
        return role_selector.list(user.id, workspace_id)

    def _map(self, selector: Repository) -> None:
        model = selector.model
        model_mapping = self.get_model_mapping(model)
        role_selectors = self._create_role_selectors_mapping(selector)
        default_selector = self._filter_default_selector(role_selectors)
        if not model_mapping:
            model_mapping = ModelMapping(model=model, selectors=role_selectors, default=default_selector)
            self._models_selectors[model] = model_mapping
            return
        selectors = model_mapping.selectors
        selectors.update(role_selectors)
        if not model_mapping.default:
            model_mapping.default = default_selector

    def _check_defaults(self) -> None:
        maps_without_defaults = list(
            filter(lambda key: not self._models_selectors[key].default, self._models_selectors)
        )
        models_name = [self._models_selectors[key].model.__name__ for key in maps_without_defaults]
        if models_name:
            raise KeepsRuntimeError(f"The models {models_name} not have default selector")

    def get_model_mapping(self, model: ModelBase) -> Optional[ModelMapping]:
        return self._models_selectors[model] if model in self._models_selectors else None

    @staticmethod
    def _create_role_selectors_mapping(selector: Repository) -> typing.Dict[str, Repository]:
        mapping = {}
        for role in selector.roles:
            mapping.update({role: selector})
        return mapping

    def _filter_default_selector(self, role_selectors: typing.Dict[str, Repository]) -> Optional[Repository]:
        return role_selectors[self.default_role] if self.default_role in role_selectors else None
