from injector import Module, inject, provider, singleton
from mission.repositories.mission.mission_by_admin import MissionByAdmin
from mission.repositories.mission.mission_by_instructor import Mission<PERSON>yInstructor
from mission.repositories.mission.mission_by_user import Mission<PERSON>yUser
from mission.repositories.mission_evaluations.mission_evaluations_by_manager import MissionEvaluationsByManager
from mission.repositories.mission_evaluations.mission_evaluations_by_user import MissionEvaluationsByUser
from mission.repositories.mission_stages.mission_stage_by_super_admin import MissionStageBySuperAdmin
from mission.repositories.mission_stages.mission_stage_default import MissionStageDefault
from repository.repository_manager import RepositoryManager


class RepositoryModule(Module):
    @singleton
    @inject
    @provider
    def repositories_manager(
        self,
        mission_by_admin: MissionByAdmin,
        mission_by_instructor: Mission<PERSON>yInstructor,
        mission_by_user: Mission<PERSON>yUser,
        mission_stage_default: MissionStageDefault,
        mission_stage_by_super_admin: MissionStageBySuperAdmin,
        mission_evaluations_by_manager: MissionEvaluations<PERSON>y<PERSON>anager,
        mission_evaluations_by_user: MissionEvaluationsByUser,
    ) -> RepositoryManager:
        return RepositoryManager(
            selectors=[
                mission_by_admin,
                mission_by_instructor,
                mission_by_user,
                mission_stage_default,
                mission_stage_by_super_admin,
                mission_evaluations_by_manager,
                mission_evaluations_by_user,
            ]
        )
