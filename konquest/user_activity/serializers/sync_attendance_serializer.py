from custom.keeps_serializer import KPSerializer
from rest_framework import serializers
from user_activity.models import LiveAttendance, MissionEnrollment
from user_activity.models.presential_attendance import PresentialAttendance


class MissionEnrollmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = MissionEnrollment
        fields = (
            "user",
            "status",
        )
        depth = 1
        ref_name = "AttendanceMissionEnrollment"


class LiveAttendanceSerializer(serializers.ModelSerializer):
    class Meta:
        model = LiveAttendance
        fields = "__all__"


class LiveAttendanceListSerializer(serializers.ModelSerializer):
    enrollment = MissionEnrollmentSerializer(read_only=True)

    class Meta:
        model = LiveAttendance
        fields = "__all__"


class PresentialAttendanceSerializer(serializers.ModelSerializer):
    class Meta:
        model = PresentialAttendance
        fields = "__all__"


class PresentialAttendanceListSerializer(serializers.ModelSerializer):
    enrollment = MissionEnrollmentSerializer(read_only=True)

    class Meta:
        model = PresentialAttendance
        fields = "__all__"


class AttendanceCheckSerializer(KPSerializer):
    presented = serializers.BooleanField(required=True)


class AttendanceBatchCheckSerializer(AttendanceCheckSerializer):
    attendance_ids = serializers.ListSerializer(child=serializers.UUIDField(), required=False)


class AttendanceReportSerializer(KPSerializer):
    mission_id = serializers.UUIDField()


class MissionEnrollmentReportSerializer(KPSerializer):
    mission_id = serializers.UUIDField()
