from custom.keeps_serializer import KPSerializer
from rest_framework import serializers
from user_activity.serializers.validators.goal_date_field import <PERSON><PERSON><PERSON><PERSON><PERSON>
from user_activity.serializers.validators.goal_date_validator import GoalDateValidator


class MissionBatchEnrollmentInputSerializer(KPSerializer):
    users = serializers.ListSerializer(child=serializers.UUIDField(), required=True)
    missions = serializers.ListSerializer(child=serializers.UUIDField(), required=True)
    goal_date = GoalDateField(validators=[GoalDateValidator()], required=False, allow_null=True)
    required_mission = serializers.BooleanField(required=False)
    required = serializers.BooleanField(default=True)
    regulatory_compliance_cycle = serializers.UUIDField(required=False)

    def to_internal_value(self, data):
        if data.get("goal_date") == "":
            data["goal_date"] = None
        return super().to_internal_value(data)
