from datetime import datetime, timedelta

import pytest
from account.models import User
from django.test import TestCase
from django.utils.translation import gettext as _
from mission.models import Mission
from model_mommy import mommy
from rest_framework import serializers
from user_activity.serializers.mission_enrollment_serializer import (
    MissionEnrollmentRetakeSerializer,
    MissionEnrollmentSerializer,
    MissionEnrollmentUpdateSerializer,
)
from user_activity.serializers.validators.goal_date_validator import ERROR_GOAL_DATE_CANNOT_BE_IN_THE_PAST


class TestMissionEnrollmentSerializers(TestCase):
    def test_should_mission_enrollment_serializer_block_old_goal_date(self):
        goal_date = datetime.now() - timedelta(days=1)
        mission = mommy.make(Mission)
        user = mommy.make(User)
        serializer = MissionEnrollmentSerializer(
            data={"goal_date": str(goal_date), "mission": mission.id, "user": user.id}
        )
        with pytest.raises(serializers.ValidationError) as error:
            serializer.is_valid(raise_exception=True)
            self.assertEqual(error, _(ERROR_GOAL_DATE_CANNOT_BE_IN_THE_PAST))

    def test_should_retake_serializer_block_old_goal_date(self):
        goal_date = datetime.now() - timedelta(days=1)
        serializer = MissionEnrollmentRetakeSerializer(data={"goal_date": str(goal_date)})
        with pytest.raises(serializers.ValidationError) as error:
            serializer.is_valid(raise_exception=True)
            self.assertEqual(error, _(ERROR_GOAL_DATE_CANNOT_BE_IN_THE_PAST))

    def test_should_update_serializer_block_old_goal_date(self):
        goal_date = datetime.now() - timedelta(days=1)
        serializer = MissionEnrollmentUpdateSerializer(data={"goal_date": str(goal_date)}, partial=True)
        with pytest.raises(serializers.ValidationError) as error:
            serializer.is_valid(raise_exception=True)
            self.assertEqual(error, _(ERROR_GOAL_DATE_CANNOT_BE_IN_THE_PAST))
