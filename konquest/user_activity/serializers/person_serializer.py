from custom.keeps_serializer import KPSerializer
from rest_framework import serializers
from user_activity.serializers.sync_attendance_serializer import AttendanceCheckSerializer


class PersonSerializer(KPSerializer):
    name = serializers.CharField()
    email = serializers.EmailField()


class AttendanceCheckPersonSerializer(AttendanceCheckSerializer):
    persons = PersonSerializer(many=True)
    date_id = serializers.UUIDField()
