# -*- coding: utf-8 -*-
from rest_framework import serializers
from user_activity.models.user_mission_content import UserMissionContent


class UserMissionContentSerializer(serializers.ModelSerializer):
    status = serializers.ReadOnlyField()

    class Meta:
        model = UserMissionContent
        fields = "__all__"


class UserMissionContentListSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserMissionContent
        fields = "__all__"
        depth = 1
