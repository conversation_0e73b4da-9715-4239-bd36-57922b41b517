# -*- coding: utf-8 -*-

from rest_framework import serializers
from user_activity.models.user_mission_stage import UserMissionStage


class UserMissionStageSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserMissionStage
        fields = "__all__"


class UserMissionStageListSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserMissionStage
        fields = "__all__"
        depth = 1
