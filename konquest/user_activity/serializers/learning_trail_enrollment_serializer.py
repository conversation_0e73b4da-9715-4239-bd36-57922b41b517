# -*- coding: utf-8 -*-
from custom.keeps_serializer import KPSerializer
from learning_trail.serializers.learning_trail_serializer import LearningTrailShortSerializer
from rest_framework import serializers
from user_activity.models import LearningTrailEnrollment
from user_activity.serializers.validators.goal_date_field import <PERSON>D<PERSON><PERSON>ield
from user_activity.serializers.validators.goal_date_validator import GoalDateValidator


class LearningTrailEnrollmentSerializer(serializers.ModelSerializer):
    goal_date = GoalDateField(validators=[GoalDateValidator()])
    regulatory_compliance_cycle = serializers.UUIDField(required=False)

    class Meta:
        model = LearningTrailEnrollment
        fields = "__all__"


class LearningTrailEnrollmentListSerializer(serializers.ModelSerializer):
    learning_trail = LearningTrailShortSerializer()

    class Meta:
        model = LearningTrailEnrollment
        fields = "__all__"
        depth = 1


class LearningTrailEnrollmentRestartSerializer(KPSerializer):
    goal_date = GoalDateField(validators=[GoalDateValidator()])
    status = serializers.Char<PERSON>ield(read_only=True)
