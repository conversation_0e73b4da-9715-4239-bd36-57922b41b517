from custom.keeps_serializer import KPSerializer
from rest_framework import serializers
from user_activity.serializers.validators.goal_date_field import GoalDate<PERSON>ield
from user_activity.serializers.validators.goal_date_validator import GoalDateValidator


class LearningTrailBatchEnrollmentInputSerializer(KPSerializer):
    users = serializers.ListSerializer(child=serializers.UUIDField(), required=True)
    learning_trails = serializers.ListSerializer(child=serializers.UUIDField(), required=True)
    goal_date = GoalDateField(validators=[GoalDateValidator()], required=True)
    regulatory_compliance_cycle = serializers.UUIDField(required=False)
    required = serializers.BooleanField(default=False)
