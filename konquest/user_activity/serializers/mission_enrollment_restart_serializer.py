from custom.keeps_serializer import KPSerializer
from rest_framework import serializers
from user_activity.serializers.validators.goal_date_field import GoalDate<PERSON>ield
from user_activity.serializers.validators.goal_date_validator import GoalDateValidator


class MissionEnrollmentRestartSerializer(KPSerializer):
    goal_date = GoalDateField(validators=[GoalDateValidator()], required=True)
    mandatory_dead_line = serializers.BooleanField(default=False)
