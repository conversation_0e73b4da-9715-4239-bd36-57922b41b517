from account.models import User
from account.serializers.user_clean_serializer import UserCleanSerializer
from custom.serializers.kp_performance_serializer import KPPerformanceSerializer
from django.utils.translation import gettext as _
from mission.models import Mission
from mission.models.mission import LIVE, PRESENTIAL
from mission.serializers.mission_serializer import ExternalMissionDetailSerializer
from rest_framework import serializers
from rest_framework.exceptions import ValidationError
from user_activity.models import MissionEnrollment
from user_activity.serializers.validators.goal_date_field import GoalDateField
from user_activity.serializers.validators.goal_date_validator import GoalDateValidator


class MissionEnrollmentSerializer(serializers.ModelSerializer):
    evaluated = serializers.ReadOnlyField()
    goal_date = GoalDateField(validators=[GoalDateValidator()], required=False, allow_null=True)
    regulatory_compliance_cycle = serializers.UUIDField(required=False)

    class Meta:
        model = MissionEnrollment
        fields = "__all__"

    def to_internal_value(self, data):
        if data.get("goal_date") == "":
            data["goal_date"] = None
        return super().to_internal_value(data)

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["goal_date"] = instance.goal_date
        return data


class MissionEnrollmentCreateSerializer(MissionEnrollmentSerializer):
    class Meta:
        model = MissionEnrollment
        fields = ("mission", "user", "workspace", "goal_date", "evaluated", "regulatory_compliance_cycle")


class MissionEnrollmentRetakeSerializer(serializers.Serializer):
    goal_date = GoalDateField(validators=[GoalDateValidator()], required=False, allow_null=True)

    def update(self, instance, validated_data):
        """
        This method is empty because updating instances is not required for this serializer.
        """
        pass

    def create(self, validated_data):
        """
        This method is empty because creation instances is not required for this serializer.
        """
        pass


class MissionEnrollmentUpdateSerializer(MissionEnrollmentCreateSerializer):
    class Meta:
        model = MissionEnrollment
        fields = ("goal_date",)


class MissionShortSerializer(serializers.ModelSerializer):
    learning_trail_linked = serializers.ReadOnlyField()
    external = ExternalMissionDetailSerializer(read_only=True)
    provider = serializers.ReadOnlyField()
    external_course_url = serializers.ReadOnlyField()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    class Meta:
        model = Mission
        fields = (
            "id",
            "name",
            "description",
            "user_creator",
            "learning_trail_linked",
            "mission_model",
            "holder_image",
            "vertical_holder_image",
            "external_course_url",
            "provider",
            "external",
            "required_evaluation",
            "development_status",
        )


class UserShortSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = (
            "id",
            "name",
            "email",
            "avatar",
        )


class MissionEnrollmentListSerializer(serializers.ModelSerializer):
    enrolled_count = serializers.ReadOnlyField()
    in_progress = serializers.ReadOnlyField()
    mission = MissionShortSerializer()
    user = UserCleanSerializer()
    evaluated = serializers.ReadOnlyField()

    class Meta:
        model = MissionEnrollment
        exclude = ("workspace",)


class MissionEnrollmentDetailSerializer(MissionEnrollmentListSerializer):
    actual_stage = serializers.ReadOnlyField()
    evaluated = serializers.ReadOnlyField()
    attended = serializers.ReadOnlyField()

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.mission.mission_model == LIVE:
            representation["attended"] = instance.liveattendance_set.filter(presented=True).exists()
        elif instance.mission.mission_model == PRESENTIAL:
            representation["attended"] = instance.presentialattendance_set.filter(presented=True).exists()

        return representation


class SyncEnrollmentApprovalSerializer(serializers.Serializer):
    approved = serializers.BooleanField(required=True)

    def update(self, instance, validated_data):
        """
        This method is empty because updating instances is not required for this serializer.
        """
        pass

    def create(self, validated_data):
        """
        This method is empty because creation instances is not required for this serializer.
        """
        pass


class MissionEnrollmentBatchActionSerializer(serializers.Serializer):
    mission_id = serializers.UUIDField(required=False)
    enrollment_ids = serializers.ListSerializer(child=serializers.UUIDField(), required=False)

    def update(self, instance, validated_data):
        """
        This method is empty because updating instances is not required for this serializer.
        """
        pass

    def create(self, validated_data):
        """
        This method is empty because creation instances is not required for this serializer.
        """
        pass

    def validate(self, attrs):
        mission_id = attrs.get("mission_id")
        enrollment_ids = attrs.get("enrollment_ids")
        if mission_id and enrollment_ids:
            raise ValidationError(_("pass_mission_id_or_enrollment_ids_never_both"))
        if not mission_id and not enrollment_ids:
            raise ValidationError(_("mission_id_or_enrollment_ids_are_required"))
        return attrs


class SyncEnrollmentBatchApprovalSerializer(MissionEnrollmentBatchActionSerializer):
    approved = serializers.BooleanField(required=True)


class EnrollmentManualFinishSerializer(KPPerformanceSerializer):
    performance = serializers.FloatField(min_value=0, max_value=1, required=True)


class SyncMissionEnrollmentSerializer(MissionEnrollmentCreateSerializer):
    goal_date = serializers.ReadOnlyField()
