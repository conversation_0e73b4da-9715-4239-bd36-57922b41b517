from custom.keeps_serializer import KPSerializer
from rest_framework import serializers


class SyncMissionBatchEnrollmentInputSerializer(KPSerializer):
    users = serializers.ListSerializer(child=serializers.UUIDField(), required=True)
    missions = serializers.ListSerializer(child=serializers.UUIDField(), required=True)
    goal_date = serializers.ReadOnlyField()
    required = serializers.BooleanField(default=True)
