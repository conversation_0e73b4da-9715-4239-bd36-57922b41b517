from django.urls import path
from user_activity.views.live_attendance_batch_check_viewset import LiveAttendanceBatchCheckViewSet
from user_activity.views.live_attendance_check_viewset import LiveAttendanceCheckViewSet
from user_activity.views.live_attendance_report_viewset import LiveAttendanceReportViewSet
from user_activity.views.live_attendance_viewset import LiveAttendanceViewSet
from user_activity.views.mission_enrollment_batch_delete_viewset import MissionEnrollmentBatchDeleteViewSet
from user_activity.views.mission_enrollment_manual_finish_viewset import MissionEnrollmentManualFinishViewSet
from user_activity.views.mission_enrollment_provider_sync_viewset import MissionEnrollmentProviderSyncViewSet
from user_activity.views.mission_enrollment_report_viewset import MissionEnrollmentReportViewSet
from user_activity.views.mission_enrollment_viewset import (
    MissionEnrollmentBatchViewSet,
    MissionEnrollmentCertificateV2ViewSet,
    MissionEnrollmentChangePerformanceViewSet,
    MissionEnrollmentCompleteViewSet,
    MissionEnrollmentCountStatusViewSet,
    MissionEnrollmentExtendDeadlineViewSet,
    MissionEnrollmentExternalReviewViewSet,
    MissionEnrollmentExternalValidateViewSet,
    MissionEnrollmentGiveUpViewSet,
    MissionEnrollmentRequestExtensionViewSet,
    MissionEnrollmentRestartViewSet,
    MissionEnrollmentRetakeViewSet,
    MissionEnrollmentTrackingViewSet,
    MissionEnrollmentViewSet,
)
from user_activity.views.presential_attendance_batch_check_viewset import PresentialAttendanceBatchCheckViewSet
from user_activity.views.presential_attendance_check_viewset import PresentialAttendanceCheckViewSet
from user_activity.views.presential_attendance_report_viewset import PresentialAttendanceReportViewSet
from user_activity.views.presential_attendance_viewset import PresentialAttendanceViewSet
from user_activity.views.sync_attendance_person_batch_check_viewset import SyncAttendancePersonBatchCheckViewSet
from user_activity.views.sync_enrollment_approval_viewset import SyncEnrollmentApprovalViewSet
from user_activity.views.sync_enrollment_batch_approval_viewset import SyncEnrollmentBatchApprovalViewSet
from user_activity.views.sync_enrollment_batch_resend_approved_email import (
    SyncEnrollmentBatchResendApprovedEmailViewSet,
)
from user_activity.views.sync_enrollment_finish_viewset import SyncEnrollmentFinishViewSet
from user_activity.views.sync_enrollment_resend_approved_email import SyncEnrollmentResendApprovedEmailViewSet
from user_activity.views.sync_mission_enrollment_viewset import (
    SyncMissionEnrollmentBatchViewSet,
    SyncMissionEnrollmentViewSet,
)

_READY_ONLY = {"get": "list"}
_SAVE_ONLY = {"post": "create"}
_PATCH_ONLY = {"patch": "partial_update"}

_LIST = {"get": "list", "post": "create"}

_DETAIL = {"get": "retrieve", "patch": "partial_update", "delete": "destroy"}


urlpatterns = [
    path("", MissionEnrollmentViewSet.as_view(_LIST), name="mission-enrollments-list"),
    # todo: verify if some use the detail to update to delete this
    path("/<uuid:pk>", MissionEnrollmentViewSet.as_view(_DETAIL), name="mission-enrollment-details"),
    path("/<uuid:pk>/finish", MissionEnrollmentCompleteViewSet.as_view(_SAVE_ONLY), name="mission-enrollment-finish"),
    path(
        "/<uuid:pk>/manual-finish",
        MissionEnrollmentManualFinishViewSet.as_view(_SAVE_ONLY),
        name="mission-enrollment-manual-finish",
    ),
    path(
        "/<uuid:pk>/sync-finish",
        SyncEnrollmentFinishViewSet.as_view(_SAVE_ONLY),
        name="mission-enrollment-sync-finish",
    ),
    path("/<uuid:pk>/retake", MissionEnrollmentRetakeViewSet.as_view(_PATCH_ONLY), name="mission-enrollment-retake"),
    path(
        "/<uuid:pk>/external-review",
        MissionEnrollmentExternalReviewViewSet.as_view(_SAVE_ONLY),
        name="mission-enrollment-external-review",
    ),
    path(
        "/<uuid:pk>/give-up",
        MissionEnrollmentGiveUpViewSet.as_view(_PATCH_ONLY),
        name="mission-enrollment-give-up",
    ),
    path(
        "/<uuid:pk>/external-validate",
        MissionEnrollmentExternalValidateViewSet.as_view(_SAVE_ONLY),
        name="mission-enrollment-external-validate",
    ),
    path(
        "/<uuid:pk>/request-extension",
        MissionEnrollmentRequestExtensionViewSet.as_view(_SAVE_ONLY),
        name="mission-enrollment-request-extension",
    ),
    path(
        "/<uuid:pk>/extend-deadline",
        MissionEnrollmentExtendDeadlineViewSet.as_view(_SAVE_ONLY),
        name="mission-enrollment-extend-deadline",
    ),
    path("/<uuid:pk>/restart", MissionEnrollmentRestartViewSet.as_view(_SAVE_ONLY), name="mission-enrollment-restart"),
    path("/<uuid:pk>/approval", SyncEnrollmentApprovalViewSet.as_view(_SAVE_ONLY), name="mission-enrollment-approval"),
    path(
        "/batch-approval",
        SyncEnrollmentBatchApprovalViewSet.as_view(_SAVE_ONLY),
        name="mission-enrollment-batch-approval",
    ),
    path(
        "/<uuid:pk>/resend-approved-email",
        SyncEnrollmentResendApprovedEmailViewSet.as_view(_SAVE_ONLY),
        name="mission-enrollment-resend-approved-email",
    ),
    path(
        "/batch-resend-approved-email",
        SyncEnrollmentBatchResendApprovedEmailViewSet.as_view(_SAVE_ONLY),
        name="mission-enrollment-batch-resend-approved-email",
    ),
    path(
        "/<uuid:pk>/change-performance",
        MissionEnrollmentChangePerformanceViewSet.as_view(_SAVE_ONLY),
        name="mission-enrollment-change-performance",
    ),
    path(
        "/<uuid:pk>/tracking",
        MissionEnrollmentTrackingViewSet.as_view(_READY_ONLY),
        name="mission-enrollment-change-tracking",
    ),
    path(
        "/<uuid:pk>/certificates",
        MissionEnrollmentCertificateV2ViewSet.as_view(_SAVE_ONLY),
        name="mission-enrollment-certificate-v2",
    ),
    path("/batch", MissionEnrollmentBatchViewSet.as_view(_SAVE_ONLY), name="mission-enrollment-batch"),
    path(
        "/batch-delete", MissionEnrollmentBatchDeleteViewSet.as_view(_SAVE_ONLY), name="mission-enrollment-batch-delete"
    ),
    path(
        "/<uuid:user_id>/resume",
        MissionEnrollmentCountStatusViewSet.as_view(_READY_ONLY),
        name="mission-enrollment-user-status-count",
    ),
    path(
        "/live-attendances",
        LiveAttendanceViewSet.as_view(_LIST),
        name="mission-enrollment-live-attendances-list",
    ),
    path(
        "/live-attendances/<uuid:pk>/check",
        LiveAttendanceCheckViewSet.as_view(_SAVE_ONLY),
        name="mission-enrollment-live-attendances-check",
    ),
    path(
        "/live-attendances/batch-check",
        LiveAttendanceBatchCheckViewSet.as_view(_SAVE_ONLY),
        name="mission-enrollment-live-attendances-batch-check",
    ),
    path(
        "/presential-attendances",
        PresentialAttendanceViewSet.as_view(_LIST),
        name="mission-enrollment-presential-attendances-list",
    ),
    path(
        "/presential-attendances/<uuid:pk>/check",
        PresentialAttendanceCheckViewSet.as_view(_SAVE_ONLY),
        name="mission-enrollment-presential-attendances-check",
    ),
    path(
        "/presential-attendances/batch-check",
        PresentialAttendanceBatchCheckViewSet.as_view(_SAVE_ONLY),
        name="mission-enrollment-presential-attendances-batch-check",
    ),
    path(
        "/presential-attendances-report/",
        PresentialAttendanceReportViewSet.as_view(_SAVE_ONLY),
        name="mission-enrollment-presential-attendances-report",
    ),
    path(
        "/live-attendances-report/",
        LiveAttendanceReportViewSet.as_view(_SAVE_ONLY),
        name="mission-enrollment-live-attendances-report",
    ),
    path(
        "/report",
        MissionEnrollmentReportViewSet.as_view(_SAVE_ONLY),
        name="mission-enrollment-report",
    ),
    path("/sync", SyncMissionEnrollmentViewSet.as_view(_SAVE_ONLY), name="sync-mission-enrollments-create"),
    path("/batch/sync", SyncMissionEnrollmentBatchViewSet.as_view(_SAVE_ONLY), name="sync-mission-enrollment-batch"),
    path(
        "/batch/sync/person-check",
        SyncAttendancePersonBatchCheckViewSet.as_view(_SAVE_ONLY),
        name="sync-attendance-person-batch-check",
    ),
    path(
        "/sync-enrollments-provider",
        MissionEnrollmentProviderSyncViewSet.as_view(_SAVE_ONLY),
        name="sync-enrollments-provider",
    ),
]
