from config import settings
from custom.discord_webhook import DiscordWebhook<PERSON>ogger
from custom.regulatory_compliance_connection_service import RegulatoryComplianceConnectionService
from custom.types import AttendanceModelsMap
from injector import Module, inject, provider, singleton
from mission.models.mission import LIVE, PRESENTIAL
from mission.services.mission_service import MissionService
from myaccount.application.services.myaccount_service import MyAccountService
from notification.services.notification_service_v2 import NotificationServiceV2
from observer.event_manager import EventManager
from pulse.services.channel_service import ChannelService
from rest_clients.kontent_client_abc import KontentClientAbc
from user_activity.application.use_cases.generate_certificate.generate_certificate_use_case import (
    GenerateCertificateUseCase,
)
from user_activity.application.use_cases.generate_certificate.generate_trail_certificate_use_case import (
    GenerateTrailCertificateUseCase,
)
from user_activity.application.use_cases.list_learning_trail_enrollments.list_learning_trail_enrollments import (
    ListLearningTrailEnrollmentsUseCase,
)
from user_activity.domain.repositories.certificate_repository import (
    CertificateRepository,
)
from user_activity.domain.repositories.i_learning_trail_enrollment_repository import ILearningTrailEnrollmentRepository
from user_activity.domain.services.content_size_formatter import ContentSizeFormatter
from user_activity.domain.services.django_translation_service import DjangoTranslationService
from user_activity.domain.services.translation_service import TranslationService
from user_activity.infrastructure.repositories.certificate_grpc_repository import (
    CertificateGrpcRepository,
)
from user_activity.infrastructure.repositories.certificate_mission_contents_orm_repository import (
    CertificateMissionContentORMRepository,
)
from user_activity.infrastructure.repositories.certificate_trail_content_orm_repository import (
    CertificateTrailContentORMRepository,
)
from user_activity.infrastructure.repositories.learning_trail_enrollment_repository import (
    LearningTrailEnrollmentRepository,
)
from user_activity.infrastructure.repositories.mission_enrollment_orm_repository import MissionEnrollmentORMRepository
from user_activity.listeners import MissionEnrollmentRestartedNotificationListener
from user_activity.models import LiveAttendance, PresentialAttendance
from user_activity.notifications.enrolled_in_trail_by_admin_notification import EnrolledInTrailByAdminNotification
from user_activity.notifications.learning_trail_enrollment_expiring_notification import (
    LearningTrailEnrollmentExpiringNotification,
)
from user_activity.notifications.mission_enrollment_restarted_notification import MissionEnrollmentRestartedNotification
from user_activity.selectors.mission_enrollment_selector import MissionEnrollmentSelector
from user_activity.selectors.sync_attendance_selector import SyncAttendanceSelector
from user_activity.services import (
    LearningTrailEnrollmentService,
    MissionBatchEnrollmentService,
    MissionEnrollmentService,
    UserService,
)
from user_activity.services.duplicated_mission_enrollments_clean_service import DuplicatedMissionEnrollmentsCleanService
from user_activity.services.duplicated_trail_enrollments_clean_service import DuplicatedTrailEnrollmentsCleanService
from user_activity.services.learning_trail_batch_enrollment_service_v2 import LearningTrailBatchEnrollmentService
from user_activity.services.learning_trail_enroll_service import LearningTrailEnrollService
from user_activity.services.learning_trail_enrollment_finish_service import LearningTrailEnrollmentFinishService
from user_activity.services.learning_trail_enrollment_give_up_service import LearningTrailEnrollmentGiveUpService
from user_activity.services.learning_trail_enrollment_tracking_service import LearningTrailEnrollmentTrackingService
from user_activity.services.mission_enrollment_attendance_service import MissionEnrollmentAttendanceService
from user_activity.services.mission_enrollment_close_service import MissionEnrollmentCloseService
from user_activity.services.mission_enrollment_finish_service import MissionEnrollmentFinishService
from user_activity.services.mission_enrollment_progress_service import MissionEnrollmentProgressService
from user_activity.services.mission_enrollment_provider_sync_service import (
    MissionEnrollmentProviderSyncService,
)
from user_activity.services.mission_enrollment_tracking_service import MissionEnrollmentTrackingResumeService
from user_activity.services.regulatory_compliance_service import RegulatoryComplianceService
from user_activity.services.report_service import MissionEnrollmentReportService
from user_activity.services.rewew_enrollment.renew_enrollment_permission_service import RenewEnrollmentPermissionService
from user_activity.tasks.learning_trail_enrollment_expiring_notification_task import (
    LearningTrailEnrollmentExpiringNotificationTask,
)
from utils.aws import S3Client

ATTENDANCE_MODELS_MAP: AttendanceModelsMap = {LIVE: LiveAttendance, PRESENTIAL: PresentialAttendance}


class UserActivityModule(Module):
    @singleton
    @provider
    def event_manager(self) -> EventManager:
        return EventManager()

    @inject
    @singleton
    @provider
    def regulatory_compliance_service(
        self, message_connection_service: RegulatoryComplianceConnectionService
    ) -> RegulatoryComplianceService:
        return RegulatoryComplianceService(message_connection_service, settings.NEW_ENROLLMENT_CYCLE_TASK_NAME)

    @inject
    @provider
    def trail_batch_enrollment_service(
        self, trail_enroll_service: LearningTrailEnrollService
    ) -> LearningTrailBatchEnrollmentService:
        return LearningTrailBatchEnrollmentService(trail_enroll_service)

    @singleton
    @inject
    @provider
    def mission_enrollment_service(
        self,
        finish_service: MissionEnrollmentFinishService,
        s3_client: S3Client,
        kontent_client: KontentClientAbc,
        event_manager: EventManager,
        regulatory_compliance_service: RegulatoryComplianceService,
        renew_enrollment_permission_service: RenewEnrollmentPermissionService,
        discord_webhook_logger: DiscordWebhookLogger,
        myaccount_service: MyAccountService
    ) -> MissionEnrollmentService:
        return MissionEnrollmentService(
            finish_service,
            s3_client,
            kontent_client,
            event_manager,
            regulatory_compliance_service,
            renew_enrollment_permission_service,
            discord_webhook_logger,
            myaccount_service=myaccount_service
        )

    @inject
    @provider
    def mission_batch_enrollment_service(
        self, mission_enrollment_service: MissionEnrollmentService
    ) -> MissionBatchEnrollmentService:
        return MissionBatchEnrollmentService(mission_enrollment_service)

    @inject
    @provider
    @singleton
    def mission_enrollment_restarted_notification_listener(self) -> MissionEnrollmentRestartedNotificationListener:
        return MissionEnrollmentRestartedNotificationListener()

    @singleton
    @provider
    def user_service(self, channel_service: ChannelService) -> UserService:
        return UserService(channel_service)

    @inject
    @singleton
    @provider
    def sync_attendance_selector(self, mission_service: MissionService) -> SyncAttendanceSelector:
        return SyncAttendanceSelector(mission_service)

    @inject
    @singleton
    @provider
    def mission_enrollment_attendance_service(
        self,
        s3_client: S3Client,
        sync_attendance_selector: SyncAttendanceSelector,
        mission_enrollment_service: MissionEnrollmentService,
    ) -> MissionEnrollmentAttendanceService:
        return MissionEnrollmentAttendanceService(
            s3_client, sync_attendance_selector, mission_enrollment_service, ATTENDANCE_MODELS_MAP
        )

    @inject
    @singleton
    @provider
    def mission_enrollment_close_service(self) -> MissionEnrollmentCloseService:
        return MissionEnrollmentCloseService(ATTENDANCE_MODELS_MAP)

    @inject
    @singleton
    @provider
    def mission_enrollment_report_service(self, s3_client: S3Client) -> MissionEnrollmentReportService:
        return MissionEnrollmentReportService(s3_client)

    @inject
    @singleton
    @provider
    def mission_enrollment_finish_service(self, kontent_client: KontentClientAbc) -> MissionEnrollmentFinishService:
        return MissionEnrollmentFinishService(kontent_client)

    @inject
    @provider
    def learning_trail_enrollment_service(
        self,
        enrollment_service: MissionEnrollmentService,
        mission_batch_enrollment_service: MissionBatchEnrollmentService,
        finish_service: LearningTrailEnrollmentFinishService,
        enroll_service: LearningTrailEnrollService,
        discord_webhook_logger: DiscordWebhookLogger,
    ) -> LearningTrailEnrollmentService:
        return LearningTrailEnrollmentService(
            enrollment_service,
            mission_batch_enrollment_service,
            finish_service,
            enroll_service,
            discord_webhook_logger,
        )

    @inject
    @provider
    def mission_enrollment_tracking_resume_service(
        self, kontent_client: KontentClientAbc
    ) -> MissionEnrollmentTrackingResumeService:
        return MissionEnrollmentTrackingResumeService(kontent_client)

    @singleton
    @provider
    def mission_enrollment_progress_service(self) -> MissionEnrollmentProgressService:
        return MissionEnrollmentProgressService(0.99, 0.01)

    @inject
    @singleton
    @provider
    def mission_enrollment_restarted_notification(
        self, notification_service: NotificationServiceV2, discord_webhook_logger: DiscordWebhookLogger
    ) -> MissionEnrollmentRestartedNotification:
        return MissionEnrollmentRestartedNotification(notification_service, discord_webhook_logger)

    @singleton
    @provider
    @inject
    def learning_trail_enrollment_finish_service(
        self, certificate_service: GenerateTrailCertificateUseCase, discord_webhook_logger: DiscordWebhookLogger
    ) -> LearningTrailEnrollmentFinishService:
        return LearningTrailEnrollmentFinishService(certificate_service, discord_webhook_logger)

    @singleton
    @provider
    def mission_enrollment_selector(self) -> MissionEnrollmentSelector:
        return MissionEnrollmentSelector()

    @inject
    @singleton
    @provider
    def learning_trail_enrollment_tracking_service(
        self, mission_enrollment_selector: MissionEnrollmentSelector
    ) -> LearningTrailEnrollmentTrackingService:
        return LearningTrailEnrollmentTrackingService(mission_enrollment_selector)

    @inject
    @singleton
    @provider
    def learning_trail_enrollment_expiring_notification(
        self, notification_service: NotificationServiceV2
    ) -> LearningTrailEnrollmentExpiringNotification:
        return LearningTrailEnrollmentExpiringNotification(notification_service)

    @inject
    @singleton
    @provider
    def learning_trail_enrollment_expiring_notification_task(
        self, learning_trail_enrollment_expiring_notification: LearningTrailEnrollmentExpiringNotification
    ) -> LearningTrailEnrollmentExpiringNotificationTask:
        return LearningTrailEnrollmentExpiringNotificationTask(learning_trail_enrollment_expiring_notification)

    @inject
    @singleton
    @provider
    def learning_trail_enroll_service(
        self,
        event_manager: EventManager,
        regulatory_compliance_service: RegulatoryComplianceService,
        renew_enrollment_permission_service: RenewEnrollmentPermissionService,
    ) -> LearningTrailEnrollService:
        return LearningTrailEnrollService(
            event_manager, regulatory_compliance_service, renew_enrollment_permission_service
        )

    @inject
    @singleton
    @provider
    def enrolled_in_trail_by_admin_notification(
        self, notification_service: NotificationServiceV2, discord_webhook_logger: DiscordWebhookLogger
    ) -> EnrolledInTrailByAdminNotification:
        return EnrolledInTrailByAdminNotification(notification_service, discord_webhook_logger)

    @inject
    @singleton
    @provider
    def learning_trail_batch_enrollment_service(
        self, learning_trail_enroll_service: LearningTrailEnrollService
    ) -> LearningTrailBatchEnrollmentService:
        return LearningTrailBatchEnrollmentService(learning_trail_enroll_service)

    @inject
    @singleton
    @provider
    def renew_enrollment_permission_service(self) -> RenewEnrollmentPermissionService:
        return RenewEnrollmentPermissionService()

    @inject
    @singleton
    @provider
    def duplicated_mission_enrollments_clean_service(
        self, mission_enrollment_service: MissionEnrollmentService
    ) -> DuplicatedMissionEnrollmentsCleanService:
        return DuplicatedMissionEnrollmentsCleanService(mission_enrollment_service)

    @inject
    @singleton
    @provider
    def duplicated_trail_enrollments_clean_service(
        self, trail_enrollment_service: LearningTrailEnrollmentService
    ) -> DuplicatedTrailEnrollmentsCleanService:
        return DuplicatedTrailEnrollmentsCleanService(trail_enrollment_service)

    @inject
    @singleton
    @provider
    def mission_enrollment_provider_sync_service(
        self, mission_enrollment_service: MissionEnrollmentService
    ) -> MissionEnrollmentProviderSyncService:
        return MissionEnrollmentProviderSyncService(mission_enrollment_service)

    @inject
    @singleton
    @provider
    def learning_trail_enrollment_give_up_service(
        self, event_manager: EventManager
    ) -> LearningTrailEnrollmentGiveUpService:
        return LearningTrailEnrollmentGiveUpService(event_manager)

    @singleton
    @provider
    def provide_learning_trail_enrollment_repository(self) -> ILearningTrailEnrollmentRepository:
        return LearningTrailEnrollmentRepository()

    @singleton
    @provider
    def provide_list_enrollments_use_case(
        self, repository: ILearningTrailEnrollmentRepository
    ) -> ListLearningTrailEnrollmentsUseCase:
        return ListLearningTrailEnrollmentsUseCase(repository)

    @singleton
    @provider
    @inject
    def provide_content_size_formatter(
        self, translation_service: TranslationService
    ) -> ContentSizeFormatter:
        return ContentSizeFormatter(translation_service)

    @singleton
    @provider
    @inject
    def provide_certificate_mission_content_orm_repository(
        self,
        tracking_service: MissionEnrollmentTrackingResumeService,
        translation_service: TranslationService,
        content_size_formatter: ContentSizeFormatter
    ) -> CertificateMissionContentORMRepository:
        return CertificateMissionContentORMRepository(
            tracking_service,
            translation_service,
            content_size_formatter
        )

    @singleton
    @provider
    @inject
    def provide_certificate_trail_content_orm_repository(
        self,
        trail_enrolment_repository: ILearningTrailEnrollmentRepository,
    ) -> CertificateTrailContentORMRepository:
        return CertificateTrailContentORMRepository(trail_enrolment_repository)

    @singleton
    @provider
    @inject
    def provide_generate_certificate_use_case(
        self,
        certificate_file_generator_repository: CertificateRepository,
        certificate_content_repository: CertificateMissionContentORMRepository,
        enrollment_repository: MissionEnrollmentORMRepository
    ) -> GenerateCertificateUseCase:
        return GenerateCertificateUseCase(
            certificate_file_generator_repository,
            certificate_content_repository,
            enrollment_repository
        )

    @singleton
    @provider
    @inject
    def provide_generate_trail_certificate_use_case(
        self,
        certificate_file_generator_repository: CertificateRepository,
        certificate_content_repository: CertificateTrailContentORMRepository,
        enrollment_repository: ILearningTrailEnrollmentRepository
    ) -> GenerateTrailCertificateUseCase:
        return GenerateTrailCertificateUseCase(
            certificate_file_generator_repository,
            certificate_content_repository,
            enrollment_repository
        )

    @singleton
    @provider
    @inject
    def provide_certificate_file_generator_repository(self) -> CertificateRepository:
        return CertificateGrpcRepository(settings.CERTIFICATE_MANAGER_GRPC_URL)

    @singleton
    @provider
    @inject
    def provide_translation_service(self) -> TranslationService:
        return DjangoTranslationService()
