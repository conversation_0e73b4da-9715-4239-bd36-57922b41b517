import mock
from django.test import TestCase
from user_activity.listeners import TrailEnrollmentGaveUpListener


class TestTrailEnrollmentGaveUpListener(TestCase):
    @mock.patch(
        "user_activity.tasks.learning_trail_enrollments.give_up_related_mission_enrollments.give_up_related_mission_enrollments.delay"
    )
    def test_should_send_task(self, task_mock: mock.MagicMock):
        mission_id = "123"
        TrailEnrollmentGaveUpListener().update(mission_id)
        task_mock.assert_called_with(mission_id)
