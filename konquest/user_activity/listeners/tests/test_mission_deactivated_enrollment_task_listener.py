import mock
from constants import CELERY_SEND_TASK_PATH
from django.test import TestCase
from user_activity.listeners import MissionDeactivatedEnrollmentTaskListener
from user_activity.task_names import DEACTIVATE_MISSION_ENROLLMENTS


class TestMissionDeactivatedEnrollmentTaskListener(TestCase):
    @mock.patch(CELERY_SEND_TASK_PATH)
    def test_should_send_task(self, send_task: mock.MagicMock):
        mission_id = "123"
        MissionDeactivatedEnrollmentTaskListener().update(mission_id)
        send_task.assert_called_with(DEACTIVATE_MISSION_ENROLLMENTS, args=(mission_id,))
