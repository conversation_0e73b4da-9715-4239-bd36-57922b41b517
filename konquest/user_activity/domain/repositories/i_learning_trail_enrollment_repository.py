from typing import Optional, Protocol

from django.db.models import QuerySet
from user_activity.domain.value_objects.certificate_url import CertificateURL
from user_activity.models import LearningTrailEnrollment


class ILearningTrailEnrollmentRepository(Protocol):
    def order_learning_trail_enrollment_list_by_status(self, workspace_id: str, ordering: str) -> QuerySet:
        """
        This method is intended to retrieve and order learning trail enrollments by status.

        Args:
            workspace_id (str): The unique identifier for the workspace.
            ordering (str): The order preference ('asc' or 'desc').

        Note:
        - The method is left unimplemented as this is a Protocol interface for other classes to inherit.
        - Specific implementations should define database interactions or any additional business rules required.

        Returns:
            QuerySet: Expected to be a Django QuerySet, filtered and ordered as per parameters.
        """
        pass

    def get_by_id(self, enrollment_id: str) -> Optional[LearningTrailEnrollment]:
        raise NotImplementedError("This method must be implemented by subclasses")

    def update_certificate_url(self, enrollment: LearningTrailEnrollment, certificate_url: CertificateURL) -> None:
        raise NotImplementedError("This method must be implemented by subclasses")
