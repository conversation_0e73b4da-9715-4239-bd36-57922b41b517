from custom.translations.translation_keys import X_HOURS_AND_Y_MINUTES, X_QUESTIONS_AND_Y_HITS, X_SECONDS
from user_activity.domain.services.translation_service import TranslationService
from utils.utils import convert_seconds_to_hours_and_minutes


class ContentSizeFormatter:
    def __init__(self, translation_service: TranslationService):
        self.translation_service = translation_service

    def format(self, content_tracking: dict) -> str:
        is_quiz = not bool(content_tracking.get("content"))
        if is_quiz:
            return self.translation_service.get_translation(
                X_QUESTIONS_AND_Y_HITS,
                content_tracking["total_questions"],
                content_tracking["total_correct_answers"],
            )
        if content_tracking["content_duration"] < 60:
            return self.translation_service.get_translation(X_SECONDS, str(content_tracking["content_duration"]))
        return self.translation_service.get_translation(
            X_HOURS_AND_Y_MINUTES,
            convert_seconds_to_hours_and_minutes(content_tracking["content_duration"])
        )
