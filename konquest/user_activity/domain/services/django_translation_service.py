from django.utils.translation import activate
from django.utils.translation import gettext as _
from user_activity.domain.services.translation_service import TranslationService


class DjangoTranslationService(TranslationService):
    def __init__(self, default_language: str = "pt-BR"):
        super().__init__(default_language)

    def set_language(self, language_code: str):
        activate(language_code)

    def get_translation(self, text: str, *args) -> str:
        """
        Retrieve the translation for the given text in the specified language.

        :param text: The text to be translated.
        :param args: Positional arguments for the translated text (used with %s placeholders)
        :return: The translated text.
        """
        if not args:
            return _(text)
        if len(args) == 1:
            return _(text) % args[0]
        return _(text) % args
