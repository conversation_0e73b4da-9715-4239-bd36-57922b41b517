from abc import ABC, abstractmethod


class TranslationService(ABC):
    def __init__(self, default_language: str = 'pt-BR'):
        self.default_language = default_language

    @abstractmethod
    def set_language(self, language_code: str):
        """
        Activate the specified language for translation.
        :param language_code: The language code to activate (e.g., 'en', 'es', 'pt-BR', 'pt-PR').
                              If None, uses the default language.
        """
        raise NotImplementedError()

    @abstractmethod
    def get_translation(self, text: str, *args) -> str:
        """
        Retrieve the translation for the given text in the specified language.

        :param text: The text to be translated.
        :param args: Formatting arguments for the translated text.
        :return: The translated text.
        """
        raise NotImplementedError()
