from django.urls import path
from user_activity.views.learn_content_activity import LearnContentActivityViewSet
from user_activity.views.scorm_activity_viewset import ScormActivityViewSet
from user_activity.views.user_mission_content_viewset import UserMissionContentViewSet
from user_activity.views.user_mission_stage_viewset import UserMissionStageViewSet
from user_activity.views.user_resume_viewset import UserActivitiesResumeViewSet, UserMissionEnrollmentResumeViewSet

_READ_ONLY = {"get": "list"}

_LIST = {"get": "list", "post": "create"}

_DETAIL = {"get": "retrieve", "put": "update", "patch": "partial_update", "delete": "destroy"}


urlpatterns = [
    path("/mission-stages", UserMissionStageViewSet.as_view(_LIST), name="user-mission-stages-list"),
    path("/mission-stages/<uuid:pk>", UserMissionStageViewSet.as_view(_DETAIL), name="user-mission-stage-detail"),
    path("/mission-contents", UserMissionContentViewSet.as_view(_LIST), name="user-mission-content-list"),
    path("/resume", UserActivitiesResumeViewSet.as_view(), name="user-resume"),
    path(
        "/mission-enrollments-resume",
        UserMissionEnrollmentResumeViewSet.as_view(),
        name="user-mission-enrollments-resume",
    ),
    path("/learn-activities", LearnContentActivityViewSet.as_view(_LIST), name="user-learn-content-activity"),
    path("/learn-activities/<uuid:pk>", LearnContentActivityViewSet.as_view(_DETAIL), name="user-learn-detail"),
    path("/scorm-activities", ScormActivityViewSet.as_view(_LIST), name="user-scorm-activity"),
    path("/scorm-activities/<uuid:pk>", ScormActivityViewSet.as_view(_DETAIL), name="user-scorm-detail-activity"),
]
