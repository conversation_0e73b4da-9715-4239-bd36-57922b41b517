from user_activity.domain.entities.certificate_enrollment import CertificateContents, CertificateSummary
from user_activity.domain.repositories.certificate_mission_content_repository import CertificateContentRepository
from user_activity.domain.services.content_size_formatter import ContentSizeFormatter
from user_activity.domain.services.translation_service import TranslationService
from user_activity.services.mission_enrollment_tracking_service import MissionEnrollmentTrackingResumeService


class CertificateMissionContentORMRepository(CertificateContentRepository):
    def __init__(
        self,
        tracking_service: MissionEnrollmentTrackingResumeService,
        translation_service: TranslationService,
        content_size_formatter: ContentSizeFormatter
    ):
        self.tracking_service = tracking_service
        self.translation_service = translation_service
        self.content_size_formatter = content_size_formatter

    def get_contents(self, enrollment_id: str) -> CertificateSummary:
        summary_data = CertificateSummary([])
        tracking_results = self.tracking_service.load_by_enrollment_id(enrollment_id)

        for result in tracking_results:
            content_size = self.content_size_formatter.format(result)
            summary_data.add_content(CertificateContents(
                title=result["name"],
                time=content_size
            ))

        return summary_data

