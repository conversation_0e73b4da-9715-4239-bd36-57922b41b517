from typing import Optional

from learning_trail.models import LearningTrailStep
from user_activity.domain.entities.certificate_enrollment import CertificateContents, CertificateSummary
from user_activity.domain.repositories.certificate_mission_content_repository import CertificateContentRepository
from user_activity.domain.repositories.i_learning_trail_enrollment_repository import ILearningTrailEnrollmentRepository
from user_activity.models import LearningTrailEnrollment, MissionEnrollment
from user_activity.models.enums.enrollment_status_enum import EnrollmentStatusEnum
from utils.utils import format_time_duration


class CertificateTrailContentORMRepository(CertificateContentRepository):
    def __init__(self, trail_enrolment_repository: ILearningTrailEnrollmentRepository):
        self.trail_enrolment_repository = trail_enrolment_repository

    def get_contents(self, enrollment_id: str) -> CertificateSummary:
        summary_data = CertificateSummary([])
        enrollment = self.trail_enrolment_repository.get_by_id(enrollment_id)
        trail_steps = enrollment.learning_trail.learningtrailstep_set.order_by("order")

        for step in trail_steps:
            certificate_content = None
            if step.mission:
                certificate_content = self._mission_step_to_content(step, enrollment)
            elif step.pulse:
                certificate_content = CertificateContents(
                    title=step.pulse.name,
                    time=format_time_duration(step.pulse.duration_time, "m")
                )
            summary_data.add_content(certificate_content)

        return summary_data

    def _mission_step_to_content(
        self,
        step: LearningTrailStep,
        enrollment: LearningTrailEnrollment
    ) -> Optional[CertificateContents]:
        mission_enrollment = MissionEnrollment.objects.filter(
            mission=step.mission,
            user=enrollment.user,
            workspace=enrollment.workspace,
            status=EnrollmentStatusEnum.COMPLETED
        ).order_by("-end_date").first()
        if not mission_enrollment:
            return None
        return CertificateContents(
            title=step.mission.name,
            time=format_time_duration(step.mission.duration_time)
        )
