from typing import Optional

from user_activity.domain.repositories.mission_enrollment_repository import MissionEnrollmentRepository
from user_activity.domain.value_objects.certificate_url import CertificateURL
from user_activity.models import MissionEnrollment


class MissionEnrollmentORMRepository(MissionEnrollmentRepository):
    def get_by_id(self, enrollment_id: str) -> Optional[MissionEnrollment]:
        return MissionEnrollment.objects.select_related('user', 'mission', 'workspace').get(id=enrollment_id)

    def update_certificate_url(self, enrollment: MissionEnrollment, certificate_url: CertificateURL) -> None:
        enrollment.certificate_url = certificate_url.value
        enrollment.save()
