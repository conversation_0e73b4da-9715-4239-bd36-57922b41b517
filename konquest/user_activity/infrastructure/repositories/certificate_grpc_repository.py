from dataclasses import asdict

from grpc_helpers.client_base import GrpcClientBase
from grpc_services.generated import custom_certificates_pb2 as pb2
from grpc_services.generated import custom_certificates_pb2_grpc
from user_activity.domain.entities.certificate_enrollment import CertificateEnrollment
from user_activity.domain.entities.enrollment_type_enum import EnrollmentType
from user_activity.domain.repositories.certificate_repository import CertificateRepository
from user_activity.domain.value_objects.certificate_url import CertificateURL


class CertificateGrpcRepository(GrpcClientBase, CertificateRepository):
    def __init__(self, host: str):
        super().__init__(host, custom_certificates_pb2_grpc.PdfServiceStub)

    def generate(self, certificate: CertificateEnrollment) -> CertificateURL:
        payload = pb2.CertificateData(
            template=pb2.CertificateTemplate.TRAIL if certificate.type == EnrollmentType.LEARNING_TRAIL else 0,
            language=certificate.course_language,
            user_name=certificate.user_name,
            performance=certificate.performance,
            time=certificate.course_duration,
            date_finish=certificate.end_date,
            course_name=certificate.course_name,
            contents=[asdict(content) for content in certificate.summary.contents],
            workspace_id=str(certificate.workspace_id),
        )

        response = self._call_with_retry(self.stub.GenerateCertificate, payload)
        return CertificateURL(value=response.url)
