from typing import Optional

from django.db.models import Case, IntegerField, Prefetch, Q, QuerySet, When
from learning_trail.models import LearningTrailStep
from user_activity.domain.repositories.i_learning_trail_enrollment_repository import ILearningTrailEnrollmentRepository
from user_activity.domain.value_objects.certificate_url import CertificateURL
from user_activity.models import LearningTrailEnrollment


class LearningTrailEnrollmentRepository(ILearningTrailEnrollmentRepository):
    def order_learning_trail_enrollment_list_by_status(self, workspace_id: str, ordering: str) -> QuerySet:
        learning_trail_steps_queryset = LearningTrailStep.objects.only(
            "id", "mission_id", "pulse_id", "learning_trail_id"
        ).filter(Q(pulse_id__isnull=False) | Q(mission_id__isnull=False))
        enrollments_by_learning_trail_queryset = LearningTrailEnrollment.objects.only(
            "id", "status", "learning_trail_id"
        )

        query = (
            LearningTrailEnrollment.objects.filter(workspace_id=workspace_id)
            .select_related("user", "learning_trail", "learning_trail__user_creator")
            .prefetch_related(
                Prefetch("learning_trail__learningtrailstep_set", queryset=learning_trail_steps_queryset),
                Prefetch(
                    "learning_trail__learningtrailenrollment_set", queryset=enrollments_by_learning_trail_queryset
                ),
            )
            .only(
                "id",
                "points",
                "performance",
                "start_date",
                "end_date",
                "goal_date",
                "give_up",
                "required",
                "progress",
                "certificate_url",
                "assessment_type",
                "status",
                "user__id",
                "user__name",
                "user__avatar",
                "learning_trail__id",
                "learning_trail__name",
                "learning_trail__expiration_date",
                "learning_trail__user_creator__id",
                "learning_trail__user_creator__name",
                "learning_trail__user_creator__avatar",
            )
            .annotate(
                status_order=Case(
                    When(status="ENROLLED", then=0),
                    When(status="STARTED", then=1),
                    When(status="COMPLETED", then=2),
                    When(status="GIVE_UP", then=3),
                    When(status="REPROVED", then=4),
                    When(status="ENROLLMENT_REPROVED", then=5),
                    default=0,
                    output_field=IntegerField(),
                )
            )
        )

        if ordering:
            if ordering == "-status":
                ordering = "-status_order"
            elif ordering == "status":
                ordering = "status_order"
            query = query.order_by(ordering)
        else:
            query = query.order_by("-start_date")

        return query

    def get_by_id(self, enrollment_id: str) -> Optional[LearningTrailEnrollment]:
        return LearningTrailEnrollment.objects.select_related(
            'user', 'learning_trail', 'workspace'
        ).get(id=enrollment_id)

    def update_certificate_url(self, enrollment: LearningTrailEnrollment, certificate_url: CertificateURL) -> None:
        enrollment.certificate_url = certificate_url.value
        enrollment.save()
