from config import settings
from constants import ACTION_LIST
from custom.discord_webhook import DiscordWebhookLogger
from django.forms import model_to_dict
from django.utils.translation import gettext_noop
from notification.models.notification import Message
from notification.services.notification_service_v2 import NotificationServiceV2
from user_activity.models import LearningTrailEnrollment
from utils.email_service.notification import notify_users

YOU_WERE_ENROLLED_IN_A_TRAIL = gettext_noop("you_were_enrolled_in_a_trail")


class EnrolledInTrailByAdminNotification:
    def __init__(self, notification_service: NotificationServiceV2, webhook_logger: DiscordWebhookLogger):
        self._notification_service = notification_service
        self._webhook_logger = webhook_logger

    def send(self, enrollment_id: str):
        enrollment = LearningTrailEnrollment.objects.get(id=enrollment_id)
        self._send_notification(enrollment)
        self._send_email(enrollment)

    def _send_notification(self, enrollment: LearningTrailEnrollment):
        message = Message(title=YOU_WERE_ENROLLED_IN_A_TRAIL, description=enrollment.learning_trail.name)
        self._notification_service.create_notification(
            user_ids=[str(enrollment.user_id)],
            type_key="ENROLLED_IN_A_TRAIL",
            action=ACTION_LIST,
            object=enrollment.learning_trail_id,
            message=message,
            workspace_id=enrollment.workspace_id,
        )

    @staticmethod
    def _send_email(enrollment: LearningTrailEnrollment):
        email_data = {
            "user_name": enrollment.user.name,
            "learning_trail_name": enrollment.learning_trail.name,
            "learning_trail_link": settings.KONQUEST_WEB_LEARNING_TRAIL_DETAIL_URL.format(
                enrollment.workspace.hash_id, enrollment.learning_trail_id
            ),
        }
        receiver = model_to_dict(enrollment.user)
        notify_users.delay(
            email_data=email_data,
            message_key="user_enrolled_in_a_trail",
            workspace_id=enrollment.workspace_id,
            users_receivers=[receiver],
        )
