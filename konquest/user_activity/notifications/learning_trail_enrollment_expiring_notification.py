from config.settings import KONQUEST_WEB_LEARNING_TRAIL_DETAIL_URL
from constants import ACTION_LIST
from django.forms import model_to_dict
from django.utils.timezone import now
from django.utils.translation import gettext_noop
from notification.models.notification import Message
from notification.services.notification_service_v2 import NotificationServiceV2
from pytz import timezone
from user_activity.models import LearningTrailEnrollment
from utils.email_service.notification import notify_users

YOUR_TRAIL_ENROLLMENT_EXPIRES_TODAY = gettext_noop("your_learning_trail_enrollment_will_expire_today")
YOUR_TRAIL_ENROLLMENT_EXPIRES_TOMORROW = gettext_noop("your_learning_trail_enrollment_will_expire_tomorrow")
YOUR_TRAIL_ENROLLMENT_EXPIRES_IN_X_DAYS = gettext_noop("your_learning_trail_enrollment_will_expire_in_x_days")


class LearningTrailEnrollmentExpiringNotification:
    def __init__(self, notification_service: NotificationServiceV2):
        self._notification_service = notification_service
        self._messages_by_days = {
            0: YOUR_TRAIL_ENROLLMENT_EXPIRES_TODAY,
            1: YOUR_TRAIL_ENROLLMENT_EXPIRES_TOMORROW,
        }

    def send(self, enrollment: LearningTrailEnrollment):
        days_difference = (enrollment.goal_date - now().date()).days
        remaining_days = max(days_difference, 0)
        self._send_notification(enrollment, remaining_days)
        self._send_email(enrollment, remaining_days)

    def _send_notification(self, enrollment: LearningTrailEnrollment, remaining_days: int):
        title_values = {"days": remaining_days} if remaining_days > 1 else {}
        title = self._messages_by_days.get(remaining_days, YOUR_TRAIL_ENROLLMENT_EXPIRES_IN_X_DAYS)

        message = Message(
            title=title,
            title_values=title_values,
            description=enrollment.learning_trail.name,
        )
        self._notification_service.create_notification(
            user_ids=[str(enrollment.user_id)],
            type_key="LEARNING_TRAIL_ENROLLMENT_EXPIRING",
            action=ACTION_LIST,
            workspace_id=enrollment.workspace_id,
            object=enrollment.learning_trail_id,
            message=message,
        )

    @staticmethod
    def _send_email(enrollment: LearningTrailEnrollment, days_remaining: int):
        email_data = {
            "user_name": enrollment.user.name,
            "learning_trail_name": enrollment.learning_trail.name,
            "learning_trail_link": KONQUEST_WEB_LEARNING_TRAIL_DETAIL_URL.format(
                enrollment.workspace.hash_id, enrollment.learning_trail_id
            ),
            "learning_trail_vertical_cover_image": enrollment.learning_trail.thumb_image,
            "days_to_expire": days_remaining,
            "enrollment_goal_date": enrollment.goal_date,
            "enrollment_created_date": enrollment.created_date.astimezone(timezone(enrollment.user.time_zone)),
            "workspace_name": enrollment.workspace.name,
        }
        user_data = model_to_dict(enrollment.user)
        notify_users.delay(
            email_data=email_data,
            message_key="learning_trail_enrollment_expiring",
            workspace_id=enrollment.workspace_id,
            users_receivers=[user_data],
        )
