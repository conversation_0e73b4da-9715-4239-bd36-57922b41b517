from config import settings
from constants import ACTION_LIST
from custom.discord_webhook import DiscordWebhookLogger
from django.forms import model_to_dict
from django.utils.translation.trans_null import gettext_noop
from notification.models.notification import Message
from notification.services.notification_service_v2 import NotificationServiceV2
from user_activity.models import MissionEnrollment
from utils.email_service.notification import notify_users

YOUR_MISSION_ENROLLMENT_HAS_BEEN_RESET = gettext_noop("your_mission_enrollment_has_been_reset")


class MissionEnrollmentRestartedNotification:
    def __init__(self, notification_service: NotificationServiceV2, webhook_logger: DiscordWebhookLogger):
        self._notification_service = notification_service
        self._webhook_logger = webhook_logger

    def send(self, enrollment_id: str):
        enrollment = MissionEnrollment.objects.get(id=enrollment_id)
        try:
            self._send_notification(enrollment)
        except Exception as error:
            self._webhook_logger.emit_short_message(
                f"error to send mission enrollment restarted notification to enrollment {enrollment.id}", error
            )
        try:
            self._send_email(enrollment)
        except Exception as error:
            self._webhook_logger.emit_short_message(
                f"error to send mission enrollment restarted email to enrollment {enrollment.id}", error
            )

    def _send_notification(self, enrollment: MissionEnrollment):
        message = Message(title=YOUR_MISSION_ENROLLMENT_HAS_BEEN_RESET, description=enrollment.mission.name)
        self._notification_service.create_notification(
            user_ids=[str(enrollment.user_id)],
            type_key="MISSION_ENROLLMENT_HAS_BEEN_RESTARTED",
            action=ACTION_LIST,
            message=message,
            object=enrollment.mission_id,
            workspace_id=str(enrollment.workspace_id),
        )

    @staticmethod
    def _send_email(enrollment: MissionEnrollment):
        email_data = {
            "user_name": enrollment.user.name,
            "mission_name": enrollment.mission.name,
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
                enrollment.workspace.hash_id, enrollment.mission_id
            ),
            "minimum_performance": enrollment.mission.minimum_performance,
        }
        receiver = model_to_dict(enrollment.user)
        notify_users.delay(
            email_data=email_data,
            message_key="mission_enrollment_restarted",
            workspace_id=enrollment.workspace_id,
            users_receivers=[receiver],
        )
