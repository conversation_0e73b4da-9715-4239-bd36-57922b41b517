from unittest.mock import MagicMock

import mock
from account.models import User, Workspace
from config import settings
from conftest import NotificationServiceStub
from constants import ACTION_LIST, NOTIFY_USERS_PATH
from django.forms import model_to_dict
from django.test import TestCase
from learning_trail.models import LearningTrail
from model_mommy import mommy
from notification.models.notification import Message
from user_activity.models import LearningTrailEnrollment
from user_activity.notifications.enrolled_in_trail_by_admin_notification import (
    YOU_WERE_ENROLLED_IN_A_TRAIL,
    EnrolledInTrailByAdminNotification,
)


class TestEnrolledInTrailByAdminNotification(TestCase):
    def setUp(self) -> None:
        self._webhook_logger = MagicMock()
        self.enrolled_in_trail_notification = EnrolledInTrailByAdminNotification(
            NotificationServiceStub(), self._webhook_logger
        )

    @mock.patch.object(NotificationServiceStub, NotificationServiceStub.create_notification.__name__)
    @mock.patch(NOTIFY_USERS_PATH)
    def test_should_send(self, notify_users: mock.MagicMock, create_notification: mock.MagicMock):
        trail = mommy.make(LearningTrail)
        user = mommy.make(User)
        workspace = mommy.make(Workspace)
        enrollment = mommy.make(LearningTrailEnrollment, learning_trail=trail, user=user, workspace=workspace)

        self.enrolled_in_trail_notification.send(enrollment.id)

        message = Message(title=YOU_WERE_ENROLLED_IN_A_TRAIL, description=trail.name)
        create_notification.assert_called_with(
            user_ids=[str(user.id)],
            type_key="ENROLLED_IN_A_TRAIL",
            action=ACTION_LIST,
            message=message,
            object=trail.id,
            workspace_id=enrollment.workspace_id,
        )
        notify_users.assert_called_with(
            email_data={
                "user_name": enrollment.user.name,
                "learning_trail_name": enrollment.learning_trail.name,
                "learning_trail_link": settings.KONQUEST_WEB_LEARNING_TRAIL_DETAIL_URL.format(
                    enrollment.workspace.hash_id, enrollment.learning_trail_id
                ),
            },
            message_key="user_enrolled_in_a_trail",
            workspace_id=enrollment.workspace_id,
            users_receivers=[model_to_dict(enrollment.user)],
        )
