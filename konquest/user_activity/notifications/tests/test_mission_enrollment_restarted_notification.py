import mock
from account.models import User, Workspace
from config import settings
from constants import ACTION_LIST, NOTIFY_USERS_PATH
from django.forms import model_to_dict
from django.test import TestCase
from mission.models import Mission
from mock.mock import MagicMock
from model_mommy import mommy
from notification.models.notification import Message
from notification.services import NotificationService
from user_activity.models import MissionEnrollment
from user_activity.models.mission_enrollment import STARTED
from user_activity.notifications.mission_enrollment_restarted_notification import (
    YOUR_MISSION_ENROLLMENT_HAS_BEEN_RESET,
    MissionEnrollmentRestartedNotification,
)
from utils.utils import workspace_hash


class NotificationServiceStub(NotificationService):
    def create_notification(self, *args, **kwargs):
        # mock the function to test
        pass

    def create_notifications(self, *args, **kwargs):
        # mock the function to test
        pass


class TestMissionEnrollmentRestartedNotification(TestCase):
    def setUp(self) -> None:
        self._webhook_logger = MagicMock()
        self.mission_enrollment_restarted_notification = MissionEnrollmentRestartedNotification(
            NotificationServiceStub(), self._webhook_logger
        )

    @mock.patch.object(NotificationServiceStub, NotificationServiceStub.create_notification.__name__)
    @mock.patch(NOTIFY_USERS_PATH)
    def test_should_send(self, notify_users: mock.MagicMock, create_notification: mock.MagicMock):
        mission = mommy.make(Mission)
        user = mommy.make(User)
        workspace = mommy.make(Workspace)
        enrollment = mommy.make(MissionEnrollment, mission=mission, user=user, status=STARTED, workspace=workspace)

        self.mission_enrollment_restarted_notification.send(enrollment.id)

        message = Message(title=YOUR_MISSION_ENROLLMENT_HAS_BEEN_RESET, description=mission.name)
        create_notification.assert_called_with(
            user_ids=[str(user.id)],
            type_key="MISSION_ENROLLMENT_HAS_BEEN_RESTARTED",
            action=ACTION_LIST,
            message=message,
            object=mission.id,
            workspace_id=str(enrollment.workspace_id),
        )
        notify_users.assert_called_with(
            email_data={
                "user_name": enrollment.user.name,
                "mission_name": enrollment.mission.name,
                "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
                    workspace_hash(str(enrollment.workspace_id)), enrollment.mission_id
                ),
                "minimum_performance": enrollment.mission.minimum_performance,
            },
            message_key="mission_enrollment_restarted",
            workspace_id=enrollment.workspace_id,
            users_receivers=[model_to_dict(enrollment.user)],
        )
