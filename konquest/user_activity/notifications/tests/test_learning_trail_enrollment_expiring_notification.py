from datetime import datetime
from unittest.mock import MagicMock

import mock
from account.models import User, Workspace
from config.settings import KONQUEST_WEB_LEARNING_TRAIL_DETAIL_URL
from constants import ACTION_LIST, ENROLLMENT_STARTED, NOTIFY_USERS_PATH
from django.forms import model_to_dict
from django.test import TestCase
from learning_trail.models import LearningTrail
from model_mommy import mommy
from notification.models.notification import Message
from pytz import timezone
from user_activity.models import LearningTrailEnrollment
from user_activity.notifications.learning_trail_enrollment_expiring_notification import (
    YOUR_TRAIL_ENROLLMENT_EXPIRES_TODAY,
    LearningTrailEnrollmentExpiringNotification,
)
from user_activity.notifications.tests.test_mission_enrollment_restarted_notification import NotificationServiceStub


class TestLearningTrailEnrollmentExpiringNotification(TestCase):
    def setUp(self) -> None:
        self._webhook_logger = MagicMock()
        self.trail_enrollment_expiring_notification = LearningTrailEnrollmentExpiringNotification(
            NotificationServiceStub()
        )

    @mock.patch.object(NotificationServiceStub, NotificationServiceStub.create_notification.__name__)
    @mock.patch(NOTIFY_USERS_PATH)
    def test_should_send(self, notify_users: mock.MagicMock, create_notification: mock.MagicMock):
        trail = mommy.make(LearningTrail)
        user = mommy.make(User)
        workspace = mommy.make(Workspace)
        enrollment = mommy.make(
            LearningTrailEnrollment,
            learning_trail=trail,
            user=user,
            status=ENROLLMENT_STARTED,
            workspace=workspace,
            goal_date=datetime.now().date(),
        )

        self.trail_enrollment_expiring_notification.send(enrollment)

        message = Message(title=YOUR_TRAIL_ENROLLMENT_EXPIRES_TODAY, description=trail.name)
        create_notification.assert_called_with(
            user_ids=[str(user.id)],
            type_key="LEARNING_TRAIL_ENROLLMENT_EXPIRING",
            action=ACTION_LIST,
            message=message,
            object=trail.id,
            workspace_id=enrollment.workspace_id,
        )
        notify_users.assert_called_with(
            email_data={
                "user_name": enrollment.user.name,
                "learning_trail_name": enrollment.learning_trail.name,
                "learning_trail_link": KONQUEST_WEB_LEARNING_TRAIL_DETAIL_URL.format(
                    enrollment.workspace.hash_id, enrollment.learning_trail_id
                ),
                "learning_trail_vertical_cover_image": enrollment.learning_trail.thumb_image,
                "days_to_expire": 0,
                "enrollment_goal_date": enrollment.goal_date,
                "enrollment_created_date": enrollment.created_date.astimezone(timezone(enrollment.user.time_zone)),
                "workspace_name": enrollment.workspace.name,
            },
            message_key="learning_trail_enrollment_expiring",
            workspace_id=enrollment.workspace_id,
            users_receivers=[model_to_dict(enrollment.user)],
        )
