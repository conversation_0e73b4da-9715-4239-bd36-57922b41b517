from account.models.user_profile_workspace import UserProfileWorkspace
from django.db.models import <PERSON>oleanField, Case, CharField, OuterRef, QuerySet, Subquery, Value, When
from user_activity.models import MissionEnrollment


def get_mission_enrollment_list_queryset(workspace_id: str) -> QuerySet:
    user_profile_subquery = UserProfileWorkspace.objects.filter(
        user=OuterRef("user"), workspace_id=workspace_id
    ).values("job_position", "job_function", "director", "manager", "area_of_activity")[:1]

    return (
        MissionEnrollment.objects.filter(workspace_id=workspace_id)
        .select_related("user", "mission", "workspace")
        .select_related("mission__external", "mission__external__provider")
        .prefetch_related("mission__learning_trail_step")
        .prefetch_related("evaluation")
        .annotate(
            attended=Value(None, output_field=BooleanField()),
            provider=Case(
                When(mission__external__provider__isnull=True, then=Value("")),
                default="mission__external__provider__name",
                output_field=CharField(),
            ),
            external_course_url=Case(
                When(mission__external__course_url__isnull=True, then=Value("")),
                default="mission__external__course_url",
                output_field=CharField(),
            ),
            job_position=Subquery(user_profile_subquery.values("job_position")),
            job_function=Subquery(user_profile_subquery.values("job_function")),
            director=Subquery(user_profile_subquery.values("director")),
            manager=Subquery(user_profile_subquery.values("manager")),
            area_of_activity=Subquery(user_profile_subquery.values("area_of_activity")),
        )
        .distinct()
    )
