from django.test import TestCase
from pulse.models import Pulse
from user_activity.models import LearnContentActivity
from user_activity.tasks.learn_content_activity_task import update_pulse_views


class LearnContentActivityTasksTestCase(TestCase):
    fixtures = ["workspace", "user", "channel_type", "channel_category", "channel", "pulse_type", "pulse"]

    def setUp(self) -> None:
        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.user_id = "489fb596-478f-40b5-b717-4931f20e4cc6"

        self.pulse_id = "7ec337a7-07f7-47e3-aaec-0b1239eec921"

    def test_learn_content_activity_update_pulse_views(self):
        LearnContentActivity.objects.create(
            user_id=self.user_id, pulse_id=self.pulse_id, time_start="2023-01-23T20:17:30.898Z", action="VIEW"
        )
        update_pulse_views(self.pulse_id)
        pulse = Pulse.objects.get(id=self.pulse_id)
        self.assertEqual(pulse.views, 1)
