import uuid

import mock
from constants import CELERY_SEND_TASK_PATH
from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient
from user_activity.models import MissionEnrollment
from user_activity.models.mission_enrollment import ENROLLED, REFUSED, WAITING_APPROVAL

PROMOTE_WAITING_VACANCIES_ENROLLMENTS = (
    "user_activity.tasks.mission_enrollment_task.promote_waiting_vacancies_enrollments.delay"
)
NOTIFY_SYNC_ENROLLMENT_REFUSED = "user_activity.tasks.notifications.notify_sync_enrollment_refused.delay"
NOTIFY_SYNC_ENROLLMENT_APPROVED = "user_activity.tasks.notifications.notify_sync_enrollment_approved.delay"
CREATE_SYNC_ENROLLMENT_ATTENDANCES = (
    "user_activity.tasks.mission_enrollment_task.create_sync_enrollment_attendances.delay"
)
DELETE_SYNC_ENROLLMENT_ATTENDANCES = (
    "user_activity.tasks.mission_enrollment_task.delete_sync_enrollment_attendances.delay"
)


@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class SyncEnrollmentApprovalViewSetTestCase(TestCase):
    fixtures = ["workspace", "user", "mission_type", "mission"]

    def setUp(self) -> None:
        self.client = APIClient()
        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.user_id = "489fb596-478f-40b5-b717-4931f20e4cc6"
        self.enrollment = mommy.make(
            MissionEnrollment,
            mission_id="c8baa509-7637-47a4-a97b-4e717f56df74",
            status=WAITING_APPROVAL,
            user_id=self.user_id,
            workspace_id=self.workspace_id,
        )

        self.headers = {"HTTP_X_CLIENT": str(self.workspace_id)}
        self.client.force_authenticate(user={"sub": str(self.user_id), "client_id": str(self.workspace_id)})
        self.url_name = "mission-enrollment-approval"
        self.url = reverse(self.url_name, args=[str(self.enrollment.id)])

    @mock.patch(CELERY_SEND_TASK_PATH)
    def test_approve_enrollment(self, *args):
        payload = {"approved": True}
        response = self.client.post(self.url, data=payload, **self.headers, format="json")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data.get("status"), ENROLLED)

    @mock.patch(CELERY_SEND_TASK_PATH)
    def test_reprove_enrollment(self, *args):
        payload = {"approved": False}
        response = self.client.post(self.url, data=payload, **self.headers, format="json")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data.get("status"), REFUSED)

    def test_enrollment_not_found(self, check_role):
        url = reverse(self.url_name, args=[uuid.uuid4()])
        payload = {"approved": False}
        response = self.client.post(url, data=payload, **self.headers, format="json")
        self.assertEqual(response.status_code, 404)
