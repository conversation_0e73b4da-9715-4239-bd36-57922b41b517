import mock
from account.models import User
from custom.keeps_exception_handler import NOT_PERMISSION_TO_MODIFY_THIS_MISSION_ENROLLMENT
from dateutil.utils import today
from django.test import TestCase
from django.urls import reverse
from django.utils.translation import gettext as _
from mission.models import LiveMissionDates, Mission
from mission.models.mission import LIVE
from model_mommy import mommy
from rest_framework.test import APIClient
from user_activity.models import LiveAttendance, MissionEnrollment
from user_activity.models.mission_enrollment import COMPLETED


@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class LiveAttendanceViewSetTestCase(TestCase):
    fixtures = [
        "user",
        "workspace",
        "mission_type",
        "mission_category",
        "mission",
        "mission_workspace",
        "mission_enrollment",
    ]

    def setUp(self) -> None:
        self._mission_id = "c8baa509-7637-47a4-a97b-4e717f56df74"
        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.another_workspace_id = "f7d9bd2b-0d0c-45e3-bebb-70b9797ce0e5"
        self.user_id = "244ef26a-eb00-4f80-9800-5e976e234452"

        self.client = APIClient()
        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.client.force_authenticate(user={"sub": self.user_id, "client_id": self.workspace_id})
        self.url_name = "mission-enrollment-sync-finish"

    def test_finish(self, check_role: mock.MagicMock):
        enrollment = self.create_live_enrollment()
        self.client.force_authenticate(user={"sub": str(enrollment.user_id), "client_id": self.workspace_id})
        url = reverse(self.url_name, args=[str(enrollment.id)])

        response = self.client.post(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data.get("status"), COMPLETED)
        self.assertEqual(response.data.get("performance"), 1)

    def test_finish_not_allowed_error(self, check_role: mock.MagicMock):
        enrollment = self.create_live_enrollment()
        url = reverse(self.url_name, args=[str(enrollment.id)])
        enrollment.workspace_id = self.another_workspace_id
        enrollment.save()

        response = self.client.post(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 403)
        self.assertEqual(response.data.get("detail"), _(NOT_PERMISSION_TO_MODIFY_THIS_MISSION_ENROLLMENT))

    def create_live_enrollment(self) -> MissionEnrollment:
        mission = Mission.objects.get(id=self._mission_id)
        user = mommy.make(User, name="Ana", email="<EMAIL>")
        enrollment_1 = mommy.make(MissionEnrollment, user=user, mission=mission, workspace_id=self.workspace_id)
        mission = enrollment_1.mission
        mission.mission_model = LIVE
        mission.save()
        live_date = mommy.make(LiveMissionDates, live=mission.live, start_at=today(), end_at=today())
        mommy.make(LiveAttendance, date=live_date, enrollment=enrollment_1, presented=True)
        return enrollment_1
