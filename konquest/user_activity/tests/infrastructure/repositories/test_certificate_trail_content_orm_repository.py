from unittest.mock import MagicMock, patch

import pytest
from learning_trail.models import LearningTrailStep
from mission.models import Mission
from pulse.models import Pulse
from user_activity.domain.entities.certificate_enrollment import CertificateContents, CertificateSummary
from user_activity.infrastructure.repositories.certificate_trail_content_orm_repository import (
    CertificateTrailContentORMRepository,
)
from user_activity.models import MissionEnrollment
from user_activity.models.enums.enrollment_status_enum import EnrollmentStatusEnum


@pytest.fixture
def trail_enrollment_repository_mock():
    return MagicMock()


@pytest.fixture
def repository(trail_enrollment_repository_mock):
    return CertificateTrailContentORMRepository(trail_enrollment_repository_mock)


@patch("user_activity.models.MissionEnrollment.objects")
def test_get_contents_with_missions_and_pulses(
    mission_enrollment_objects_mock,
    repository,
    trail_enrollment_repository_mock
):
    mission = MagicMock(spec=Mission)
    mission.name = "Mission Step"
    mission.duration_time = 3600

    pulse = MagicMock(spec=Pulse)
    pulse.name = "Pulse Step"
    pulse.duration_time = 900

    step_mission = MagicMock(spec=LearningTrailStep)
    step_mission.mission = mission
    step_mission.pulse = None

    step_pulse = MagicMock(spec=LearningTrailStep)
    step_pulse.mission = None
    step_pulse.pulse = pulse

    trail_steps = [step_mission, step_pulse]

    enrollment = MagicMock()
    enrollment.learning_trail.learningtrailstep_set.order_by.return_value = trail_steps
    enrollment.user = "user_id"
    enrollment.workspace = "workspace_id"

    trail_enrollment_repository_mock.get_by_id.return_value = enrollment

    mock_mission_enrollment = MagicMock(spec=MissionEnrollment)
    mock_mission_enrollment.mission.name = "Mission Step"
    mock_mission_enrollment.mission.duration_time = 3600

    mission_enrollment_objects_mock.filter.return_value.order_by.return_value.first.return_value = (
        mock_mission_enrollment
    )

    result = repository.get_contents("enrollment-id")

    assert len(result.contents) == 2

    assert isinstance(result.contents[0], CertificateContents)
    assert result.contents[0].title == "Mission Step"
    assert result.contents[0].time == "1h00m"

    assert isinstance(result.contents[1], CertificateContents)
    assert result.contents[1].title == "Pulse Step"
    assert result.contents[1].time == "15m"

    trail_enrollment_repository_mock.get_by_id.assert_called_once_with("enrollment-id")
    mission_enrollment_objects_mock.filter.assert_called_once_with(
        mission=mission,
        user="user_id",
        workspace="workspace_id",
        status=EnrollmentStatusEnum.COMPLETED
    )
    mission_enrollment_objects_mock.filter.return_value.order_by.return_value.first.assert_called_once()


@patch("user_activity.models.MissionEnrollment.objects")
def test_get_contents_skips_if_no_mission_enrollment(
    mission_enrollment_objects_mock,
    repository,
    trail_enrollment_repository_mock
):
    mission = MagicMock()
    mission.name = "Mission Step"
    mission.duration_time = 3600

    step_mission = MagicMock()
    step_mission.mission = mission
    step_mission.pulse = None

    trail_steps = [step_mission]

    enrollment = MagicMock()
    enrollment.learning_trail.learningtrailstep_set.order_by.return_value = trail_steps
    enrollment.user = "user_id"
    enrollment.workspace = "workspace_id"

    trail_enrollment_repository_mock.get_by_id.return_value = enrollment
    mission_enrollment_objects_mock.filter.return_value.order_by.return_value.first.return_value = None

    result = repository.get_contents("enrollment-id")

    assert result == CertificateSummary([])

    trail_enrollment_repository_mock.get_by_id.assert_called_once_with("enrollment-id")
    mission_enrollment_objects_mock.filter.assert_called_once()
