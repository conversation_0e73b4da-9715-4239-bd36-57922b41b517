from unittest.mock import MagicMock

import pytest
from user_activity.domain.entities.certificate_enrollment import CertificateContents, CertificateSummary
from user_activity.infrastructure.repositories.certificate_mission_contents_orm_repository import (
    CertificateMissionContentORMRepository,
)


@pytest.fixture
def tracking_service_mock():
    return MagicMock()


@pytest.fixture
def translation_service_mock():
    return MagicMock()


@pytest.fixture
def content_size_formatter_mock():
    return MagicMock()


@pytest.fixture
def repository(tracking_service_mock, translation_service_mock, content_size_formatter_mock):
    return CertificateMissionContentORMRepository(
        tracking_service=tracking_service_mock,
        translation_service=translation_service_mock,
        content_size_formatter=content_size_formatter_mock
    )


def test_get_contents_success(repository, tracking_service_mock, content_size_formatter_mock):
    enrollment_id = "test-enrollment-id"

    tracking_results = [
        {"name": "Lesson 1", "content_duration": 300, "content": True},
        {"name": "Quiz 1", "total_questions": 10, "total_correct_answers": 8, "content": None}
    ]
    tracking_service_mock.load_by_enrollment_id.return_value = tracking_results

    content_size_formatter_mock.format.side_effect = ["5m", "10 questions, 8 correct"]

    result = repository.get_contents(enrollment_id)

    assert len(result.contents) == 2

    assert isinstance(result.contents[0], CertificateContents)
    assert result.contents[0].title == "Lesson 1"
    assert result.contents[0].time == "5m"

    assert isinstance(result.contents[1], CertificateContents)
    assert result.contents[1].title == "Quiz 1"
    assert result.contents[1].time == "10 questions, 8 correct"

    tracking_service_mock.load_by_enrollment_id.assert_called_once_with(enrollment_id)
    assert content_size_formatter_mock.format.call_count == 2


def test_get_contents_empty_tracking(repository, tracking_service_mock):
    enrollment_id = "test-enrollment-id"
    tracking_service_mock.load_by_enrollment_id.return_value = []

    result = repository.get_contents(enrollment_id)

    assert result == CertificateSummary([])
    tracking_service_mock.load_by_enrollment_id.assert_called_once_with(enrollment_id)
