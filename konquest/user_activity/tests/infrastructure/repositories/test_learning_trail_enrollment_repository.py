import uuid
from datetime import date

from account.models import User, Workspace
from config.settings import LEARNING_TRAIL_OPEN_FOR_COMPANY
from django.test import TestCase
from learning_trail.models import LearningTrail, LearningTrailStep, LearningTrailType, LearningTrailWorkspace
from mission.models import Mission
from model_mommy import mommy
from user_activity.infrastructure.repositories.learning_trail_enrollment_repository import (
    LearningTrailEnrollmentRepository,
)
from user_activity.models import LearningTrailEnrollment


class LearningTrailEnrollmentRepositoryTest(TestCase):
    def setUp(self):
        self.user = mommy.make(User, id=uuid.uuid4(), email="<EMAIL>")
        self.workspace = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps")
        self.closed_trail_type = mommy.make(LearningTrailType, name=LEARNING_TRAIL_OPEN_FOR_COMPANY)

        learning_trail = mommy.make(
            LearningTrail,
            id=uuid.uuid4(),
            name="Keeps Open For Workspace Learning Trail",
            user_creator=self.user,
            is_active=True,
        )

        LearningTrailWorkspace(learning_trail=learning_trail, workspace=self.workspace).save()

        mission = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Mission Name",
            user_creator=self.user,
            development_status="DONE",
            is_active=True,
        )

        LearningTrailStep(learning_trail=learning_trail, mission=mission, order=1).save()

        self.repository = LearningTrailEnrollmentRepository()

        self.enrollment1 = LearningTrailEnrollment.objects.create(
            learning_trail_id=learning_trail.id,
            user_id=self.user.id,
            workspace_id=self.workspace.id,
            status="ENROLLED",
            start_date="2023-01-01",
            end_date="2023-01-10",
            goal_date=date(2023, 1, 15),
        )
        self.enrollment2 = LearningTrailEnrollment.objects.create(
            learning_trail_id=learning_trail.id,
            user_id=self.user.id,
            workspace_id=self.workspace.id,
            status="COMPLETED",
            start_date="2023-01-02",
            end_date="2023-01-12",
            goal_date=date(2023, 1, 20),
        )

        self.enrollment3 = mommy.make(
            LearningTrailEnrollment,
            learning_trail_id=learning_trail.id,
            user_id=self.user.id,
            workspace_id=self.workspace.id,
            status="STARTED",
            start_date="2023-01-03",
            end_date="2023-01-13",
            goal_date=date(2023, 1, 20),
        )

        self.workspace_id = self.workspace.id

    def test_order_by_start_date(self):
        result = self.repository.order_learning_trail_enrollment_list_by_status(
            workspace_id=self.workspace.id, ordering="start_date"
        )
        self.assertEqual(list(result), [self.enrollment1, self.enrollment2, self.enrollment3])

    def test_order_by_end_date(self):
        result = self.repository.order_learning_trail_enrollment_list_by_status(
            workspace_id=self.workspace.id, ordering="-end_date"
        )
        self.assertEqual(list(result), [self.enrollment3, self.enrollment2, self.enrollment1])

    def test_filter_by_status(self):
        result = self.repository.order_learning_trail_enrollment_list_by_status(
            workspace_id=self.workspace.id, ordering="status"
        )

        statuses = [enrollment.status for enrollment in result]
        self.assertEqual(statuses, ["ENROLLED", "STARTED", "COMPLETED"])

    def test_filter_by_status_desc(self):
        result = self.repository.order_learning_trail_enrollment_list_by_status(
            workspace_id=self.workspace.id, ordering="-status"
        )

        statuses = [enrollment.status for enrollment in result]
        self.assertEqual(statuses, ["COMPLETED", "STARTED", "ENROLLED"])
