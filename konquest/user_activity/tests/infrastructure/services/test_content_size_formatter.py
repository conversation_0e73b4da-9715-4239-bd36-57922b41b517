from unittest.mock import MagicMock

import pytest
from custom.translations.translation_keys import X_HOURS_AND_Y_MINUTES, X_QUESTIONS_AND_Y_HITS, X_SECONDS
from user_activity.domain.services.content_size_formatter import ContentSizeFormatter
from user_activity.domain.services.translation_service import TranslationService


@pytest.fixture
def translation_service_mock():
    mock = MagicMock(spec=TranslationService)
    mock.get_translation.side_effect = lambda key, *args: f"Translated: {key} {args}"
    return mock


@pytest.fixture
def content_size_formatter(translation_service_mock):
    return ContentSizeFormatter(translation_service=translation_service_mock)


def test_format_quiz(content_size_formatter, translation_service_mock):
    content_tracking = {
        "content": False,
        "total_questions": 10,
        "total_correct_answers": 8
    }

    result = content_size_formatter.format(content_tracking)

    translation_service_mock.get_translation.assert_called_once_with(
        X_QUESTIONS_AND_Y_HITS, 10, 8
    )
    assert "Translated" in result


def test_format_content_less_than_60_seconds(content_size_formatter, translation_service_mock):
    content_tracking = {
        "content": True,
        "content_duration": 45
    }

    result = content_size_formatter.format(content_tracking)

    translation_service_mock.get_translation.assert_called_once_with(
        X_SECONDS, "45"
    )
    assert "Translated" in result


def test_format_content_more_than_60_seconds(content_size_formatter, translation_service_mock):
    content_tracking = {
        "content": True,
        "content_duration": 3660
    }

    result = content_size_formatter.format(content_tracking)

    translation_service_mock.get_translation.assert_called_once_with(
        X_HOURS_AND_Y_MINUTES, (1, 1)
    )
    assert "Translated" in result
