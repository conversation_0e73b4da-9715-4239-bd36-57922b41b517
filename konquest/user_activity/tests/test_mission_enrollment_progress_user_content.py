from account.models import User
from django.test import TestCase
from learn_content.models import Answer, Exam, Question
from mission.models import Mission, MissionStage, MissionStageContent
from model_mommy import mommy
from user_activity.models import LearnContentActivity, MissionEnrollment, UserMissionContent
from user_activity.models.mission_enrollment import COMPLETED
from user_activity.services.mission_enrollment_progress_service import MissionEnrollmentProgressService


class TestMissionEnrollmentProgressUserContentService(TestCase):
    def setUp(self):
        self.user = mommy.make(User)
        self.mission = mommy.make(Mission)
        self.stage = mommy.make(MissionStage, mission=self.mission)
        self.exam = mommy.make(Exam)
        self.question = mommy.make(Question, exam=self.exam)
        self.content = mommy.make(
            MissionStageContent, id=self.exam.id, stage=self.stage, learn_content_uuid=self.exam.id
        )
        self.enrollment = mommy.make(MissionEnrollment, mission=self.mission, user_id=self.user.id)
        self.answer = mommy.make(
            Answer,
            exam_has_question=self.question,
            enrollment=self.enrollment,
            user_id=self.user.id,
        )
        self.activity = mommy.make(
            LearnContentActivity,
            mission_enrollment=self.enrollment,
            user_id=self.user.id,
            mission_stage_content=self.content,
        )
        self.service = MissionEnrollmentProgressService(0.9, 0.1)

    def test_compute_question_answered(self):
        self.service.compute_question_answered(self.answer)
        user_mission_content = UserMissionContent.objects.get(
            user_id=self.answer.user_id, content_id=self.answer.exam_has_question.exam_id
        )
        self.assertEqual(user_mission_content.status, COMPLETED)

    def test_compute_content_consumed(self):
        self.service.compute_content_consumed(self.activity)
        updated_user_mission_content = UserMissionContent.objects.get(
            user_id=self.answer.user_id, content_id=self.content.id
        )
        self.assertEqual(updated_user_mission_content.status, COMPLETED)
