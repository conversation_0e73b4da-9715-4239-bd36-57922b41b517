import datetime
import uuid
from unittest import mock

from account.models import User, Workspace
from conftest import get_injector
from constants import CELERY_SEND_TASK_PATH
from django.core.exceptions import ValidationError
from django.test import TestCase
from django.utils.translation import gettext as _
from learning_trail.models import LearningTrail, LearningTrailStep, LearningTrailType, LearningTrailWorkspace
from mission.models import Mission
from mission.models.mission import LIVE
from model_mommy import mommy
from pulse.models import Pulse
from user_activity.models import LearnContentActivity, LearningTrailEnrollment, MissionEnrollment
from user_activity.services import LearningTrailEnrollmentService
from user_activity.services.learning_trail_enrollment_finish_service import (
    USER_DONT_COMPLETED_ALL_LEARNING_TRAIL_MISSIONS,
    LearningTrailEnrollmentFinishService,
)

GENERATE_LEARNING_TRAIL_CERTIFICATE = (
    "user_activity.application.use_cases.generate_certificate."
    "generate_trail_certificate_use_case.GenerateTrailCertificateUseCase.execute"
)
UPDATE_ENROLLMENT_PROGRESS = "user_activity.tasks.learning_trail_enrollment_task.update_enrollments_progress.delay"


@mock.patch(GENERATE_LEARNING_TRAIL_CERTIFICATE, return_values=None)
@mock.patch(UPDATE_ENROLLMENT_PROGRESS, return_values=None)
class LearningTrailEnrollmentServiceTestCase(TestCase):
    def setUp(self):
        self.workspace = mommy.make(Workspace, id=uuid.uuid4())
        self.user = mommy.make(User, id=uuid.uuid4())

        self.learning_trail_type = mommy.make(LearningTrailType)

        self.learning_trail = mommy.make(LearningTrail, id=uuid.uuid4(), learning_trail_type=self.learning_trail_type)
        self.learning_trail_to_restart = mommy.make(
            LearningTrail, id=uuid.uuid4(), learning_trail_type=self.learning_trail_type
        )
        self.learning_trail_workspace = mommy.make(
            LearningTrailWorkspace, learning_trail=self.learning_trail, workspace=self.workspace
        )
        self.mission_1 = mommy.make(
            Mission, id=uuid.uuid4(), name="Mission Name", development_status="DONE", points=200, is_active=True
        )
        self.mission_2 = mommy.make(
            Mission, id=uuid.uuid4(), name="Mission Name 2", development_status="DONE", points=200, is_active=True
        )
        self.pulse = mommy.make(Pulse, points=100)
        LearningTrailStep(learning_trail=self.learning_trail, mission=self.mission_1, order=1).save()
        LearningTrailStep(learning_trail=self.learning_trail, mission=self.mission_2, order=2).save()
        LearningTrailStep(learning_trail=self.learning_trail, pulse=self.pulse, order=3).save()

        self.trail_enrollment = mommy.make(
            LearningTrailEnrollment, learning_trail=self.learning_trail, workspace=self.workspace, user=self.user
        )

        self.mission_enrollment_1 = mommy.make(
            MissionEnrollment, mission=self.mission_1, workspace=self.workspace, user=self.user
        )
        self.mission_enrollment_2 = mommy.make(
            MissionEnrollment, mission=self.mission_2, workspace=self.workspace, user=self.user
        )
        LearnContentActivity(pulse=self.pulse, user=self.user, time_start=datetime.datetime.today()).save()

        self.injector = get_injector()
        self.trail_enrollment_finish_service = self.injector.get(LearningTrailEnrollmentFinishService)
        self.trail_service = self.injector.get(LearningTrailEnrollmentService)

    def test_finish_enrollment(self, update_enrollment_progress: mock.MagicMock, generate_certificate: mock.MagicMock):
        mission_enrollment_1_performance = 0.8
        mission_enrollment_1_points = 1000

        MissionEnrollment.objects.filter(id=self.mission_enrollment_1.id).update(
            status="COMPLETED", performance=mission_enrollment_1_performance, points=mission_enrollment_1_points
        )

        mission_enrollment_2_performance = 0.3
        mission_enrollment_2_points = 400

        MissionEnrollment.objects.filter(id=self.mission_enrollment_2.id).update(
            status="COMPLETED", performance=mission_enrollment_2_performance, points=mission_enrollment_2_points
        )

    def test_finish_enrollment_when_live_mission_exists(
        self, update_enrollment_progress: mock.MagicMock, generate_certificate: mock.MagicMock
    ):
        live_mission = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Live Mission",
            development_status="DONE",
            points=200,
            is_active=True,
            mission_model=LIVE,
        )
        LearningTrailStep(learning_trail=self.learning_trail, mission=live_mission, order=4).save()

        mission_enrollment_1_performance = 0.8
        mission_enrollment_1_points = 1000

        MissionEnrollment.objects.filter(id=self.mission_enrollment_1.id).update(
            status="COMPLETED", performance=mission_enrollment_1_performance, points=mission_enrollment_1_points
        )

        mission_enrollment_2_performance = 0.3
        mission_enrollment_2_points = 400

        MissionEnrollment.objects.filter(id=self.mission_enrollment_2.id).update(
            status="COMPLETED", performance=mission_enrollment_2_performance, points=mission_enrollment_2_points
        )

        with self.assertRaises(ValidationError) as context:
            self.trail_enrollment_finish_service.process(self.trail_enrollment)

            self.assertEqual(context, _(USER_DONT_COMPLETED_ALL_LEARNING_TRAIL_MISSIONS))

        live_mission.development_status = "CLOSED"
        live_mission.save()

        self.trail_enrollment_finish_service.process(self.trail_enrollment)

        performance_expected = (mission_enrollment_1_performance + mission_enrollment_2_performance) / 2
        points_expected = mission_enrollment_1_points + mission_enrollment_2_points + self.pulse.points

        lt_enrollment_updated = LearningTrailEnrollment.objects.get(id=self.trail_enrollment.id)

        self.assertEqual(lt_enrollment_updated.status, "COMPLETED")
        self.assertEqual(lt_enrollment_updated.performance, performance_expected)
        self.assertEqual(lt_enrollment_updated.points, points_expected)

    def test_error_no_finish_all_trail_missions(
        self, update_enrollment_progress: mock.MagicMock, generate_certificate: mock.MagicMock
    ):
        MissionEnrollment.objects.filter(id=self.mission_enrollment_2.id).update(status="COMPLETED")
        mommy.make(
            MissionEnrollment, mission=self.mission_2, user=self.user, status="COMPLETED", workspace=self.workspace
        )

        with self.assertRaises(ValidationError) as context:
            self.trail_enrollment_finish_service.process(self.trail_enrollment)

            self.assertEqual(context, _(USER_DONT_COMPLETED_ALL_LEARNING_TRAIL_MISSIONS))

    def test_finish_enrollment_in_trail_without_missions(
        self, update_enrollment_progress: mock.MagicMock, generate_certificate: mock.MagicMock
    ):
        self.learning_trail.learningtrailstep_set.filter(mission_id__isnull=False).delete()
        generate_certificate.return_value = self.trail_enrollment

        enrollment_updated = self.trail_enrollment_finish_service.process(self.trail_enrollment)
        self.assertEqual(enrollment_updated.performance, 1)

    @mock.patch(CELERY_SEND_TASK_PATH)
    def test_delete_enrollment(self, *args):
        self.trail_service.delete_enrollment(self.trail_enrollment)

        enrollment_exists = LearningTrailEnrollment.objects.filter(id=self.trail_enrollment.id).exists()
        self.assertFalse(enrollment_exists)

    def test_restart(self, *args):
        enrollment_completed = mommy.make(
            LearningTrailEnrollment,
            id=uuid.uuid4(),
            user=self.user,
            learning_trail=self.learning_trail_to_restart,
            workspace=self.workspace,
            performance=0.5,
            status="COMPLETED",
            goal_date=datetime.datetime.now(),
        )

        goal_date = "2021-01-21"
        enrollment_restarted = self.trail_service.restart(enrollment=enrollment_completed, goal_date=goal_date)

        self.assertEqual(goal_date, enrollment_restarted.goal_date)
        self.assertEqual(enrollment_completed.start_date, enrollment_restarted.start_date)
        self.assertEqual(None, enrollment_restarted.performance)
        self.assertEqual(None, enrollment_restarted.points)
        self.assertEqual(None, enrollment_restarted.end_date)
        self.assertEqual(None, enrollment_restarted.certificate_url)
        self.assertEqual(None, enrollment_restarted.give_up_comment)
        self.assertEqual(False, enrollment_restarted.give_up)
        self.assertEqual("STARTED", enrollment_restarted.status)
