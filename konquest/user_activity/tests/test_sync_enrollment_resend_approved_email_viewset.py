import mock
from django.test import TestCase
from django.urls import reverse
from django.utils.translation import gettext as _
from model_mommy import mommy
from rest_framework.test import APIClient
from user_activity.models import MissionEnrollment
from user_activity.models.mission_enrollment import ENROLLED, STARTED, WAITING_APPROVAL
from user_activity.services.mission_enrollment_service import (
    INVALID_ENROLLMENT_STATUS_TO_SEND_ENROLLMENT_APPROVED_EMAIL,
)

SEND_SYNC_ENROLLMENT_APPROVED_EMAIL = "user_activity.tasks.notifications.send_sync_enrollment_approved_email.delay"


@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class SyncEnrollmentResendApprovedEmailTestCase(TestCase):
    fixtures = ["workspace", "user", "mission_type", "mission"]

    def setUp(self) -> None:
        self.client = APIClient()
        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.user_id = "489fb596-478f-40b5-b717-4931f20e4cc6"
        self.mission_id = "c8baa509-7637-47a4-a97b-4e717f56df74"

        self.headers = {"HTTP_X_CLIENT": str(self.workspace_id)}
        self.client.force_authenticate(user={"sub": str(self.user_id), "client_id": str(self.workspace_id)})
        self.url_name = "mission-enrollment-resend-approved-email"

    @mock.patch(SEND_SYNC_ENROLLMENT_APPROVED_EMAIL)
    def test_resend_email_to_enrolment_with_enrolled_status(
        self, send_sync_enrollment_approved_email: mock.MagicMock, check_role
    ):
        enrollment = mommy.make(
            MissionEnrollment,
            mission_id=self.mission_id,
            status=ENROLLED,
            user_id=self.user_id,
            workspace_id=self.workspace_id,
        )
        url = reverse(self.url_name, args=[str(enrollment.id)])

        response = self.client.post(url, **self.headers, format="json")
        self.assertEqual(response.status_code, 200)
        send_sync_enrollment_approved_email.assert_called_with(enrollment.id)

    @mock.patch(SEND_SYNC_ENROLLMENT_APPROVED_EMAIL)
    def test_resend_email_to_enrolment_with_started_status(
        self, send_sync_enrollment_approved_email: mock.MagicMock, check_role
    ):
        enrollment = mommy.make(
            MissionEnrollment,
            mission_id=self.mission_id,
            status=STARTED,
            user_id=self.user_id,
            workspace_id=self.workspace_id,
        )
        url = reverse(self.url_name, args=[str(enrollment.id)])

        response = self.client.post(url, **self.headers, format="json")
        self.assertEqual(response.status_code, 200)
        send_sync_enrollment_approved_email.assert_called_with(enrollment.id)

    def test_invalid_enrollment_status_error(self, check_role):
        enrollment = mommy.make(
            MissionEnrollment,
            mission_id=self.mission_id,
            status=WAITING_APPROVAL,
            user_id=self.user_id,
            workspace_id=self.workspace_id,
        )
        url = reverse(self.url_name, args=[str(enrollment.id)])

        response = self.client.post(url, **self.headers, format="json")
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data.get("detail"), [_(INVALID_ENROLLMENT_STATUS_TO_SEND_ENROLLMENT_APPROVED_EMAIL)])
