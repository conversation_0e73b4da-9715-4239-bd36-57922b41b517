import uuid
from unittest import mock

from account.models import User, Workspace
from django.test import TestCase
from django.urls import reverse
from mission.models import Mission, MissionCategory, MissionStage, MissionType, MissionWorkspace
from model_mommy import mommy
from rest_framework.test import APIClient
from user_activity.models import MissionEnrollment
from user_activity.models.user_mission_stage import UserMissionStage


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class UserMissionStageViewsetTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = mommy.make(User, id=uuid.uuid4())
        self.user_2 = mommy.make(User, id=uuid.uuid4())

        self.mission_type = mommy.make(MissionType, id="a58aa724-ed55-4567-9850-206b3c2f393a")
        self.mission_category = mommy.make(MissionCategory, id=uuid.uuid4())
        self.mission = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Test",
            mission_type=self.mission_type,
            mission_category=self.mission_category,
        )
        self.mission_2 = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="BLABLABLA",
            mission_type=self.mission_type,
            mission_category=self.mission_category,
        )

        self.workspace = mommy.make(Workspace, id=uuid.uuid4())

        self.mission_workspace = mommy.make(MissionWorkspace, workspace=self.workspace, mission=self.mission)
        self.mission_workspace = mommy.make(MissionWorkspace, workspace=self.workspace, mission=self.mission_2)

        self.mission_stage = mommy.make(MissionStage, mission=self.mission, name="Stage")
        self.mission_stage_2 = mommy.make(MissionStage, mission=self.mission_2)

        self.mission_enrollment_1 = mommy.make(
            MissionEnrollment, mission=self.mission, user=self.user, workspace=self.workspace
        )
        self.mission_enrollment_2 = mommy.make(
            MissionEnrollment, mission=self.mission_2, user=self.user, workspace=self.workspace
        )
        self.mission_enrollment_3 = mommy.make(
            MissionEnrollment, mission=self.mission_2, user=self.user_2, workspace=self.workspace
        )

        self.user_mission_stage = mommy.make(
            UserMissionStage,
            stage=self.mission_stage,
            user=self.user,
            mission_enrollment=self.mission_enrollment_1,
            status="COMPLETED",
        )
        self.user_mission_stage_2 = mommy.make(
            UserMissionStage,
            stage=self.mission_stage_2,
            user=self.user,
            mission_enrollment=self.mission_enrollment_2,
            status="STARTED",
        )
        self.user_mission_stage_3 = mommy.make(
            UserMissionStage,
            stage=self.mission_stage_2,
            user=self.user_2,
            mission_enrollment=self.mission_enrollment_3,
            status="STARTED",
        )

        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.url = reverse("user-mission-stages-list")

    def test_user_mission_stage_list(self, mock_return_roles, mock_return):
        mock_return.return_value = {"sub": str(self.user.id)}

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 2)

    def test_user_mission_stage_list_filter_by_mission(self, mock_return_roles, mock_return):
        mock_return.return_value = {"sub": str(self.user.id)}

        response = self.client.get(
            self.url + "?stage__mission=" + str(self.mission.id), **self.headers, format="json"
        ).json()
        self.assertEqual(len(response["results"]), 1)

    def test_user_mission_stage_list_filter_by_stage(self, mock_return_roles, mock_return):
        mock_return.return_value = {"sub": str(self.user.id)}

        response = self.client.get(
            self.url + "?stage=" + str(self.mission_stage.id), **self.headers, format="json"
        ).json()
        self.assertEqual(len(response["results"]), 1)

    def test_user_mission_stage_list_filter_by_status(self, mock_return_roles, mock_return):
        mock_return.return_value = {"sub": str(self.user.id)}

        response = self.client.get(self.url + "?status=COMPLETED", **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

    def test_user_mission_stage_list_search_by_name(self, mock_return_roles, mock_return):
        mock_return.return_value = {"sub": str(self.user.id)}

        response = self.client.get(self.url + "?search=Test", **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

    def test_mission_stage_get_success(self, mock_return_roles, mock_return):
        mock_return.return_value = {"sub": str(self.user.id)}

        response = self.client.get(
            reverse("user-mission-stage-detail", args=[str(self.user_mission_stage.id)]), **self.headers, format="json"
        )
        response_json = response.json()
        self.assertEqual(response_json["id"], str(self.user_mission_stage.id))
        self.assertEqual(response.status_code, 200)

    def test_user_mission_stage_get_not_found(self, mock_return_roles, mock_return):
        mock_return.return_value = {"sub": str(self.user.id)}

        response = self.client.get(
            reverse("user-mission-stage-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_user_mission_stage_post_success(self, mock_return_roles, mock_return):
        mock_return.return_value = {"sub": str(self.user.id)}

        data = {"stage": str(self.mission_stage.id), "user": str(self.user.id)}

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data.get("mission_enrollment"), self.mission_enrollment_1.id)

    def test_user_mission_stage_post_invalid_foreign_key(self, mock_return_roles, mock_return):
        mock_return.return_value = {"sub": str(self.user.id)}

        data = {"stage": str(uuid.uuid4()), "user": str(self.user.id)}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertIsNotNone(response_json.get("stage"))
        self.assertEqual(response.status_code, 400)

    def test_user_mission_stage_patch_success(self, mock_return_roles, mock_return):
        mock_return.return_value = {"sub": str(self.user.id)}

        data = {
            "stage": str(self.mission_stage_2.id),
        }

        response = self.client.patch(
            reverse("user-mission-stage-detail", args=[str(self.user_mission_stage.id)]),
            **self.headers,
            data=data,
            format="json",
        )
        response_json = response.json()

        self.assertEqual(response_json["stage"], data["stage"])
        self.assertEqual(response.status_code, 200)

    def test_user_mission_stage_put_success(self, mock_return_roles, mock_return):
        mock_return.return_value = {"sub": str(self.user.id)}

        data = {"stage": str(self.mission_stage_2.id), "user": str(self.user.id)}

        response = self.client.put(
            reverse("user-mission-stage-detail", args=[str(self.user_mission_stage.id)]),
            **self.headers,
            data=data,
            format="json",
        )

        response_json = response.json()
        self.assertEqual(response_json["stage"], data["stage"])
        self.assertEqual(response.status_code, 200)

    def test_user_mission_stage_delete_success(self, mock_return_roles, mock_return):
        mock_return.return_value = {"sub": str(self.user.id)}

        response = self.client.delete(
            reverse("user-mission-stage-detail", args=[str(self.user_mission_stage.id)]), **self.headers, format="json"
        )

        self.assertEqual(response.status_code, 204)

    def test_user_mission_stage_delete_not_found(self, mock_return_roles, mock_return):
        mock_return.return_value = {"sub": str(self.user.id)}

        response = self.client.delete(
            reverse("user-mission-stage-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)
