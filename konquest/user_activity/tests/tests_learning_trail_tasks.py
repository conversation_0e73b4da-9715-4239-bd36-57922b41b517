import uuid
from datetime import datetime
from unittest import mock

from account.models import User, Workspace
from constants import CELERY_SEND_TASK_PATH, <PERSON>NR<PERSON>LED, ENROLLMENT_COMPLETED
from django.test import TestCase
from learn_content.models import Answer, Exam, Question
from learning_trail.models import LearningTrail, LearningTrailStep, LearningTrailType, LearningTrailWorkspace
from mission.models import Mission, MissionWorkspace
from mission.models.mission_development_status_enum import IN_REVIEW
from mock.mock import MagicMock
from model_mommy import mommy
from myaccount.infrastructure.repositories.myaccount_repository import MyAccountRepository
from pulse.models import Pulse
from user_activity.models import LearnContentActivity, LearningTrailEnrollment, MissionEnrollment
from user_activity.services.learning_trail_enrollment_finish_service import LearningTrailEnrollmentFinishService
from user_activity.tasks.learning_trail_enrollment_task import (
    delete_step_mission_enrollments,
    enroll_users_in_new_step_mission,
    enroll_users_in_the_learning_trail_missions,
    update_enrollments_progress,
)

NOTIFY_ENROLLED_IN_A_NEW_MISSION = "user_activity.tasks.notifications.notify_enrolled_in_a_new_mission.delay"


class LearningTrailEnrollmentTasksTestCase(TestCase):
    def setUp(self):
        self.workspace = mommy.make(Workspace, id=uuid.uuid4())
        self.user_1 = mommy.make(User, id=uuid.uuid4())
        self.user_2 = mommy.make(User, id=uuid.uuid4())

        self.mission_1 = mommy.make(
            Mission, name="Mission Name", development_status="DONE", points=200, duration_time=1000, is_active=True
        )
        mommy.make(MissionWorkspace, mission=self.mission_1, workspace=self.workspace)
        self.mission_2 = mommy.make(
            Mission, name="Mission Name 2", development_status="DONE", points=200, duration_time=3000, is_active=True
        )
        mommy.make(MissionWorkspace, mission=self.mission_2, workspace=self.workspace)
        self.pulse = mommy.make(Pulse, duration_time=300, points=100)

        self.learning_trail_type = mommy.make(LearningTrailType)

        self.learning_trail = mommy.make(
            LearningTrail, id=uuid.uuid4(), duration_time=4300, learning_trail_type=self.learning_trail_type
        )
        self.learning_trail_workspace = mommy.make(
            LearningTrailWorkspace, learning_trail=self.learning_trail, workspace=self.workspace
        )

        self.lt_step_1 = mommy.make(
            LearningTrailStep, learning_trail=self.learning_trail, mission=self.mission_1, order=1
        )
        self.lt_step_2 = mommy.make(
            LearningTrailStep, learning_trail=self.learning_trail, mission=self.mission_2, order=1
        )
        self.lt_step_pulse = mommy.make(
            LearningTrailStep, learning_trail=self.learning_trail, pulse=self.pulse, order=3
        )

        self.lt_enrollment_1 = mommy.make(
            LearningTrailEnrollment,
            learning_trail=self.learning_trail,
            user=self.user_1,
            goal_date=datetime.now(),
            workspace=self.workspace,
        )
        self.lt_enrollment_2 = mommy.make(
            LearningTrailEnrollment,
            learning_trail=self.learning_trail,
            user=self.user_2,
            goal_date=datetime.now(),
            workspace=self.workspace,
        )

        self.mission_enrollment_1_1 = mommy.make(
            MissionEnrollment, mission=self.mission_1, user=self.user_1, workspace=self.workspace
        )
        self.mission_enrollment_1_2 = mommy.make(
            MissionEnrollment, mission=self.mission_2, user=self.user_1, workspace=self.workspace
        )
        self.mission_enrollment_2_1 = mommy.make(
            MissionEnrollment, mission=self.mission_1, user=self.user_2, workspace=self.workspace
        )
        self.mission_enrollment_2_2 = mommy.make(
            MissionEnrollment, mission=self.mission_2, user=self.user_2, workspace=self.workspace
        )

    @mock.patch(NOTIFY_ENROLLED_IN_A_NEW_MISSION)
    @mock.patch.object(MyAccountRepository, 'user_has_permission', return_value=True)
    def test_enroll_users_in_trail_missions(self, mock_user_has_permission, notify_enrolled):
        user = mommy.make(User)
        trail_enrollment = mommy.make(
            LearningTrailEnrollment, learning_trail=self.learning_trail, user=user, workspace=self.workspace
        )
        enroll_users_in_the_learning_trail_missions(
            learning_trail_id=self.learning_trail.id,
            user_id=user.id,
            workspace_id=self.workspace.id,
            goal_date="2021-10-21",
        )
        count_enrollments_created = MissionEnrollment.objects.filter(
            user=user, learning_trail_enrollment_id=trail_enrollment.id
        ).count()

        self.assertEqual(count_enrollments_created, 2)

    @mock.patch(NOTIFY_ENROLLED_IN_A_NEW_MISSION)
    @mock.patch.object(MyAccountRepository, 'user_has_permission', return_value=True)
    def test_enroll_users_in_trail_missions_when_one_mission_was_already_consumed(
        self, mock_user_has_permission, notify_enrolled
    ):
        user = mommy.make(User)
        trail_enrollment = mommy.make(
            LearningTrailEnrollment, learning_trail=self.learning_trail, user=user, workspace=self.workspace
        )
        mommy.make(MissionEnrollment, mission=self.mission_1, user=user, status="COMPLETED", workspace=self.workspace)
        enroll_users_in_the_learning_trail_missions(
            learning_trail_id=self.learning_trail.id,
            user_id=user.id,
            workspace_id=self.workspace.id,
            goal_date="2021-10-21",
        )
        count_enrollments_created = MissionEnrollment.objects.filter(
            user=user, status="ENROLLED", learning_trail_enrollment_id=trail_enrollment.id
        ).count()

        self.assertEqual(count_enrollments_created, 1)

    @mock.patch(NOTIFY_ENROLLED_IN_A_NEW_MISSION)
    @mock.patch.object(MyAccountRepository, 'user_has_permission', return_value=True)
    def test_enroll_users_in_new_step_mission(
        self, mock_user_has_permission, notify_enrolled_in_new_mission: mock.MagicMock
    ):
        new_mission = self._create_new_mission()

        with self.captureOnCommitCallbacks(execute=True):
            enroll_users_in_new_step_mission(learning_trail_id=self.learning_trail.id, mission_id=new_mission.id)

        count_enrollments_created = MissionEnrollment.objects.filter(
            user_id__in=[self.user_1.id, self.user_2.id], mission_id=new_mission.id
        ).count()
        self.assertEqual(count_enrollments_created, 2)
        notify_enrolled_in_new_mission.assert_called()

    @mock.patch(NOTIFY_ENROLLED_IN_A_NEW_MISSION)
    @mock.patch.object(MyAccountRepository, 'user_has_permission', return_value=True)
    def test_enroll_user_already_enrolled_in_new_step_mission(
        self,
        notify_enrolled_in_new_mission: mock.MagicMock,
        user_has_permission: mock.MagicMock
    ):
        new_mission = self._create_new_mission()
        mommy.make(
            MissionEnrollment,
            status=ENROLLED,
            user=self.user_2,
            mission=new_mission,
            workspace=self.workspace,
            goal_date=datetime.now(),
        )

        with self.captureOnCommitCallbacks(execute=True):
            enroll_users_in_new_step_mission(learning_trail_id=self.learning_trail.id, mission_id=new_mission.id)

        count_enrollments_created = MissionEnrollment.objects.filter(
            user_id__in=[self.user_1.id, self.user_2.id], mission_id=new_mission.id
        ).count()

        self.assertEqual(count_enrollments_created, 2)
        notify_enrolled_in_new_mission.assert_called_once()

    def _create_new_mission(self):
        new_mission = mommy.make(Mission, id=uuid.uuid4())
        mommy.make(MissionWorkspace, mission=new_mission, workspace=self.workspace)
        LearningTrailStep(learning_trail=self.learning_trail, mission=new_mission, order=5).save()
        return new_mission

    @mock.patch(CELERY_SEND_TASK_PATH)
    def test_delete_step_mission_enrollments(self, celery_send_task_mock):
        delete_step_mission_enrollments(
            learning_trail_id=self.lt_step_2.learning_trail.id, mission_id=self.lt_step_2.mission.id
        )

        exist_mission_enrollments = MissionEnrollment.objects.filter(
            user_id__in=[self.user_1.id, self.user_2.id], mission=self.lt_step_2.mission
        ).exists()
        exist_other_mission_enrollments = MissionEnrollment.objects.filter(
            user_id__in=[self.user_1.id, self.user_2.id], mission=self.lt_step_1.mission
        ).exists()

        self.assertFalse(exist_mission_enrollments)
        self.assertTrue(exist_other_mission_enrollments)

    def test_update_enrollments_0_progress_percentage(self):
        update_enrollments_progress(self.lt_enrollment_1.id)

        lt_enrollment_updated = LearningTrailEnrollment.objects.get(id=self.lt_enrollment_1.id)
        self.assertEqual(0, lt_enrollment_updated.progress)

    def test_update_enrollment_that_consume_one_of_the_three_contents(self):
        MissionEnrollment.objects.filter(id=self.mission_enrollment_1_1.id).update(status="COMPLETED")

        update_enrollments_progress(self.lt_enrollment_1.id)

        lt_enrollment_updated = LearningTrailEnrollment.objects.get(id=self.lt_enrollment_1.id)
        self.assertEqual(0.3333, lt_enrollment_updated.progress)

    def test_ignore_missions_with_not_available_development_status(self):
        MissionEnrollment.objects.filter(id=self.mission_enrollment_1_1.id).update(status=ENROLLMENT_COMPLETED)
        self.mission_2.development_status = IN_REVIEW
        self.mission_2.save()

        update_enrollments_progress(self.lt_enrollment_1.id)

        lt_enrollment_updated = LearningTrailEnrollment.objects.get(id=self.lt_enrollment_1.id)
        self.assertEqual(0.5, lt_enrollment_updated.progress)

    @mock.patch.object(LearningTrailEnrollmentFinishService, "process")
    def test_pulse_quiz_answer(self, process: MagicMock):
        MissionEnrollment.objects.filter(id=self.mission_enrollment_1_1.id).update(status="COMPLETED")
        MissionEnrollment.objects.filter(id=self.mission_enrollment_1_2.id).update(status="COMPLETED")
        LearnContentActivity(pulse=self.pulse, user=self.user_1, time_start=datetime.today()).save()
        LearnContentActivity(pulse=self.pulse, user=self.user_1, time_start=datetime.today()).save()
        LearnContentActivity(pulse=self.pulse, user=self.user_1, time_start=datetime.today()).save()

        pulse = mommy.make(Pulse, duration_time=300, points=100)
        exam = mommy.make(Exam, id=uuid.uuid4(), pulse=pulse)
        question = mommy.make(Question, id=uuid.uuid4(), exam=exam)
        pulse.learn_content_uuid = exam.id
        pulse.save()
        self.lt_step_pulse = mommy.make(LearningTrailStep, learning_trail=self.learning_trail, pulse=pulse, order=4)
        mommy.make(Answer, id=uuid.uuid4(), exam_has_question=question, user=self.user_1, options="1,2,3,4", is_ok=True)
        update_enrollments_progress(self.lt_enrollment_1.id)
        lt_enrollment_updated = LearningTrailEnrollment.objects.get(id=self.lt_enrollment_1.id)
        self.assertEqual(1, lt_enrollment_updated.progress)

    @mock.patch.object(LearningTrailEnrollmentFinishService, "process")
    def test_pulse_quiz_not_answer(self, process: MagicMock):
        MissionEnrollment.objects.filter(id=self.mission_enrollment_1_1.id).update(status="COMPLETED")
        MissionEnrollment.objects.filter(id=self.mission_enrollment_1_2.id).update(status="COMPLETED")
        LearnContentActivity(pulse=self.pulse, user=self.user_1, time_start=datetime.today()).save()
        LearnContentActivity(pulse=self.pulse, user=self.user_1, time_start=datetime.today()).save()
        LearnContentActivity(pulse=self.pulse, user=self.user_1, time_start=datetime.today()).save()

        pulse = mommy.make(Pulse, duration_time=300, points=100)
        exam = mommy.make(Exam, id=uuid.uuid4(), pulse=pulse)
        mommy.make(Question, id=uuid.uuid4(), exam=exam)
        pulse.learn_content_uuid = exam.id
        pulse.save()
        self.lt_step_pulse = mommy.make(LearningTrailStep, learning_trail=self.learning_trail, pulse=pulse, order=4)
        update_enrollments_progress(self.lt_enrollment_1.id)
        lt_enrollment_updated = LearningTrailEnrollment.objects.get(id=self.lt_enrollment_1.id)
        self.assertEqual(0.75, lt_enrollment_updated.progress)

    @mock.patch.object(LearningTrailEnrollmentFinishService, "process")
    def test_pulse_quiz_many_questions_answer(self, process: MagicMock):
        MissionEnrollment.objects.filter(id=self.mission_enrollment_1_1.id).update(status="COMPLETED")
        MissionEnrollment.objects.filter(id=self.mission_enrollment_1_2.id).update(status="COMPLETED")
        LearnContentActivity(pulse=self.pulse, user=self.user_1, time_start=datetime.today()).save()
        LearnContentActivity(pulse=self.pulse, user=self.user_1, time_start=datetime.today()).save()
        LearnContentActivity(pulse=self.pulse, user=self.user_1, time_start=datetime.today()).save()

        pulse = mommy.make(Pulse, duration_time=300, points=100)
        exam = mommy.make(Exam, id=uuid.uuid4(), pulse=pulse)
        question = mommy.make(Question, id=uuid.uuid4(), exam=exam)
        mommy.make(Question, id=uuid.uuid4(), exam=exam)
        pulse.learn_content_uuid = exam.id
        pulse.save()
        self.lt_step_pulse = mommy.make(LearningTrailStep, learning_trail=self.learning_trail, pulse=pulse, order=4)
        mommy.make(Answer, id=uuid.uuid4(), exam_has_question=question, user=self.user_1, options="1,2,3,4", is_ok=True)
        update_enrollments_progress(self.lt_enrollment_1.id)
        lt_enrollment_updated = LearningTrailEnrollment.objects.get(id=self.lt_enrollment_1.id)
        self.assertEqual(0.75, lt_enrollment_updated.progress)
