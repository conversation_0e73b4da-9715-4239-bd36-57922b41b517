from unittest.mock import MagicMock

from account.models import User, Workspace
from constants import <PERSON><PERSON><PERSON><PERSON>ENT_COMPLETED, ENROLLMENT_STARTED
from django.test import TestCase
from learning_trail.models import LearningTrail
from model_mommy import mommy
from user_activity.models import LearningTrailEnrollment
from user_activity.services.learning_trail_enrollment_finish_service import LearningTrailEnrollmentFinishService
from utils.utils import truncate_decimal_value


class LearningTrailEnrollmentFinishServiceTestCase(TestCase):
    def setUp(self) -> None:
        self.workspace = mommy.make(Workspace)
        self.user = mommy.make(User)
        self.trail = mommy.make(LearningTrail, points=120)
        self._service = LearningTrailEnrollmentFinishService(MagicMock(), MagicMock())

    def test_should_finish_manually(self):
        enrollment = mommy.make(
            LearningTrailEnrollment,
            status=ENROLLMENT_STARTED,
            user=self.user,
            learning_trail=self.trail,
            workspace=self.workspace,
        )
        performance = 1

        enrollment = self._service.finish_manually(enrollment, performance)

        self.assertEqual(enrollment.status, ENROLLMENT_COMPLETED)
        self.assertEqual(enrollment.performance, performance)
        self.assertEqual(enrollment.points, self.trail.points)
        self.assertIsNotNone(enrollment.end_date)

    def test_truncate_performance(self):
        self.assertEqual(truncate_decimal_value(0.999), 0.99)
