from unittest import mock

from django.test import TestCase, override_settings
from django.utils.timezone import now
from user_activity.models import LearningTrailEnrollment

ENROLL_USER_IN_TRAIL_MISSIONS = (
    "user_activity.tasks.learning_trail_enrollment_task.enroll_users_in_the_learning_trail_missions.delay"
)
UPDATE_ENROLLMENTS_PROGRESS = "user_activity.tasks.learning_trail_enrollment_task.update_enrollments_progress.delay"


class LearningTrailEnrollmentSignalsTestCase(TestCase):
    fixtures = ["workspace", "user", "learning_trail_type", "learning_trail", "learning_trail_workspace"]

    def setUp(self) -> None:
        self.user_consumer_1_id = "4b5880b1-ddaf-4b4f-b72b-1aa0cf49fcf1"
        self.trail_id = "610d49e0-e9a5-4a04-9c13-56d51d4a55e2"

    @mock.patch(ENROLL_USER_IN_TRAIL_MISSIONS)
    @mock.patch(UPDATE_ENROLLMENTS_PROGRESS)
    @override_settings(SUSPEND_SIGNALS=False)
    def test_enroll_user_in_trail_missions_when_enrolled_in_the_trail(
        self,
        update_enrollments_progress: mock.MagicMock,
        enroll_trail_missions: mock.MagicMock,
    ):
        with self.captureOnCommitCallbacks(execute=True):
            enrollment = LearningTrailEnrollment.objects.create(
                user_id=self.user_consumer_1_id, learning_trail_id=self.trail_id, goal_date=now()
            )

        enroll_trail_missions.assert_called_with(
            self.trail_id, self.user_consumer_1_id, enrollment.workspace_id, str(enrollment.goal_date)
        )
        update_enrollments_progress.assert_called_with(enrollment.id)
