from django.test import TestCase
from mission.models import Mission, MissionStage, MissionStageContent
from mission.models.mission_evaluation import MissionEvaluation
from model_mommy import mommy
from user_activity.models import MissionEnrollment, UserMissionContent
from user_activity.models.user_mission_content import COMPLETED
from user_activity.services.mission_enrollment_progress_service import MissionEnrollmentProgressService


class TestMissionEnrollmentProgress(TestCase):
    def setUp(self) -> None:
        self.max_consume_progress = 0.99
        self.evaluation_progress = 0.01
        self.mission = mommy.make(Mission, required_evaluation=False)
        self.contents_num = 5
        self.mission_stage = mommy.make(MissionStage, mission=self.mission)
        self.contents = []
        for number in range(self.contents_num):
            self.contents.append(mommy.make(MissionStageContent, stage=self.mission_stage))
        self.enrollment = mommy.make(MissionEnrollment, mission=self.mission)
        self.service = MissionEnrollmentProgressService(self.max_consume_progress, self.evaluation_progress)

    def test_progress_calc_when_some_contents_were_consumed_from_mission_without_required_evaluation(self):
        self._consume_contents(3)

        progress = self.service.calc(self.enrollment)

        self.assertEqual(progress, 0.6)

    def test_progress_calc_when_all_contents_were_consumed_from_mission_without_required_evaluation(self):
        self._consume_contents(5)

        progress = self.service.calc(self.enrollment)

        self.assertEqual(progress, 1)

    def test_progress_calc_when_not_contents_were_consumed_from_mission_without_required_evaluation(self):
        self._consume_contents(0)

        progress = self.service.calc(self.enrollment)

        self.assertEqual(progress, 0)

    def _consume_contents(self, contents_consumed_count: int):
        for content_num in range(contents_consumed_count):
            mommy.make(
                UserMissionContent,
                content=self.contents[content_num],
                mission_enrollment=self.enrollment,
                status=COMPLETED,
            )

    def test_progress_calc_when_all_contents_were_consumed_from_mission_with_required_evaluation(self):
        self.mission.required_evaluation = True
        self.mission.save()
        self._consume_contents(5)

        progress = self.service.calc(self.enrollment)

        self.assertEqual(progress, self.max_consume_progress)

    def test_progress_calc_when_all_contents_were_consumed_from_mission_with_required_evaluation_and_evaluated(self):
        self.mission.required_evaluation = True
        self.mission.save()
        mommy.make(MissionEvaluation, enrollment=self.enrollment)
        self._consume_contents(5)

        progress = self.service.calc(self.enrollment)

        self.assertEqual(progress, 1)
