import uuid
from collections import OrderedDict

import mock
from account.models import User, Workspace
from authentication.keeps_permissions import ADMIN, USER
from conftest import get_injector
from custom.keeps_exception_handler import KeepsPermissionError
from dateutil.utils import today
from django.test import TestCase
from django.utils.translation import gettext as _
from mission.models import LiveMissionDates, Mission, PresentialMissionDates
from model_mommy import mommy
from user_activity.dtos.person import Person
from user_activity.models import LiveAttendance, MissionEnrollment
from user_activity.services.mission_enrollment_attendance_service import (
    NOT_ALLOWED_TO_ATTEND,
    SELF_ATTENDANCE_IS_BLOCKED_TO_THIS_PERIOD,
    MissionEnrollmentAttendanceService,
)

GET_ALLOWED_ATTENDANCES = (
    "user_activity.selectors.sync_attendance_selector.SyncAttendanceSelector.get_allowed_attendances"
)


@mock.patch(GET_ALLOWED_ATTENDANCES)
class MissionEnrollmentAttendanceServiceTestCase(TestCase):
    fixtures = ["user", "mission_category", "mission_type", "live_mission"]

    def setUp(self) -> None:
        self.user = mommy.make(User, email="<EMAIL>")
        self.mission_id = "c8baa509-7637-47a4-a97b-4e717f56df77"
        self.mission = Mission.objects.get(id=self.mission_id)
        self.live_date = mommy.make(LiveMissionDates, live_id=self.mission.sync.id, start_at=today(), end_at=today())
        self.enrollment = mommy.make(MissionEnrollment, mission_id=self.mission_id, user=self.user)
        self.workspace = mommy.make(Workspace)

        self.injector = get_injector()
        self.service = self.injector.get(MissionEnrollmentAttendanceService)

    def test_user_check_the_attendance(self, get_allowed_attendances: mock.MagicMock):
        self.attendance = mommy.make(LiveAttendance, date=self.live_date, enrollment=self.enrollment)
        get_allowed_attendances.return_value = LiveAttendance.objects.filter()

        attendance = self.service.check(self.attendance, True, USER, self.user.id, str(uuid.uuid4()))
        self.assertTrue(attendance.presented)

    def test_error_when_user_try_to_check_an_attendance_without_permission(
        self, get_allowed_attendances: mock.MagicMock
    ):
        self.attendance = mommy.make(LiveAttendance, date=self.live_date, enrollment=self.enrollment)
        get_allowed_attendances.return_value = LiveAttendance.objects.exclude(id=self.attendance.id)

        with self.assertRaises(KeepsPermissionError) as context:
            self.service.check(self.attendance, True, USER, self.user.id, str(uuid.uuid4()))

        self.assertEqual(context.exception.message, _(NOT_ALLOWED_TO_ATTEND))

    def test_error_when_user_try_to_check_attendance_with_self_attendance_blocked(
        self, get_allowed_attendances: mock.MagicMock
    ):
        self.live_date.allow_self_attendance = False
        self.live_date.save()
        self.attendance = mommy.make(LiveAttendance, date=self.live_date, enrollment=self.enrollment)
        get_allowed_attendances.return_value = LiveAttendance.objects.filter()

        with self.assertRaises(KeepsPermissionError) as context:
            self.service.check(self.attendance, True, USER, self.user.id, str(uuid.uuid4()))

        self.assertEqual(context.exception.message, _(SELF_ATTENDANCE_IS_BLOCKED_TO_THIS_PERIOD))

    def test_admin_user_confirm_attendance(self, get_allowed_attendances: mock.MagicMock):
        self.live_date.allow_self_attendance = False
        self.live_date.save()
        self.attendance = mommy.make(LiveAttendance, date=self.live_date, enrollment=self.enrollment)
        get_allowed_attendances.return_value = LiveAttendance.objects.filter()

        attendance = self.service.check(self.attendance, True, ADMIN, self.user.id, str(uuid.uuid4()))
        self.assertTrue(attendance.presented)

    def test_get_valid_date(self, get_allowed_attendances: mock.MagicMock):
        valid_date = self.service.get_valid_date(self.live_date.id)
        self.assertEqual(valid_date, self.live_date)

        presential_date = mommy.make(PresentialMissionDates)
        valid_presential_date = self.service.get_valid_date(presential_date.id)
        self.assertEqual(valid_presential_date, presential_date)

    def test_filter_persons_not_enrolled(self, get_allowed_attendances: mock.MagicMock):
        person = Person(email=self.user.email, name=self.user.name)
        mission = mommy.make(Mission)
        not_enrolled_persons = self.service.filter_persons_not_enrolled([person], mission)
        self.assertIn(person, not_enrolled_persons)

        mommy.make(MissionEnrollment, mission=mission, user=self.user)
        not_enrolled_persons = self.service.filter_persons_not_enrolled([person], mission)
        self.assertNotIn(person, not_enrolled_persons)

    @mock.patch(
        "user_activity.services.mission_enrollment_attendance_service.MissionEnrollmentService.force_enroll_person"
    )
    def test_enroll_persons(self, mock_force_enroll_person, get_allowed_attendances: mock.MagicMock):
        mission = mommy.make(Mission)
        person = Person(email="<EMAIL>", name="Test 1")
        self.service.enroll_persons([person], mission, "workspace_id", "token")
        mock_force_enroll_person.assert_called_once_with(
            email=person.email, name=person.name, mission_id=mission.id, workspace_id="workspace_id", token="token"
        )

    def test_check_in_batch_force(self, get_allowed_attendances: mock.MagicMock):
        date = mommy.make(LiveMissionDates, live__mission__mission_model="LIVE")
        person = OrderedDict(email=self.user.email, name=self.user.name)
        mommy.make(MissionEnrollment, mission=date.mission, user=self.user)
        self.service.check_in_batch_force(
            persons=[person],
            date_id=date.id,
            role=ADMIN,
            user_id=self.user.id,
            workspace_id=self.workspace.id,
            token="token",
        )
        attendance = LiveAttendance.objects.filter(date=date, enrollment__user__email=self.user.email).first()
        self.assertIsNotNone(attendance)
        self.assertTrue(attendance.presented)
