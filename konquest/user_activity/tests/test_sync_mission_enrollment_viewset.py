import uuid
from unittest import mock

from account.models import User, Workspace
from authentication.keeps_permissions import USER
from constants import CELERY_SEND_TASK_PATH, CLIENT_ID, REQUIRED, ROLE, SUB
from django.test import TestCase
from django.urls import reverse
from mission.models import (
    LiveMission,
    Mission,
    MissionType,
    MissionWorkspace,
)
from mission.models.mission import LIVE
from model_mommy import mommy
from myaccount.infrastructure.repositories.myaccount_repository import MyAccountRepository
from rest_framework.test import APIClient
from user_activity.models import MissionEnrollment

NOTIFY_ENROLLED_IN_A_NEW_MISSION = "user_activity.tasks.notifications.notify_enrolled_in_a_new_mission.delay"


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class SyncSyncMissionEnrollmentViewsetTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.workspace = mommy.make(Workspace)
        self.user = mommy.make(User, id=uuid.uuid4())
        self.mission_type = mommy.make(MissionType)
        self.mission = mommy.make(Mission, id=uuid.uuid4(), mission_type=self.mission_type, mission_model=LIVE)
        mommy.make(LiveMission, mission=self.mission)
        self.mission_workspace = mommy.make(MissionWorkspace, mission=self.mission, workspace=self.workspace)

        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={SUB: str(self.user.id), CLIENT_ID: str(self.workspace.id), ROLE: USER})
        self.url = reverse("sync-mission-enrollments-create")

    @mock.patch(CELERY_SEND_TASK_PATH)
    @mock.patch.object(MyAccountRepository, 'user_has_permission', return_value=True)
    def test_sync_enroll_success(self, *args):
        args[0].return_value = [str(self.workspace.id)]
        data = {"user": str(self.user.id), "mission": str(self.mission.id)}

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        print(response.data)
        self.assertFalse(response.data[REQUIRED])

    @mock.patch.object(MyAccountRepository, 'user_has_permission', return_value=True)
    @mock.patch(NOTIFY_ENROLLED_IN_A_NEW_MISSION)
    @mock.patch(CELERY_SEND_TASK_PATH)
    def test_sync_mission_enrollment_batch_success(
        self,
        mock_return,
        mock_return_roles,
        mock_user_has_permission,
        notify_enrolled,
        celery_send_task_path
    ):
        required_mission = True

        data = {
            "users": [str(self.user.id)],
            "missions": [str(self.mission.id)],
            "required": required_mission,
        }

        url = reverse("sync-mission-enrollment-batch")
        response = self.client.post(url, **self.headers, data=data, format="json")
        response_json = response.json()
        enrollment = MissionEnrollment.objects.filter(user=self.user, mission=self.mission).first()

        self.assertEqual(0, len(response_json["enrollment_errors"]))
        self.assertEqual(200, response.status_code)
        self.assertEqual(required_mission, enrollment.required)
