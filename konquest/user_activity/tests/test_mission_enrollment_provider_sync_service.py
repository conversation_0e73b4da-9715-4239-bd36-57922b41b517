import uuid
from unittest.mock import MagicMock

from account.models import User, Workspace
from config import settings
from conftest import get_injector
from django.test import TestCase
from django.utils import timezone
from mission.models import Mission, MissionWorkspace
from model_mommy import mommy
from rest_framework.test import APIClient
from user_activity.dtos.mission_enrollment_provider_dto import MissionEnrollmentProviderDto
from user_activity.models import MissionEnrollment
from user_activity.models.mission_enrollment import COMPLETED
from user_activity.services.mission_enrollment_provider_sync_service import (
    MissionEnrollmentProviderSyncService,
)


class MissionEnrollmentProviderSyncTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.workspace = mommy.make(Workspace, id=settings.KEEPS_COMPANY_ID, min_performance_certificate=0.5)
        self.user = mommy.make(User, id=uuid.uuid4(), email="<EMAIL>")
        self.mission = mommy.make(Mission)

        self.mission_workspace = mommy.make(MissionWorkspace, mission=self.mission, workspace=self.workspace)

        injector = get_injector()
        self.service = injector.get(MissionEnrollmentProviderSyncService)

    def test_sync_enrollments(self):
        self.service.mission_enrollment_service.enroll_user = MagicMock()
        enrollments = [
            MissionEnrollmentProviderDto(
                email=self.user.email,
                mission_id=self.mission.id,
                workspace_id=self.workspace.id,
                progress=50,
                certificate_url="http://certificate.com",
            )
        ]

        self.service.create_enrollment(enrollments[0], self.user.id)
        self.service.mission_enrollment_service.enroll_user.assert_called_once()
        self.service.mission_enrollment_service.enroll_user.assert_called_with(
            {
                "user_id": self.user.id,
                "mission_id": self.mission.id,
                "workspace_id": self.workspace.id,
            },
            triggered_by_internal_service=True,
        )

        self.service.create_enrollment = MagicMock()
        self.service.notify_evaluate = MagicMock()
        enrollment = MissionEnrollment.objects.create(
            user=self.user, mission=self.mission, workspace=self.workspace, goal_date=timezone.now()
        )
        self.service.create_enrollment.return_value = enrollment

        self.service.sync_enrollment(enrollments)
        enrollment.refresh_from_db()
        assert enrollment.status == COMPLETED
        assert enrollment.performance == 0.5
        assert enrollment.certificate_provider_url == "http://certificate.com"
        self.service.notify_evaluate.assert_called_once()
