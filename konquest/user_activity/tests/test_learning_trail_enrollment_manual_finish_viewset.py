from account.models import User, Workspace
from authentication.keeps_permissions import ADMIN
from constants import <PERSON><PERSON><PERSON><PERSON>ENT_STARTED
from django.test import TestCase
from learning_trail.models import LearningTrail
from mock import mock
from model_mommy import mommy
from rest_framework.reverse import reverse
from rest_framework.test import APIClient
from user_activity.models import LearningTrailEnrollment


@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class TestLearningTrailEnrollmentManualFinishViewSet(TestCase):
    def setUp(self) -> None:
        self.workspace = mommy.make(Workspace)
        self.user = mommy.make(User)
        self.trail = mommy.make(LearningTrail)
        self.client = APIClient()

        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}

        self.client.force_authenticate(
            user={"sub": str(self.user.id), "role": ADMIN, "client_id": str(self.workspace.id)}
        )
        self.url_name = "learning-trail-enrollment-manual-finish"

    def test_admin_should_finish_enrollment(self, check_role):
        enrollment = mommy.make(
            LearningTrailEnrollment,
            user=self.user,
            learning_trail=self.trail,
            status=ENROLLMENT_STARTED,
            workspace=self.workspace,
        )
        url = reverse(self.url_name, args=[str(enrollment.id)])

        payload = {"performance": 1}

        response = self.client.post(url, data=payload, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)
