from datetime import timed<PERSON><PERSON>
from unittest.mock import MagicMock

import pytest
from account.models import User, Workspace
from constants import ENROLLMENT_COMPLETED, ENROLLMENT_GIVE_UP
from custom.exceptions.unable_to_give_up_enrollment_finished import UnableToGiveUpEnrollmentFinished
from custom.exceptions.unable_to_give_up_mandatory_enrollment import UnableToGiveUpMandatoryEnrollment
from custom.keeps_exception_handler import KeepsNoPermissionToEditLearningTrailEnrollment
from django.test import TestCase
from django.utils import timezone
from learning_trail.models import LearningTrail, LearningTrailStep
from mission.models import Mission
from model_mommy import mommy
from user_activity.models import LearningTrailEnrollment, MissionEnrollment
from user_activity.services import LearningTrailEnrollmentService


class TestLearningTrailEnrollmentService(TestCase):
    def setUp(self) -> None:
        self.mission_enrollment_service = MagicMock()
        self.mission_batch_enrollment_service = MagicMock()
        self.certificate_service = MagicMock()

        self.user = mommy.make(User)
        self.workspace = mommy.make(Workspace)
        self.enroll_service = MagicMock()
        self.service = LearningTrailEnrollmentService(
            MagicMock(), <PERSON>Mock(), <PERSON>Mock(), MagicMock(), self.enroll_service, MagicMock()
        )

    def test_should_give_up(self):
        enrollment = mommy.make(LearningTrailEnrollment, user=self.user, workspace=self.workspace)
        self.user.workspace_id = self.workspace.id
        comment = "DESISTOOOOOOOOOO"

        enrollment = self.service.to_give_up(enrollment.id, comment, self.user)

        self.assertEqual(enrollment.status, ENROLLMENT_GIVE_UP)
        self.assertTrue(enrollment.give_up)
        self.assertEqual(enrollment.give_up_comment, comment)

    def test_should_raise_no_permission_to_edit_learning_trail_enrollment(self):
        enrollment = mommy.make(LearningTrailEnrollment, workspace=self.workspace)
        self.user.workspace_id = self.workspace.id
        comment = "DESISTOOOOOOOOOO"

        with pytest.raises(KeepsNoPermissionToEditLearningTrailEnrollment):
            self.service.to_give_up(enrollment.id, comment, self.user)

    def test_should_raise_unable_to_give_up_enrollment_finished(self):
        enrollment = mommy.make(
            LearningTrailEnrollment, user=self.user, workspace=self.workspace, status=ENROLLMENT_COMPLETED
        )
        self.user.workspace_id = self.workspace.id
        comment = "DESISTOOOOOOOOOO"

        with pytest.raises(UnableToGiveUpEnrollmentFinished):
            self.service.to_give_up(enrollment.id, comment, self.user)

    def test_should_raise_unable_to_give_up_mandatory_enrollment(self):
        enrollment = mommy.make(LearningTrailEnrollment, user=self.user, workspace=self.workspace, required=True)
        self.user.workspace_id = self.workspace.id
        comment = "DESISTOOOOOOOOOO"

        with pytest.raises(UnableToGiveUpMandatoryEnrollment):
            self.service.to_give_up(enrollment.id, comment, self.user)

    def test_extend_deadline(self):
        mission = mommy.make(Mission)
        trail = mommy.make(LearningTrail, user_creator=mommy.make(User))
        user = mommy.make(User)
        mommy.make(LearningTrailStep, mission=mission, learning_trail=trail)
        trail_enrollment = mommy.make(LearningTrailEnrollment, learning_trail=trail, user=user)
        trail_enrollment_2 = mommy.make(LearningTrailEnrollment, learning_trail=trail)
        mission_enrollment = mommy.make(MissionEnrollment, user=user, mission=mission)
        mission_enrollment2 = mommy.make(MissionEnrollment, mission=mission)
        now_time = timezone.now().date() + timedelta(days=20)
        self.assertFalse(mission_enrollment.goal_date == now_time)
        self.assertFalse(trail_enrollment.goal_date == now_time)
        self.service.extend_deadline(trail_enrollment, now_time)
        mission_enrollment.refresh_from_db()
        trail_enrollment_2.refresh_from_db()
        trail_enrollment.refresh_from_db()
        mission_enrollment2.refresh_from_db()
        self.assertEqual(mission_enrollment.goal_date, now_time)
        self.assertEqual(trail_enrollment.goal_date, now_time)
        self.assertFalse(mission_enrollment2.goal_date == now_time)
        self.assertFalse(trail_enrollment_2.goal_date == now_time)
