import uuid
from datetime import datetime
from unittest import mock

from account.models import User, Workspace
from authentication.keeps_permissions import ADMIN, SUPER_ADMIN, USER
from config import settings
from constants import (
    CELERY_SEND_TASK_PATH,
    CLIENT_ID,
    ENROLLMENT_EXPIRED,
    REGULATORY_COMPLIANCE_CYCLE,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ROL<PERSON>,
    SUB,
)
from dateutil.utils import today
from django.test import TestCase
from django.urls import reverse
from mission.models import ExternalMission, Mission, MissionCategory, MissionType, MissionTypeEnum, MissionWorkspace
from mission.models.mission_provider import MissionProvider
from model_mommy import mommy
from myaccount.infrastructure.repositories.myaccount_repository import MyAccountRepository
from observer.event_manager import EventManager
from rest_framework.test import APIClient
from user_activity.models import MissionEnrollment
from user_activity.models.mission_enrollment import COMPLETED, ENROLLED, GIVE_UP, STARTED
from user_activity.services.regulatory_compliance_service import RegulatoryComplianceService

REQUEST_EXTENSION = "user_activity.services.mission_enrollment_service.MissionEnrollmentService.request_extension"
EXTEND_DEADLINE = "user_activity.services.mission_enrollment_service.MissionEnrollmentService.extend_deadline"
RESTART_ENROLLMENT = "user_activity.services.mission_enrollment_service.MissionEnrollmentService.restart"
CHANGE_PERFORMANCE = "user_activity.services.mission_enrollment_service.MissionEnrollmentService.change_performance"
GENERATE_CERTIFICATE_USE_CASE = ("user_activity.application.use_cases.generate_certificate."
                                 "generate_certificate_use_case.GenerateCertificateUseCase.execute")
NOTIFY_ENROLLED_IN_A_NEW_MISSION = "user_activity.tasks.notifications.notify_enrolled_in_a_new_mission.delay"
NOTIFY_INTERNAL_MISSION_ENROLLMENT_APPROVED_BY_ADMIN = (
    "user_activity.tasks.notifications.notify_internal_mission_enrollment_approved_by_admin.delay"
)
GET_TRACK = "user_activity.services.mission_enrollment_tracking_service.MissionEnrollmentTrackingResumeService.load"


@mock.patch("utils.utils.Utils.temp_file")
@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
@mock.patch.object(RegulatoryComplianceService, "create_enrollment_cycle")
class MissionSubscriptionViewsetTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.workspace = mommy.make(Workspace, id=settings.KEEPS_COMPANY_ID, min_performance_certificate=0.5)
        self.user = mommy.make(User, id=uuid.uuid4())
        self.user_creator = mommy.make(User, id=uuid.uuid4())

        self.mission_type = mommy.make(MissionType)
        self.mission_type_close_for_workspace = mommy.make(
            MissionType, id=uuid.UUID(MissionTypeEnum.CLOSE_FOR_COMPANY.value)
        )
        self.mission_category = mommy.make(MissionCategory)

        self.mission = mommy.make(
            Mission, id=uuid.uuid4(), mission_type=self.mission_type, mission_category=self.mission_category
        )
        self.mission_new = mommy.make(
            Mission,
            id=uuid.uuid4(),
            mission_type=self.mission_type,
            mission_category=self.mission_category,
            user_creator=self.user_creator,
        )
        self.mission_new_shared = mommy.make(
            Mission,
            id=uuid.uuid4(),
            mission_type=self.mission_type,
            mission_category=self.mission_category,
            user_creator=self.user_creator,
        )

        self.mission_workspace = mommy.make(MissionWorkspace, mission=self.mission, workspace=self.workspace)
        self.mission_shared_workspace = mommy.make(
            MissionWorkspace, mission=self.mission_new_shared, workspace=self.workspace, relationship_type="SHARED"
        )
        self.mission_workspace = mommy.make(MissionWorkspace, mission=self.mission_new, workspace=self.workspace)

        self.enrollment = mommy.make(
            MissionEnrollment, mission=self.mission, user=self.user, workspace=self.workspace, status="COMPLETED"
        )
        self.enrollment_in_shared_mission = mommy.make(
            MissionEnrollment, mission=self.mission_new_shared, user=self.user, workspace=self.workspace
        )

        self.alura_provider = mommy.make(MissionProvider, name="alura")
        self.external_mission = mommy.make(Mission)
        mommy.make(ExternalMission, mission=self.external_mission, provider=self.alura_provider)
        mommy.make(MissionWorkspace, mission=self.external_mission, workspace=self.workspace)

        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={SUB: str(self.user.id), CLIENT_ID: str(self.workspace.id), ROLE: USER})
        self.url = reverse("mission-enrollments-list")

        self.closed_mission = mommy.make(
            Mission,
            mission_type=self.mission_type_close_for_workspace,
            development_status="DONE",
            is_active=True,
        )
        mommy.make(MissionWorkspace, mission=self.closed_mission, workspace=self.workspace)

    def test_mission_enrollments_list(self, *args):
        args[0].return_value = [str(self.workspace.id)]
        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 2)

    def test_should_ignore_deleted_enollment_in_enrollments_count(self, *args):
        mommy.make(
            MissionEnrollment,
            mission=self.enrollment.mission,
            user=self.enrollment.user,
            workspace=self.enrollment.workspace,
            deleted=True,
        )

        response = self.client.get(self.url, **self.headers, format="json").json()
        results = response["results"]
        enrollment_ids = [result["id"] for result in results]
        duplicated_serializer = list(filter(lambda result: result["id"] == str(self.enrollment.id), results))[0]

        self.assertEqual(len(enrollment_ids), 2)
        self.assertIn(str(self.enrollment.id), enrollment_ids)
        self.assertEqual(duplicated_serializer["enrolled_count"], 1)

    def test_mission_enrollment_success(self, *args):
        args[0].return_value = [str(self.workspace.id)]
        response = self.client.get(
            reverse("mission-enrollment-details", args=[str(self.enrollment.id)]), **self.headers, format="json"
        )
        self.assertEqual(response.status_code, 200)

    def test_mission_enrollment_not_found(self, *args):
        args[0].return_value = [str(self.workspace.id)]
        response = self.client.get(
            reverse("mission-enrollment-details", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    @mock.patch.object(MyAccountRepository, "user_has_permission", return_value=True)
    def test_self_enroll_success(self, *args):
        args[0].return_value = [str(self.workspace.id)]
        data = {"user": str(self.user.id), "mission": str(self.mission_new.id), "goal_date": str(datetime.now())}

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        self.assertFalse(response.data[REQUIRED])

    @mock.patch.object(MyAccountRepository, "user_has_permission", return_value=True)
    def test_self_enroll_success_without_goal_date(self, *args):
        args[0].return_value = [str(self.workspace.id)]
        data = {"user": str(self.user.id), "mission": str(self.mission_new.id)}

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        self.assertFalse(response.data[REQUIRED])

    @mock.patch.object(MyAccountRepository, "user_has_permission", return_value=True)
    def test_self_enroll_success_with_blank_goal_date(self, *args):
        args[0].return_value = [str(self.workspace.id)]
        data = {"user": str(self.user.id), "mission": str(self.mission_new.id), "goal_date": ""}

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        self.assertFalse(response.data[REQUIRED])

    @mock.patch.object(MyAccountRepository, "user_has_permission", return_value=True)
    def test_enroll_with_regulatory_compliance_cycle(self, *args):
        args[0].return_value = [str(self.workspace.id)]
        data = {
            "user": str(self.user.id),
            "mission": str(self.mission_new.id),
            "goal_date": str(datetime.now()),
            REGULATORY_COMPLIANCE_CYCLE: uuid.uuid4(),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        self.assertFalse(response.data[REQUIRED])

    @mock.patch.object(MyAccountRepository, "user_has_permission", return_value=True)
    @mock.patch(NOTIFY_ENROLLED_IN_A_NEW_MISSION)
    def test_enroll_by_admin(self, *args):
        self.client.force_authenticate(user={SUB: str(self.user.id), CLIENT_ID: str(self.workspace.id), ROLE: ADMIN})

        consumer = mommy.make(User)
        data = {"user": str(consumer.id), "mission": str(self.mission_new.id), "goal_date": str(datetime.now())}

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        self.assertTrue(response.data[REQUIRED])

    @mock.patch.object(MyAccountRepository, "user_has_permission", return_value=True)
    def test_create_mission_enrollment_already_exist(self, *args):
        args[0].return_value = [str(self.workspace.id)]
        self.enrollment.status = "STARTED"
        self.enrollment.save()
        data = {"user": str(self.user.id), "mission": str(self.mission.id), "goal_date": str(datetime.now())}

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data.get("i18n"), "user_mission_enrollment_in_progress_already_exists")

    def test_mission_enrollment_required_field(self, *args):
        args[0].return_value = [str(self.workspace.id)]
        data = {"user": str(self.user.id)}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response_json["mission"], ["This field is required."])
        self.assertEqual(response.status_code, 400)

    def test_mission_enrollment_invalid_foreign_key(self, *args):
        args[0].return_value = [str(self.workspace.id)]
        data = {"user": str(uuid.uuid4()), "mission": str(self.mission.id)}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertIsNotNone(response_json.get("user"))
        self.assertEqual(response.status_code, 400)

    @mock.patch.object(MyAccountRepository, "user_has_permission", return_value=True)
    def test_mission_put_not_found(self, *args):
        args[0].return_value = [str(self.workspace.id)]
        new_mission = mommy.make(Mission)

        data = {"user": str(self.user.id), "mission": str(new_mission.id)}

        response = self.client.patch(
            reverse("mission-enrollment-details", args=[str(uuid.uuid4())]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Mission enrollment not found")
        self.assertEqual(response.status_code, 404)

    @mock.patch(CELERY_SEND_TASK_PATH)
    def test_mission_enrollment_delete_success(self, *args):
        args[0].return_value = [str(self.workspace.id)]
        response = self.client.delete(
            reverse("mission-enrollment-details", args=[str(self.enrollment.id)]), **self.headers, format="json"
        )
        self.assertEqual(response.status_code, 204)

    def test_mission_delete_not_found(
        self, mock_create_enrollment_cycle, mock_return, mock_return_roles, mock_temp_file
    ):
        response = self.client.delete(
            reverse("mission-enrollment-details", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )

        self.assertEqual(response.status_code, 404)

    def test_mission_enrollment_batch_error_not_found_mission(
        self, mock_create_enrollment_cycle, mock_return, mock_return_roles, mock_temp_file
    ):
        new_mission = mommy.make(Mission)

        data = {"users": [str(self.user.id)], "missions": [str(new_mission.id)], "goal_date": str(today())}

        url = reverse("mission-enrollment-batch")
        response = self.client.post(url, **self.headers, data=data, format="json")
        response_json = response.json()
        self.assertEqual(len(response_json["enrollment_errors"]), 1)
        self.assertEqual(response_json["enrollment_errors"][0]["error"]["i18n"], "not_found_missions_or_users")
        self.assertEqual(response.status_code, 200)

    @mock.patch.object(MyAccountRepository, "user_has_permission", return_value=True)
    def test_mission_enrollment_batch_error_user_creator(
        self,
        mock_create_enrollment_cycle,
        mock_return,
        mock_return_roles,
        mock_user_has_permission,
        mock_temp_file
    ):
        self.client.force_authenticate(user={SUB: str(self.user.id), CLIENT_ID: str(self.workspace.id), ROLE: ADMIN})

        data = {
            "users": [str(self.mission_new.user_creator.id)],
            "missions": [str(self.mission_new.id)],
            "goal_date": str(today()),
            "required": True,
        }

        url = reverse("mission-enrollment-batch")
        response = self.client.post(url, **self.headers, data=data, format="json")
        response_json = response.json()
        self.assertEqual(len(response_json["enrollment_errors"]), 1)
        self.assertEqual(
            response_json["enrollment_errors"][0]["error"]["i18n"], "mission_contributor_or_creator_cannot_be_enrolled"
        )
        self.assertEqual(response.status_code, 200)

    @mock.patch.object(MyAccountRepository, "user_has_permission", return_value=True)
    @mock.patch(NOTIFY_ENROLLED_IN_A_NEW_MISSION)
    def test_mission_enrollment_batch_success(
        self,
        mock_create_enrollment_cycle,
        mock_return,
        mock_return_roles,
        mock_temp_file,
        mock_user_has_permission,
        notify_enrolled,
    ):
        new_mission = mommy.make(Mission)
        mommy.make(MissionWorkspace, mission=new_mission, workspace=self.workspace)
        goal_date = str(today())
        required_mission = True

        data = {
            "users": [str(self.user.id)],
            "missions": [str(new_mission.id)],
            "goal_date": goal_date,
            "required": required_mission,
        }

        url = reverse("mission-enrollment-batch")
        response = self.client.post(url, **self.headers, data=data, format="json")
        response_json = response.json()
        enrollment = MissionEnrollment.objects.filter(user=self.user, mission=new_mission).first()

        self.assertEqual(0, len(response_json["enrollment_errors"]))
        self.assertEqual(200, response.status_code)
        self.assertEqual(required_mission, enrollment.required)
        self.assertIsNotNone(enrollment.goal_date)

    @mock.patch.object(MyAccountRepository, "user_has_permission", return_value=True)
    @mock.patch(NOTIFY_ENROLLED_IN_A_NEW_MISSION)
    def test_mission_enrollment_with_invalid_user_id(
        self,
        mock_create_enrollment_cycle,
        mock_return,
        mock_return_roles,
        mock_temp_file,
        mock_user_has_permission,
        notify_enrolled,
    ):
        new_mission = mommy.make(Mission)
        mommy.make(MissionWorkspace, mission=new_mission, workspace=self.workspace)
        self.client.force_authenticate(user={SUB: str(self.user.id), CLIENT_ID: str(self.workspace.id), ROLE: ADMIN})
        goal_date = str(today())
        required_mission = True

        data = {
            "users": [(uuid.uuid4()), str(self.user.id)],
            "missions": [str(new_mission.id)],
            "goal_date": goal_date,
            "required": required_mission,
        }

        url = reverse("mission-enrollment-batch")
        response = self.client.post(url, **self.headers, data=data, format="json")
        response_json = response.json()
        enrollment = MissionEnrollment.objects.filter(user=self.user, mission=new_mission).first()
        errors = response_json["enrollment_errors"]

        self.assertEqual(1, len(errors))
        self.assertEqual("user_not_found", errors[0]["error"]["i18n"])
        self.assertEqual(200, response.status_code)
        self.assertEqual(required_mission, enrollment.required)
        self.assertIsNotNone(enrollment.goal_date)

    @mock.patch("user_activity.services.mission_enrollment_service.MissionEnrollmentService.external_review")
    def test_mission_enrollment_external_review_success(
        self,
        mock_create_enrollment_cycle,
        mock_external_review,
        mock_return,
        mock_return_roles,
        mock_temp_file,
    ):
        new_enrollment = mommy.make(MissionEnrollment, mission=self.external_mission, user=self.user)
        mock_temp_file.return_value = "file.png"

        data = {"file": "file.png"}
        url = reverse("mission-enrollment-external-review", args=[str(new_enrollment.id)])

        response = self.client.post(url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 200)

    @mock.patch("user_activity.services.mission_enrollment_service.MissionEnrollmentService.external_review")
    def test_mission_enrollment_external_review_error_not_found(
        self,
        mock_create_enrollment_cycle,
        mock_external,
        mock_return,
        mock_return_roles,
        mock_temp_file,
    ):
        mock_temp_file.return_value = "file.png"
        data = {"file": "file.png"}

        url = reverse("mission-enrollment-external-review", args=[str(uuid.uuid4())])

        response = self.client.post(url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 404)

    def test_mission_enrollment_external_review_error_integrated_mission(
        self, mock_create_enrollment_cycle, mock_return, mock_return_roles, mock_temp_file
    ):
        mock_temp_file.return_value = "file.png"
        data = {"file": "file.png"}
        user_creator = mommy.make(User, id=settings.USER_OWNER_ALURA_INTEGRATION_ID, name=settings.GROUP_NAME_ALURA)
        self.mission.user_creator = user_creator
        self.mission.save()
        self.enrollment.refresh_from_db()
        url = reverse("mission-enrollment-external-review", args=[str(self.enrollment.id)])

        response = self.client.post(url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json(), {"i18n": "validation_error", "detail": ["mission enrollment invalid status"]})

    def contest_mission_enrollment_external_validate_approved_success(
        self, mock_create_enrollment_cycle, mock_return, mock_return_roles, mock_temp_file
    ):
        new_enrollment = mommy.make(
            MissionEnrollment,
            mission=self.external_mission,
            user=self.user,
            status="PENDING_VALIDATION",
            workspace_id=self.workspace.id,
        )

        data = {"performance": "0.9", "approved": True}

        url = reverse("mission-enrollment-external-validate", args=[str(new_enrollment.id)])

        response = self.client.post(url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 200)

    def test_mission_enrollment_external_validate_refuse_success(
        self,
        mock_create_enrollment_cycle,
        mock_return,
        mock_return_roles,
        mock_temp_file,
    ):
        new_enrollment = mommy.make(
            MissionEnrollment, mission=self.external_mission, user=self.user, status="PENDING_VALIDATION"
        )

        data = {"performance": None, "approved": False, "reject_comment": "Certificate Invalid"}

        url = reverse("mission-enrollment-external-validate", args=[str(new_enrollment.id)])
        response = self.client.post(url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 200)

    @mock.patch(GENERATE_CERTIFICATE_USE_CASE)
    def test_mission_enrollment_certificate(self, *args):
        user = mommy.make(User)
        enrollment = mommy.make(
            MissionEnrollment,
            user=user,
            mission=self.mission,
            workspace=self.workspace,
            status="COMPLETED",
            performance=1,
        )
        args[0].return_value = {"url": "certificate_url.com"}
        self.client.force_authenticate(user={SUB: str(user.id), CLIENT_ID: str(self.workspace.id)})
        url = reverse("mission-enrollment-certificate-v2", args=[str(enrollment.id)])
        response = self.client.post(url, **self.headers, data={}, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.data.get("certificate_url"), "certificate_url.com")

    @mock.patch.object(EventManager, "notify")
    def test_mission_enrollment_restart(self, *args):
        user = mommy.make(User)
        enrollment = mommy.make(
            MissionEnrollment,
            user=user,
            mission=self.mission,
            workspace=self.workspace,
            status="REPROVED",
            goal_date=datetime.now(),
        )

        data = {
            "goal_date": str(datetime.now()),
        }

        args[0].return_value = enrollment
        url = reverse("mission-enrollment-restart", args=[str(enrollment.id)])

        response = self.client.post(url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["status"], STARTED)

    def test_mission_enrollment_extend_deadline(self, *args):
        user = mommy.make(User)
        enrollment = mommy.make(
            MissionEnrollment,
            user=user,
            mission=self.mission,
            workspace=self.workspace,
            status=ENROLLMENT_EXPIRED,
            required=True,
        )

        data = {
            "new_goal_date": str(datetime.now()),
        }

        url = reverse("mission-enrollment-extend-deadline", args=[str(enrollment.id)])

        response = self.client.post(url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["status"], STARTED)

    def test_mission_enrollment_give_up(self, *args):
        enrollment = mommy.make(
            MissionEnrollment,
            user=self.user,
            mission=self.mission,
            workspace=self.workspace,
            goal_date=datetime.now().date(),
        )
        data = {
            "give_up_comment": "I give up, sorry!",
        }
        url = reverse("mission-enrollment-give-up", args=[str(enrollment.id)])
        response = self.client.patch(url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.data.get("give_up"))
        self.assertEqual(response.data.get("give_up_comment"), data.get("give_up_comment"))

    def test_mission_enrollment_give_up_mandatory_error(self, *args):
        enrollment = mommy.make(
            MissionEnrollment, user=self.user, mission=self.mission, workspace=self.workspace, required=True
        )
        data = {
            "give_up_comment": "I give up sorry!",
        }
        url = reverse("mission-enrollment-give-up", args=[str(enrollment.id)])
        response = self.client.patch(url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data.get("i18n"), "not_allowed_to_give_up_a_mandatory_enrollment")

    def test_mission_enrollment_user_status_count(self, *args):
        url = reverse("mission-enrollment-user-status-count", args=[str(self.user.id)])

        response = self.client.get(url, **self.headers, format="json")
        data = response.data

        self.assertEqual(response.status_code, 200)
        self.assertEqual(data["enrolled"], 1)
        self.assertEqual(data["started"], 0)
        self.assertEqual(data["completed"], 1)
        self.assertEqual(data["pending_validation"], 0)
        self.assertEqual(data["reproved"], 0)
        self.assertEqual(data["refused"], 0)
        self.assertEqual(data["expired"], 0)
        self.assertEqual(data["request_extension"], 0)

    def test_mission_restart_enrollment_without_goal_date(self, *args):
        user = mommy.make(User)
        enrollment = mommy.make(
            MissionEnrollment,
            user=user,
            mission=self.mission,
            workspace=self.workspace,
            status="COMPLETED",
            goal_date=datetime.now(),
        )

        data = {
            "performance": 0.9,
        }

        args[0].return_value = enrollment
        url = reverse("mission-enrollment-restart", args=[str(enrollment.id)])

        response = self.client.post(url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 400)

    def test_retake_enrollment(self, *args):
        user = mommy.make(User)
        self.client.force_authenticate(user={SUB: str(user.id), CLIENT_ID: str(self.workspace.id)})
        enrollment = mommy.make(
            MissionEnrollment,
            user=user,
            mission=self.mission,
            workspace=self.workspace,
            status=GIVE_UP,
            give_up=True,
        )

        data = {
            "goal_date": str(datetime.now()),
        }
        url = reverse("mission-enrollment-retake", args=[str(enrollment.id)])
        response = self.client.patch(url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["status"], ENROLLED)

    def test_retake_enrollment_without_goal_date(self, *args):
        user = mommy.make(User)
        self.client.force_authenticate(user={SUB: str(user.id), CLIENT_ID: str(self.workspace.id)})
        enrollment = mommy.make(
            MissionEnrollment,
            user=user,
            mission=self.mission,
            workspace=self.workspace,
            status=GIVE_UP,
            give_up=True,
        )

        data = {}
        url = reverse("mission-enrollment-retake", args=[str(enrollment.id)])
        response = self.client.patch(url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["status"], ENROLLED)

    def test_admin_can_retake_enrollment(self, *args):
        user = mommy.make(User)
        self.client.force_authenticate(user={SUB: str(user.id), CLIENT_ID: str(self.workspace.id), ROLE: ADMIN})
        self.enrollment.give_up = True
        self.enrollment.save()

        data = {
            "goal_date": str(datetime.now()),
        }
        url = reverse("mission-enrollment-retake", args=[str(self.enrollment.id)])
        response = self.client.patch(url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["status"], ENROLLED)

    @mock.patch(NOTIFY_INTERNAL_MISSION_ENROLLMENT_APPROVED_BY_ADMIN)
    def test_manual_finish_enrollment(self, *args):
        self.client.force_authenticate(user={SUB: str(self.user.id), CLIENT_ID: str(self.workspace.id), ROLE: ADMIN})
        user = mommy.make(User)
        enrollment = mommy.make(MissionEnrollment, user=user, mission=self.mission, workspace=self.workspace)

        data = {
            "performance": 1,
        }
        url = reverse("mission-enrollment-manual-finish", args=[str(enrollment.id)])
        response = self.client.post(url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["status"], COMPLETED)
        self.assertEqual(response.data["performance"], data["performance"])

    def test_user_can_track_enrollment(self, *args):
        enrollment = mommy.make(MissionEnrollment, user=self.user, mission=self.mission, workspace=self.workspace)

        url = reverse("mission-enrollment-change-tracking", args=[str(enrollment.id)])
        response = self.client.get(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)

    def test_admin_can_track_any_workspace_enrollment(self, *args):
        self.client.force_authenticate(user={SUB: str(self.user.id), CLIENT_ID: str(self.workspace.id), ROLE: ADMIN})
        consumer_user = mommy.make(User)
        enrollment = mommy.make(MissionEnrollment, user=consumer_user, mission=self.mission, workspace=self.workspace)

        url = reverse("mission-enrollment-change-tracking", args=[str(enrollment.id)])
        response = self.client.get(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)

    @mock.patch.object(MyAccountRepository, "user_has_permission", return_value=True)
    def test_self_enroll_success_admin(self, *args):
        args[0].return_value = [str(self.workspace.id)]
        self.client.force_authenticate(user={SUB: str(self.user.id), CLIENT_ID: str(self.workspace.id), ROLE: ADMIN})
        data = {"user": str(self.user.id), "mission": str(self.closed_mission.id), "goal_date": str(datetime.now())}

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)

    @mock.patch.object(MyAccountRepository, "user_has_permission", return_value=True)
    def test_self_enroll_success_super_admin(self, *args):
        args[0].return_value = [str(self.workspace.id)]
        self.client.force_authenticate(
            user={SUB: str(self.user.id), CLIENT_ID: str(self.workspace.id), ROLE: SUPER_ADMIN}
        )
        data = {"user": str(self.user.id), "mission": str(self.closed_mission.id), "goal_date": str(datetime.now())}

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
