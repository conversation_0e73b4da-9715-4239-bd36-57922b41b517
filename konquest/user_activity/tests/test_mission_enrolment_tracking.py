import uuid
from datetime import datetime, timedelta
from unittest import mock

from account.models import User, Workspace
from conftest import get_injector
from django.test import TestCase
from learn_content.models import Answer, Exam, Question
from mission.models import (
    Mission,
    MissionStage,
    MissionStageContent,
    MissionWorkspace,
)
from mission.models.mission_content_type_enum import HTML
from model_mommy import mommy
from user_activity.models import LearnContentActivity, MissionEnrollment, UserMissionContent, UserMissionStage
from user_activity.models.mission_enrollment import COMPLETED
from user_activity.services.mission_enrollment_tracking_service import MissionEnrollmentTrackingResumeService

MOCK_UPDATE_MS_ENROLMENT_PROGRESS = (
    "user_activity.tasks.mission_enrollment_task.update_mission_enrollment_progress.delay"
)
NOTIFY_INTERNAL_ENROLLMENT_APPROVED = (
    "user_activity.tasks.notifications.notify_internal_mission_enrollment_approved_by_admin.delay"
)


@mock.patch(MOCK_UPDATE_MS_ENROLMENT_PROGRESS, return_value={})
@mock.patch("rest_clients.kontent.KontentClient.get_docs")
class MissionEnrollmentTrackingServiceTestCase(TestCase):
    @mock.patch(MOCK_UPDATE_MS_ENROLMENT_PROGRESS, return_value={})
    def setUp(self, mock_update_enrollment):
        injector = get_injector()
        self.service = injector.get(MissionEnrollmentTrackingResumeService)

        self.workspace = mommy.make(Workspace, id=uuid.uuid4())
        self.user = mommy.make(User, id=uuid.uuid4())

        self.mission = mommy.make(Mission, id=uuid.uuid4())
        mommy.make(MissionWorkspace, mission=self.mission, workspace=self.workspace)

        self.enrollment = mommy.make(MissionEnrollment, mission=self.mission, user=self.user)

        self.mission_stage = mommy.make(MissionStage, mission=self.mission)
        self.mission_exam = mommy.make(Exam, stage=self.mission_stage)
        self.exam_question = mommy.make(Question, exam=self.mission_exam)

        self.mission_stage_content = mommy.make(
            MissionStageContent,
            learn_content_uuid=uuid.uuid4(),
            content_type="CONTENT",
            order=0,
            stage=self.mission_stage,
        )
        self.mission_stage_content_2 = mommy.make(
            MissionStageContent,
            learn_content_uuid=self.mission_exam.id,
            content_type="EXAM",
            order=1,
            stage=self.mission_stage,
        )
        self.mission_stage_content_4 = mommy.make(
            MissionStageContent,
            learn_content_uuid=uuid.uuid4(),
            content_type=HTML,
            order=4,
            stage=self.mission_stage,
        )
        self.mission_stage_content_3 = mommy.make(
            MissionStageContent, learn_content_uuid=uuid.uuid4(), content_type="CONTENT", stage=self.mission_stage
        )
        self.default_content_duration = 1000
        self.learn_contents = [
            {"id": str(self.mission_stage_content.learn_content_uuid), "duration": self.default_content_duration},
            {"id": str(self.mission_stage_content_3.learn_content_uuid), "duration": self.default_content_duration},
            {"id": str(self.mission_stage_content_4.learn_content_uuid), "duration": self.default_content_duration},
        ]

        self.user_answers = mommy.make(
            Answer, user=self.user, enrollment=self.enrollment, exam_has_question=self.exam_question, is_ok=True
        )

        self.user_activity_1 = mommy.make(
            LearnContentActivity,
            user=self.user,
            time_in=timedelta(minutes=10),
            mission_stage_content=self.mission_stage_content,
            mission_enrollment=self.enrollment,
        )
        self.user_activity_2 = mommy.make(
            LearnContentActivity,
            user=self.user,
            time_in=timedelta(minutes=10),
            mission_stage_content=self.mission_stage_content,
            mission_enrollment=self.enrollment,
        )

        self.user_stage = mommy.make(
            UserMissionStage, user=self.user, stage=self.mission_stage, mission_enrollment=self.enrollment
        )
        self.user_mission_content = mommy.make(
            UserMissionContent, user=self.user, content=self.mission_stage_content, mission_enrollment=self.enrollment
        )
        self.user_mission_content_2 = mommy.make(
            UserMissionContent, user=self.user, content=self.mission_stage_content_2, mission_enrollment=self.enrollment
        )

    def test_enrollment_tracking(self, get_docs, update_enrollment_progress):
        get_docs.return_value = self.learn_contents

        result = self.service.load(self.enrollment)

        consume_time = self.user_activity_1.time_in + self.user_activity_2.time_in

        # CONTENT 1 CONSUME
        self.assertEqual(self.default_content_duration, result[0].get("content_duration"))
        self.assertEqual(consume_time.seconds, result[0].get("consume_duration"))
        self.assertEqual(self.user_activity_1.created_date, result[0].get("first_access"))
        self.assertEqual(self.user_activity_2.created_date, result[0].get("last_access"))

        # QUIZ CONSUME
        self.assertEqual(self.user_answers.created_date, result[1].get("first_access"))
        self.assertEqual(self.user_answers.created_date, result[1].get("last_access"))
        self.assertEqual(1, result[1].get("total_questions"))
        self.assertEqual(1, result[1].get("total_correct_answers"))

        # CONTENT 2 CONSUME
        self.assertEqual(self.default_content_duration, result[2].get("content_duration"))
        self.assertEqual(0, result[2].get("consume_duration"))
        self.assertEqual(None, result[2].get("first_access"))
        self.assertEqual(None, result[2].get("last_access"))

        # CONTENT 4 CONSUME
        scorm_content_index = 3
        self.assertEqual(self.default_content_duration, result[scorm_content_index].get("content_duration"))
        self.assertEqual(0, result[scorm_content_index].get("consume_duration"))
        self.assertEqual(None, result[scorm_content_index].get("first_access"))
        self.assertEqual(None, result[scorm_content_index].get("last_access"))

    def test_get_exam_consume(self, get_docs, update_enrollment_progress):
        result = self.service.get_exam_consume(self.mission_stage_content_2, self.enrollment)
        # older answer
        mommy.make(Answer, user=self.user, exam_has_question=self.exam_question, is_ok=True)

        self.assertEqual(self.user_answers.created_date, result.get("first_access"))
        self.assertEqual(self.user_answers.created_date, result.get("last_access"))
        self.assertEqual(1, result.get("total_questions"))
        self.assertEqual(1, result.get("total_correct_answers"))

    def test_enrollment_without_consume_tracking(self, get_docs, update_enrollment_progress):
        get_docs.return_value = self.learn_contents

        user = mommy.make(User, email="<EMAIL>")
        enrollment_without_consume = mommy.make(MissionEnrollment, user=user, mission=self.mission)
        result = self.service.load(enrollment_without_consume)

        # CONTENT 1 CONSUME
        self.assertEqual(self.default_content_duration, result[0].get("content_duration"))
        self.assertEqual(0, result[0].get("consume_duration"))
        self.assertEqual(None, result[0].get("first_access"))
        self.assertEqual(None, result[0].get("last_access"))

        # QUIZ CONSUME
        self.assertEqual(None, result[1].get("first_access"))
        self.assertEqual(None, result[1].get("last_access"))
        self.assertEqual(1, result[1].get("total_questions"))
        self.assertEqual(0, result[1].get("total_correct_answers"))

        # CONTENT 2 CONSUME
        self.assertEqual(self.default_content_duration, result[2].get("content_duration"))
        self.assertEqual(0, result[2].get("consume_duration"))
        self.assertEqual(None, result[2].get("first_access"))
        self.assertEqual(None, result[2].get("last_access"))

    def test_mission_without_consume_tracking(self, get_docs, update_enrollment_progress):
        get_docs.return_value = []

        user = mommy.make(User, email="<EMAIL>")
        mission = mommy.make(Mission)
        enrollment = mommy.make(MissionEnrollment, user=user, mission=mission)
        result = self.service.load(enrollment)

        self.assertEqual([], result)

    def test_mission_consume_after_reproved_enrollment(self, get_docs, update_enrollment_progress):
        get_docs.return_value = self.learn_contents
        consume_time = self.user_activity_1.time_in + self.user_activity_2.time_in
        self.enrollment.status = COMPLETED
        self.enrollment.end_date = datetime.today()
        self.enrollment.save()
        mommy.make(
            LearnContentActivity,
            user=self.user,
            time_in=timedelta(minutes=12),
            mission_stage_content=self.mission_stage_content,
            mission_enrollment=self.enrollment,
        )

        result = self.service.load(self.enrollment)

        # CONTENT 1 CONSUME AFTER ENROLLMENT HAS BENN FINISHED
        self.assertEqual(self.default_content_duration, result[0].get("content_duration"))
        self.assertEqual(consume_time.seconds, result[0].get("consume_duration"))
        self.assertEqual(self.user_activity_1.created_date, result[0].get("first_access"))
        self.assertEqual(self.user_activity_2.created_date, result[0].get("last_access"))
