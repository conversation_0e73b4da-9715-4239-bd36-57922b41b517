from datetime import datetime, timed<PERSON>ta
from typing import Optional

from account.models import User, Workspace
from django.test import TestCase
from django.utils.timezone import now
from learning_trail.models import LearningTrail, LearningTrailStep
from mission.models import Mission
from mock import mock
from model_mommy import mommy
from pulse.models import Pulse
from user_activity.models import LearnContentActivity, LearningTrailEnrollment, MissionEnrollment
from user_activity.services.abstracts.mission_enrollment_selector_abstract import MissionEnrollmentSelectorAbstract
from user_activity.services.learning_trail_enrollment_tracking_service import LearningTrailEnrollmentTrackingService


class MissionEnrollmentSelectorStubAbstract(MissionEnrollmentSelectorAbstract):
    @staticmethod
    def get_status_count(user_id: str, workspace_id: str) -> dict:
        return {}

    @staticmethod
    def get_actual_user_enrollment(user_id: str, mission_id: str, workspace_id: str) -> Optional[MissionEnrollment]:
        return None


class TestLearningTrailTrackingService(TestCase):
    def setUp(self) -> None:
        self.workspace = mommy.make(Workspace)
        self.trail = mommy.make(LearningTrail)
        self.pulse = mommy.make(Pulse, duration_time=120)
        self.mission = mommy.make(Mission)
        self.first_step = mommy.make(LearningTrailStep, pulse=self.pulse, learning_trail=self.trail, order=0)
        self.second_step = mommy.make(LearningTrailStep, mission=self.mission, learning_trail=self.trail, order=1)

        self.service = LearningTrailEnrollmentTrackingService(MissionEnrollmentSelectorStubAbstract())

    @mock.patch.object(
        MissionEnrollmentSelectorStubAbstract, MissionEnrollmentSelectorStubAbstract.get_actual_user_enrollment.__name__
    )
    def test_should_return_trail_enrollment_tracking(self, get_actual_user_enrollment: mock.MagicMock):
        user = mommy.make(User)
        enrollment = mommy.make(LearningTrailEnrollment, user=user, learning_trail=self.trail, workspace=self.workspace)
        mission_enrollment = mommy.make(MissionEnrollment, user=user, mission=self.mission)
        get_actual_user_enrollment.return_value = mission_enrollment

        tracking = self.service.load(enrollment)

        first_tracking = tracking[0]
        self.assertEqual(first_tracking.name, self.first_step.pulse.name)
        self.assertEqual(first_tracking.id, str(self.first_step.pulse_id))
        self.assertEqual(first_tracking.mission_enrollment_id, None)
        self.assertEqual(first_tracking.step_type, "PULSE")

        second = tracking[1]
        self.assertEqual(second.name, self.second_step.mission.name)
        self.assertEqual(second.id, str(self.second_step.mission_id))
        self.assertEqual(second.mission_enrollment_id, mission_enrollment.id)
        self.assertEqual(second.step_type, "MISSION")

    def test_should_return_step_consume_tracking(self):
        expected_first_access = now() - timedelta(days=2)
        expected_last_access = now()
        consume_duration = 60
        user = mommy.make(User)
        enrollment = LearningTrailEnrollment(
            user=user,
            learning_trail=self.trail,
            workspace=self.workspace,
            created_date=expected_first_access,
            end_date=expected_last_access,
        )
        self._create_pulses_activites(user, self.pulse, expected_first_access, expected_last_access, consume_duration)

        tracking = self.service.load_step_pulse(enrollment, self.pulse)

        self.assertEqual(tracking.first_access, expected_first_access)
        self.assertEqual(tracking.last_access, expected_last_access)
        self.assertEqual(tracking.consume_duration, consume_duration)
        self.assertEqual(tracking.content_duration, self.pulse.duration_time)

    @staticmethod
    def _create_pulses_activites(
        user: User, pulse: Pulse, first_access: datetime, last_access: datetime, consume_duration: int
    ):
        mommy.make(
            LearnContentActivity,
            user=user,
            pulse=pulse,
            time_start=first_access,
            time_in=timedelta(seconds=consume_duration / 2),
        )
        mommy.make(
            LearnContentActivity,
            user=user,
            pulse=pulse,
            time_start=last_access,
            time_stop=last_access,
            time_in=timedelta(seconds=consume_duration / 2),
        )
