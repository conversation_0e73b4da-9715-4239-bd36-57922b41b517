import uuid
from unittest import mock

from account.models import User, Workspace
from authentication.keeps_permissions import USER
from conftest import get_injector
from custom.exceptions.unable_to_enroll_another_user import UnableToEnrollAnotherUser
from custom.exceptions.unable_to_enroll_user_without_permission import UnableToEnrollUserWithoutPermission
from django.test import TestCase
from mission.models import (
    Mission,
    MissionTypeEnum,
    MissionWorkspace,
)
from mission.models.mission_development_status_enum import DON<PERSON>
from model_mommy import mommy
from myaccount.application.services.myaccount_service import MyAccountService
from notification.models import NotificationType
from user_activity.models import MissionEnrollment
from user_activity.models.live_attendance import LiveAttendance
from user_activity.models.presential_attendance import PresentialAttendance
from user_activity.services import MissionEnrollmentService

MOCK_UPDATE_MS_ENROLMENT_PROGRESS = (
    "user_activity.tasks.mission_enrollment_task.update_mission_enrollment_progress.delay"
)
NOTIFY_INTERNAL_ENROLLMENT_APPROVED = (
    "user_activity.services.mission_enrollment_service.notify_internal_mission_enrollment_approved_by_admin.delay"
)
UPDATE_SYNC_REMAINING_SEATS = "user_activity.tasks.mission_enrollment_task.update_sync_remaining_seats.delay"
PROMOTE_WAITING_VACANCIES_ENROLLMENTS = (
    "user_activity.tasks.mission_enrollment_task.promote_waiting_vacancies_enrollments.delay"
)
NOTIFY_SYNC_ENROLLMENT_REFUSED = "user_activity.tasks.notifications.notify_sync_enrollment_refused.delay"
NOTIFY_SYNC_ENROLLMENT_APPROVED = "user_activity.tasks.notifications.notify_sync_enrollment_approved.delay"
CREATE_SYNC_ENROLLMENT_ATTENDANCES = (
    "user_activity.tasks.mission_enrollment_task.create_sync_enrollment_attendances.delay"
)
DELETE_SYNC_ENROLLMENT_ATTENDANCES = (
    "user_activity.tasks.mission_enrollment_task.delete_sync_enrollment_attendances.delay"
)
NOTIFY_ENROLLED_IN_A_NEW_MISSION = "user_activity.tasks.notifications.notify_enrolled_in_a_new_mission.delay"
SEND_TASK = "celery.Celery.send_task"


@mock.patch("utils.aws.aws_s3.S3Client.send_file_path")
@mock.patch("utils.email_service.notification.notify_users.delay", return_value={})
@mock.patch(MOCK_UPDATE_MS_ENROLMENT_PROGRESS, return_value={})
class MissionEnrollmentServiceTestCase(TestCase):
    fixtures = [
        "user",
        "workspace",
        "mission_workspace",
        "mission_type",
        "mission_category",
        "mission",
        "mission_enrollment",
    ]

    @mock.patch(MOCK_UPDATE_MS_ENROLMENT_PROGRESS, return_value={})
    def setUp(self, mock_update_enrollment_progress):
        injector = get_injector()
        self.enrollment_service = injector.get(MissionEnrollmentService)

        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.workspace = mommy.make(
            Workspace, id="e76b5082-f4fe-4f41-be79-1977840e16a7", min_performance_certificate=0.7
        )
        self.user = mommy.make(User, id=uuid.uuid4())
        self.user_2_id = "489fb596-478f-40b5-b717-4931f20e4cc6"

        self.mission_type_id = MissionTypeEnum.OPEN_FOR_COMPANY.value
        self.mission_type_close_id = MissionTypeEnum.CLOSE_FOR_COMPANY.value
        self.mission_category_id = "e5077e79-1ac8-4f84-a54e-c768eaf6a552"

        self.mission: Mission = mommy.make(
            Mission,
            id=uuid.uuid4(),
            mission_type_id=self.mission_type_id,
            mission_category_id=self.mission_category_id,
            development_status=DONE,
            points=10,
            enrollment_goal_duration_days=10,
        )
        self.mission_workspace = mommy.make(MissionWorkspace, mission=self.mission, workspace_id=self.workspace_id)
        self.mission_external_id = "a755df79-c6c4-488d-9a54-03dc23b556ec"
        self.mission_live_id = "c8baa509-7637-47a4-a97b-4e717f56df74"
        self.mission_presential_id = "f1e43871-454b-4726-bbc4-eeb6e959381d"

        self.enrollment = mommy.make(MissionEnrollment, mission=self.mission, user=self.user)

        self.enrollment_in_external_mission = mommy.make(
            MissionEnrollment,
            mission_id=self.mission_external_id,
            user=self.user,
            status="PENDING_VALIDATION",
            certificate_provider_url="file_name.png",
            workspace_id=self.workspace.id,
        )

        self.notification_admin = mommy.make(NotificationType, object_type="CERTIFICATE", action="LIST")
        self.notification_user = mommy.make(NotificationType, object_type="MISSION_ENROLLMENT", action="LIST")

    @mock.patch.object(MyAccountService, "has_access", return_value=True)
    @mock.patch.object(MyAccountService, "create_user")
    def test_force_enroll_person_creates_user_if_not_exists(self, mock_myaccount_client, *args):
        mock_myaccount_client.return_value = {"id": uuid.uuid4()}
        self.enrollment_service.force_enroll_person(
            email="<EMAIL>",
            name="new person",
            mission_id=self.mission.id,
            workspace_id=self.workspace_id,
            token="token",
        )
        user = User.objects.get(email="<EMAIL>")
        self.assertIsNotNone(user)
        user_enrollment = MissionEnrollment.objects.get(user=user, mission=self.mission)
        self.assertIsNotNone(user_enrollment)

    @mock.patch.object(MyAccountService, "has_access", return_value=True)
    def test_consumer_user_cannot_enroll_another_user(self, *args):
        action_user = self.user
        action_user.role = USER
        another_user = mommy.make(User)

        with self.assertRaises(UnableToEnrollAnotherUser):
            self.enrollment_service.enroll_user(
                {"user_id": another_user.id, "mission_id": self.mission.id, "workspace_id": self.workspace.id},
                action_user,
            )

    @mock.patch.object(MyAccountService, "has_access", return_value=False)
    def test_unable_to_enroll_user_without_permission(self, *args):
        with self.assertRaises(UnableToEnrollUserWithoutPermission):
            self.enrollment_service.enroll_user(
                {"user_id": self.user_2_id, "mission_id": self.mission.id, "workspace_id": self.workspace.id},
                self.user,
            )

    @mock.patch(SEND_TASK)
    def test_delete_enrollment_deletes_live_attendance_for_live_mission(self, *args):
        live_mission = mommy.make(
            Mission,
            id=uuid.uuid4(),
            mission_type_id=self.mission_type_id,
            mission_category_id=self.mission_category_id,
            development_status=DONE,
            mission_model="LIVE",
        )
        mommy.make(MissionWorkspace, mission=live_mission, workspace_id=self.workspace_id)

        enrollment = mommy.make(MissionEnrollment, mission=live_mission, user=self.user)

        live_attendance = mommy.make(LiveAttendance, enrollment=enrollment)

        self.enrollment_service.delete_enrollment(enrollment)

        enrollment.refresh_from_db()
        self.assertTrue(enrollment.deleted)

        with self.assertRaises(LiveAttendance.DoesNotExist):
            LiveAttendance.objects.get(id=live_attendance.id)

    @mock.patch(SEND_TASK)
    def test_delete_enrollment_deletes_presential_attendance_for_presential_mission(self, *args):
        presential_mission = mommy.make(
            Mission,
            id=uuid.uuid4(),
            mission_type_id=self.mission_type_id,
            mission_category_id=self.mission_category_id,
            development_status=DONE,
            mission_model="PRESENTIAL",
        )
        mommy.make(MissionWorkspace, mission=presential_mission, workspace_id=self.workspace_id)

        enrollment = mommy.make(MissionEnrollment, mission=presential_mission, user=self.user)

        presential_attendance = mommy.make(PresentialAttendance, enrollment=enrollment)

        self.enrollment_service.delete_enrollment(enrollment)

        enrollment.refresh_from_db()
        self.assertTrue(enrollment.deleted)

        with self.assertRaises(PresentialAttendance.DoesNotExist):
            PresentialAttendance.objects.get(id=presential_attendance.id)
