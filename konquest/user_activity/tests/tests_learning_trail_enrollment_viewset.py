import uuid
from datetime import datetime
from uuid import UUID

import mock
from account.models import User, Workspace
from authentication.keeps_permissions import ADMIN
from config.settings import LEARNING_TRAIL_OPEN_FOR_COMPANY
from constants import REGULATORY_COMPLIANCE_CYCLE
from dateutil.utils import today
from django.test import TestCase
from django.urls import reverse
from django.utils.timezone import now
from learning_trail.models import LearningTrail, LearningTrailType, LearningTrailWorkspace
from model_mommy import mommy
from rest_framework.status import HTTP_204_NO_CONTENT
from rest_framework.test import APIClient
from user_activity.dtos.regulatory_compliance_cycle_enrollment import EnrollmentCycleCreateDTO
from user_activity.listeners import TrailEnrollmentGaveUpListener
from user_activity.models import LearningTrailEnrollment
from user_activity.services.regulatory_compliance_service import RegulatoryComplianceService

ENROLL_USERS_IN_THE_LEARNING_TRAIL_MISSIONS = (
    "user_activity.tasks.learning_trail_enrollment_task.enroll_users_in_the_learning_trail_missions.delay"
)
CERTIFICATE_SERVICE = (
    "user_activity.application.use_cases.generate_certificate."
    "generate_trail_certificate_use_case.GenerateTrailCertificateUseCase.execute"
)
ENROLL_USERS = (
    "user_activity.tasks.learning_trail_enrollment_task.enroll_users.delay"
)


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class LearningTrailSubscriptionViewsetTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()

        self.workspace = mommy.make(Workspace, id=uuid.uuid4())
        self.user = mommy.make(User, id=uuid.uuid4())
        self.user_creator = mommy.make(User, id=uuid.uuid4())
        self.suoer_admin_user_creator = mommy.make(User, id=uuid.uuid4())

        self.learning_trail_type = mommy.make(LearningTrailType)

        self.learning_trail = mommy.make(LearningTrail, id=uuid.uuid4(), learning_trail_type=self.learning_trail_type)

        self.learning_trail_new = mommy.make(
            LearningTrail, id=uuid.uuid4(), learning_trail_type=self.learning_trail_type, user_creator=self.user_creator
        )

        self.learning_trail_workspace = mommy.make(
            LearningTrailWorkspace, learning_trail=self.learning_trail, workspace=self.workspace
        )
        self.learning_trail_new_workspace = mommy.make(
            LearningTrailWorkspace, learning_trail=self.learning_trail_new, workspace=self.workspace
        )

        self.enrollment = mommy.make(
            LearningTrailEnrollment,
            learning_trail=self.learning_trail,
            workspace=self.workspace,
            goal_date=now(),
            user=self.user,
        )

        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})
        self.url = reverse("learning-trail-enrollments-list")

    def test_learning_trail_enrollments_list(self, mock_return, mock_return_roles):
        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

    def test_learning_trail_enrollment_detail_success(self, mock_return, mock_return_roles):
        response = self.client.get(
            reverse("learning-trail-enrollment-details", args=[str(self.enrollment.id)]), **self.headers, format="json"
        )
        self.assertEqual(response.status_code, 200)

    def test_learning_trail_enrollment_detail_not_found(self, mock_return, mock_return_roles):
        response = self.client.get(
            reverse("learning-trail-enrollment-details", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_create_learning_trail_enrollment_success(self, mock_return, mock_return_roles):
        self.learning_trail_new.learning_trail_type_id = mommy.make(
            LearningTrailType, name=LEARNING_TRAIL_OPEN_FOR_COMPANY
        ).id
        self.learning_trail_new.save()
        data = {
            "user": str(self.user.id),
            "learning_trail": str(self.learning_trail_new.id),
            "goal_date": now().date(),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)

    @mock.patch.object(RegulatoryComplianceService, "create_enrollment_cycle")
    def test_create_learning_trail_enrollment_with_regulatory_cycle_success(
        self, mock_create_enrollment_cycle, mock_return, mock_return_roles
    ):
        self.learning_trail_new.learning_trail_type_id = mommy.make(
            LearningTrailType, name=LEARNING_TRAIL_OPEN_FOR_COMPANY
        ).id
        self.learning_trail_new.save()
        data = {
            "user": str(self.user.id),
            "learning_trail": str(self.learning_trail_new.id),
            "goal_date": now().date(),
            REGULATORY_COMPLIANCE_CYCLE: uuid.uuid4(),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.json()["normative"], True)
        mock_create_enrollment_cycle.assert_called_with(
            EnrollmentCycleCreateDTO(
                enrollment_id=UUID(response.json()["id"]), cycle_id=data[REGULATORY_COMPLIANCE_CYCLE]
            )
        )

    def test_create_learning_trail_enrollment_already_exist(
        self, mock_return, mock_return_roles
    ):
        date_now = datetime.now()
        data = {
            "user": str(self.user.id),
            "learning_trail": str(self.learning_trail.id),
            "goal_date": f"{date_now.year}-{date_now.month}-{date_now.day}T19:20:00.000Z",
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 400)

    def test_learning_trail_enrollment_required_field(self, mock_return, mock_return_roles):
        date_now = datetime.now()
        data = {
            "user": str(self.user.id),
            "goal_date": f"{date_now.year}-{date_now.month}-{date_now.day}T19:20:00.000Z",
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response_json["learning_trail"], ["This field is required."])
        self.assertEqual(response.status_code, 400)

    def test_learning_trail_patch_success(self, mock_return, mock_return_roles):
        new_learning_trail = mommy.make(LearningTrail)

        data = {"learning_trail": str(new_learning_trail.id)}

        response = self.client.patch(
            reverse("learning-trail-enrollment-details", args=[str(self.enrollment.id)]),
            **self.headers,
            data=data,
            format="json",
        )
        response_json = response.json()

        self.assertEqual(response_json["learning_trail"], str(new_learning_trail.id))
        self.assertEqual(response.status_code, 200)

    def test_learning_trail_enrollment_put_success(self, mock_return, mock_return_roles):
        new_learning_trail = mommy.make(LearningTrail)

        data = {"user": str(self.user.id), "learning_trail": str(new_learning_trail.id)}
        response = self.client.patch(
            reverse("learning-trail-enrollment-details", args=[str(self.enrollment.id)]),
            **self.headers,
            data=data,
            format="json",
        )
        response_json = response.json()

        self.assertEqual(response_json["learning_trail"], str(new_learning_trail.id))
        self.assertEqual(response.status_code, 200)

    def test_learning_trail_put_not_found(self, mock_return, mock_return_roles):
        new_learning_trail = mommy.make(LearningTrail)

        data = {"user": str(self.user.id), "learning_trail": str(new_learning_trail.id)}

        response = self.client.put(
            reverse("learning-trail-enrollment-details", args=[str(uuid.uuid4())]),
            **self.headers,
            data=data,
            format="json",
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_learning_trail_enrollment_delete_success(self, mock_return, mock_return_roles):
        response = self.client.delete(
            reverse("learning-trail-enrollment-details", args=[str(self.enrollment.id)]), **self.headers, format="json"
        )
        self.assertEqual(response.status_code, 204)

    def test_learning_trail_enrollment_delete_not_found(self, mock_return, mock_return_roles):
        response = self.client.delete(
            reverse("learning-trail-enrollment-details", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )

        self.assertEqual(response.status_code, 404)

    @mock.patch.object(TrailEnrollmentGaveUpListener, "update")
    def test_learning_trail_enrollment_give_up(
        self, mock_return, mock_return_roles, mock_listener_update
    ):
        give_up_comment = "Não aguento mais"
        data = {"give_up_comment": give_up_comment}

        response = self.client.post(
            reverse("learning-trail-enrollment-give-up", args=[str(self.enrollment.id)]),
            data=data,
            **self.headers,
            format="json",
        )

        self.assertEqual(response.status_code, HTTP_204_NO_CONTENT)

    def test_learning_trail_enrollment_retake(self, mock_return, mock_return_roles):

        self.enrollment.give_up = True
        self.enrollment.save()
        data = {"goal_date": str(datetime.now())}

        response = self.client.post(
            reverse("learning-trail-enrollment-retake", args=[str(self.enrollment.id)]),
            data=data,
            **self.headers,
            format="json",
        )
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertFalse(response_json.get("give_up"))
        self.assertEqual(response_json.get("status"), "STARTED")
        self.assertEqual(response_json.get("give_up_comment"), None)
        self.assertEqual(response_json.get("points"), None)
        self.assertEqual(response_json.get("performance"), None)
        self.assertEqual(response_json.get("progress"), 0)

    @mock.patch(ENROLL_USERS)
    def test_learning_trail_enrollment_batch_success(
        self, mock_enroll_users, mock_return, mock_return_roles
    ):
        admin = mommy.make(User)
        self.client.force_authenticate(user={"sub": admin.id, "role": ADMIN, "client_id": self.workspace.id})

        data = {
            "users": [str(self.user.id)],
            "learning_trails": [str(uuid.uuid4())],
            "goal_date": str(today()),
        }

        url = reverse("learning-trail-enrollment-batch")

        with self.captureOnCommitCallbacks(execute=True):
            response = self.client.post(url, **self.headers, data=data, format="json")

        mock_enroll_users.mock_assert_called_with(
            trails=data["learning_trails"],
            user_ids=data["users"],
            workspace_id=str(self.workspace.id),
            goal_date=data["goal_date"],
            regulatory_compliance_cycle_id=None,
            required=False
        )
        self.assertEqual(response.status_code, 202)

    def test_create_learning_trail_enrollment_goal_date_must_be_future_date(
        self, mock_return, mock_return_roles
    ):
        data = {
            "user": str(self.user.id),
            "learning_trail": str(self.learning_trail.id),
            "goal_date": str(today()),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 400)

    def test_learning_trail_enrollment_restart(self, *args):
        user = mommy.make(User)
        enrollment = mommy.make(
            LearningTrailEnrollment,
            user=user,
            learning_trail=self.learning_trail,
            workspace=self.workspace,
            status="COMPLETED",
            goal_date=datetime.now(),
        )

        data = {
            "goal_date": str(datetime.now()),
        }

        args[0].return_value = enrollment
        url = reverse("learning-trail-enrollment-restart", args=[str(enrollment.id)])
        response = self.client.post(url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["status"], "STARTED")

    @mock.patch(CERTIFICATE_SERVICE, return_value={})
    def test_learning_trail_enrollment_certificate(self, *args):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})

        enrollment = mommy.make(
            LearningTrailEnrollment,
            learning_trail=self.learning_trail,
            workspace=self.workspace,
            goal_date=now(),
            end_date=now(),
            user=self.user_creator,
            status="COMPLETED",
            performance=0.9,
        )

        response = self.client.post(
            reverse("learning-trail-enrollment-certificate", args=[str(enrollment.id)]),
            **self.headers,
            format="json"
        )
        self.assertEqual(response.status_code, 200)
