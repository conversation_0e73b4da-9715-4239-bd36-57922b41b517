from datetime import timedelta

from django.test import TestCase
from model_mommy import mommy
from pulse.models import Pulse
from user_activity.models.learn_content_activity import LearnContentActivity


class TotalPulseConsuptionTestCase(TestCase):
    def setUp(self):
        pulse = mommy.make(Pulse, duration_time=1080)
        self.learn_content_activity = mommy.make(LearnContentActivity, pulse=pulse, time_in=timedelta(minutes=3))
        mommy.make(LearnContentActivity, pulse=pulse, time_in=timedelta(minutes=5))
        mommy.make(LearnContentActivity, pulse=pulse, time_in=timedelta(minutes=1))

    def test_total_pulse_consumption_sum_all_pulses(self):
        self.assertEqual(self.learn_content_activity.total_pulse_consumption, 0.5)
