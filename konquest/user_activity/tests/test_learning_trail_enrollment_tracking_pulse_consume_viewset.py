from account.models import User, Workspace
from authentication.keeps_permissions import ADMIN
from django.test import TestCase
from django.utils.timezone import now
from learning_trail.models import LearningTrail
from mock import mock
from model_mommy import mommy
from pulse.models import Pulse
from rest_framework.reverse import reverse
from rest_framework.test import APIClient
from user_activity.dtos.pulse_consume_tracking import PulseConsumeTracking
from user_activity.models import LearningTrailEnrollment
from user_activity.services.learning_trail_enrollment_tracking_service import LearningTrailEnrollmentTrackingService


@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class TestLearningTrailEnrollmentTrackingPulseConsumeViewset(TestCase):
    def setUp(self) -> None:
        self.learning_trail = mommy.make(LearningTrail)
        self.workspace = mommy.make(Workspace)
        self.user = mommy.make(User)
        self.pulse = mommy.make(Pulse)

        self.client = APIClient()
        self.headers = {"HTTP_X_CLIENT": self.workspace.id}
        self.client.force_authenticate(user={"sub": self.user.id, "client_id": self.workspace.id, "role": ADMIN})
        self.url_name = "learning-trail-enrollment-tracking-pulse-consume"

    @mock.patch.object(
        LearningTrailEnrollmentTrackingService, LearningTrailEnrollmentTrackingService.load_step_pulse.__name__
    )
    def test_should_return_enrollment_pulse_consume_tracking(self, tracking_load: mock.MagicMock, check_role):
        trail_enrollment = mommy.make(
            LearningTrailEnrollment, workspace=self.workspace, learning_trail=self.learning_trail, user=self.user
        )
        tracking_load.return_value = PulseConsumeTracking(
            consume_duration=10, content_duration=10, first_access=now(), last_access=now()
        )

        response = self.client.get(
            reverse(self.url_name, args=[str(trail_enrollment.id), str(self.pulse.id)]), **self.headers, format="json"
        )

        tracking_load.assert_called_once()
        self.assertEqual(response.status_code, 200)
