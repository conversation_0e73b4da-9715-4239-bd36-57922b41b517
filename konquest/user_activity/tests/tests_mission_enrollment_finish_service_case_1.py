from conftest import get_injector
from django.test import TestCase
from learn_content.models import Answer, Exam, Question
from mission.models import MissionStage, MissionStageContent
from mock import mock
from model_mommy import mommy
from user_activity.models import LearnContentActivity, MissionEnrollment
from user_activity.services.mission_enrollment_finish_service import MissionEnrollmentFinishService

KONTENT_GET_DOCS = "rest_clients.kontent.KontentClient.get_docs"


class MissionEnrollmentFinishServiceCase1(TestCase):
    """Case High Performance in FULL, CONTENT and QUIZ"""

    fixtures = ["workspace", "user", "mission_type", "mission_category", "mission", "mission_enrollment"]

    def setUp(self) -> None:
        self._enrollment = MissionEnrollment.objects.get(id="55193de6-521a-4e2a-8320-095390b4571a")
        self.mission = self._enrollment.mission
        self._injector = get_injector()
        self._service = self._injector.get(MissionEnrollmentFinishService)
        self._load_content_data()
        self._load_exam_data()

    @mock.patch(KONTENT_GET_DOCS)
    def test_full_process(self, get_docs):
        self._enrollment.mission.assessment_type = "FULL"
        contents = MissionStageContent.objects.filter(stage__mission=self._enrollment.mission, content_type="CONTENT")
        get_docs_return = []
        for content in contents:
            get_docs_return.append({"id": content.learn_content_uuid, "duration": 60, "points": 10})
        get_docs.return_value = get_docs_return

        score = self._service.process(self._enrollment)

        self.assertEqual(score.performance, 1)
        self.assertEqual(score.exam_awarded_score, 40)
        self.assertEqual(score.exam_available_score, 40)
        self.assertEqual(score.content_available_score, 20)
        self.assertEqual(score.content_awarded_score, 20)
        self.assertEqual(score.contents_available_time, 120)
        self.assertEqual(score.contents_consume_time, 120)
        self.assertEqual(score.total_awarded_score, 60)
        self.assertEqual(score.total_available_score, 60)
        self.assertEqual(score.total_mission_questions, 4)
        self.assertEqual(score.total_correct_answers, 4)

    @mock.patch(KONTENT_GET_DOCS)
    def test_content_process(self, get_docs):
        self._enrollment.mission.assessment_type = "CONTENT"
        contents = MissionStageContent.objects.filter(stage__mission=self._enrollment.mission, content_type="CONTENT")
        get_docs_return = []
        for content in contents:
            get_docs_return.append({"id": content.learn_content_uuid, "duration": 60, "points": 10})
        get_docs.return_value = get_docs_return

        score = self._service.process(self._enrollment)

        self.assertEqual(score.performance, 1)
        self.assertEqual(score.content_available_score, 20)
        self.assertEqual(score.content_awarded_score, 20)
        self.assertEqual(score.contents_available_time, 120)
        self.assertEqual(score.contents_consume_time, 120)
        self.assertEqual(score.total_awarded_score, 20)
        self.assertEqual(score.total_available_score, 20)

    @mock.patch(KONTENT_GET_DOCS)
    def test_content_process_when_content_duration_is_none(self, get_docs):
        self._enrollment.mission.assessment_type = "CONTENT"
        contents = MissionStageContent.objects.filter(stage__mission=self._enrollment.mission, content_type="CONTENT")
        get_docs_return = []
        for content in contents:
            get_docs_return.append({"id": content.learn_content_uuid, "duration": None, "points": 10})
        get_docs.return_value = get_docs_return

        score = self._service.process(self._enrollment)

        self.assertEqual(score.performance, 1)
        self.assertEqual(score.content_available_score, 20)
        self.assertEqual(score.content_awarded_score, 20)
        self.assertEqual(score.contents_available_time, 2)
        self.assertEqual(score.contents_consume_time, 2)
        self.assertEqual(score.total_awarded_score, 20)
        self.assertEqual(score.total_available_score, 20)

    @mock.patch(KONTENT_GET_DOCS)
    def test_content_process_without_contents_duration(self, get_docs):
        self._enrollment.mission.assessment_type = "CONTENT"
        contents = MissionStageContent.objects.filter(stage__mission=self._enrollment.mission, content_type="CONTENT")
        get_docs_return = []
        for content in contents:
            get_docs_return.append({"id": content.learn_content_uuid})
        get_docs.return_value = get_docs_return

        score = self._service.process(self._enrollment)

        self.assertEqual(score.performance, 1)
        self.assertEqual(score.content_available_score, 2)
        self.assertEqual(score.content_awarded_score, 2)
        self.assertEqual(score.contents_available_time, 2)
        self.assertEqual(score.contents_consume_time, 2)
        self.assertEqual(score.total_awarded_score, 2)
        self.assertEqual(score.total_available_score, 2)

    def test_quiz_process(self):
        self._enrollment.mission.assessment_type = "QUIZ"
        score = self._service.process(self._enrollment)

        self.assertEqual(score.performance, 1)
        self.assertEqual(score.exam_awarded_score, 40)
        self.assertEqual(score.exam_available_score, 40)
        self.assertEqual(score.total_awarded_score, 40)
        self.assertEqual(score.total_available_score, 40)
        self.assertEqual(score.total_mission_questions, 4)
        self.assertEqual(score.total_correct_answers, 4)
        self._enrollment.refresh_from_db()
        self.assertEqual(self._enrollment.assessment_type, "QUIZ")

    def _load_content_data(self):
        # mission stage
        stage_1 = mommy.make(MissionStage, mission=self.mission)
        stage_2 = mommy.make(MissionStage, mission=self.mission)

        # mission stage content
        stage_content_1 = mommy.make(MissionStageContent, stage=stage_1, content_type="CONTENT")
        stage_content_2 = mommy.make(MissionStageContent, stage=stage_2, content_type="CONTENT")

        # activities
        mommy.make(
            LearnContentActivity,
            mission_stage_content=stage_content_1,
            mission_enrollment=self._enrollment,
            user=self._enrollment.user,
            time_in="00:00:30.000",
        )
        mommy.make(
            LearnContentActivity,
            mission_stage_content=stage_content_1,
            mission_enrollment=self._enrollment,
            user=self._enrollment.user,
            time_in="00:02:00.000",
        )
        mommy.make(
            LearnContentActivity,
            mission_stage_content=stage_content_2,
            mission_enrollment=self._enrollment,
            user=self._enrollment.user,
            time_in="00:01:00.000",
        )

    def _load_exam_data(self):
        # mission stage
        stage_1 = mommy.make(MissionStage, mission=self.mission)
        stage_2 = mommy.make(MissionStage, mission=self.mission)

        # mission stage exam
        exam_1 = mommy.make(Exam, stage=stage_1)
        exam_2 = mommy.make(Exam, stage=stage_2)

        # mission stage exam
        mommy.make(MissionStageContent, stage=stage_1, content_type="EXAM", learn_content_uuid=exam_1.id)
        mommy.make(MissionStageContent, stage=stage_2, content_type="EXAM", learn_content_uuid=exam_2.id)

        question_1 = mommy.make(Question, exam=exam_1, points=10)
        question_2 = mommy.make(Question, exam=exam_1, points=10)
        question_3 = mommy.make(Question, exam=exam_2, points=10)
        question_4 = mommy.make(Question, exam=exam_2, points=10)

        mommy.make(
            Answer,
            exam_has_question=question_1,
            user=self._enrollment.user,
            is_ok=True,
            enrollment=self._enrollment,
        )
        mommy.make(
            Answer,
            exam_has_question=question_2,
            user=self._enrollment.user,
            is_ok=True,
            enrollment=self._enrollment,
        )
        mommy.make(
            Answer,
            exam_has_question=question_3,
            user=self._enrollment.user,
            is_ok=True,
            enrollment=self._enrollment,
        )
        mommy.make(
            Answer,
            exam_has_question=question_4,
            user=self._enrollment.user,
            is_ok=True,
            enrollment=self._enrollment,
        )

    @mock.patch(KONTENT_GET_DOCS)
    def test_full_process_mission_min_performance_pass(self, get_docs):
        self._enrollment.mission.assessment_type = "FULL"
        self._enrollment.workspace.min_performance_certificate = 0.9
        self._enrollment.mission.minimum_performance = 0.5
        contents = MissionStageContent.objects.filter(stage__mission=self._enrollment.mission, content_type="CONTENT")
        get_docs_return = []
        for content in contents:
            get_docs_return.append({"id": content.learn_content_uuid, "duration": 120, "points": 20})
        get_docs.return_value = get_docs_return
        self._service.process(self._enrollment)
        self.assertEqual(self._enrollment.status, "COMPLETED")
