from datetime import datetime
from unittest import mock

from account.models import User
from django.test import TestCase
from mission.models import LiveMission, LiveMissionDates, PresentialMission, PresentialMissionDates
from mission.models.mission import LIVE, PRESENTIAL, Mission
from model_mommy import mommy
from myaccount.application.services.myaccount_service import MyAccountService
from notification.services.notification_service import Message
from user_activity.models import MissionEnrollment
from user_activity.tasks import notifications

CREATE_NOTIFICATION = "notification.services.notification_service_v2.NotificationServiceV2.create_notification"
EMAIL_NOTIFY_DELAY = "utils.email_service.notification.notify_users.delay"
EMAIL_NOTIFY = "utils.email_service.notification.notify_users"
SEND_SYNC_ENROLLMENT_APPROVED_EMAIL = "user_activity.tasks.notifications.send_sync_enrollment_approved_email.delay"


@mock.patch(CREATE_NOTIFICATION)
class NotificationTasksTestCase(TestCase):
    fixtures = ["workspace", "user", "mission_category", "mission_type", "mission", "mission_enrollment"]

    def setUp(self) -> None:
        self.user_admin_1_id = "4b5880b1-ddaf-4b4f-b72b-1aa0cf49fcf1"
        self.User_admin_2_id = "97e1e5ce-9b33-434d-afc0-ccf71162e84f"
        self.user_consumer_1_id = "4b5880b1-ddaf-4b4f-b72b-1aa0cf49fcf1"
        self.user_consumer_2_id = "244ef26a-eb00-4f80-9800-5e976e234452"
        self.mission_id = "0ac0b202-f41b-49c8-ad9a-4ed6e150fb6b"
        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.external_mission_id = "a755df79-c6c4-488d-9a54-03dc23b556ec"
        self.mission_enrollment_id = "55193de6-521a-4e2a-8320-095390b4571a"

    def test_enrolled_in_a_new_mission(self, create_notification: mock.MagicMock):
        enrollment = MissionEnrollment.objects.get(id=self.mission_enrollment_id)
        notifications.notify_enrolled_in_a_new_mission(enrollment.id)
        message = Message(title="you_have_been_enrolled_in_a_mission", description=enrollment.mission.name)

        create_notification.assert_called_with(
            user_ids=[enrollment.user.id],
            type_key="ENROLLED_IN_A_MISSION",
            action="ENROLLED",
            object=enrollment.mission.id,
            message=message,
            workspace_id=enrollment.workspace_id,
        )

    @mock.patch(EMAIL_NOTIFY_DELAY)
    def test_enrolled_in_a_new_live_mission(self, email_notify: mock.MagicMock, create_notification: mock.MagicMock):
        mission = mommy.make(Mission, mission_model=LIVE)
        live = mommy.make(LiveMission, mission=mission)
        mommy.make(LiveMissionDates, live=live, start_at=datetime.today(), end_at=datetime.today())
        enrollment = mommy.make(
            MissionEnrollment, user_id=self.user_consumer_1_id, mission=mission, workspace_id=self.workspace_id
        )
        notifications.notify_enrolled_in_a_new_mission(enrollment.id)

        message = Message(title="you_have_been_enrolled_in_a_mission", description=enrollment.mission.name)
        create_notification.assert_called_with(
            user_ids=[enrollment.user.id],
            type_key="ENROLLED_IN_A_MISSION",
            action="ENROLLED",
            object=enrollment.mission.id,
            message=message,
            workspace_id=enrollment.workspace.id,
        )
        email_notify.assert_called()

    @mock.patch(EMAIL_NOTIFY_DELAY)
    def test_enrolled_in_a_new_presential_mission(
        self, email_notify: mock.MagicMock, create_notification: mock.MagicMock
    ):
        mission = mommy.make(Mission, mission_model=PRESENTIAL)
        presential = mommy.make(PresentialMission, mission=mission)
        mommy.make(PresentialMissionDates, presential=presential, start_at=datetime.today(), end_at=datetime.today())
        enrollment = mommy.make(
            MissionEnrollment, user_id=self.user_consumer_1_id, mission=mission, workspace_id=self.workspace_id
        )

        notifications.notify_enrolled_in_a_new_mission(enrollment.id)

        message = Message(title="you_have_been_enrolled_in_a_mission", description=enrollment.mission.name)
        create_notification.assert_called_with(
            user_ids=[enrollment.user.id],
            type_key="ENROLLED_IN_A_MISSION",
            action="ENROLLED",
            object=enrollment.mission.id,
            message=message,
            workspace_id=enrollment.workspace.id,
        )
        email_notify.assert_called()

    @mock.patch.object(MyAccountService, "list_all_admin_users")
    @mock.patch(EMAIL_NOTIFY_DELAY)
    def test_new_mission_enrollment_certificate_to_review(
        self, email_notify: mock.MagicMock, list_all_admin_users: mock.MagicMock, create_notification: mock.MagicMock
    ):
        users = User.objects.filter()
        list_all_admin_users.return_value = users
        enrollment = MissionEnrollment.objects.get(id=self.mission_enrollment_id)
        message = Message(title="you_have_a_new_certificate_to_review", description=enrollment.user.name)
        notifications.notify_new_mission_enrollment_certificate_to_review(enrollment.id)
        user = User.objects.get(id=self.user_admin_1_id)

        create_notification.assert_any_call(
            user_ids=[user.id],
            type_key="NEW_EXTERNAL_ENROLLMENT_CERTIFICATE_TO_REVIEW",
            action="LIST",
            message=message,
            object=enrollment.id,
            workspace_id=enrollment.workspace_id,
        )

        email_notify.assert_called_once()

    @mock.patch.object(MyAccountService, "list_all_admin_users")
    def test_mission_enrollment_request_extension(
        self, list_all_admin_users: mock.MagicMock, create_notification: mock.MagicMock
    ):
        users = User.objects.filter()
        list_all_admin_users.return_value = users
        enrollment = MissionEnrollment.objects.get(id=self.mission_enrollment_id)
        message = Message(
            title="you_have_a_mission_enrollment_request_extension_to_review", description=enrollment.user.name
        )
        notifications.notify_mission_enrollment_request_extension(enrollment.id)

        create_notification.assert_any_call(
            user_ids=[users.first().id],
            type_key="USER_REQUESTED_MISSION_ENROLLMENT_DEADLINE_EXTENSION",
            action="ANALYSE_EXTENSION_REQUEST",
            message=message,
            object=enrollment.id,
            workspace_id=enrollment.workspace_id,
        )
        create_notification.assert_any_call(
            user_ids=[users[2].id],
            type_key="USER_REQUESTED_MISSION_ENROLLMENT_DEADLINE_EXTENSION",
            action="ANALYSE_EXTENSION_REQUEST",
            message=message,
            object=enrollment.id,
            workspace_id=enrollment.workspace_id,
        )

    def test_mission_enrollment_has_expired(self, create_notification: mock.MagicMock):
        enrollment = MissionEnrollment.objects.get(id=self.mission_enrollment_id)
        message = Message(title="your_mission_enrollment_has_expired", description=enrollment.mission.name)
        notifications.notify_mission_enrollment_has_expired(enrollment.id)
        create_notification.assert_called_with(
            user_ids=[enrollment.user_id],
            type_key="MISSION_ENROLLMENT_HAS_EXPIRED",
            action="EXPIRED",
            message=message,
            object=enrollment.mission_id,
            workspace_id=enrollment.workspace_id,
        )

    def test_enrollment_certificate_reviewed(self, create_notification: mock.MagicMock):
        enrollment = MissionEnrollment.objects.get(id=self.mission_enrollment_id)
        message = Message(title="your_enrollment_has_been_reviewed", description=enrollment.mission.name)
        notifications.notify_enrollment_certificate_reviewed(enrollment.id)
        create_notification.assert_called_with(
            user_ids=[enrollment.user_id],
            type_key="EXTERNAL_ENROLLMENT_REVIEWED",
            action="LIST",
            message=message,
            object=enrollment.mission_id,
            workspace_id=enrollment.workspace_id,
        )

    def test_mission_enrollment_deadline_extended(self, create_notification: mock.MagicMock):
        enrollment = MissionEnrollment.objects.get(id=self.mission_enrollment_id)
        notifications.notify_mission_enrollment_deadline_extended(enrollment.id)
        message = Message(
            title="your_mission_enrollment_deadline_has_been_extended", description=enrollment.mission.name
        )
        create_notification.assert_called_with(
            user_ids=[enrollment.user_id],
            type_key="MISSION_ENROLLMENT_DEADLINE_HAS_BEEN_EXTENDED",
            action="DEADLINE_EXTENDED",
            object=enrollment.mission_id,
            message=message,
            workspace_id=enrollment.workspace_id,
        )

    def test_mission_enrollment_is_expiring_today(self, create_notification: mock.MagicMock):
        enrollment = MissionEnrollment.objects.get(id=self.mission_enrollment_id)
        notifications.notify_mission_enrollment_is_expiring(enrollment.id, 0)
        message = Message(
            title="your_mission_enrollment_expires_today", title_values={}, description=enrollment.mission.name
        )

        create_notification.assert_called_with(
            user_ids=[enrollment.user_id],
            type_key="MISSION_ENROLLMENT_IS_EXPIRING",
            action="EXPIRING",
            object=enrollment.mission_id,
            message=message,
            workspace_id=enrollment.workspace_id,
        )

    @mock.patch(SEND_SYNC_ENROLLMENT_APPROVED_EMAIL)
    def test_live_enrollment_approved(self, send_email: mock.MagicMock, create_notification: mock.MagicMock):
        enrollment = MissionEnrollment.objects.get(id=self.mission_enrollment_id)
        mission = enrollment.mission
        mission.mission_model = "LIVE"
        mission.save()
        notifications.notify_sync_enrollment_approved(enrollment.id)
        message = Message(title="your_enrollment_was_approved", title_values={}, description=enrollment.mission.name)

        create_notification.assert_called_with(
            user_ids=[enrollment.user_id],
            type_key="MISSION_ENROLLMENT_APPROVED",
            action="ENROLLED",
            object=enrollment.mission_id,
            message=message,
            workspace_id=enrollment.workspace_id,
        )
        send_email.assert_called_with(enrollment.id)

    def test_live_enrollment_refused(self, create_notification: mock.MagicMock):
        enrollment = MissionEnrollment.objects.get(id=self.mission_enrollment_id)
        mission = enrollment.mission
        mission.mission_model = "LIVE"
        mission.save()
        notifications.notify_sync_enrollment_refused(enrollment.id)
        message = Message(title="your_enrollment_was_refused", title_values={}, description=enrollment.mission.name)

        create_notification.assert_called_with(
            user_ids=[enrollment.user_id],
            type_key="MISSION_ENROLLMENT_REFUSED",
            action="ENROLLED",
            object=enrollment.mission_id,
            message=message,
            workspace_id=enrollment.workspace_id,
        )

    @mock.patch("user_activity.tasks.notifications.send_sync_enrollment_approved_email.delay")
    def test_presential_enrollment_approved(self, send_email: mock.MagicMock, create_notification: mock.MagicMock):
        enrollment = MissionEnrollment.objects.get(id=self.mission_enrollment_id)
        mission = enrollment.mission
        mission.mission_model = "PRESENTIAL"
        mission.save()
        notifications.notify_sync_enrollment_approved(enrollment.id)
        message = Message(title="your_enrollment_was_approved", title_values={}, description=enrollment.mission.name)

        create_notification.assert_called_with(
            user_ids=[enrollment.user_id],
            type_key="MISSION_ENROLLMENT_APPROVED",
            action="ENROLLED",
            object=enrollment.mission_id,
            message=message,
            workspace_id=enrollment.workspace_id,
        )
        send_email.assert_called_with(enrollment.id)

    def test_presential_enrollment_refused(self, create_notification: mock.MagicMock):
        enrollment = MissionEnrollment.objects.get(id=self.mission_enrollment_id)
        mission = enrollment.mission
        mission.mission_model = "PRESENTIAL"
        mission.save()
        notifications.notify_sync_enrollment_refused(enrollment.id)
        message = Message(title="your_enrollment_was_refused", title_values={}, description=enrollment.mission.name)

        create_notification.assert_called_with(
            user_ids=[enrollment.user_id],
            type_key="MISSION_ENROLLMENT_REFUSED",
            action="ENROLLED",
            object=enrollment.mission_id,
            message=message,
            workspace_id=enrollment.workspace_id,
        )

    def test_mission_enrollment_is_expiring_in_one_day(self, create_notification: mock.MagicMock):
        enrollment = MissionEnrollment.objects.get(id=self.mission_enrollment_id)
        notifications.notify_mission_enrollment_is_expiring(enrollment.id, 1)
        message = Message(
            title="your_mission_enrollment_expires_tomorrow", title_values={}, description=enrollment.mission.name
        )

        create_notification.assert_called_with(
            user_ids=[enrollment.user_id],
            type_key="MISSION_ENROLLMENT_IS_EXPIRING",
            action="EXPIRING",
            object=enrollment.mission_id,
            message=message,
            workspace_id=enrollment.workspace_id,
        )


def _get_account_fake(user_id) -> dict:
    user = User.objects.get(id=user_id)
    users_data = {"id": str(user.id), "name": user.name, "email": user.email, "language": {"name": "pt-BR"}}
    return users_data
