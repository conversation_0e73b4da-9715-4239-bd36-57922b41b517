from uuid import uuid4

from conftest import get_injector
from django.core.exceptions import ValidationError
from django.test import TestCase
from learn_content.models import Answer, Exam, Question
from mission.models import MissionStage, MissionStageContent
from mission.models.mission_content_type_enum import EXAM, HTML, SCORM
from mock import mock
from model_mommy import mommy
from user_activity.models import MissionEnrollment, UserMissionContent
from user_activity.services.mission_enrollment_finish_service import (
    USER_CONSUME_NOT_ENOUGH_TO_FINISH,
    MissionEnrollmentFinishService,
)

KONTENT_GET_DOCS = "rest_clients.kontent.KontentClient.get_docs"


class MissionEnrollmentFinishServiceWhenMissionOnlyHaveUntrackedContents(TestCase):
    fixtures = ["workspace", "user", "mission_type", "mission_category", "mission", "mission_enrollment"]

    def setUp(self) -> None:
        self._enrollment = MissionEnrollment.objects.get(id="55193de6-521a-4e2a-8320-095390b4571a")
        self.mission = self._enrollment.mission
        self._injector = get_injector()
        self._service = self._injector.get(MissionEnrollmentFinishService)

    @mock.patch(KONTENT_GET_DOCS)
    def test_finish_score_when_user_consume_all_scorm_contents(self, get_docs):
        self._load_contents(load_activities_data=True)
        contents = MissionStageContent.objects.filter(stage__mission=self._enrollment.mission, content_type=SCORM)
        get_docs_return = []
        magic_duration = 60
        magic_points = 10
        for content in contents:
            get_docs_return.append(
                {"id": content.learn_content_uuid, "duration": magic_duration, "points": magic_points}
            )
        get_docs.return_value = get_docs_return

        score = self._service.process(self._enrollment)

        total_contents_points = magic_points * contents.count()
        total_contents_duration = magic_duration * contents.count()
        self.assertEqual(score.performance, 1)
        self.assertEqual(score.content_available_score, total_contents_points)
        self.assertEqual(score.content_awarded_score, total_contents_points)
        self.assertEqual(score.contents_available_time, total_contents_duration)
        self.assertEqual(score.contents_consume_time, total_contents_duration)
        self.assertEqual(score.total_awarded_score, total_contents_points)
        self.assertEqual(score.total_available_score, total_contents_points)
        self.assertEqual(score.exam_awarded_score, 0)
        self.assertEqual(score.exam_available_score, 0)

    @mock.patch(KONTENT_GET_DOCS)
    def test_finish_score_when_user_consume_all_genially_contents(self, get_docs):
        self._load_contents(True, HTML)
        contents = MissionStageContent.objects.filter(stage__mission=self._enrollment.mission, content_type=HTML)
        get_docs_return = []
        magic_duration = 60
        magic_points = 10
        for content in contents:
            get_docs_return.append(
                {"id": content.learn_content_uuid, "duration": magic_duration, "points": magic_points}
            )
        get_docs.return_value = get_docs_return

        score = self._service.process(self._enrollment)

        total_contents_points = magic_points * contents.count()
        total_contents_duration = magic_duration * contents.count()
        self.assertEqual(score.performance, 1)
        self.assertEqual(score.content_available_score, total_contents_points)
        self.assertEqual(score.content_awarded_score, total_contents_points)
        self.assertEqual(score.contents_available_time, total_contents_duration)
        self.assertEqual(score.contents_consume_time, total_contents_duration)
        self.assertEqual(score.total_awarded_score, total_contents_points)
        self.assertEqual(score.total_available_score, total_contents_points)
        self.assertEqual(score.exam_awarded_score, 0)
        self.assertEqual(score.exam_available_score, 0)

    @mock.patch(KONTENT_GET_DOCS)
    def test_finish_when_the_mission_has_genially_and_exams(self, get_docs):
        self._load_contents(True, HTML)
        self._load_exams()
        magic_duration = 60
        magic_points = 10
        question_points = 10
        contents = MissionStageContent.objects.filter(stage__mission=self._enrollment.mission, content_type=HTML)
        get_docs_return = []
        for content in contents:
            get_docs_return.append(
                {"id": content.learn_content_uuid, "duration": magic_duration, "points": magic_points}
            )
        get_docs.return_value = get_docs_return

        score = self._service.process(self._enrollment)

        total_contents_points = magic_points * contents.count()
        total_contents_duration = magic_duration * contents.count()
        self.assertEqual(score.performance, 1)
        self.assertEqual(score.content_available_score, total_contents_points)
        self.assertEqual(score.content_awarded_score, total_contents_points)
        self.assertEqual(score.contents_available_time, total_contents_duration)
        self.assertEqual(score.contents_consume_time, total_contents_duration)
        self.assertEqual(score.total_awarded_score, total_contents_points + question_points)
        self.assertEqual(score.total_available_score, total_contents_points + question_points)
        self.assertEqual(score.exam_awarded_score, question_points)
        self.assertEqual(score.exam_available_score, question_points)

    @mock.patch(KONTENT_GET_DOCS)
    def test_error_when_user_not_consume_all_scorm_contents(self, get_docs):
        self._load_contents(load_activities_data=False)
        contents = MissionStageContent.objects.filter(stage__mission=self._enrollment.mission, content_type=SCORM)
        get_docs_return = []
        magic_duration = 60
        magic_points = 10
        for content in contents:
            get_docs_return.append(
                {"id": content.learn_content_uuid, "duration": magic_duration, "points": magic_points}
            )
        get_docs.return_value = get_docs_return

        with self.assertRaises(ValidationError) as context:
            self._service.process(self._enrollment)

        self.assertEqual(context.exception.message, USER_CONSUME_NOT_ENOUGH_TO_FINISH)

    @mock.patch(KONTENT_GET_DOCS)
    def test_error_when_user_not_consume_all_genially_contents(self, get_docs):
        self._load_contents(False, HTML)
        contents = MissionStageContent.objects.filter(stage__mission=self._enrollment.mission, content_type=HTML)
        get_docs_return = []
        magic_duration = 60
        magic_points = 10
        for content in contents:
            get_docs_return.append(
                {"id": content.learn_content_uuid, "duration": magic_duration, "points": magic_points}
            )
        get_docs.return_value = get_docs_return

        with self.assertRaises(ValidationError) as context:
            self._service.process(self._enrollment)

        self.assertEqual(context.exception.message, USER_CONSUME_NOT_ENOUGH_TO_FINISH)

    def _load_contents(self, load_activities_data: bool, content_type: str = SCORM):
        # mission stage
        stage_1 = mommy.make(MissionStage, mission=self.mission)
        stage_2 = mommy.make(MissionStage, mission=self.mission)

        # mission stage content
        stage_content_1 = mommy.make(
            MissionStageContent, stage=stage_1, content_type=content_type, learn_content_uuid=uuid4()
        )
        stage_content_2 = mommy.make(
            MissionStageContent, stage=stage_2, content_type=content_type, learn_content_uuid=uuid4()
        )

        if load_activities_data:
            mommy.make(
                UserMissionContent,
                content=stage_content_1,
                mission_enrollment=self._enrollment,
                user=self._enrollment.user,
            )
            mommy.make(
                UserMissionContent,
                content=stage_content_2,
                mission_enrollment=self._enrollment,
                user=self._enrollment.user,
            )

    def _load_exams(self):
        stage = mommy.make(MissionStage, mission=self.mission)
        exam = mommy.make(Exam, stage=stage)
        stage_content = mommy.make(MissionStageContent, stage=stage, content_type=EXAM, learn_content_uuid=exam.id)
        question = mommy.make(Question, exam=exam, points=10)
        mommy.make(Answer, enrollment=self._enrollment, is_ok=True, exam_has_question=question)
        return stage_content
