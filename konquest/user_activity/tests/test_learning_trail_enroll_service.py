from unittest.mock import MagicMock

from account.models import User, Workspace
from authentication.keeps_permissions import ADMIN, USER
from config.settings import LEARNING_TRAIL_OPEN_FOR_COMPANY
from constants import ENROLLMENT_ENROLLED
from custom.exceptions.unable_to_enroll_another_user import UnableToEnrollAnotherUser
from custom.exceptions.unable_to_enroll_in_a_inactive_trail import UnableToEnrollInAInactiveTrail
from custom.exceptions.user_is_not_linked_to_any_trail_group import UserIsNotLinkedToAnyTrailGroup
from custom.keeps_exception_handler import KeepsNoPermissionToRenewEnrollment
from django.test import TestCase
from django.utils.timezone import now
from learning_trail.models import LearningTrail, LearningTrailType, LearningTrailWorkspace
from model_mommy import mommy
from user_activity.services.learning_trail_enroll_service import LearningTrailEnrollService
from user_activity.services.rewew_enrollment.renew_enrollment_permission_service import RenewEnrollmentPermissionService


class TestLearningTrailEnrollService(TestCase):
    def setUp(self) -> None:
        self.user = mommy.make(User)
        self.workspace = mommy.make(Workspace, block_reenrollment=True)
        self.open_trail_type = mommy.make(LearningTrailType, name=LEARNING_TRAIL_OPEN_FOR_COMPANY)
        self.trail = mommy.make(LearningTrail, learning_trail_type=self.open_trail_type)
        self.trail_workspace = mommy.make(LearningTrailWorkspace, learning_trail=self.trail, workspace=self.workspace)
        self.renew_enrollment_permission_service = MagicMock(
            spec=RenewEnrollmentPermissionService,
        )
        self.service = LearningTrailEnrollService(MagicMock(), MagicMock(), self.renew_enrollment_permission_service)

    def test_should_raise_user_is_not_linked_to_any_trail_group(self, *args):
        self._close_trail()
        action_user = self.user

        with self.assertRaises(UserIsNotLinkedToAnyTrailGroup):
            self.service.enroll(
                {"user_id": self.user.id, "learning_trail_id": self.trail.id, "workspace_id": self.workspace.id},
                action_user,
            )

    def test_consumer_user_cannot_enroll_another_user(self, *args):
        action_user = self.user
        action_user.role = USER
        another_user = mommy.make(User)

        with self.assertRaises(UnableToEnrollAnotherUser):
            self.service.enroll(
                {"user_id": another_user.id, "learning_trail_id": self.trail.id, "workspace_id": self.workspace.id},
                action_user,
            )

    def _close_trail(self):
        closed_trail_type = mommy.make(LearningTrailType, name="CLOSED")
        self.trail.update(learning_trail_type_id=closed_trail_type.id)
        self.trail.save()

    def test_should_raise_unable_to_enroll_in_inactive_trail(self, *args):
        self.trail.update(is_active=False)
        self.trail.save()
        action_user = User()
        action_user.role = ADMIN

        with self.assertRaises(UnableToEnrollInAInactiveTrail):
            self.service.enroll(
                {"user_id": self.user.id, "learning_trail_id": self.trail.id, "workspace_id": self.workspace.id},
                action_user,
            )

    def test_enroll_user_in_closed_mission_by_admin(self, *args):
        self._close_trail()
        enrollment_data = {
            "user_id": self.user.id,
            "learning_trail_id": self.trail.id,
            "workspace_id": self.workspace.id,
            "goal_date": now(),
        }
        action_user = User()
        action_user.role = ADMIN
        enrollment = self.service.enroll(enrollment_data, action_user)

        self.assertEqual(enrollment.status, ENROLLMENT_ENROLLED)
        self.assertTrue(enrollment.required)

    def test_enroll_user_with_regulatory_compliance_cycle(self, *args):
        enrollment_data = {
            "user_id": self.user.id,
            "learning_trail_id": self.trail.id,
            "workspace_id": self.workspace.id,
            "goal_date": now(),
        }
        mock_create_enrollment_cycle = MagicMock()
        self.service._regulatory_compliance_service.create_enrollment_cycle = mock_create_enrollment_cycle
        action_user = User()
        action_user.role = ADMIN

        enrollment = self.service.enroll(enrollment_data, action_user, regulatory_compliance_cycle_id="123")

        mock_create_enrollment_cycle.assert_called()
        self.assertEqual(enrollment.status, ENROLLMENT_ENROLLED)
        self.assertTrue(enrollment.required)

    def test_permission_chain_workspace_blocks_reenrollment(self, *args):
        action_user = User()
        action_user.role = ADMIN
        enrollment_data = {
            "user_id": self.user.id,
            "learning_trail_id": self.trail.id,
            "workspace_id": self.workspace.id,
            "goal_date": now(),
        }
        enrollment = self.service.enroll(enrollment_data, action_user)
        enrollment.status = "REPROVED"
        enrollment.save()
        self.renew_enrollment_permission_service = RenewEnrollmentPermissionService()
        self.service = LearningTrailEnrollService(MagicMock(), MagicMock(), self.renew_enrollment_permission_service)
        with self.assertRaises(KeepsNoPermissionToRenewEnrollment):
            self.service.enroll(enrollment_data, action_user)
