import mock
from account.models import User
from constants import CELERY_SEND_TASK_PATH
from dateutil.utils import today
from django.test import TestCase
from mission.models import (
    LiveMission,
    LiveMissionDates,
    Mission,
    MissionStage,
    MissionStageContent,
    PresentialMission,
    PresentialMissionDates,
)
from mission.models.mission import LIVE, PRESENTIAL
from model_mommy import mommy
from user_activity.models import LiveAttendance, MissionEnrollment, PresentialAttendance, UserMissionContent
from user_activity.models.mission_enrollment import (
    COMPLETED,
    ENROLLED,
    INACTIVATED,
    REFUSED,
    STARTED,
    WAITING_APPROVAL,
    WAITING_VACANCIES,
)
from user_activity.tasks.mission_enrollment_task import (
    active_mission_enrollments,
    close_sync_enrollments,
    create_live_enrollment_attendances_for_new_date,
    create_presential_enrollment_attendances_for_new_date,
    create_sync_enrollment_attendances,
    delete_sync_enrollment_attendances,
    inactive_mission_enrollments,
    promote_waiting_vacancies_enrollments,
    update_mission_enrollment_progress,
)

MOCK_UPDATE_MS_ENROLMENT_PROGRESS = (
    "user_activity.tasks.mission_enrollment_task.update_mission_enrollment_progress.delay"
)


@mock.patch(MOCK_UPDATE_MS_ENROLMENT_PROGRESS, return_value={})
class MissionEnrollmentTasksViewSet(TestCase):
    def setUp(self):
        self.user = mommy.make(User)
        self.mission = mommy.make(Mission)

        self.mission_enrollment_1 = mommy.make(MissionEnrollment, mission=self.mission, user=self.user, status=ENROLLED)

        self.mission_stage = mommy.make(MissionStage, mission=self.mission, name="Stage")
        self.mission_stage_content_11 = mommy.make(
            MissionStageContent, stage=self.mission_stage, name="Stage Content 11"
        )
        self.mission_stage_content_12 = mommy.make(
            MissionStageContent, stage=self.mission_stage, name="Stage Content 12"
        )
        self.mission_stage_content_13 = mommy.make(
            MissionStageContent, stage=self.mission_stage, name="Stage Content 13"
        )

    def test_update_progress_and_update_status(self, mock_enrollment_progress):
        contents = [self.mission_stage_content_11, self.mission_stage_content_12, self.mission_stage_content_13]
        for content in contents:
            mommy.make(
                UserMissionContent,
                content=content,
                user=self.user,
                status="COMPLETED",
                mission_enrollment=self.mission_enrollment_1,
            )

        enrollment = update_mission_enrollment_progress(self.mission_enrollment_1.id)

        self.assertEqual(0.99, enrollment.progress)
        self.assertEqual(STARTED, enrollment.status)

    def test_update_mission_enrollment_progress_without_contents(self, mock_enrollment_progress):
        mission = mommy.make(Mission)
        enrollment = mommy.make(MissionEnrollment, user=self.user, mission=mission)
        enrollment_updated = update_mission_enrollment_progress(enrollment.id)

        self.assertEqual(0, enrollment_updated.progress)

    @mock.patch("user_activity.services.MissionEnrollmentService.calc_remaining_seats")
    @mock.patch(CELERY_SEND_TASK_PATH)
    def test_promote_waiting_vacancies_live_enrollments(
        self, send_task, calc_remaining_seats, mock_enrollment_progress
    ):
        mission = mommy.make(Mission, mission_model="LIVE")
        user_2 = mommy.make(User)
        user_3 = mommy.make(User)
        mommy.make(LiveMission, mission=mission, allow_any_enrollment=False, seats=2)
        calc_remaining_seats.return_value = 2
        enrollment_1 = mommy.make(MissionEnrollment, user=self.user, mission=mission, status=WAITING_VACANCIES)
        enrollment_2 = mommy.make(MissionEnrollment, user=user_2, mission=mission, status=WAITING_VACANCIES)
        enrollment_3 = mommy.make(MissionEnrollment, user=user_3, mission=mission, status=WAITING_VACANCIES)

        promote_waiting_vacancies_enrollments(mission.id)

        enrollment_1.refresh_from_db()
        enrollment_2.refresh_from_db()
        enrollment_3.refresh_from_db()

        self.assertEqual(enrollment_1.status, WAITING_APPROVAL)
        self.assertEqual(enrollment_2.status, WAITING_APPROVAL)
        self.assertEqual(enrollment_3.status, WAITING_VACANCIES)

    def test_active_mission_enrollments(self, mock_enrollment_progress):
        mission = mommy.make(Mission)
        user_2 = mommy.make(User)
        inactivated_enrollment = mommy.make(MissionEnrollment, user=self.user, mission=mission, status=INACTIVATED)
        completed_enrollment = mommy.make(MissionEnrollment, user=user_2, mission=mission, status=COMPLETED)

        active_mission_enrollments(mission.id)

        inactivated_enrollment.refresh_from_db()
        completed_enrollment.refresh_from_db()

        self.assertEqual(inactivated_enrollment.status, ENROLLED)
        self.assertEqual(completed_enrollment.status, COMPLETED)

    def test_active_enrollment_already_started(self, mock_enrollment_progress):
        mission = mommy.make(Mission)
        inactivated_enrollment = mommy.make(MissionEnrollment, user=self.user, mission=mission, status=INACTIVATED)
        stage = mommy.make(MissionStage, mission=mission)
        content = mommy.make(MissionStageContent, stage=stage)
        mommy.make(UserMissionContent, content=content, user=self.user, mission_enrollment=inactivated_enrollment)

        active_mission_enrollments(mission.id)

        inactivated_enrollment.refresh_from_db()

        self.assertEqual(inactivated_enrollment.status, STARTED)

    def test_inactive_mission_enrollments(self, mock_enrollment_progress):
        mission = mommy.make(Mission)
        user_2 = mommy.make(User)
        enrollment_in_progress = mommy.make(MissionEnrollment, user=self.user, mission=mission, status=STARTED)
        completed_enrollment = mommy.make(MissionEnrollment, user=user_2, mission=mission, status=COMPLETED)

        inactive_mission_enrollments(mission.id)

        enrollment_in_progress.refresh_from_db()
        completed_enrollment.refresh_from_db()

        self.assertEqual(enrollment_in_progress.status, INACTIVATED)
        self.assertEqual(completed_enrollment.status, COMPLETED)

    def test_create_live_enrollment_attendances(self, mock_enrollment_progress):
        mission = mommy.make(Mission, mission_model=LIVE)
        live = mommy.make(LiveMission, mission=mission, allow_any_enrollment=False)
        mommy.make(LiveMissionDates, live=live, start_at=today(), end_at=today())
        enrollment = mommy.make(MissionEnrollment, user=self.user, mission=mission, status=ENROLLED)

        create_sync_enrollment_attendances(enrollment.id)
        attendances = LiveAttendance.objects.filter(enrollment=enrollment)
        attendance = attendances.first()
        self.assertEqual(attendances.count(), 1)
        self.assertEqual(attendance.presented, None)

    def test_create_live_enrollment_attendances_duplicated(self, mock_enrollment_progress):
        mission = mommy.make(Mission, mission_model=LIVE)
        live = mommy.make(LiveMission, mission=mission, allow_any_enrollment=False)
        mommy.make(LiveMissionDates, live=live, start_at=today(), end_at=today())
        enrollment = mommy.make(MissionEnrollment, user=self.user, mission=mission, status=ENROLLED)

        create_sync_enrollment_attendances(enrollment.id)
        create_sync_enrollment_attendances(enrollment.id)
        attendances = LiveAttendance.objects.filter(enrollment=enrollment)
        attendance = attendances.first()
        self.assertEqual(attendances.count(), 1)
        self.assertEqual(attendance.presented, None)

    def test_create_presential_enrollment_attendances(self, mock_enrollment_progress):
        mission = mommy.make(Mission, mission_model=PRESENTIAL)
        presential = mommy.make(PresentialMission, mission=mission, allow_any_enrollment=False)
        mommy.make(PresentialMissionDates, presential=presential, start_at=today(), end_at=today())
        enrollment = mommy.make(MissionEnrollment, user=self.user, mission=mission, status=ENROLLED)

        create_sync_enrollment_attendances(enrollment.id)
        attendances = PresentialAttendance.objects.filter(enrollment=enrollment)
        attendance = attendances.first()
        self.assertEqual(attendances.count(), 1)
        self.assertEqual(attendance.presented, None)

    def test_create_presential_enrollment_attendances_for_new_date(self, mock_enrollment_progress):
        mission = mommy.make(Mission, mission_model=PRESENTIAL)
        presential = mommy.make(PresentialMission, mission=mission, allow_any_enrollment=False)
        date = mommy.make(PresentialMissionDates, presential=presential, start_at=today(), end_at=today())
        enrollment = mommy.make(MissionEnrollment, user=self.user, mission=mission, status=ENROLLED)

        create_presential_enrollment_attendances_for_new_date(date.id)
        attendances = PresentialAttendance.objects.filter(enrollment=enrollment)
        attendance = attendances.first()
        self.assertEqual(attendances.count(), 1)
        self.assertEqual(attendance.presented, None)

    def test_create_live_enrollment_attendances_for_new_date(self, mock_enrollment_progress):
        mission = mommy.make(Mission, mission_model=LIVE)
        live = mommy.make(LiveMission, mission=mission, allow_any_enrollment=False)
        date = mommy.make(LiveMissionDates, live=live, start_at=today(), end_at=today())
        enrollment = mommy.make(MissionEnrollment, user=self.user, mission=mission, status=ENROLLED)

        create_live_enrollment_attendances_for_new_date(date.id)
        attendances = LiveAttendance.objects.filter(enrollment=enrollment)
        attendance = attendances.first()
        self.assertEqual(attendances.count(), 1)
        self.assertEqual(attendance.presented, None)

    def test_delete_live_enrollment_attendances(self, mock_enrollment_progress):
        mission = mommy.make(Mission, mission_model=LIVE)
        live = mommy.make(LiveMission, mission=mission, allow_any_enrollment=False)
        date = mommy.make(LiveMissionDates, live=live, start_at=today(), end_at=today())
        enrollment = mommy.make(MissionEnrollment, user=self.user, mission=mission, status=ENROLLED)
        mommy.make(LiveAttendance, date=date, enrollment=enrollment)

        delete_sync_enrollment_attendances(enrollment.id)
        attendances = LiveAttendance.objects.filter(enrollment=enrollment)
        self.assertEqual(attendances.count(), 0)

    def test_delete_presential_enrollment_attendances(self, mock_enrollment_progress):
        mission = mommy.make(Mission, mission_model=PRESENTIAL)
        presential = mommy.make(PresentialMission, mission=mission, allow_any_enrollment=False)
        date = mommy.make(PresentialMissionDates, presential=presential, start_at=today(), end_at=today())
        enrollment = mommy.make(MissionEnrollment, user=self.user, mission=mission, status=ENROLLED)
        mommy.make(PresentialAttendance, date=date, enrollment=enrollment)

        delete_sync_enrollment_attendances(enrollment.id)
        attendances = PresentialAttendance.objects.filter(enrollment=enrollment)
        self.assertEqual(attendances.count(), 0)

    def test_close_sync_enrollments(self, mock_enrollment_progress):
        mission = mommy.make(Mission, mission_model=PRESENTIAL)
        presential: PresentialMission = mommy.make(PresentialMission, mission=mission)
        date = mommy.make(PresentialMissionDates, presential=presential)
        enrollment_enrolled = mommy.make(MissionEnrollment, user=self.user, mission=mission, status=ENROLLED)
        enrollment_started = mommy.make(MissionEnrollment, user=self.user, mission=mission, status=STARTED)
        enrollment_completed = mommy.make(MissionEnrollment, user=self.user, mission=mission, status=COMPLETED)
        enrollment_refused = mommy.make(MissionEnrollment, user=self.user, mission=mission, status=REFUSED)
        mommy.make(PresentialAttendance, enrollment=enrollment_enrolled, date=date, presented=True)
        mommy.make(PresentialAttendance, enrollment=enrollment_started, date=date, presented=True)

        close_sync_enrollments(mission.id)

        enrollment_enrolled.refresh_from_db()
        enrollment_started.refresh_from_db()
        enrollment_completed.refresh_from_db()
        enrollment_refused.refresh_from_db()

        self.assertEqual(enrollment_enrolled.status, COMPLETED)
        self.assertEqual(enrollment_started.status, COMPLETED)
        self.assertEqual(enrollment_completed.status, COMPLETED)
        self.assertEqual(enrollment_refused.status, REFUSED)

    @mock.patch("user_activity.services.MissionEnrollmentService.calc_remaining_seats")
    @mock.patch(CELERY_SEND_TASK_PATH)
    def test_promote_waiting_vacancies_presential_enrollments(
        self, send_task, calc_remaining_seats, mock_enrollment_progress
    ):
        mission = mommy.make(Mission, mission_model="PRESENTIAL")
        user_2 = mommy.make(User)
        user_3 = mommy.make(User)
        mommy.make(PresentialMission, mission=mission, allow_any_enrollment=True, seats=2)
        calc_remaining_seats.return_value = 2
        enrollment_1 = mommy.make(MissionEnrollment, user=self.user, mission=mission, status=WAITING_VACANCIES)
        enrollment_2 = mommy.make(MissionEnrollment, user=user_2, mission=mission, status=WAITING_VACANCIES)
        enrollment_3 = mommy.make(MissionEnrollment, user=user_3, mission=mission, status=WAITING_VACANCIES)

        promote_waiting_vacancies_enrollments(mission.id)

        enrollment_1.refresh_from_db()
        enrollment_2.refresh_from_db()
        enrollment_3.refresh_from_db()

        self.assertEqual(enrollment_1.status, ENROLLED)
        self.assertEqual(enrollment_2.status, ENROLLED)
        self.assertEqual(enrollment_3.status, WAITING_VACANCIES)
