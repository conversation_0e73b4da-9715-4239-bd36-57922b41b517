import mock
from account.models import User
from authentication.keeps_permissions import AD<PERSON><PERSON>, INSTRUCTOR, USER
from dateutil.utils import today
from django.test import TestCase
from django.urls import reverse
from mission.models import LiveMissionDates, Mission
from model_mommy import mommy
from rest_framework.test import APIClient
from user_activity.models import LiveAttendance, MissionEnrollment

GET_PRIORITY_USER_ROLE = "authentication.keeps_permissions.KeepsBasePermission.get_priority_user_role_for_sync_missions"


@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
@mock.patch(GET_PRIORITY_USER_ROLE)
class LiveAttendanceViewSetTestCase(TestCase):
    fixtures = [
        "user",
        "workspace",
        "mission_type",
        "mission_category",
        "mission",
        "mission_workspace",
        "mission_enrollment",
    ]

    def setUp(self) -> None:
        self._mission_id = "c8baa509-7637-47a4-a97b-4e717f56df74"
        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.user_id = "244ef26a-eb00-4f80-9800-5e976e234452"

        self.client = APIClient()
        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.client.force_authenticate(user={"sub": self.user_id, "client_id": self.workspace_id})
        self.url = reverse("mission-enrollment-live-attendances-list")

    def test_consumer_user_list_attendances(self, get_priority_user_role: mock.MagicMock, check_role: mock.MagicMock):
        self.create_attendances()
        get_priority_user_role.return_value = USER

        response = self.client.get(self.url, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data.get("count"), 1)

    def test_admin_user_list_attendances(self, get_priority_user_role: mock.MagicMock, check_role: mock.MagicMock):
        self.create_attendances()
        get_priority_user_role.return_value = ADMIN

        response = self.client.get(self.url, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data.get("count"), 3)

    def test_instructor_user_list_attendances(self, get_priority_user_role: mock.MagicMock, check_role: mock.MagicMock):
        self.create_attendances()
        get_priority_user_role.return_value = INSTRUCTOR

        response = self.client.get(self.url, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data.get("count"), 3)

    def test_search_by_user_name(self, get_priority_user_role: mock.MagicMock, check_role: mock.MagicMock):
        self.create_attendances()
        get_priority_user_role.return_value = ADMIN

        response = self.client.get(f"{self.url}?search=Ana", **self.headers, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data.get("count"), 1)
        self.assertEqual(response.data.get("results")[0]["enrollment"]["user"]["name"], "Ana")

    def create_attendances(self):
        mission = Mission.objects.get(id=self._mission_id)
        request_user = User.objects.get(id=self.user_id)
        mission.live.instructors.set([request_user])
        ana = mommy.make(User, name="Ana", email="<EMAIL>")
        gabriel = mommy.make(User, name="Gabriel", email="<EMAIL>")
        enrollment_1 = mommy.make(MissionEnrollment, user=ana, mission=mission, workspace_id=self.workspace_id)
        enrollment_2 = mommy.make(MissionEnrollment, user=gabriel, mission=mission, workspace_id=self.workspace_id)
        live_date = mommy.make(LiveMissionDates, live=mission.live, start_at=today(), end_at=today())
        attendance_1 = mommy.make(LiveAttendance, date=live_date, enrollment=enrollment_1)
        attendance_2 = mommy.make(LiveAttendance, date=live_date, enrollment=enrollment_2)
        attendance_3 = mommy.make(LiveAttendance, date=live_date, enrollment_id="55193de6-521a-4e2a-8320-095390b4571a")
        return [attendance_1, attendance_2, attendance_3]
