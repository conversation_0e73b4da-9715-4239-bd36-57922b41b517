import uuid
from unittest.mock import MagicMock

import pytest
from custom.exceptions.certificate_exceptions import EnrollmentNotCompletedError
from user_activity.application.use_cases.generate_certificate.generate_trail_certificate_use_case import (
    GenerateTrailCertificateUseCase,
)
from user_activity.domain.entities.certificate_enrollment import CertificateContents
from user_activity.domain.repositories.certificate_mission_content_repository import CertificateContentRepository
from user_activity.domain.repositories.certificate_repository import (
    CertificateRepository,
)
from user_activity.domain.repositories.i_learning_trail_enrollment_repository import ILearningTrailEnrollmentRepository
from user_activity.domain.value_objects.certificate_url import CertificateURL
from user_activity.models import LearningTrailEnrollment
from user_activity.models.enums.enrollment_status_enum import EnrollmentStatusEnum

certificate_content_repository = MagicMock(spec=CertificateContentRepository)
enrollment_repository = MagicMock(spec=ILearningTrailEnrollmentRepository)
certificate_file_generator_repository = MagicMock(spec=CertificateRepository)
certificate_url = CertificateURL(
    value="http://certificate.trail/test.pdf"
)


@pytest.fixture(autouse=True)
def reset_mocks():
    certificate_content_repository.reset_mock()
    enrollment_repository.reset_mock()
    certificate_file_generator_repository.reset_mock()


def get_mocked_trail_enrollment():
    enrollment = MagicMock(spec=LearningTrailEnrollment)
    enrollment.id = uuid.uuid4()
    enrollment.user.name = "Test User"
    enrollment.user.time_zone = "UTC"
    enrollment.performance = 0.9
    enrollment.status = "COMPLETED"
    enrollment.workspace_id = "workspace_1"
    enrollment.certificate_url = None
    enrollment.learning_trail.name = "Advanced Learning Trail"
    enrollment.learning_trail.language = "en"
    enrollment.learning_trail.duration_time = 7200
    enrollment.end_date = None
    return enrollment


def test_generate_trail_certificate_success():
    enrollment = get_mocked_trail_enrollment()

    certificate_file_generator_repository.generate.return_value = certificate_url
    enrollment_repository.get_by_id.return_value = enrollment
    certificate_content_repository.get_contents.return_value = MagicMock(return_value=[
        CertificateContents(title="MISSION", time="3h2min", performance="100%")
    ])
    use_case = GenerateTrailCertificateUseCase(
        certificate_file_generator_repository,
        certificate_content_repository,
        enrollment_repository
    )

    response = use_case.execute(enrollment.id)

    assert response == certificate_url.value
    certificate_file_generator_repository.generate.assert_called_once()
    enrollment_repository.update_certificate_url.assert_called_once_with(enrollment, certificate_url)


def test_generate_trail_certificate_invalid_enrollment():
    enrollment = get_mocked_trail_enrollment()
    enrollment.status = EnrollmentStatusEnum.STARTED.value

    certificate_file_generator_repository.generate.return_value = certificate_url
    enrollment_repository.get_by_id.return_value = enrollment
    certificate_content_repository.get_contents.return_value = MagicMock(return_value=[
        CertificateContents(title="MISSION", time="3h2min", performance="100%")
    ])
    use_case = GenerateTrailCertificateUseCase(
        certificate_file_generator_repository,
        certificate_content_repository,
        enrollment_repository
    )

    with pytest.raises(EnrollmentNotCompletedError):
        use_case.execute(enrollment.id)

    certificate_file_generator_repository.generate.assert_not_called()
    enrollment_repository.update_certificate_url.assert_not_called()
