import uuid
from datetime import datetime
from unittest.mock import MagicMock

import pytest
from custom.exceptions.certificate_exceptions import EnrollmentNotCompletedError
from user_activity.application.use_cases.generate_certificate.generate_certificate_use_case import (
    GenerateCertificateUseCase,
)
from user_activity.domain.entities.certificate_enrollment import CertificateEnrollment
from user_activity.domain.entities.enrollment_type_enum import EnrollmentType
from user_activity.domain.repositories.certificate_mission_content_repository import CertificateContentRepository
from user_activity.domain.repositories.certificate_repository import (
    CertificateRepository,
)
from user_activity.domain.repositories.mission_enrollment_repository import MissionEnrollmentRepository
from user_activity.domain.value_objects.certificate_url import CertificateURL
from user_activity.models import MissionEnrollment
from user_activity.models.enums.enrollment_status_enum import EnrollmentStatusEnum

certificate_content_repository = MagicMock(spec=CertificateContentRepository)
enrollment_repository = MagicMock(spec=MissionEnrollmentRepository)
certificate_file_generator_repository = MagicMock(spec=CertificateRepository)
certificate_url = CertificateURL(value="http://certificate.url/test.pdf")


@pytest.fixture(autouse=True)
def reset_mocks():
    certificate_content_repository.reset_mock()
    enrollment_repository.reset_mock()
    certificate_file_generator_repository.reset_mock()


def get_mocked_enrollment():
    enrollment = MagicMock(spec=MissionEnrollment)
    enrollment.id = uuid.uuid4()
    enrollment.user.name = "Test User"
    enrollment.user.time_zone = "UTC"
    enrollment.performance = 0.85
    enrollment.status = EnrollmentStatusEnum.COMPLETED.value
    enrollment.workspace_id = uuid.uuid4()
    enrollment.certificate_url = None
    enrollment.mission.name = "Test Course"
    enrollment.mission.language = "en"
    enrollment.mission.duration_time = 3600
    enrollment.end_date = datetime.now()
    return enrollment


def test_generate_certificate_success():
    enrollment = get_mocked_enrollment()

    certificate_content_repository.get_contents.return_value = [
        {"name": "Lesson 1", "content": True, "content_duration": 300},
        {"name": "Quiz 1", "total_questions": 10, "total_correct_answers": 8},
    ]
    certificate_file_generator_repository.generate.return_value = certificate_url
    enrollment_repository.get_by_id.return_value = enrollment
    use_case = GenerateCertificateUseCase(
        certificate_file_generator_repository, certificate_content_repository, enrollment_repository
    )

    response = use_case.execute(enrollment.id)

    assert response == certificate_url.value
    certificate_file_generator_repository.generate.assert_called()
    enrollment_repository.update_certificate_url.assert_called_once_with(enrollment, certificate_url)


def test_generate_certificate_invalid_enrollment():
    enrollment = get_mocked_enrollment()

    enrollment.status = EnrollmentStatusEnum.STARTED
    enrollment_repository.get_by_id.return_value = enrollment
    use_case = GenerateCertificateUseCase(
        certificate_file_generator_repository, certificate_content_repository, enrollment_repository
    )

    with pytest.raises(EnrollmentNotCompletedError):
        use_case.execute(enrollment.id)

    certificate_file_generator_repository.generate.assert_not_called()
    enrollment_repository.update_certificate_url.assert_not_called()


def test_generate_certificate_no_tracking_data():
    enrollment = get_mocked_enrollment()
    enrollment.end_date = datetime(2025, 5, 29)

    certificate_content_repository.get_contents.return_value = []
    certificate_file_generator_repository.generate.return_value = certificate_url
    enrollment_repository.get_by_id.return_value = enrollment
    use_case = GenerateCertificateUseCase(
        certificate_file_generator_repository, certificate_content_repository, enrollment_repository
    )

    response = use_case.execute(enrollment.id)

    assert response == certificate_url.value
    certificate_file_generator_repository.generate.assert_called_once_with(
        CertificateEnrollment(
            enrollment_id=enrollment.id,
            user_name="Test User",
            user_time_zone="UTC",
            course_name="Test Course",
            course_language="en",
            course_duration="1h00m",
            performance="85%",
            status="COMPLETED",
            type=EnrollmentType.MISSION,
            summary=[],
            end_date="29/05/2025",
            workspace_id=enrollment.workspace_id,
            certificate_url=None,
            certificate_provider_url=None,
        )
    )
    enrollment_repository.update_certificate_url.assert_called_once_with(enrollment, certificate_url)
