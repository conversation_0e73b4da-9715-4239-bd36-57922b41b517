import uuid

import mock
from account.models import User, Workspace
from authentication.keeps_permissions import ADMIN
from django.test import TestCase
from django.urls import reverse
from learn_content.models import Answer, Exam, Question
from mission.models import Mission, MissionCategory, MissionStage, MissionStageContent, MissionType, MissionWorkspace
from model_mommy import mommy
from rest_framework.test import APIClient
from user_activity.models import LearnContentActivity, MissionEnrollment
from user_activity.models.mission_enrollment import INACTIVATED, REPROVED
from user_activity.services.mission_enrollment_finish_service import MISSION_ENROLLMENT_INACTIVATED


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
@mock.patch("rest_clients.kontent.KontentClient.get_docs")
class MissionEnrollmentFinishViewsetTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()

        self.workspace = mommy.make(Workspace, id=uuid.uuid4(), min_performance_certificate=0.66)
        self.user = mommy.make(User, id=uuid.uuid4())

        self.mission_type = mommy.make(MissionType)
        self.mission_category = mommy.make(MissionCategory)

        self.mission = mommy.make(
            Mission, id=uuid.uuid4(), mission_type=self.mission_type, mission_category=self.mission_category
        )

        self.mission = mommy.make(
            Mission, id=uuid.uuid4(), mission_type=self.mission_type, mission_category=self.mission_category
        )

        self.mission_workspace = mommy.make(MissionWorkspace, mission=self.mission, workspace=self.workspace)
        self.enrollment = mommy.make(MissionEnrollment, mission=self.mission, user=self.user, workspace=self.workspace)

        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user.id), "client_id": str(self.workspace.id), "role": ADMIN}
        )

    def test_finish_mission_enrollment_100_percent(
        self, mock_kontent, mock_return, mock_return_roles
    ):
        """
        Check if user consume 100% of contents
        """
        self.data_content_fixture()
        return_get_docs = self._get_get_docs_return()
        mock_kontent.return_value = return_get_docs

        response = self.client.post(
            reverse("mission-enrollment-finish", args=[str(self.enrollment.id)]), **self.headers
        )
        response_json = response.json()

        enrollment = MissionEnrollment.objects.filter(id=self.enrollment.id).first()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json["performance"], 1)
        self.assertEqual(enrollment.status, "COMPLETED")
        self.assertEqual(response_json["content_available_score"], 20)
        self.assertEqual(response_json["contents_available_time"], 120)
        self.assertEqual(response_json["contents_consume_time"], 120)
        self.assertEqual(response_json["content_awarded_score"], 20)
        self.assertEqual(response_json["exam_available_score"], 0)
        self.assertEqual(response_json["exam_awarded_score"], 0)
        self.assertEqual(response_json["total_awarded_score"], 20)
        self.assertEqual(response_json["total_available_score"], 20)

    def test_finish_mission_enrollment_stage_content_time_null(
        self, mock_kontent, mock_return, mock_return_roles
    ):
        """
        Check if user consume 100% of contents
        """
        self.data_content_fixture()
        stage_2 = mommy.make(MissionStage, id=uuid.uuid4(), mission=self.mission)
        stage_content_time_null = mommy.make(
            MissionStageContent, id=uuid.uuid4(), stage=stage_2, content_type="CONTENT"
        )

        mommy.make(
            LearnContentActivity,
            id=uuid.uuid4(),
            mission_stage_content=stage_content_time_null,
            mission_enrollment=self.enrollment,
            user=self.user,
            time_in=None,
        )
        return_get_docs = self._get_get_docs_return()
        mock_kontent.return_value = return_get_docs

        response = self.client.post(
            reverse("mission-enrollment-finish", args=[str(self.enrollment.id)]), **self.headers
        )
        response_json = response.json()

        enrollment = MissionEnrollment.objects.filter(id=self.enrollment.id).first()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json["performance"], 0.66)
        self.assertEqual(enrollment.status, "COMPLETED")

    def test_finish_enrollment_50_percent_without_content(
        self, mock_kontent, mock_return, mock_return_roles
    ):
        """
        Check if user consume 100% of contents
        """
        self.data_exam_fixture()
        mock_kontent.return_value = []

        response = self.client.post(
            reverse("mission-enrollment-finish", args=[str(self.enrollment.id)]), **self.headers
        )
        response_json = response.json()

        enrollment = MissionEnrollment.objects.filter(id=self.enrollment.id).first()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json["performance"], 0.5)
        self.assertEqual(enrollment.status, REPROVED)
        self.assertEqual(response_json["content_available_score"], 0)
        self.assertEqual(response_json["contents_available_time"], 0)
        self.assertEqual(response_json["contents_consume_time"], 0)
        self.assertEqual(response_json["content_awarded_score"], 0)
        self.assertEqual(response_json["exam_available_score"], 40)
        self.assertEqual(response_json["exam_awarded_score"], 20)
        self.assertEqual(response_json["total_awarded_score"], 20)
        self.assertEqual(response_json["total_available_score"], 40)

    def test_finish_enrollment_over_consume(self, mock_kontent, mock_return, mock_return_roles):
        self.data_content_fixture()
        return_get_docs = self._get_get_docs_return(30)
        mock_kontent.return_value = return_get_docs

        response = self.client.post(
            reverse("mission-enrollment-finish", args=[str(self.enrollment.id)]), **self.headers
        )
        response_json = response.json()

        enrollment = MissionEnrollment.objects.filter(id=self.enrollment.id).first()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json["performance"], 1)
        self.assertEqual(enrollment.status, "COMPLETED")
        self.assertEqual(response_json["content_available_score"], 20)
        self.assertEqual(response_json["contents_available_time"], 60)
        self.assertEqual(response_json["contents_consume_time"], 60)
        self.assertEqual(response_json["content_awarded_score"], 20)
        self.assertEqual(response_json["exam_available_score"], 0)
        self.assertEqual(response_json["exam_awarded_score"], 0)
        self.assertEqual(response_json["total_awarded_score"], 20)
        self.assertEqual(response_json["total_available_score"], 20)

    def test_finish_enrollment_lower_consume(
        self, mock_kontent, mock_return, mock_return_roles
    ):
        """
        Check if user don't consume the total of content
        """
        self.data_content_fixture()
        return_get_docs = self._get_get_docs_return(120)
        mock_kontent.return_value = return_get_docs

        response = self.client.post(
            reverse("mission-enrollment-finish", args=[str(self.enrollment.id)]), **self.headers
        )
        response_json = response.json()

        enrollment = MissionEnrollment.objects.filter(id=self.enrollment.id).first()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json["performance"], 0.5)
        self.assertEqual(enrollment.status, "REPROVED")
        self.assertEqual(response_json["content_available_score"], 20)
        self.assertEqual(response_json["contents_available_time"], 240)
        self.assertEqual(response_json["contents_consume_time"], 120)
        self.assertEqual(response_json["content_awarded_score"], 10)
        self.assertEqual(response_json["exam_available_score"], 0)
        self.assertEqual(response_json["exam_awarded_score"], 0)
        self.assertEqual(response_json["total_awarded_score"], 10)
        self.assertEqual(response_json["total_available_score"], 20)

    def test_finish_enrollment_hit_all_questions(
        self, mock_kontent, mock_return, mock_return_roles
    ):

        self.data_exam_fixture()

        response = self.client.post(
            reverse("mission-enrollment-finish", args=[str(self.enrollment.id)]), **self.headers
        )
        response_json = response.json()

        enrollment = MissionEnrollment.objects.filter(id=self.enrollment.id).first()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json["performance"], 0.5)
        self.assertEqual(enrollment.status, "REPROVED")
        self.assertEqual(response_json["content_available_score"], 0)
        self.assertEqual(response_json["contents_available_time"], 0)
        self.assertEqual(response_json["contents_consume_time"], 0)
        self.assertEqual(response_json["content_awarded_score"], 0)
        self.assertEqual(response_json["exam_available_score"], 40)
        self.assertEqual(response_json["exam_awarded_score"], 20)
        self.assertEqual(response_json["total_awarded_score"], 20)
        self.assertEqual(response_json["total_available_score"], 40)

    def test_finish_enrollment_content_and_exam(
        self, mock_kontent, mock_return, mock_return_roles
    ):
        self.data_content_fixture()
        self.data_exam_fixture()
        return_get_docs = self._get_get_docs_return()
        mock_kontent.return_value = return_get_docs

        response = self.client.post(
            reverse("mission-enrollment-finish", args=[str(self.enrollment.id)]), **self.headers
        )
        response_json = response.json()

        enrollment = MissionEnrollment.objects.filter(id=self.enrollment.id).first()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(enrollment.status, "COMPLETED")
        self.assertEqual(response_json["performance"], 0.66)
        self.assertEqual(response_json["content_available_score"], 20)
        self.assertEqual(response_json["contents_available_time"], 120)
        self.assertEqual(response_json["contents_consume_time"], 120)
        self.assertEqual(response_json["content_awarded_score"], 20)
        self.assertEqual(response_json["exam_available_score"], 40)
        self.assertEqual(response_json["exam_awarded_score"], 20)
        self.assertEqual(response_json["total_awarded_score"], 40)
        self.assertEqual(response_json["total_available_score"], 60)

    def test_finish_enrollment_in_mission_with_ghost_exams(
        self, mock_kontent, mock_return, mock_return_roles
    ):
        self.data_content_fixture()
        self.data_exam_fixture()
        self.create_ghost_exams()
        return_get_docs = self._get_get_docs_return()
        mock_kontent.return_value = return_get_docs

        response = self.client.post(
            reverse("mission-enrollment-finish", args=[str(self.enrollment.id)]), **self.headers
        )
        response_json = response.json()

        MissionEnrollment.objects.filter(id=self.enrollment.id).first()
        self.assertEqual(response.status_code, 200)
        # self.assertEqual(enrollment.status, "COMPLETED")
        self.assertEqual(response_json["performance"], 0.66)

    def _get_get_docs_return(self, duration: int = 60):
        contents = MissionStageContent.objects.filter(stage__mission=self.enrollment.mission, content_type="CONTENT")
        return_get_docs = []
        for content in contents:
            return_get_docs.append({"id": content.learn_content_uuid, "duration": duration, "points": 10})
        return return_get_docs

    def test_finish_enrollment_in_mission_without_consume_time_with_user_consume(
        self, mock_kontent, mock_return, mock_return_roles
    ):
        """
        Check if user don't consume the total of content
        """

        response = self.client.post(
            reverse("mission-enrollment-finish", args=[str(self.enrollment.id)]), **self.headers
        )
        response_json = response.json()

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response_json.get("i18n"), "mission_not_enough_data_to_finish")

    def test_finish_enrollment_already_finished_error(
        self, mock_kontent, mock_return, mock_return_roles
    ):
        """
        Check the exception when mission enrollment has been already finished
        """
        self.enrollment.status = "COMPLETED"
        self.enrollment.save()
        response = self.client.post(
            reverse("mission-enrollment-finish", args=[str(self.enrollment.id)]), **self.headers
        )
        response_json = response.json()

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response_json.get("i18n"), "mission_enrollment_already_finished")

    def test_finish_enrollment_inactivated_error(
        self, mock_kontent, mock_return, mock_return_roles
    ):
        """
        Check the exception when mission enrollment has been already finished
        """
        self.enrollment.status = INACTIVATED
        self.enrollment.save()
        response = self.client.post(
            reverse("mission-enrollment-finish", args=[str(self.enrollment.id)]), **self.headers
        )
        response_json = response.json()

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response_json.get("i18n"), MISSION_ENROLLMENT_INACTIVATED)

    def test_finish_enrollment_without_user_consume(
        self, mock_kontent, mock_return, mock_return_roles
    ):
        """
        Check if user don't consume the total of content
        """
        self.data_content_fixture()
        return_get_docs = self._get_get_docs_return()
        mock_kontent.return_value = return_get_docs

        user = mommy.make(User, email="<EMAIL>")
        enrollment = mommy.make(MissionEnrollment, mission=self.mission, user=user, workspace=self.workspace)

        response = self.client.post(reverse("mission-enrollment-finish", args=[str(enrollment.id)]), **self.headers)
        response_json = response.json()

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response_json.get("i18n"), "user_consume_not_enough_to_finish")

    def data_content_fixture(self):
        # mission stage
        stage_1 = mommy.make(MissionStage, id=uuid.uuid4(), mission=self.mission)
        stage_2 = mommy.make(MissionStage, id=uuid.uuid4(), mission=self.mission)

        # mission stage content
        stage_content_1 = mommy.make(MissionStageContent, id=uuid.uuid4(), stage=stage_1, content_type="CONTENT")
        stage_content_2 = mommy.make(MissionStageContent, id=uuid.uuid4(), stage=stage_2, content_type="CONTENT")

        # activities
        mommy.make(
            LearnContentActivity,
            id=uuid.uuid4(),
            mission_stage_content=stage_content_1,
            mission_enrollment=self.enrollment,
            user=self.user,
            time_in="00:00:30.000",
        )
        mommy.make(
            LearnContentActivity,
            id=uuid.uuid4(),
            mission_stage_content=stage_content_1,
            mission_enrollment=self.enrollment,
            user=self.user,
            time_in="00:00:30.000",
        )
        mommy.make(
            LearnContentActivity,
            id=uuid.uuid4(),
            mission_stage_content=stage_content_2,
            mission_enrollment=self.enrollment,
            user=self.user,
            time_in="00:01:00.000",
        )

    def data_exam_fixture(self):
        # mission stage
        stage_1 = mommy.make(MissionStage, id=uuid.uuid4(), mission=self.mission)
        stage_2 = mommy.make(MissionStage, id=uuid.uuid4(), mission=self.mission)

        # mission stage exam
        exam_1 = mommy.make(Exam, id=uuid.uuid4(), stage=stage_1)
        exam_2 = mommy.make(Exam, id=uuid.uuid4(), stage=stage_2)

        # mission stage exam
        mommy.make(
            MissionStageContent, id=uuid.uuid4(), stage=stage_1, content_type="EXAM", learn_content_uuid=exam_1.id
        )
        mommy.make(
            MissionStageContent, id=uuid.uuid4(), stage=stage_2, content_type="EXAM", learn_content_uuid=exam_2.id
        )

        question_1 = mommy.make(Question, id=uuid.uuid4, exam=exam_1, points=10)
        question_2 = mommy.make(Question, id=uuid.uuid4, exam=exam_1, points=10)
        question_3 = mommy.make(Question, id=uuid.uuid4, exam=exam_2, points=10)
        question_4 = mommy.make(Question, id=uuid.uuid4, exam=exam_2, points=10)

        mommy.make(
            Answer,
            id=uuid.uuid4(),
            exam_has_question=question_1,
            user=self.user,
            is_ok=True,
            enrollment=self.enrollment,
        )
        mommy.make(
            Answer,
            id=uuid.uuid4(),
            exam_has_question=question_2,
            user=self.user,
            is_ok=False,
            enrollment=self.enrollment,
        )
        mommy.make(
            Answer,
            id=uuid.uuid4(),
            exam_has_question=question_3,
            user=self.user,
            is_ok=True,
            enrollment=self.enrollment,
        )
        mommy.make(
            Answer,
            id=uuid.uuid4(),
            exam_has_question=question_4,
            user=self.user,
            is_ok=False,
            enrollment=self.enrollment,
        )

    def create_ghost_exams(self):
        stage_1 = mommy.make(MissionStage, id=uuid.uuid4(), mission=self.mission)
        stage_2 = mommy.make(MissionStage, id=uuid.uuid4(), mission=self.mission)
        mommy.make(Exam, id=uuid.uuid4(), stage=stage_1)
        mommy.make(Exam, id=uuid.uuid4(), stage=stage_2)
