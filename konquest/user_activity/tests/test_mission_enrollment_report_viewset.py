from typing import Sequence

import mock
from account.models import User, Workspace
from django.test import TestCase
from django.urls import reverse
from mission.models import Mission
from model_mommy import mommy
from rest_framework.test import APIClient
from user_activity.models import MissionEnrollment

SEND_FILE_PATH = "utils.aws.S3Client.send_file_path"


@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
@mock.patch(SEND_FILE_PATH)
class MissionEnrollmentReportViewSetTestCase(TestCase):
    def setUp(self) -> None:
        self.mission = mommy.make(Mission)
        self.workspace = mommy.make(Workspace)
        self.user = mommy.make(User)

        self.client = APIClient()
        self.headers = {"HTTP_X_CLIENT": self.workspace.id}
        self.client.force_authenticate(user={"sub": self.user.id, "client_id": self.workspace.id})
        self.url = reverse("mission-enrollment-report")

    def test_generate_mission_enrollment_report(self, send_file_path: mock.MagicMock, check_role: mock.MagicMock):
        self.create_enrollments()
        send_file_path.return_value = {"url": "some.url"}
        data = {"mission_id": self.mission.id}

        response = self.client.post(self.url, data=data, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {"url": "some.url"})

    def create_enrollments(self) -> Sequence[MissionEnrollment]:
        ana = mommy.make(User, name="Ana", email="<EMAIL>")
        gabriel = mommy.make(User, name="Gabriel", email="<EMAIL>")
        enrollment_1 = mommy.make(MissionEnrollment, user=ana, mission=self.mission, workspace_id=self.workspace.id)
        enrollment_2 = mommy.make(MissionEnrollment, user=gabriel, mission=self.mission, workspace_id=self.workspace.id)
        return [enrollment_1, enrollment_2]
