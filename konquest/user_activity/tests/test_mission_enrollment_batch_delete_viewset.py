import mock
from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient
from user_activity.models import MissionEnrollment
from user_activity.models.mission_enrollment import ENROLLED, WAITING_APPROVAL


@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class MissionEnrollmentBatchDeleteViewSetTestCase(TestCase):
    fixtures = ["workspace", "user", "mission_type", "mission"]

    def setUp(self) -> None:
        self.client = APIClient()
        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.mission_id = "c8baa509-7637-47a4-a97b-4e717f56df74"
        self.user_id = "489fb596-478f-40b5-b717-4931f20e4cc6"
        self.user_2_id = "97e1e5ce-9b33-434d-afc0-ccf71162e84f"
        self.enrollment_1 = mommy.make(
            MissionEnrollment,
            mission_id=self.mission_id,
            status=WAITING_APPROVAL,
            user_id=self.user_id,
            workspace_id=self.workspace_id,
        )
        self.enrollment_2 = mommy.make(
            MissionEnrollment,
            mission_id=self.mission_id,
            status=ENROLLED,
            user_id=self.user_2_id,
            workspace_id=self.workspace_id,
        )

        self.headers = {"HTTP_X_CLIENT": str(self.workspace_id)}
        self.client.force_authenticate(user={"sub": str(self.user_id), "client_id": str(self.workspace_id)})
        self.url_name = "mission-enrollment-batch-delete"
        self.url = reverse(self.url_name)

    def test_delete_by_enrollment_ids(self, check_role):
        payload = {"enrollment_ids": [self.enrollment_1.id, self.enrollment_2.id]}
        response = self.client.post(self.url, data=payload, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)

        self.assertFalse(MissionEnrollment.objects.filter(id=self.enrollment_1.id).exists())
        self.assertFalse(MissionEnrollment.objects.filter(id=self.enrollment_2.id).exists())

    def test_delete_all_enrollments_by_mission_id(self, check_role):
        payload = {"mission_id": self.mission_id}
        enrollment_in_other_mission = mommy.make(
            MissionEnrollment,
            user_id=self.user_id,
            mission_id="0ac0b202-f41b-49c8-ad9a-4ed6e150fb6b",
            workspace_id=self.workspace_id,
        )

        response = self.client.post(self.url, data=payload, **self.headers, format="json")
        self.assertEqual(response.status_code, 200)

        self.assertFalse(MissionEnrollment.objects.filter(id=self.enrollment_1.id).exists())
        self.assertFalse(MissionEnrollment.objects.filter(id=self.enrollment_2.id).exists())
        self.assertTrue(MissionEnrollment.objects.filter(id=enrollment_in_other_mission.id).exists())
