import uuid

import mock
from account.models import User, Workspace
from django.test import TestCase
from django.urls import reverse
from mission.models import Mission, MissionCategory, MissionStage, MissionStageContent, MissionType, MissionWorkspace
from model_mommy import mommy
from rest_framework.test import APIClient
from user_activity.models import MissionEnrollment
from user_activity.models.user_mission_content import STARTED, UserMissionContent

MOCK_UPDATE_MS_ENROLMENT_PROGRESS = (
    "user_activity.tasks.mission_enrollment_task.update_mission_enrollment_progress.delay"
)


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
@mock.patch(MOCK_UPDATE_MS_ENROLMENT_PROGRESS, return_value={})
class UserMissionContentViewsetTestCase(TestCase):
    @mock.patch(MOCK_UPDATE_MS_ENROLMENT_PROGRESS, return_value={})
    def setUp(self, mock_update_enrollment_progress):
        self.client = APIClient()
        self.user = mommy.make(User, id=uuid.uuid4())
        self.user_2 = mommy.make(User, id=uuid.uuid4())

        self.mission_type = mommy.make(MissionType, id=uuid.uuid4())
        self.mission_category = mommy.make(MissionCategory, id=uuid.uuid4())
        self.mission = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Test",
            mission_type=self.mission_type,
            mission_category=self.mission_category,
        )
        self.mission_2 = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Test 2",
            mission_type=self.mission_type,
            mission_category=self.mission_category,
        )

        self.workspace = mommy.make(Workspace, id=uuid.uuid4())

        self.mission_workspace = mommy.make(MissionWorkspace, workspace=self.workspace, mission=self.mission)
        self.mission_workspace = mommy.make(MissionWorkspace, workspace=self.workspace, mission=self.mission_2)

        self.mission_stage = mommy.make(MissionStage, mission=self.mission, name="Stage")
        self.mission_stage_content_11 = mommy.make(
            MissionStageContent, stage=self.mission_stage, name="Stage Content 11"
        )
        self.mission_stage_content_12 = mommy.make(
            MissionStageContent, stage=self.mission_stage, name="Stage Content 12"
        )

        self.mission_stage_2 = mommy.make(MissionStage, mission=self.mission_2)
        self.mission_stage_content_21 = mommy.make(
            MissionStageContent, stage=self.mission_stage_2, name="Stage Content 21"
        )
        self.mission_stage_content_22 = mommy.make(
            MissionStageContent, stage=self.mission_stage_2, name="Stage Content 22"
        )

        self.enrollment = mommy.make(MissionEnrollment, mission=self.mission, user=self.user, workspace=self.workspace)
        self.enrollment_2 = mommy.make(
            MissionEnrollment, mission=self.mission_2, user=self.user, workspace=self.workspace
        )
        self.enrollment_3 = mommy.make(
            MissionEnrollment, mission=self.mission_2, user=self.user_2, workspace=self.workspace
        )

        self.user_mission_stage_content = mommy.make(
            UserMissionContent,
            content=self.mission_stage_content_11,
            user=self.user,
            mission_enrollment=self.enrollment,
            status="COMPLETED",
        )
        self.user_mission_stage_content_2 = mommy.make(
            UserMissionContent,
            content=self.mission_stage_content_12,
            user=self.user,
            mission_enrollment=self.enrollment_2,
            status="STARTED",
        )
        self.user_mission_stage_content_3 = mommy.make(
            UserMissionContent,
            content=self.mission_stage_content_21,
            user=self.user_2,
            mission_enrollment=self.enrollment_3,
            status="COMPLETED",
        )

        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.url = reverse("user-mission-content-list")

    def test_user_mission_content_list(
        self, mock_update_enrollment_progress, mock_return_roles, mock_return
    ):
        mock_return.return_value = {"sub": str(self.user.id)}

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 2)

    def test_user_mission_content_list_filter_by_status(
        self, mock_update_enrollment_progress, mock_return_roles, mock_return
    ):
        mock_return.return_value = {"sub": str(self.user.id)}

        response = self.client.get(self.url + "?status=COMPLETED", **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

    def test_user_mission_content_list_search_by_name(
        self, mock_update_enrollment_progress, mock_return_roles, mock_return
    ):
        mock_return.return_value = {"sub": str(self.user.id)}

        response = self.client.get(self.url + "?search=Stage Content 11", **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

    def test_user_mission_content_post_success(
        self, mock_update_enrollment_progress, mock_return_roles, mock_return
    ):
        mock_return.return_value = {"sub": str(self.user.id)}

        data = {"content": str(self.mission_stage_content_21.id), "user": str(self.user.id)}

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["status"], STARTED)
        self.assertEqual(response.data.get("mission_enrollment"), self.enrollment_2.id)

    def test_user_mission_content_post_invalid_foreign_key(
        self, mock_update_enrollment_progress, mock_return_roles, mock_return
    ):
        mock_return.return_value = {"sub": str(self.user.id)}

        data = {"content": str(uuid.uuid4()), "user": str(self.user.id)}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()
        print(response_json)
        self.assertIsNotNone(response_json.get("content"))
        self.assertEqual(response.status_code, 400)

    def test_user_mission_content_post_success_existing_content(
        self, mock_update_enrollment_progress, mock_return_roles, mock_return
    ):
        mock_return.return_value = {"sub": str(self.user.id)}

        data = {"content": str(self.mission_stage_content_11.id), "user": str(self.user.id)}

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["id"], str(self.user_mission_stage_content.id))
