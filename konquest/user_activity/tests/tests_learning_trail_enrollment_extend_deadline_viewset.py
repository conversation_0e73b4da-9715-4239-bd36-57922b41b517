import uuid

import mock
from account.models import User, Workspace
from django.test import TestCase
from django.urls import reverse
from django.utils.timezone import now
from learning_trail.models import LearningTrail
from model_mommy import mommy
from rest_framework.test import APIClient
from user_activity.models import LearningTrailEnrollment


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class LearningTrailSubscriptionViewsetTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()

        self.workspace = mommy.make(Workspace, id=uuid.uuid4())
        self.user = mommy.make(User, id=uuid.uuid4())
        self.learning_trail = mommy.make(LearningTrail, id=uuid.uuid4())

        self.enrollment = mommy.make(
            LearningTrailEnrollment,
            learning_trail=self.learning_trail,
            workspace=self.workspace,
            goal_date=now(),
            user=self.user,
        )

        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})
        self.url = reverse("trail-enrollment-extend-deadline", args=[self.enrollment.id])

    def test_extend_deadline_learning_trail_enrollment_success(
        self, mock_return, mock_return_roles
    ):
        data = {
            "new_goal_date": now().date(),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 204)
