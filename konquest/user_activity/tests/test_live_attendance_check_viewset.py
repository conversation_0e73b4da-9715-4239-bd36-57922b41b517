import mock
from account.models import User
from authentication.keeps_permissions import ADMIN, USER
from dateutil.utils import today
from django.test import TestCase
from django.urls import reverse
from django.utils.translation import gettext as _
from mission.models import LiveMissionDates, Mission
from model_mommy import mommy
from rest_framework.test import APIClient
from user_activity.models import LiveAttendance, MissionEnrollment
from user_activity.services.mission_enrollment_attendance_service import (
    NOT_ALLOWED_TO_ATTEND,
    SELF_ATTENDANCE_IS_BLOCKED_TO_THIS_PERIOD,
)

GET_PRIORITY_USER_ROLE = "authentication.keeps_permissions.KeepsBasePermission.get_priority_user_role_for_sync_missions"


@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
@mock.patch(GET_PRIORITY_USER_ROLE)
class LiveAttendanceCheckViewSetTestCase(TestCase):
    fixtures = [
        "user",
        "workspace",
        "mission_type",
        "mission_category",
        "mission",
        "mission_workspace",
        "mission_enrollment",
    ]

    def setUp(self) -> None:
        self._mission_id = "c8baa509-7637-47a4-a97b-4e717f56df74"
        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.enrollment = MissionEnrollment.objects.get(id="55193de6-521a-4e2a-8320-095390b4571a")
        self.user_id = self.enrollment.user_id

        self.client = APIClient()
        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.client.force_authenticate(user={"sub": self.user_id, "client_id": self.workspace_id})
        self.url_name = "mission-enrollment-live-attendances-check"

    def test_self_check(self, get_priority_user_role: mock.MagicMock, check_role: mock.MagicMock):
        get_priority_user_role.return_value = USER
        attendance = self.create_attendance()
        data = {"presented": True}

        response = self.client.post(
            reverse(self.url_name, args=[str(attendance.id)]), **self.headers, data=data, format="json"
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["presented"], data.get("presented"))

    def test_check_false_live_attendance(self, get_priority_user_role: mock.MagicMock, check_role: mock.MagicMock):
        attendance = self.create_attendance()
        get_priority_user_role.return_value = USER
        data = {"presented": False}

        response = self.client.post(
            reverse(self.url_name, args=[str(attendance.id)]), **self.headers, data=data, format="json"
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["presented"], data.get("presented"))

    def test_check_by_admin(self, get_priority_user_role: mock.MagicMock, check_role: mock.MagicMock):
        attendance = self.create_attendance()
        admin_user = mommy.make(User)
        self.client.force_authenticate(user={"sub": admin_user.id, "client_id": self.workspace_id})
        get_priority_user_role.return_value = ADMIN
        data = {"presented": False}

        response = self.client.post(
            reverse(self.url_name, args=[str(attendance.id)]), **self.headers, data=data, format="json"
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["presented"], data.get("presented"))

    def test_user_not_allowed_to_check_error(self, get_priority_user_role: mock.MagicMock, check_role: mock.MagicMock):
        attendance = self.create_attendance()
        strange_user = mommy.make(User)
        self.client.force_authenticate(user={"sub": strange_user.id, "client_id": self.workspace_id})
        get_priority_user_role.return_value = USER
        data = {"presented": False}

        response = self.client.post(
            reverse(self.url_name, args=[str(attendance.id)]), **self.headers, data=data, format="json"
        )

        self.assertEqual(response.status_code, 403)
        self.assertEqual(response.data.get("detail"), _(NOT_ALLOWED_TO_ATTEND))

    def test_user_self_attendance_blocked_error(
        self, get_priority_user_role: mock.MagicMock, check_role: mock.MagicMock
    ):
        attendance = self.create_attendance(False)
        get_priority_user_role.return_value = USER
        data = {"presented": False}

        response = self.client.post(
            reverse(self.url_name, args=[str(attendance.id)]), **self.headers, data=data, format="json"
        )

        self.assertEqual(response.status_code, 403)
        self.assertEqual(response.data.get("detail"), _(SELF_ATTENDANCE_IS_BLOCKED_TO_THIS_PERIOD))

    def test_admin_check_in_a_self_attendance_blocked(
        self, get_priority_user_role: mock.MagicMock, check_role: mock.MagicMock
    ):
        attendance = self.create_attendance(False)
        admin_user = mommy.make(User)
        self.client.force_authenticate(user={"sub": admin_user.id, "client_id": self.workspace_id})
        get_priority_user_role.return_value = ADMIN
        data = {"presented": False}

        response = self.client.post(
            reverse(self.url_name, args=[str(attendance.id)]), **self.headers, data=data, format="json"
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["presented"], data.get("presented"))

    def create_attendance(self, allow_self_attendance=True):
        mission = Mission.objects.get(id=self._mission_id)
        live_date = mommy.make(
            LiveMissionDates,
            live=mission.sync,
            start_at=today(),
            end_at=today(),
            allow_self_attendance=allow_self_attendance,
        )
        attendance = mommy.make(LiveAttendance, date=live_date, enrollment=self.enrollment)
        return attendance
