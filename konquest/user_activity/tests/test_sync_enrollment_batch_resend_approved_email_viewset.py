import mock
from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient
from user_activity.models import MissionEnrollment
from user_activity.models.mission_enrollment import ENROLLED

SEND_SYNC_ENROLLMENT_APPROVED_EMAIL = "user_activity.tasks.notifications.send_sync_enrollment_approved_email.delay"


@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class SyncEnrollmentBatchResendApprovedEmailTestCase(TestCase):
    fixtures = ["workspace", "user", "mission_type", "mission"]

    def setUp(self) -> None:
        self.client = APIClient()
        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.user_id = "489fb596-478f-40b5-b717-4931f20e4cc6"
        self.mission_id = "c8baa509-7637-47a4-a97b-4e717f56df74"

        self.headers = {"HTTP_X_CLIENT": str(self.workspace_id)}
        self.client.force_authenticate(user={"sub": str(self.user_id), "client_id": str(self.workspace_id)})
        self.url_name = "mission-enrollment-batch-resend-approved-email"
        self.url = reverse(self.url_name)

    @mock.patch(SEND_SYNC_ENROLLMENT_APPROVED_EMAIL)
    def test_resend_email_to_enrolment_with_valid_status(
        self, send_sync_enrollment_approved_email: mock.MagicMock, check_role
    ):
        enrollment_1 = mommy.make(
            MissionEnrollment,
            mission_id=self.mission_id,
            status=ENROLLED,
            user_id=self.user_id,
            workspace_id=self.workspace_id,
        )
        enrollment_2 = mommy.make(
            MissionEnrollment,
            mission_id=self.mission_id,
            status=ENROLLED,
            user_id=self.user_id,
            workspace_id=self.workspace_id,
        )
        payload = {"enrollment_ids": [enrollment_1.id, enrollment_2.id]}

        response = self.client.post(self.url, data=payload, **self.headers, format="json")
        self.assertEqual(response.status_code, 200)
        send_sync_enrollment_approved_email.assert_any_call(enrollment_1.id)
        send_sync_enrollment_approved_email.assert_any_call(enrollment_2.id)
