from account.models import User, Workspace
from authentication.keeps_permissions import ADMIN
from django.test import TestCase
from learning_trail.models import LearningTrail
from mock import mock
from model_mommy import mommy
from rest_framework.reverse import reverse
from rest_framework.test import APIClient
from user_activity.dtos.trail_enrollment_tracking import TrailEnrollmentTracking
from user_activity.models import LearningTrailEnrollment
from user_activity.services.learning_trail_enrollment_tracking_service import LearningTrailEnrollmentTrackingService


@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class TestLearningTrailEnrollmentTrackingViewset(TestCase):
    def setUp(self) -> None:
        self.learning_trail = mommy.make(LearningTrail)
        self.workspace = mommy.make(Workspace)
        self.user = mommy.make(User)
        self.trail_enrollment = mommy.make(
            LearningTrailEnrollment, workspace=self.workspace, learning_trail=self.learning_trail, user=self.user
        )

        self.client = APIClient()
        self.headers = {"HTTP_X_CLIENT": self.workspace.id}
        self.client.force_authenticate(user={"sub": self.user.id, "client_id": self.workspace.id, "role": ADMIN})
        self.url_name = "learning-trail-enrollment-tracking"

    @mock.patch.object(LearningTrailEnrollmentTrackingService, "load")
    def test_should_return_enrollment_tracking(self, check_role, tracking_load: mock.MagicMock):
        tracking_load.return_value = [
            TrailEnrollmentTracking(
                id="4a360327-63ef-4d7b-8635-3c1199c97709",
                name="TESTE 02",
                step_type="MISSION",
                mission_enrollment_id="3fed37e5-0b74-4c1b-8104-793feb54c0b3",
            ),
            TrailEnrollmentTracking(
                id="ad08efed-ab92-4618-9306-3c6405e28e71",
                name="capa.jpg",
                step_type="PULSE",
                mission_enrollment_id=None,
            ),
        ]

        response = self.client.get(
            reverse(self.url_name, args=[str(self.trail_enrollment.id)]), **self.headers, format="json"
        )

        tracking_load.assert_called_once()
        self.assertEqual(response.status_code, 200)
