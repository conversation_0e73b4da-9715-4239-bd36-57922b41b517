from typing import Optional

from django.db.models import Count
from user_activity.models import MissionEnrollment
from user_activity.models.mission_enrollment import STATUS_MISSION_ENROLLMENT
from user_activity.services.abstracts.mission_enrollment_selector_abstract import MissionEnrollmentSelectorAbstract

# pylint: disable=too-few-public-methods


class MissionEnrollmentSelector(MissionEnrollmentSelectorAbstract):
    @staticmethod
    def get_status_count(user_id: str, workspace_id: str) -> dict:
        enrollments = (
            MissionEnrollment.objects.filter(user_id=user_id, workspace_id=workspace_id)
            .values("status")
            .annotate(total=Count("status"))
        )

        status_count = {row["status"]: row["total"] for row in enrollments}

        data = {}
        for status_tuple in STATUS_MISSION_ENROLLMENT:
            status = status_tuple[0]
            data.update({status.lower(): status_count.get(status, 0)})
        return data

    @staticmethod
    def get_actual_user_enrollment(user_id: str, mission_id: str, workspace_id: str) -> Optional[MissionEnrollment]:
        return (
            MissionEnrollment.objects.filter(mission_id=mission_id, user_id=user_id, workspace_id=workspace_id)
            .order_by("-end_date")
            .first()
        )
