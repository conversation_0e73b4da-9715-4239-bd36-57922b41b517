from typing import Union

from authentication.keeps_permissions import ADMIN, INSTRUCTOR, SUPER_ADMIN, USER
from custom.keeps_exception_handler import KeepsRuntimeError
from django.db.models import QuerySet
from django.utils.translation import gettext as _
from mission.models.mission import LIVE, PRESENTIAL
from mission.services.mission_service import MissionService
from user_activity.models import LiveAttendance, MissionEnrollment
from user_activity.models.mission_enrollment import COMPLETED, ENROLLED, STARTED
from user_activity.models.presential_attendance import PresentialAttendance

OTHERS = "OTHERS"


class SyncAttendanceSelector:
    def __init__(self, mission_service: MissionService):
        self._get_attendance_by_role = {
            SUPER_ADMIN: self._get_admin_attendances,
            USER: self._get_user_attendances,
            ADMIN: self._get_admin_attendances,
            INSTRUCTOR: self._get_instructor_attendances,
            OTHERS: self._get_user_attendances,
        }
        self._models = {LIVE: LiveAttendance, PRESENTIAL: PresentialAttendance}
        self._mission_service = mission_service

    def get_allowed_attendances(self, user_id: str, workspace_id: str, mission_model: str, role: str) -> QuerySet:
        if role not in self._get_attendance_by_role:
            role = OTHERS
        return self._get_attendance_by_role[role](user_id, workspace_id, mission_model)

    def get_mission_attendances(
        self, mission_id: str, user_id: str, workspace_id: str, mission_model: str, role: str
    ) -> QuerySet:
        allowed_attendances = self.get_allowed_attendances(user_id, workspace_id, mission_model, role)
        mission_model = mission_model.lower()
        return allowed_attendances.filter(**{f"date__{mission_model}__mission_id": mission_id})

    def _get_user_attendances(self, user_id: str, workspace_id: str, mission_model: str) -> QuerySet:
        enrollment_ids = MissionEnrollment.objects.filter(
            user_id=user_id,
            workspace_id=workspace_id,
            status__in=[ENROLLED, STARTED, COMPLETED],
        ).values_list("id", flat=True)
        model = self.get_sync_attendance_model(mission_model)
        return model.objects.filter(enrollment_id__in=enrollment_ids)

    def _get_admin_attendances(self, user_id: str, workspace_id: str, mission_model: str) -> QuerySet:
        missions = self._mission_service.get_workspace_missions(workspace_id)
        date_ids = self._mission_service.filter_sync_dates(missions, mission_model).values_list("id", flat=True)

        model = self.get_sync_attendance_model(mission_model)
        return model.objects.filter(date_id__in=date_ids)

    def _get_instructor_attendances(self, user_id: str, workspace_id: str, mission_model: str) -> QuerySet:
        missions = self._mission_service.get_missions_by_instructor(workspace_id, user_id)
        date_ids = self._mission_service.filter_sync_dates(missions, mission_model).values_list("id", flat=True)

        model = self.get_sync_attendance_model(mission_model)
        return model.objects.filter(date_id__in=date_ids)

    def get_sync_attendance_model(self, mission_model: str) -> Union[LiveAttendance, PresentialAttendance]:
        try:
            return self._models[mission_model]
        except KeyError as exc:
            raise KeepsRuntimeError(_(f"{mission_model}_is_invalid_attendance_model")) from exc
