from django.db.models.signals import post_delete, post_save
from django.db.transaction import on_commit
from learning_trail.models import LearningTrailStep
from user_activity.models import LearningTrailEnrollment, MissionEnrollment
from user_activity.tasks.learning_trail_enrollment_task import (
    enroll_users_in_the_learning_trail_missions,
    update_enrollments_progress,
)
from user_activity.tasks.learning_trail_enrollments.update_all_trail_enrollments_progress import (
    update_all_trail_enrollments_progress,
)
from utils.signals import receiver

# pylint: disable=W0640


@receiver(post_save, sender=MissionEnrollment)
def update_trail_enrollment_by_mission_enrollment_completed(sender, instance, **kwargs):
    if instance.status != "COMPLETED":
        return

    learning_trail_ids = LearningTrailStep.objects.filter(mission=instance.mission).values_list(
        "learning_trail_id", flat=True
    )
    lt_enrollment_ids = LearningTrailEnrollment.objects.filter(
        user=instance.user, learning_trail_id__in=learning_trail_ids, status__in=["ENROLLED", "STARTED"]
    ).values_list("id", flat=True)

    for enrollment_id in lt_enrollment_ids:
        on_commit(lambda enrollment_id=enrollment_id: update_enrollments_progress.delay(enrollment_id))


@receiver(post_save, sender=LearningTrailEnrollment)
def enroll_users_in_the_trail_missions(sender, instance, created, **kwargs):
    if created:
        on_commit(
            lambda learning_trail_id=instance.learning_trail_id,
            user_id=instance.user_id,
            workspace_id=instance.workspace_id,
            goal_date=str(instance.goal_date): enroll_users_in_the_learning_trail_missions.delay(
                learning_trail_id, user_id, workspace_id, goal_date
            )
        )


@receiver(post_save, sender=LearningTrailEnrollment)
def compute_initial_enrollment_progress(sender, instance: LearningTrailEnrollment, created, **kwargs):
    if created:
        on_commit(lambda enrollment_id=instance.id: update_enrollments_progress.delay(enrollment_id))


@receiver(post_save, sender=LearningTrailStep)
def update_enrollments_progress_when_created_new_step(sender, instance, created, **kwargs):
    if created:
        on_commit(
            lambda learning_trail_id=instance.learning_trail.id: update_all_trail_enrollments_progress.delay(
                learning_trail_id
            )
        )


@receiver(post_delete, sender=LearningTrailStep)
def update_enrollments_progress_when_deleted_new_step(sender, instance, **kwargs):
    on_commit(
        lambda learning_trail_id=instance.learning_trail.id: update_all_trail_enrollments_progress.delay(
            learning_trail_id
        )
    )
