from account.models import User
from custom import KeepsBadRequestError
from django.db.models import Q
from django.db.models.signals import post_delete, post_save, pre_save
from django.db.transaction import on_commit
from django.utils.translation import gettext as _
from gamification.services.gamification_service import GamificationService
from learn_content.models import Answer
from mission.models import LiveMissionDates, Mission, MissionContributor, PresentialMissionDates
from user_activity.models import LearnContentActivity, MissionEnrollment, ScormActivity, UserMissionContent
from user_activity.models.mission_enrollment import EXPIRED, INACTIVATED, STATUS_IN_PROGRESS
from user_activity.tasks.mission_enrollment_task import (
    create_live_enrollment_attendances_for_new_date,
    create_presential_enrollment_attendances_for_new_date,
    promote_waiting_vacancies_enrollments,
    update_mission_enrollment_progress,
)
from user_activity.tasks.mission_enrollments.compute_progress_by_activity import compute_progress_by_activity
from user_activity.tasks.mission_enrollments.compute_progress_by_question_answer import (
    compute_progress_by_question_answer,
)
from user_activity.tasks.mission_enrollments.update_enrolled_count import update_enrolled_count
from user_activity.tasks.mission_enrollments.verify_mission_enrollment_integrate_after_create import (
    verify_mission_enrollment_integrate_after_create,
)
from utils.signals import receiver

# pylint: disable=W0212


@receiver(post_save, sender=UserMissionContent)
def update_enrollment_progress_by_content_consume(sender, created, instance: UserMissionContent, **kwargs):
    if not created or not instance.mission_enrollment_id:
        return
    on_commit(lambda: update_mission_enrollment_progress.delay(enrollment_id=instance.mission_enrollment_id))


@receiver(post_save, sender=ScormActivity)
def update_enrollment_progress_by_scorm_consume(sender, created, instance: ScormActivity, **kwargs):
    if not created or not instance.enrollment_id:
        on_commit(lambda: update_mission_enrollment_progress.delay(enrollment_id=instance.enrollment_id))


@receiver(post_delete, sender=MissionEnrollment)
def deleted_sync_enrollment(sender, instance: MissionEnrollment, **kwargs):
    mission = Mission.objects.filter(id=instance.mission_id).first()
    if not mission or not mission.is_sync:
        return
    on_commit(lambda: promote_waiting_vacancies_enrollments.delay(mission.id))


@receiver(post_save, sender=LiveMissionDates)
def created_new_live_mission_date(sender, instance, created, **kwargs):
    if not created:
        return
    on_commit(lambda: create_live_enrollment_attendances_for_new_date.delay(instance.id))


@receiver(post_save, sender=PresentialMissionDates)
def created_new_presential_mission_date(sender, instance, created, **kwargs):
    if not created:
        return
    on_commit(lambda: create_presential_enrollment_attendances_for_new_date.delay(instance.id))


@receiver(pre_save, sender=MissionEnrollment)
def check_create_enrollment_integrity(sender, instance: MissionEnrollment, **kwargs):
    if not instance._state.adding:
        return
    if instance.deleted:
        return
    exist_another_in_progress = (
        MissionEnrollment.objects.filter(
            Q(user_id=instance.user_id, mission_id=instance.mission_id, workspace_id=instance.workspace_id)
            & (Q(status__in=STATUS_IN_PROGRESS) | Q(status__in=[EXPIRED, INACTIVATED]))
        )
        .exclude(id=instance.id)
        .exists()
    )
    if instance.in_progress and exist_another_in_progress:
        raise KeepsBadRequestError(
            detail=_("user_mission_enrollment_in_progress_already_exists"),
            i18n="user_mission_enrollment_in_progress_already_exists",
        )


@receiver(post_save, sender=LearnContentActivity)
def created_new_content_activity(sender, created, instance: LearnContentActivity, **kwargs):
    if not created or instance.pulse_id:
        return
    on_commit(lambda: compute_progress_by_activity.delay(instance.id))


@receiver(post_save, sender=Answer)
def created_answer(sender, created, instance: Answer, **kwargs):
    if not created or not instance.enrollment:
        return
    on_commit(lambda: compute_progress_by_question_answer.delay(instance.id))


def mission_manager(user: User, mission: Mission) -> bool:
    mission_contributor = MissionContributor.objects.filter(mission=mission, user=user).exists()
    mission_owner = mission.user_creator == user
    return mission_contributor or mission_owner


@receiver(post_save, sender=MissionEnrollment)
def check_new_enrollment_integrity(sender, instance: MissionEnrollment, created, **kwargs):
    if created:
        on_commit(lambda: verify_mission_enrollment_integrate_after_create.delay(instance.id))


@receiver(post_save, sender=MissionEnrollment)
def mission_enrollment_update_gamification(sender, instance: MissionEnrollment, **kwargs):
    service = GamificationService()
    on_commit(lambda: service.create_gamification_history_from_mission(instance))


@receiver(post_save, sender=MissionEnrollment)
def update_enrolled_count_post_save(sender, instance: MissionEnrollment, created, **kwargs):
    if created:
        on_commit(
            lambda: update_enrolled_count.delay(
                str(instance.user_id), str(instance.mission_id), str(instance.workspace_id)
            )
        )


@receiver(post_delete, sender=MissionEnrollment)
def update_enrolled_count_post_delete(sender, instance: MissionEnrollment, **kwargs):
    on_commit(
        lambda: update_enrolled_count.delay(str(instance.user_id), str(instance.mission_id), str(instance.workspace_id))
    )
