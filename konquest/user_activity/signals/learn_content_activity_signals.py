from django.db.models.signals import post_save
from django.db.transaction import on_commit
from django.dispatch import receiver
from gamification.services.gamification_service import GamificationService
from user_activity.models.learn_content_activity import LearnContentActivity
from user_activity.tasks.learn_content_activity_task import update_pulse_views


@receiver(post_save, sender=LearnContentActivity)
def learn_content_activity_update_pulse_views(sender, instance, created, **kwargs):
    if not created or not instance.pulse:
        return
    learn_content_activity_exists = (
        LearnContentActivity.objects.filter(user=instance.user, pulse=instance.pulse).exclude(id=instance.id).exists()
    )
    if not learn_content_activity_exists:
        on_commit(lambda: update_pulse_views.delay(instance.pulse.id))


@receiver(post_save, sender=LearnContentActivity)
def learn_content_activity_update_gamification(sender, instance, created, **kwargs):
    if not instance.pulse:
        return
    service = GamificationService()
    on_commit(lambda: service.create_gamification_history_from_pulse(instance))
