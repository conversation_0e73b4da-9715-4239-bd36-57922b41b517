from django.db.models.signals import post_save
from django.db.transaction import on_commit
from user_activity.models import MissionEnrollment
from user_activity.tasks import notifications
from utils.signals import receiver


@receiver(post_save, sender=MissionEnrollment)
def create_new_mission_enrollment_certificate_to_review_notification(sender, instance, created, **kwargs):
    update_fields = kwargs.get("update_fields") or []
    expected_fields = ["status", "certificate_provider_url"]
    updated_expected_fields = all(expected_field in update_fields for expected_field in expected_fields)

    if updated_expected_fields and (instance.status == "PENDING_VALIDATION"):
        on_commit(lambda: notifications.notify_new_mission_enrollment_certificate_to_review.delay(instance.id))


@receiver(post_save, sender=MissionEnrollment)
def create_mission_enrollment_certificate_reviewed_notification(sender, instance, created, **kwargs):
    update_fields = kwargs.get("update_fields") or []
    expected_fields = ["status", "approve_msg"]
    updated_expected_fields = all(expected_field in update_fields for expected_field in expected_fields)
    mission_is_external = instance.mission.mission_model = "EXTERNAL_PROVIDER"

    if (
        updated_expected_fields
        and mission_is_external
        and (instance.status == "REFUSED" or instance.status == "COMPLETED")
    ):
        on_commit(lambda: notifications.notify_enrollment_certificate_reviewed.delay(instance.id))


@receiver(post_save, sender=MissionEnrollment)
def create_mission_enrollment_request_extension_notification(sender, instance, created, **kwargs):
    update_fields = kwargs.get("update_fields") or []

    if ("status" in update_fields) and (instance.status == "REQUEST_EXTENSION"):
        on_commit(lambda: notifications.notify_mission_enrollment_request_extension.delay(instance.id))


@receiver(post_save, sender=MissionEnrollment)
def create_mission_enrollment_has_expired_notification(sender, instance, created, **kwargs):
    update_fields = kwargs.get("update_fields") or []

    if ("status" in update_fields) and (instance.status == "EXPIRED"):
        on_commit(lambda: notifications.notify_mission_enrollment_has_expired.delay(instance.id))


@receiver(post_save, sender=MissionEnrollment)
def create_mission_enrollment_deadline_extended_notification(sender, instance: MissionEnrollment, created, **kwargs):
    update_fields = kwargs.get("update_fields") or []
    expected_fields = ["status", "goal_date"]
    updated_expected_fields = all(expected_field in update_fields for expected_field in expected_fields)

    if updated_expected_fields and instance.required and (instance.status == "STARTED"):
        on_commit(lambda: notifications.notify_mission_enrollment_deadline_extended.delay(instance.id))
