import os
import pathlib

from account.models import User
from authentication import keeps_permissions
from authentication.keeps_permissions import ADMIN_PERMISSIONS, ALL_PERMISSIONS
from constants import REGULATORY_COMPLIANCE_CYCLE, RULE_CAN_CHANGE_MISSION_ENROLLMENT
from custom.keeps_exception_handler import (
    KeepsBadRequestError,
    KeepsNoPermissionToEditLearningTrailEnrollment,
    KeepsNoPermissionToEditMissionEnrollment,
    KeepsNoPermissionToTrackMissionEnrollment,
)
from django_filters.rest_framework import DjangoFilterBackend
from injector import Provider, inject
from mission.models import MissionWorkspace
from rest_framework import status, viewsets
from rest_framework.filters import SearchFilter
from rest_framework.response import Response
from rules import test_rule
from user_activity.application.use_cases.generate_certificate.generate_certificate_use_case import (
    GenerateCertificateUseCase,
)
from user_activity.models import MissionEnrollment
from user_activity.selectors.mission_enrollment_selector import MissionEnrollmentSelector
from user_activity.serializers.mission_batch_enrollment_input_serializer import MissionBatchEnrollmentInputSerializer
from user_activity.serializers.mission_enrollment_extend_deadline_serializer import (
    MissionEnrollmentExtendDeadlineSerializer,
)
from user_activity.serializers.mission_enrollment_restart_serializer import MissionEnrollmentRestartSerializer
from user_activity.serializers.mission_enrollment_serializer import (
    MissionEnrollmentCreateSerializer,
    MissionEnrollmentDetailSerializer,
    MissionEnrollmentListSerializer,
    MissionEnrollmentRetakeSerializer,
    MissionEnrollmentSerializer,
    MissionEnrollmentUpdateSerializer,
)
from user_activity.services import MissionBatchEnrollmentService, MissionEnrollmentService
from user_activity.services.mission_batch_enrollment_service import EnrollmentsBatch
from user_activity.services.mission_enrollment_finish_service import MissionEnrollmentFinishService
from user_activity.services.mission_enrollment_tracking_service import MissionEnrollmentTrackingResumeService
from user_activity.services.mission_enrollment_update_service import MissionEnrollmentUpdateService
from user_activity.utils import get_mission_enrollment_list_queryset
from user_activity.views.filters.user_activity_filters import MissionEnrollmentFilter
from user_activity.views.ordering_filters.mission_enrollment_ordering_filters import MissionEnrollmentOrderingFilterSet
from utils.utils import Utils, load_request_user, swagger_safe_queryset


class MissionEnrollmentViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (
        DjangoFilterBackend,
        SearchFilter,
        MissionEnrollmentOrderingFilterSet,
    )
    filterset_class = MissionEnrollmentFilter
    search_fields = ("mission__name", "user__name", "user__email")
    ordering = ("goal_date",)

    permission_classes = (
        keeps_permissions.KeepsSuperAdminPermission
        | keeps_permissions.KeepsPlatformAdminPermission
        | keeps_permissions.KeepsCuratorPermission
        | keeps_permissions.KeepsContentPermission
        | keeps_permissions.KeepsUserPermission
        | keeps_permissions.KeepsInstructorPermission
        | keeps_permissions.KeepsContributorMissionPermission,
    )

    @inject
    def __init__(
        self,
        service: MissionEnrollmentService = Provider[MissionEnrollmentService],
        update_service: MissionEnrollmentUpdateService = Provider[MissionEnrollmentUpdateService],
        **kwargs
    ):
        super().__init__(**kwargs)
        self._service = service
        self._update_service = update_service

    @swagger_safe_queryset(MissionEnrollment)
    def get_queryset(self):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        return get_mission_enrollment_list_queryset(workspace_id)

    def get_serializer_class(self):
        serializers = {
            "POST": MissionEnrollmentCreateSerializer,
            "PUT": MissionEnrollmentUpdateSerializer,
            "PATCH": MissionEnrollmentUpdateSerializer,
            "GET": MissionEnrollmentListSerializer,
            "RETRIEVE": MissionEnrollmentDetailSerializer,
        }
        return serializers.get(self.request.method, MissionEnrollmentListSerializer)

    def destroy(self, request, *args, **kwargs):
        enrollment_id = self.kwargs.get("pk")
        enrollment = MissionEnrollment.objects.get(id=enrollment_id)
        self._service.delete_enrollment(enrollment)
        return Response(status=status.HTTP_204_NO_CONTENT)

    def partial_update(self, request, *args, **kwargs):
        enrollment_id = self.kwargs.get("pk")
        enrollment = MissionEnrollment.objects.get(id=enrollment_id)

        serializer = self.get_serializer_class()(enrollment, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        enrollment = self._update_service.update_enrollment(
            enrollment_id, serializer.validated_data, load_request_user(request)
        )

        return Response(MissionEnrollmentSerializer(enrollment).data, status=status.HTTP_200_OK)

    def create(self, request, *args, **kwargs):
        workspace_id = request.user.get("client_id")
        action_user = load_request_user(request)
        request.data["workspace"] = workspace_id

        serializer = self.get_serializer_class()(data=request.data)
        serializer.is_valid(raise_exception=True)
        regulatory_compliance_cycle_id = None
        if REGULATORY_COMPLIANCE_CYCLE in serializer.validated_data:
            regulatory_compliance_cycle_id = serializer.validated_data.pop(REGULATORY_COMPLIANCE_CYCLE)

        enrollment = self._service.enroll_user(
            serializer.validated_data,
            action_user,
            regulatory_compliance_cycle_id
        )

        return Response(data=MissionEnrollmentSerializer(enrollment).data, status=201)


class MissionEnrollmentCountStatusViewSet(viewsets.ViewSet):
    permission_classes = (
        keeps_permissions.KeepsSuperAdminPermission
        | keeps_permissions.KeepsPlatformAdminPermission
        | keeps_permissions.KeepsCuratorPermission
        | keeps_permissions.KeepsContentPermission
        | keeps_permissions.KeepsUserPermission
        | keeps_permissions.KeepsInstructorPermission,
    )

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._selector = MissionEnrollmentSelector()

    def list(self, request, *args, **kwargs):
        """
        Return a data of user enrollments by status
        """
        user_id = self.kwargs.get("user_id")
        workspace_id = self.request.user.get("client_id")
        data = self._selector.get_status_count(user_id, workspace_id)

        return Response(data=data)


class MissionEnrollmentBatchViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides create enrollment batch in a mission
    """

    permission_classes = ADMIN_PERMISSIONS
    serializer_class = MissionBatchEnrollmentInputSerializer

    @inject
    def __init__(self, service: MissionBatchEnrollmentService = Provider[MissionBatchEnrollmentService], **kwargs):
        super().__init__(**kwargs)
        self._batch_enrollment_service = service

    def create(self, request, *args, **kwargs):
        """
        Create many enrollment by many users and missions

        body:
            "users": [user_id_list], the list of users to be enrolled
            "missions": [mission_id_list], the mission list
            "goal_date": "YYYY-MM-DD HH:MM:SS", the goal date to finish the enrollment
            "required": bool, if true, after the goal date, the enrollment will expire
        """
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        users = serializer.validated_data["users"]
        missions = serializer.validated_data["missions"]
        goal_date = serializer.validated_data.get("goal_date")
        required = serializer.validated_data.get("required_mission") or serializer.validated_data.get("required")
        regulatory_compliance_cycle_id = serializer.validated_data.get(REGULATORY_COMPLIANCE_CYCLE)
        workspace_id = self.request.user.get("client_id")
        action_user = load_request_user(request)

        missions = (
            MissionWorkspace.objects.filter(mission_id__in=missions, workspace_id=workspace_id)
            .all()
            .values_list("mission_id", flat=True)
        )
        enrollments_batch = EnrollmentsBatch(
            mission_ids=missions,
            user_ids=users,
            workspace_id=workspace_id,
            action_user=action_user,
            goal_date=goal_date,
            required=required,
            regulatory_compliance_cycle_id=regulatory_compliance_cycle_id,
        )

        result = self._batch_enrollment_service.enroll(enrollments_batch)

        return Response(result, status=status.HTTP_200_OK)

    def get_serializer_class(self):
        return MissionEnrollmentSerializer


class MissionEnrollmentCompleteViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    permission_classes = ALL_PERMISSIONS

    @inject
    def __init__(self, service: MissionEnrollmentFinishService = Provider[MissionEnrollmentFinishService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    def create(self, request, *args, **kwargs):
        enrollment = MissionEnrollment.objects.get(id=self.kwargs.get("pk"))
        if not test_rule(RULE_CAN_CHANGE_MISSION_ENROLLMENT, load_request_user(request), enrollment):
            raise KeepsNoPermissionToEditLearningTrailEnrollment()
        response = self._service.process(enrollment)
        return Response(response.__dict__, status=status.HTTP_200_OK)


class MissionEnrollmentGiveUpViewSet(viewsets.ModelViewSet):
    """
    API to give up a mission enrollment
    """

    permission_classes = ALL_PERMISSIONS

    @inject
    def __init__(self, service: MissionEnrollmentService = Provider[MissionEnrollmentService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    def get_serializer_class(self):
        return MissionEnrollmentSerializer

    def partial_update(self, request, *args, **kwargs):
        """
        Give up a mission enrollment, only the enrollment can give up. The enrollment must be in progress
        and cannot be mandatory(required=True)

        ---
            body request:
                give_up_comment: "comment"

            return:
                MissionEnrollment
        """
        enrollment_id = self.kwargs.get("pk")
        user_id = self.request.user.get("sub")
        comment_key = "give_up_comment"
        if comment_key not in request.data:
            raise KeepsBadRequestError(
                detail="pass the give_up_comment in the request body", i18n="give_up_comment_cannot_be_none"
            )
        enrollment = MissionEnrollment.objects.get(id=enrollment_id)

        if str(enrollment.user_id) != str(user_id):
            raise KeepsBadRequestError(detail="no permission to give up", i18n="only_the_user_enrolled_can_give_up")

        enrollment = self._service.give_up(enrollment, request.data.get(comment_key))

        return Response(data=self.get_serializer_class()(enrollment).data, status=status.HTTP_200_OK)


FILE_EXTENSIONS = [".png", ".pdf"]


class MissionEnrollmentExternalReviewViewSet(viewsets.ModelViewSet):
    """
    API to send an external enrollment to review or resend, all admins workspace will receive email and notification
    to evaluate the enrollment. If the file was reject, can be resend other.
    """

    permission_classes = ALL_PERMISSIONS

    @inject
    def __init__(self, service: MissionEnrollmentService = Provider[MissionEnrollmentService], **kwargs):
        super().__init__(**kwargs)
        self.__service = service

    def create(self, request, *args, **kwargs):
        """
        Submit a file [png, pdf] that proves the completion enrollments of an external mission for validation. Only the
        enrolled user can send your own enrollment for validation and the enrollment must have STARTED status
        or REFUSED status

        ---
            body request:
            file: binary_file

            return:
            "enrollment_was_sent_for_review"
        """
        if "file" not in request.data:
            raise KeepsBadRequestError(detail="File not found", i18n="file_not_found")

        file_to_review = Utils.temp_file(request.data.get("file"))
        extension_file = pathlib.Path(file_to_review).suffix

        if extension_file not in FILE_EXTENSIONS:
            os.remove(file_to_review)
            raise KeepsBadRequestError(detail="File extension not allowed", i18n="file_extension_not_allowed")

        enrollment = MissionEnrollment.objects.get(id=self.kwargs.get("pk"))
        if not test_rule(RULE_CAN_CHANGE_MISSION_ENROLLMENT, load_request_user(request), enrollment):
            raise KeepsNoPermissionToEditLearningTrailEnrollment()

        self.__service.external_review(enrollment, file_to_review)

        return Response("enrollment_was_sent_for_review", status=status.HTTP_200_OK)


class MissionEnrollmentExternalValidateViewSet(viewsets.ModelViewSet):
    """
    API to validate an external enrollment sent for review, the user reviewer can reject or approved. The user consumer
    will receive a notification after the review
    """

    permission_classes = (keeps_permissions.KeepsSuperAdminPermission | keeps_permissions.KeepsPlatformAdminPermission,)

    @inject
    def __init__(self, service: MissionEnrollmentService = Provider[MissionEnrollmentService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    def create(self, request, *args, **kwargs):
        """
        Submit a review of an external enrollment. Only Admin HAVE this access. If approve, approved value in body
        it has to be true and performance not null and reject_approve null. If disapprove, approved value in body
        it has to be false, performance null and reject_approve not null.

        ---
            body request:
                performance: int,
                approved: bool,
                reject_approve: str,
        """
        enrollment_id = self.kwargs.get("pk")
        approved = self.request.data.get("approved")
        enrollment = MissionEnrollment.objects.get(id=enrollment_id)
        reject_approve = self.request.data.get("reject_comment")
        reviewer_id = self.request.user.get("sub")

        if not test_rule(RULE_CAN_CHANGE_MISSION_ENROLLMENT, load_request_user(request), enrollment):
            raise KeepsNoPermissionToEditLearningTrailEnrollment()

        reviewer = User.objects.get(id=reviewer_id)
        if approved:
            try:
                performance = float(self.request.data.get("performance"))
            except ValueError as exc:
                raise KeepsBadRequestError(
                    i18n="invalid_performance_value", detail="pass a valid value in performance, exp:0.7"
                ) from exc
            self._service.approve_external_enrollment(enrollment, reviewer, performance)
        else:
            self._service.refuse_external_enrollment(enrollment, reject_approve, reviewer)

        return Response(status=status.HTTP_200_OK)


class MissionEnrollmentRetakeViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    permission_classes = ALL_PERMISSIONS
    serializer_class = MissionEnrollmentRetakeSerializer

    @inject
    def __init__(self, service: MissionEnrollmentService = Provider[MissionEnrollmentService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    def partial_update(self, request, *args, **kwargs):
        enrollment = MissionEnrollment.objects.get(id=self.kwargs.get("pk"))
        if not test_rule(RULE_CAN_CHANGE_MISSION_ENROLLMENT, load_request_user(self.request), enrollment):
            raise KeepsNoPermissionToEditLearningTrailEnrollment()
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        goal_date = serializer.validated_data.get("goal_date")
        enrollment = self._service.retake(enrollment, goal_date)
        return Response(MissionEnrollmentSerializer(enrollment).data, status=status.HTTP_200_OK)


class MissionEnrollmentCertificateV2ViewSet(viewsets.ModelViewSet):
    """
    Create a new certificate
    """

    permission_classes = ALL_PERMISSIONS

    @inject
    def __init__(self, use_case: GenerateCertificateUseCase = Provider[GenerateCertificateUseCase], **kwargs):
        super().__init__(**kwargs)
        self.use_case = use_case

    def create(self, request, *args, **kwargs):
        """
        Generate the mission enrollment certificate
        """
        enrollment_id = self.kwargs["pk"]
        enrollment = MissionEnrollment.objects.get(id=enrollment_id)
        if not test_rule(RULE_CAN_CHANGE_MISSION_ENROLLMENT, load_request_user(self.request), enrollment):
            raise KeepsNoPermissionToEditMissionEnrollment()

        certificate_url = self.use_case.execute(enrollment.id)

        return Response({"certificate_url": certificate_url}, status=status.HTTP_200_OK)


class MissionEnrollmentRequestExtensionViewSet(viewsets.ModelViewSet):
    """
    Request extension of mission enrollment expired
    """

    permission_classes = ALL_PERMISSIONS

    @inject
    def __init__(self, service: MissionEnrollmentService = Provider[MissionEnrollmentService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    def create(self, request, *args, **kwargs):
        """
        Mission Enrollment Request Extension

        Request extension of an expired mission enrollment

        ---
            rules:
                - Only the mission enrollment user(logged) can request an extension

            tasks:
                - All admin users will be notified
        """
        enrollment_id = self.kwargs.get("pk")
        user_id = self.request.user.get("sub")
        workspace_id = self.request.user.get("client_id") if self.request.user else None

        enrollment = MissionEnrollment.objects.filter(
            id=enrollment_id, user_id=user_id, workspace_id=workspace_id
        ).first()

        if not enrollment:
            raise KeepsBadRequestError(
                i18n="mission_enrollment_not_found", detail="could not find your mission enrollment"
            )

        self._service.request_extension(enrollment=enrollment)

        return Response("request_extension_was_sent_for_review", status=status.HTTP_200_OK)


class MissionEnrollmentExtendDeadlineViewSet(viewsets.ModelViewSet):
    """
    Extend mission enrollment Dead Line (goal-date)
    """

    permission_classes = (keeps_permissions.KeepsPlatformAdminPermission | keeps_permissions.KeepsSuperAdminPermission,)
    serializer_class = MissionEnrollmentExtendDeadlineSerializer

    @inject
    def __init__(self, service: MissionEnrollmentService = Provider[MissionEnrollmentService], **kwargs):
        super().__init__(**kwargs)
        self.__service = service

    def create(self, request, *args, **kwargs):
        """
        Mission Enrollment Extend Deadline

        Extend deadline of a mission enrollment

        ---
            body:
            {
                'new_goal_date': 'YYYY-MM-DD'
            }

            rules:
                - The mission enrollment must be in progress, status equal to
                    STARTED, REQUEST_EXTENSION, REFUSED, EXPIRED, PENDING_VALIDATION
                - The mission enrollment must have the mandatory deadline field equal to True

            tasks:
                - The enrolment user will be notified
        """
        enrollment_id = self.kwargs.get("pk")
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        new_goal_date = serializer.validated_data["new_goal_date"]
        enrollment = MissionEnrollment.objects.get(id=enrollment_id, workspace_id=workspace_id)

        enrollment = self.__service.extend_deadline(enrollment=enrollment, new_goal_date=new_goal_date)

        return Response(data=MissionEnrollmentSerializer(enrollment).data, status=status.HTTP_200_OK)


class MissionEnrollmentRestartViewSet(viewsets.ModelViewSet):
    """
    Mission Enrollment Restart
    """

    permission_classes = (keeps_permissions.KeepsPlatformAdminPermission | keeps_permissions.KeepsSuperAdminPermission,)
    serializer_class = MissionEnrollmentRestartSerializer

    @inject
    def __init__(self, service: MissionEnrollmentService = Provider[MissionEnrollmentService], **kwargs):
        super().__init__(**kwargs)
        self.__service = service

    def create(self, request, *args, **kwargs):
        """
        Mission Enrollment Restart

        Restart a mission enrollment completed or reproved

        ---
            body:
                'goal_date': str, 'YYYY-MM-DD'
                'mandatory_dead_line': bool

            rules:
                - The mission enrollment cannot be in progress, status equal to COMPLETED or REPROVED

            tasks:
                - The enrolment user will be notified
        """
        enrollment_id = self.kwargs.get("pk")
        workspace_id = self.request.user.get("client_id") if self.request.user else None

        enrollment = MissionEnrollment.objects.get(id=enrollment_id, workspace_id=workspace_id)
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        enrollment = self.__service.restart(
            enrollment=enrollment,
            goal_date=validated_data["goal_date"],
            required=validated_data["mandatory_dead_line"],
        )

        return Response(data=MissionEnrollmentSerializer(enrollment).data, status=status.HTTP_200_OK)


class MissionEnrollmentChangePerformanceViewSet(viewsets.ModelViewSet):
    """
    Mission Enrollment Change Performance
    """

    permission_classes = (keeps_permissions.KeepsPlatformAdminPermission | keeps_permissions.KeepsSuperAdminPermission,)

    @inject
    def __init__(self, service: MissionEnrollmentService = Provider[MissionEnrollmentService], **kwargs):
        super().__init__(**kwargs)
        self.__service = service

    def create(self, request, *args, **kwargs):
        """
        Mission Enrollment Change Performance

        Change the mission enrollment performance

        ---
            body:
                'performance': float, 0.0 - 1.0

            rules:
                - The mission enrollment cannot be in progress, status equal to COMPLETED or REPROVED
        """
        enrollment_id = self.kwargs.get("pk")
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        try:
            performance = float(self.request.data.get("performance"))
        except ValueError as exc:
            raise KeepsBadRequestError(
                i18n="invalid_performance_value", detail="pass a valid value in performance, exp:0.7"
            ) from exc

        enrollment = MissionEnrollment.objects.filter(id=enrollment_id, workspace_id=workspace_id).first()

        if not enrollment:
            raise KeepsBadRequestError(i18n="mission_enrollment_not_found", detail="mission enrollment not found")

        enrollment = self.__service.change_performance(enrollment=enrollment, performance=performance)

        return Response(data=MissionEnrollmentSerializer(enrollment).data, status=status.HTTP_200_OK)


class MissionEnrollmentTrackingViewSet(viewsets.ModelViewSet):
    """
    Mission Enrollment Tracking
    """

    permission_classes = ALL_PERMISSIONS

    @inject
    def __init__(
        self,
        service: MissionEnrollmentTrackingResumeService = Provider[MissionEnrollmentTrackingResumeService],
        **kwargs,
    ):
        super().__init__(**kwargs)
        self._service = service

    queryset = MissionEnrollment.objects.none()

    def list(self, request, *args, **kwargs):
        """
        Mission Enrollment Tracking
        """
        enrollment_id = self.kwargs.get("pk")
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        user = load_request_user(self.request)
        enrollment = MissionEnrollment.objects.get(id=enrollment_id, workspace_id=workspace_id)
        if not test_rule(RULE_CAN_CHANGE_MISSION_ENROLLMENT, user, enrollment):
            raise KeepsNoPermissionToTrackMissionEnrollment()
        track = self._service.load(enrollment=enrollment)
        return Response(data=track, status=status.HTTP_200_OK)
