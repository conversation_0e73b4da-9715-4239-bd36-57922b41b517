from injector import Provider, inject
from user_activity.models import PresentialAttendance
from user_activity.services.mission_enrollment_attendance_service import MissionEnrollmentAttendanceService
from user_activity.views.bases.sync_attendance_batch_check_viewset_base import SyncAttendanceBatchCheckViewSetBase


class PresentialAttendanceBatchCheckViewSet(SyncAttendanceBatchCheckViewSetBase):
    @inject
    def __init__(
        self, service: MissionEnrollmentAttendanceService = Provider[MissionEnrollmentAttendanceService], **kwargs
    ):
        super().__init__(service, PresentialAttendance, **kwargs)
