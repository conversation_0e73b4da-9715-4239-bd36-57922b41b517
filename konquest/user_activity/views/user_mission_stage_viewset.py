from authentication import keeps_permissions
from django.core.exceptions import ObjectDoesNotExist
from django_filters.rest_framework import DjangoFilterBackend
from injector import Provider, inject
from mission.models import MissionStage
from mission.services.mission_service import MissionService
from rest_framework import status, viewsets
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter
from rest_framework.response import Response
from user_activity.models import MissionEnrollment
from user_activity.models.user_mission_stage import STARTED, UserMissionStage
from user_activity.selectors.mission_enrollment_selector import MissionEnrollmentSelector
from user_activity.serializers.user_mission_stage_serializer import (
    UserMissionStageListSerializer,
    UserMissionStageSerializer,
)
from utils.utils import swagger_safe_queryset


class UserMissionStageViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = ("stage", "stage__mission", "status")
    search_fields = ("stage__mission__name",)
    ordering_fields = ("updated_date",)
    ordering = ("updated_date",)

    permission_classes = (
        keeps_permissions.KeepsSuperAdminPermission
        | keeps_permissions.KeepsPlatformAdminPermission
        | keeps_permissions.KeepsCuratorPermission
        | keeps_permissions.KeepsContentPermission
        | keeps_permissions.KeepsUserPermission
        | keeps_permissions.KeepsInstructorPermission,
    )

    @inject
    def __init__(self, mission_service: MissionService = Provider[MissionService], **kwargs):
        super().__init__(**kwargs)
        self._service = mission_service
        self._enrollment_selector = MissionEnrollmentSelector()

    @swagger_safe_queryset(UserMissionStage)
    def get_queryset(self):
        user_id = self.request.user["sub"] if self.request.user else None
        workspace_id = self.request.user["client_id"] if self.request.user else None
        mission_enrollment_ids = MissionEnrollment.objects.filter(
            user_id=user_id, workspace_id=workspace_id
        ).values_list("id", flat=True)

        return UserMissionStage.objects.filter(mission_enrollment_id__in=mission_enrollment_ids)

    def get_serializer_class(self):
        return UserMissionStageListSerializer if self.request.method == "GET" else UserMissionStageSerializer

    def create(self, request, *args, **kwargs):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        serializer = self.get_serializer(data=request.data)
        serializer.initial_data["user"] = self.request.user["sub"]
        serializer.initial_data["status"] = STARTED

        stage_id = serializer.initial_data.get("stage")
        user_id = serializer.initial_data.get("user")

        try:
            mission_id = MissionStage.objects.get(id=stage_id).mission_id
        except ObjectDoesNotExist:
            mission_id = None

        enrollment = self._enrollment_selector.get_actual_user_enrollment(user_id, mission_id, workspace_id)
        enrollment_id = enrollment.id if enrollment else None
        serializer.initial_data["mission_enrollment"] = enrollment_id

        serializer.is_valid(raise_exception=True)

        user_stages = UserMissionStage.objects.filter(
            stage_id=stage_id, user_id=user_id, mission_enrollment_id=enrollment_id
        )

        if user_stages.exists():
            serializer = UserMissionStageSerializer(user_stages.first())
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        serializer.save()

        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)
