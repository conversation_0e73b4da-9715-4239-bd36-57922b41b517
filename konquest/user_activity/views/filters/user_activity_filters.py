from datetime import timedelta

import django_filters
from django.utils import timezone
from rest_framework.filters import BaseFilterBackend
from user_activity.models import MissionEnrollment


class LearningTrailEnrollmentStatusFilterBackend(BaseFilterBackend):
    def filter_queryset(self, request, queryset, view):
        status = request.query_params.get("status")

        if status:
            status = status.split(",")
            return queryset.filter(status__in=status)

        return queryset.filter()


class MissionEnrollmentFilter(django_filters.FilterSet):
    performance__gte = django_filters.NumberFilter(field_name="performance", lookup_expr="gte")
    performance__lte = django_filters.NumberFilter(field_name="performance", lookup_expr="lte")

    start_date__range = django_filters.DateRangeFilter(field_name="start_date")
    start_date__gte = django_filters.DateFilter(field_name="start_date", lookup_expr="gte")
    start_date__lte = django_filters.DateFilter(field_name="start_date", lookup_expr="lte")

    end_date__range = django_filters.DateRangeFilter(field_name="end_date")
    end_date__gte = django_filters.DateFilter(field_name="end_date", lookup_expr="gte")
    end_date__lte = django_filters.DateFilter(field_name="end_date", lookup_expr="lte")

    goal_date__range = django_filters.DateRangeFilter(field_name="goal_date")
    goal_date__gte = django_filters.DateFilter(field_name="goal_date", lookup_expr="gte")
    goal_date__lte = django_filters.DateFilter(field_name="goal_date", lookup_expr="lte")

    status = django_filters.BaseInFilter(field_name="status")
    id = django_filters.BaseInFilter(field_name="id")

    job_position = django_filters.UUIDFilter(field_name="job_position")
    job_function = django_filters.UUIDFilter(field_name="job_position")

    director = django_filters.CharFilter(field_name="director", lookup_expr="icontains")
    manager = django_filters.CharFilter(field_name="manager", lookup_expr="icontains")
    area_of_activity = django_filters.CharFilter(field_name="area_of_activity", lookup_expr="icontains")

    days_late = django_filters.NumberFilter(method="filter_days_late")

    def filter_days_late(self, queryset, name, value):
        today = timezone.now().date()
        cutoff_date = today - timedelta(days=int(value))
        return queryset.filter(goal_date__lte=cutoff_date)

    class Meta:
        model = MissionEnrollment
        fields = [
            "mission",
            "user",
            "mission__user_creator",
            "mission_id",
            "end_date",
            "start_date",
            "give_up",
            "performance",
            "id",
            "goal_date",
        ]
