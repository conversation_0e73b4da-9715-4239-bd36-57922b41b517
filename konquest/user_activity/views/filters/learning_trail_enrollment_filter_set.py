import django_filters
from user_activity.models import LearningTrailEnrollment


class LearningTrailEnrollmentFilterSet(django_filters.FilterSet):
    performance__gte = django_filters.NumberFilter(field_name="performance", lookup_expr="gte")
    performance__lte = django_filters.NumberFilter(field_name="performance", lookup_expr="lte")

    start_date__range = django_filters.DateRangeFilter(field_name="start_date")
    start_date__gte = django_filters.DateFilter(field_name="start_date", lookup_expr="gte")
    start_date__lte = django_filters.DateFilter(field_name="start_date", lookup_expr="lte")

    end_date__range = django_filters.DateRangeFilter(field_name="end_date")
    end_date__gte = django_filters.DateFilter(field_name="end_date", lookup_expr="gte")
    end_date__lte = django_filters.DateFilter(field_name="end_date", lookup_expr="lte")

    status = django_filters.BaseInFilter(field_name="status")

    class Meta:
        model = LearningTrailEnrollment
        fields = [
            "learning_trail",
            "user",
            "learning_trail__user_creator",
            "learning_trail__is_active",
            "learning_trail_id",
            "end_date",
            "start_date",
            "give_up",
            "performance",
        ]
