from authentication import keeps_permissions
from custom import KeepsBadRequestError
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
from rest_framework.response import Response
from user_activity.models import ScormActivity
from user_activity.serializers.scorm_activity_serializer import ScormActivitySerializer


class ScormActivityViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = (
        "user",
        "user__name",
        "enrollment",
        "enrollment__mission",
        "enrollment__mission__name",
        "content",
    )

    search_fields = (
        "user",
        "user__name",
        "enrollment",
    )
    ordering_fields = ("created_date",)
    ordering = ("created_date",)

    permission_classes = (
        keeps_permissions.KeepsSuperAdminPermission
        | keeps_permissions.KeepsPlatformAdminPermission
        | keeps_permissions.KeepsCuratorPermission
        | keeps_permissions.KeepsContentPermission
        | keeps_permissions.KeepsUserPermission
        | keeps_permissions.KeepsInstructorPermission,
    )

    def get_queryset(self):
        return ScormActivity.objects.all()

    def get_serializer_class(self):
        return ScormActivitySerializer

    def create(self, request, *args, **kwargs):
        try:
            instance = ScormActivity.objects.filter(
                user=request.data["user"], content=request.data["content"], enrollment=request.data["enrollment"]
            ).first()
        except Exception as exc:
            raise KeepsBadRequestError(
                detail="To save scorm activities, should inform user, content and enrollment",
                i18n="missing_scorm_activities_field",
            ) from exc

        if instance:
            serializer = self.get_serializer(instance, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)

            if getattr(instance, "_prefetched_objects_cache", None):
                # pylint: disable=W0212
                instance._prefetched_objects_cache = {}

            return Response(serializer.data)

        return super().create(request, *args, **kwargs)
