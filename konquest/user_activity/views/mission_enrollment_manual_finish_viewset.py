from authentication.keeps_permissions import ADMIN_PERMISSIONS
from constants import RULE_CAN_CHANGE_MISSION_ENROLLMENT
from custom.keeps_exception_handler import KeepsNoPermissionToEditLearningTrailEnrollment
from django.forms import model_to_dict
from injector import Provider, inject
from rest_framework import status, viewsets
from rest_framework.response import Response
from rules import test_rule
from user_activity.models import MissionEnrollment
from user_activity.serializers.mission_enrollment_serializer import EnrollmentManualFinishSerializer
from user_activity.services import MissionEnrollmentService
from utils.utils import load_request_user


class MissionEnrollmentManualFinishViewSet(viewsets.ViewSet):
    permission_classes = ADMIN_PERMISSIONS
    serializer_class = EnrollmentManualFinishSerializer

    @inject
    def __init__(self, service: MissionEnrollmentService = Provider[MissionEnrollmentService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    def create(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        enrollment = MissionEnrollment.objects.get(id=self.kwargs.get("pk"))
        if not test_rule(RULE_CAN_CHANGE_MISSION_ENROLLMENT, load_request_user(request), enrollment):
            raise KeepsNoPermissionToEditLearningTrailEnrollment()
        response = self._service.finish_manually(enrollment, serializer.validated_data["performance"])
        return Response(model_to_dict(response), status=status.HTTP_200_OK)
