from authentication.keeps_permissions import MANAGED_MISSION_PERMISSIONS
from injector import Provider, inject
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK
from user_activity.models import MissionEnrollment
from user_activity.services import MissionEnrollmentService


class SyncEnrollmentResendApprovedEmailViewSet(viewsets.GenericViewSet):
    permission_classes = MANAGED_MISSION_PERMISSIONS

    @inject
    def __init__(self, service: MissionEnrollmentService = Provider[MissionEnrollmentService], **kwargs):
        self._service = service
        super().__init__(**kwargs)

    def create(self, request, *args, **kwargs):
        enrollment_id = self.kwargs["pk"]
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        enrollment = MissionEnrollment.objects.get(id=enrollment_id, workspace_id=workspace_id)

        self._service.send_sync_enrollment_approved_email(enrollment)

        return Response(status=HTTP_200_OK)
