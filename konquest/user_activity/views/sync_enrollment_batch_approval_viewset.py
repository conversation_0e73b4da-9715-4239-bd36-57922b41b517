from authentication.keeps_permissions import MANAGED_MISSION_PERMISSIONS
from injector import Provider, inject
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK
from user_activity.serializers.mission_enrollment_serializer import (
    MissionEnrollmentListSerializer,
    SyncEnrollmentBatchApprovalSerializer,
)
from user_activity.services import MissionEnrollmentService


class SyncEnrollmentBatchApprovalViewSet(viewsets.GenericViewSet):
    permission_classes = MANAGED_MISSION_PERMISSIONS
    serializer_class = SyncEnrollmentBatchApprovalSerializer

    @inject
    def __init__(self, service: MissionEnrollmentService = Provider[MissionEnrollmentService], **kwargs):
        self._service = service
        super().__init__(**kwargs)

    def create(self, request, *args, **kwargs):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        approved = serializer.validated_data["approved"]
        mission_id = serializer.validated_data.get("mission_id")
        enrollment_ids = serializer.validated_data.get("enrollment_ids")

        enrollments = self._service.batch_approval_sync(mission_id, enrollment_ids, workspace_id, approved)

        enrollments = MissionEnrollmentListSerializer(enrollments, many=True)
        return Response(data=enrollments.data, status=HTTP_200_OK)
