from authentication import keeps_permissions
from authentication.keeps_permissions import ADMIN_PERMISSIONS, ALL_PERMISSIONS
from constants import REGULATORY_COMPLIANCE_CYCLE
from custom import keeps_exception_handler as keeps_exception
from django_filters.rest_framework import DjangoFilter<PERSON><PERSON>end
from injector import Provider, inject
from learning_trail.models import LearningTrailWorkspace
from rest_framework import status, viewsets
from rest_framework.filters import Ordering<PERSON><PERSON><PERSON>, SearchFilter
from rest_framework.response import Response
from user_activity.application.use_cases.generate_certificate.generate_trail_certificate_use_case import (
    GenerateTrailCertificateUseCase,
)
from user_activity.application.use_cases.list_learning_trail_enrollments.list_learning_trail_enrollments import (
    ListLearningTrailEnrollmentsInputDTO,
    ListLearningTrailEnrollmentsUseCase,
)
from user_activity.application.use_cases.list_learning_trail_enrollments.serializers import (
    ListLearningTrailEnrollmentsSerializer,
)
from user_activity.models import LearningTrailEnrollment
from user_activity.serializers.learning_trail_enrollment_serializer import (
    LearningTrailEnrollment<PERSON>estartSerializer,
    LearningTrailEnrollmentSerializer,
)
from user_activity.services import LearningTrailEnrollmentService
from user_activity.services.learning_trail_batch_enrollment_service import LearningTrailBatchEnrollmentService
from user_activity.services.learning_trail_enroll_service import LearningTrailEnrollService
from user_activity.views.filters.learning_trail_enrollment_filter_set import LearningTrailEnrollmentFilterSet
from user_activity.views.filters.user_activity_filters import LearningTrailEnrollmentStatusFilterBackend
from utils.utils import action_permission, load_request_user, swagger_safe_queryset


class LearningTrailEnrollmentViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter, LearningTrailEnrollmentStatusFilterBackend)
    filterset_class = LearningTrailEnrollmentFilterSet
    search_fields = ("learning_trail__name", "user__name", "user__email")
    ordering_fields = (
        "created_date",
        "user__name",
        "learning_trail__name",
        "start_date",
        "end_date",
        "performance",
        "goal_date",
    )

    permission_classes = (
        keeps_permissions.KeepsSuperAdminPermission
        | keeps_permissions.KeepsPlatformAdminPermission
        | keeps_permissions.KeepsCuratorPermission
        | keeps_permissions.KeepsContentPermission
        | keeps_permissions.KeepsUserPermission
        | keeps_permissions.KeepsInstructorPermission,
    )

    permission_classes_to_delete = (
        keeps_permissions.KeepsSuperAdminPermission
        | keeps_permissions.KeepsPlatformAdminPermission
        | keeps_permissions.KeepsCuratorPermission
        | keeps_permissions.KeepsContentPermission,
    )

    @inject
    def __init__(
        self,
        enrollment_service: LearningTrailEnrollmentService = Provider[LearningTrailEnrollmentService],
        enroll_service: LearningTrailEnrollService = Provider[LearningTrailEnrollService],
        list_learning_trail_enrollments_use_case: ListLearningTrailEnrollmentsUseCase = None,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self._service = enrollment_service
        self._enroll_service = enroll_service
        self.list_enrollments_use_case = list_learning_trail_enrollments_use_case

    @swagger_safe_queryset(LearningTrailEnrollment)
    def get_queryset(self):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        ordering = self.request.query_params.get("ordering")
        input_dto = ListLearningTrailEnrollmentsInputDTO(workspace_id, ordering)
        return self.list_enrollments_use_case.execute(input_dto)

    def get_serializer_class(self):
        if self.request.method == "GET":
            return ListLearningTrailEnrollmentsSerializer

        return LearningTrailEnrollmentSerializer

    def create(self, request, *args, **kwargs):
        """
        Learning Trail Enrollment Create

        Create Learning Trail Enrollment by token user

        ---
            body data:
                'learning_trail': 'learning_trail_id',
                'goal_date': 'goal_date'

            tasks:
                The user will be enrolled in all mission of the Learning Trail
        """
        workspace_id = self.request.user.get("client_id")
        request_user = load_request_user(self.request)
        request.data["user"] = request.data.get("user") or request_user.id
        request.data["workspace"] = workspace_id
        serializer = self.get_serializer_class()(data=request.data)
        serializer.is_valid(raise_exception=True)
        action_user = load_request_user(request)

        regulatory_compliance_cycle_id = None
        if REGULATORY_COMPLIANCE_CYCLE in serializer.validated_data:
            regulatory_compliance_cycle_id = serializer.validated_data.pop(REGULATORY_COMPLIANCE_CYCLE)

        enrollment = self._enroll_service.enroll(
            serializer.validated_data, action_user, regulatory_compliance_cycle_id=regulatory_compliance_cycle_id
        )

        return Response(data=self.get_serializer_class()(enrollment).data, status=201)

    @action_permission(ADMIN_PERMISSIONS)
    def destroy(self, request, *args, **kwargs):
        """
        Learning Trail Enrollment Destroy

        All in progress user mission enrollments linked in the trail enrollment will be deleted
        """
        enrollment_id = self.kwargs.get("pk")

        enrollment = self.get_queryset().get(id=enrollment_id)

        self._service.delete_enrollment(enrollment)
        return Response(status=status.HTTP_204_NO_CONTENT)


class LearningTrailEnrollmentBatchViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides create enrollment batch in many learning trails

    ---
        body:
        {"users": [user_id_list], "learning_trails": [learning_trail_id_list] "goal_date": "YYYY-MM-DD HH:MM:SS"}
    """

    permission_classes = ADMIN_PERMISSIONS

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def create(self, request, *args, **kwargs):
        users = request.data.get("users")
        learning_trail_ids = request.data.get("learning_trails")
        goal_date = request.data.get("goal_date")
        workspace_id = self.request.user.get("client_id")

        learning_trails = (
            LearningTrailWorkspace.objects.filter(learning_trail_id__in=learning_trail_ids, workspace_id=workspace_id)
            .all()
            .values_list("learning_trail_id", flat=True)
        )

        result = LearningTrailBatchEnrollmentService().enroll(
            learning_trail_id_list=learning_trails, workspace_id=workspace_id, user_id_list=users, goal_date=goal_date
        )

        return Response(result, status=status.HTTP_200_OK)

    def get_serializer_class(self):
        return LearningTrailEnrollmentSerializer


class LearningTrailEnrollmentRetakeViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    permission_classes = ALL_PERMISSIONS

    @inject
    def __init__(self, service: LearningTrailEnrollmentService = Provider[LearningTrailEnrollmentService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    def get_serializer_class(self):
        return LearningTrailEnrollmentSerializer

    def create(self, request, *args, **kwargs):
        """
        Learning Trail Enrollment Retake

        ---
            body data:
                'goal_date': 'str'
        """
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        lts = LearningTrailWorkspace.objects.filter(workspace_id=workspace_id).values_list(
            "learning_trail_id", flat=True
        )
        instance = LearningTrailEnrollment.objects.filter(id=self.kwargs.get("pk"), learning_trail_id__in=lts).first()
        serializer = self.get_serializer_class()(data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        new_goal_date = serializer.validated_data["goal_date"]

        if not instance:
            raise keeps_exception.KeepsNotFoundError(
                i18n="learning_trail_enrollment_not_found", detail="Learning Trail Enrollment not found"
            )

        if not instance.give_up:
            raise keeps_exception.KeepsNotFoundError(
                i18n="user_did_not_give_up_on_this_enrollment", detail="User did not give up on this enrollment"
            )

        instance_updated = self._service.retake(instance, new_goal_date)

        serializer = self.get_serializer(instance=instance_updated)
        return Response(serializer.data, status=status.HTTP_200_OK)


class LearningTrailEnrollmentCertificateViewSet(viewsets.ModelViewSet):
    """
    Create a new certificate
    """

    permission_classes = ALL_PERMISSIONS

    @inject
    def __init__(self, use_case: GenerateTrailCertificateUseCase = Provider[GenerateTrailCertificateUseCase], **kwargs):
        super().__init__(**kwargs)
        self.use_case: GenerateTrailCertificateUseCase = use_case

    def create(self, request, *args, **kwargs):
        """
        Generate a new learning trail enrollment certificate
        """
        certificate_url = self.use_case.execute(kwargs["pk"])
        return Response({"certificate_url": certificate_url}, status=status.HTTP_200_OK)


class LearningTrailEnrollmentRestartViewSet(viewsets.ModelViewSet):
    """
    Learning Trail Enrollment Enrollment Restart
    """

    serializer_class = LearningTrailEnrollmentRestartSerializer
    permission_classes = keeps_permissions.ADMIN_PERMISSIONS

    @inject
    def __init__(self, service: LearningTrailEnrollmentService = Provider[LearningTrailEnrollmentService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    def create(self, request, *args, **kwargs):
        """
        Learning Trail Enrollment Enrollment Restart

        Restart an learning trail enrollment enrollment completed or reproved

        ---
            body:
                'goal_date': str, 'YYYY-MM-DD'

            rules:
                - The learning trail enrollment enrollment cannot be in progress, status equal to COMPLETED or GIVE_UP

        """
        enrollment_id = self.kwargs.get("pk")
        workspace_id = self.request.user.get("client_id") if self.request.user else None

        enrollment = LearningTrailEnrollment.objects.get(id=enrollment_id, workspace_id=workspace_id)

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid()
        data = serializer.validated_data

        enrollment = self._service.restart(enrollment=enrollment, **data)

        return Response(data=LearningTrailEnrollmentRestartSerializer(enrollment).data, status=status.HTTP_200_OK)
