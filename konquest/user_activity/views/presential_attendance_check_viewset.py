from authentication.keeps_permissions import ALL_PERMISSIONS
from injector import Provider, inject
from user_activity.models import PresentialAttendance
from user_activity.serializers.sync_attendance_serializer import AttendanceCheckSerializer
from user_activity.services.mission_enrollment_attendance_service import MissionEnrollmentAttendanceService
from user_activity.views.bases.sync_attendance_check_viewset import SyncAttendanceCheckViewSetBase


class PresentialAttendanceCheckViewSet(SyncAttendanceCheckViewSetBase):
    permission_classes = ALL_PERMISSIONS
    serializer_class = AttendanceCheckSerializer

    @inject
    def __init__(
        self, service: MissionEnrollmentAttendanceService = Provider[MissionEnrollmentAttendanceService], **kwargs
    ):
        super().__init__(service, PresentialAttendance, **kwargs)
