from authentication.keeps_permissions import ALL_PERMISSIONS
from constants import RULE_CAN_CHANGE_MISSION_ENROLLMENT
from custom.keeps_exception_handler import KeepsNoPermissionToEditLearningTrailEnrollment
from django.utils.translation import gettext_noop
from injector import Provider, inject
from rest_framework import status, viewsets
from rest_framework.response import Response
from rules import test_rule
from user_activity.models import MissionEnrollment
from user_activity.serializers.mission_enrollment_serializer import MissionEnrollmentSerializer
from user_activity.services import MissionEnrollmentService
from utils.utils import load_request_user

NOT_ALLOWED_TO_MODIFY_THE_ENROLLMENT = gettext_noop("not_allowed_to_modify_the_enrollment")


class SyncEnrollmentFinishViewSet(viewsets.ModelViewSet):
    permission_classes = ALL_PERMISSIONS

    @inject
    def __init__(self, service: MissionEnrollmentService = Provider[MissionEnrollmentService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    def create(self, request, *args, **kwargs):
        enrollment = MissionEnrollment.objects.get(id=self.kwargs.get("pk"))
        if not test_rule(RULE_CAN_CHANGE_MISSION_ENROLLMENT, load_request_user(request), enrollment):
            raise KeepsNoPermissionToEditLearningTrailEnrollment()
        enrollment = self._service.finish_sync(enrollment)
        return Response(data=MissionEnrollmentSerializer(enrollment).data, status=status.HTTP_200_OK)
