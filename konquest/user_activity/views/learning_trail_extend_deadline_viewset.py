from authentication import keeps_permissions
from injector import Provider, inject
from rest_framework import status, viewsets
from rest_framework.response import Response
from user_activity.models import LearningTrailEnrollment
from user_activity.serializers.learning_trail_enrollment_extend_deadline_serializer import (
    TrailEnrollmentExtendDeadlineSerializer,
)
from user_activity.services import LearningTrailEnrollmentService


class TrailEnrollmentExtendDeadlineViewSet(viewsets.ModelViewSet):
    """
    Extend trail enrollment Dead Line (goal-date)
    """

    permission_classes = (keeps_permissions.KeepsPlatformAdminPermission | keeps_permissions.KeepsSuperAdminPermission,)
    serializer_class = TrailEnrollmentExtendDeadlineSerializer

    @inject
    def __init__(self, service: LearningTrailEnrollmentService = Provider[LearningTrailEnrollmentService], **kwargs):
        super().__init__(**kwargs)
        self.__service = service

    def create(self, request, *args, **kwargs):
        enrollment_id = self.kwargs.get("pk")
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        new_goal_date = serializer.validated_data["new_goal_date"]
        enrollment = LearningTrailEnrollment.objects.get(id=enrollment_id, workspace_id=workspace_id)

        enrollment = self.__service.extend_deadline(trail_enrollment=enrollment, new_goal_date=new_goal_date)

        return Response(status=status.HTTP_204_NO_CONTENT)
