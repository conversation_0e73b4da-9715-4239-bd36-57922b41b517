from authentication.keeps_permissions import ALL_PERMISSIONS
from constants import RULE_CAN_CHANGE_LEARNING_TRAIL_ENROLLMENT
from custom.exceptions.not_allowed_to_track_learning_trail_enrollment import NotAllowedToTrackLearningTrailEnrollment
from injector import Provider, inject
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK
from rules import test_rule
from user_activity.dtos.trail_enrollment_tracking import TrailEnrollmentTracking
from user_activity.models import LearningTrailEnrollment
from user_activity.serializers.learning_trail_enrollment_tracking_serializer import (
    LearningTrailEnrollmentTrackingSerializer,
)
from user_activity.services.learning_trail_enrollment_tracking_service import LearningTrailEnrollmentTrackingService
from utils.utils import load_request_user


class LearningTrailEnrollmentTrackingViewSet(viewsets.ModelViewSet):
    """
    LearningTrail Enrollment Tracking
    """

    serializer_class = LearningTrailEnrollmentTrackingSerializer
    permission_classes = ALL_PERMISSIONS
    queryset = TrailEnrollmentTracking

    @inject
    def __init__(
        self,
        service: LearningTrailEnrollmentTrackingService = Provider[LearningTrailEnrollmentTrackingService],
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.service = service

    def list(self, request, *args, **kwargs):
        """
        LearningTrail Enrollment Tracking
        """
        enrollment_id = self.kwargs.get("pk")
        user = load_request_user(self.request)
        enrollment = LearningTrailEnrollment.objects.get(id=enrollment_id)
        if not test_rule(RULE_CAN_CHANGE_LEARNING_TRAIL_ENROLLMENT, user, enrollment):
            raise NotAllowedToTrackLearningTrailEnrollment()

        track = self.service.load(enrollment)
        serializer = self.serializer_class(data=track, many=True)
        serializer.is_valid()
        return Response(data=serializer.data, status=HTTP_200_OK)
