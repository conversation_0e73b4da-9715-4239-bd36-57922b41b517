from datetime import datetime, timed<PERSON>ta

from authentication.keeps_permissions import ALL_PERMISSIONS
from django_filters.rest_framework import DjangoFilterBackend
from mission.models import MissionStageContent
from rest_framework import status, viewsets
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter
from rest_framework.response import Response
from user_activity.models import LearnContentActivity
from user_activity.selectors.mission_enrollment_selector import MissionEnrollmentSelector
from user_activity.serializers.learn_content_activity_serializer import LearnContentActivitySerializer
from user_activity.tasks.user_activity import update_learning_activity


class LearnContentActivityViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = ("user", "user__name", "mission_stage_content", "pulse")
    search_fields = ("user", "user__name", "mission_stage_content", "pulse")
    ordering_fields = ("created_date",)
    ordering = ("created_date",)

    permission_classes = ALL_PERMISSIONS

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._enrollment_selector = MissionEnrollmentSelector()

    def get_queryset(self):
        return LearnContentActivity.objects.all()

    def get_serializer_class(self):
        return LearnContentActivitySerializer

    def partial_update(self, request, *args, **kwargs):
        time_stop = request.data.get("time_stop")
        if time_stop:
            time_stop = datetime.strptime(time_stop, "%Y-%m-%dT%H:%M:%S.%fZ")
            update_learning_activity.delay(
                activity_id=str(kwargs.get("pk")),
                time_stop=time_stop,
                speed=request.data.get("speed", 1),
            )
        return Response(status=status.HTTP_200_OK)

    def create(self, request, *args, **kwargs):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        user_id = self.request.user.get("sub") if self.request.user else None
        mission_stage_content_id = request.data.get("mission_stage_content")

        request.data["workspace"] = workspace_id

        if mission_stage_content_id:
            mission_id = MissionStageContent.objects.get(id=mission_stage_content_id).stage.mission_id
            enrollment = self._enrollment_selector.get_actual_user_enrollment(user_id, mission_id, workspace_id)
            enrollment_id = enrollment.id if enrollment else None
            request.data["mission_enrollment"] = enrollment_id

        response = super().create(request, *args, **kwargs)
        instance = response.data.serializer.instance
        speed = request.data.get("speed", 1)

        if response.data["time_stop"]:
            instance.time_in = (instance.time_stop - instance.time_start) * speed

        else:
            instance.time_in = timedelta(0)
            instance.time_stop = datetime.now()

        instance.save()
        response.data["time_in"] = str(instance.time_in)

        return response
