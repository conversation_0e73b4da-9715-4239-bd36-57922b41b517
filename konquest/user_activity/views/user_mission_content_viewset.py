from authentication import keeps_permissions
from custom import keeps_exception_handler as keeps_exception
from django.core.exceptions import ObjectDoesNotExist
from django.db import IntegrityError
from django_filters.rest_framework import DjangoFilterBackend
from injector import Provider, inject
from mission.models import MissionStageContent
from mission.services.mission_service import MissionService
from rest_framework import status, viewsets
from rest_framework.filters import Ordering<PERSON><PERSON><PERSON>, SearchFilter
from rest_framework.response import Response
from user_activity.models import MissionEnrollment
from user_activity.models.user_mission_content import UserMissionContent
from user_activity.selectors.mission_enrollment_selector import MissionEnrollmentSelector
from user_activity.serializers.user_mission_content_serializer import (
    UserMissionContentListSerializer,
    UserMissionContentSerializer,
)
from utils.utils import swagger_safe_queryset


class UserMissionContentViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoF<PERSON>er<PERSON>ackend, Search<PERSON>ilter, OrderingFilter)
    filterset_fields = ("content", "status")
    search_fields = ("content__name", "status")
    ordering_fields = ("-updated_date",)
    ordering = ("-updated_date",)

    permission_classes = (
        keeps_permissions.KeepsSuperAdminPermission
        | keeps_permissions.KeepsPlatformAdminPermission
        | keeps_permissions.KeepsCuratorPermission
        | keeps_permissions.KeepsContentPermission
        | keeps_permissions.KeepsUserPermission
        | keeps_permissions.KeepsInstructorPermission,
    )

    @inject
    def __init__(
        self,
        mission_service: MissionService = Provider[MissionService],
        mission_enrollment_selector: MissionEnrollmentSelector = Provider[MissionEnrollmentSelector],
        **kwargs,
    ):
        super().__init__(**kwargs)
        self._service = mission_service
        self._enrollment_selector = mission_enrollment_selector

    @swagger_safe_queryset(UserMissionContent)
    def get_queryset(self):
        user_id = self.request.user["sub"] if self.request.user else None
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        mission_enrollment_ids = MissionEnrollment.objects.filter(
            user_id=user_id, workspace_id=workspace_id
        ).values_list("id", flat=True)

        return UserMissionContent.objects.filter(mission_enrollment_id__in=mission_enrollment_ids)

    def get_serializer_class(self):
        return UserMissionContentListSerializer if self.request.method == "GET" else UserMissionContentSerializer

    def create(self, request, *args, **kwargs):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        user_id = self.request.user["sub"]

        serializer = self.get_serializer(data=request.data)
        serializer.initial_data["user"] = self.request.user["sub"]

        content_id = serializer.initial_data["content"]

        # Get enrollment
        try:
            mission_id = MissionStageContent.objects.get(id=content_id).stage.mission_id
        except ObjectDoesNotExist:
            mission_id = None
        enrollment = self._enrollment_selector.get_actual_user_enrollment(user_id, mission_id, workspace_id)
        enrollment_id = enrollment.id if enrollment else None
        serializer.initial_data["mission_enrollment"] = enrollment_id

        user_stage_content = UserMissionContent.objects.filter(
            content_id=content_id, user_id=user_id, mission_enrollment=enrollment_id
        )

        if user_stage_content.exists():
            serializer = UserMissionContentSerializer(user_stage_content.first())
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        serializer.is_valid(raise_exception=True)
        try:
            serializer.save()
        except IntegrityError:
            raise keeps_exception.KeepsBadRequestError(
                detail="User mission content already exists", i18n="user_mission_content_already_exists"
            )

        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)
