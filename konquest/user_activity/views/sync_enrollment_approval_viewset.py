from authentication.keeps_permissions import MANAGED_MISSION_PERMISSIONS
from django.forms import model_to_dict
from injector import Provider, inject
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK
from user_activity.models import MissionEnrollment
from user_activity.serializers.mission_enrollment_serializer import SyncEnrollmentApprovalSerializer
from user_activity.services import MissionEnrollmentService


class SyncEnrollmentApprovalViewSet(viewsets.GenericViewSet):
    permission_classes = MANAGED_MISSION_PERMISSIONS
    serializer_class = SyncEnrollmentApprovalSerializer

    @inject
    def __init__(self, service: MissionEnrollmentService = Provider[MissionEnrollmentService], **kwargs):
        self._service = service
        super().__init__(**kwargs)

    def create(self, request, *args, **kwargs):
        enrollment_id = self.kwargs["pk"]
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        enrollment = MissionEnrollment.objects.get(id=enrollment_id, workspace_id=workspace_id)
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        approved = serializer.validated_data["approved"]

        enrollment = self._service.approval_sync(enrollment, approved)

        return Response(data=model_to_dict(enrollment), status=HTTP_200_OK)
