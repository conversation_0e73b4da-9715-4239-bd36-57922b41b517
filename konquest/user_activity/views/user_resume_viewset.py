from authentication import keeps_permissions
from injector import Provider, inject
from rest_framework.response import Response
from rest_framework.views import APIView
from user_activity.services.user_service import UserService


class UserActivitiesResumeViewSet(APIView):
    """
    A viewset that provides user activities resume
    """

    permission_classes = (
        keeps_permissions.KeepsSuperAdminPermission
        | keeps_permissions.KeepsPlatformAdminPermission
        | keeps_permissions.KeepsCuratorPermission
        | keeps_permissions.KeepsContentPermission
        | keeps_permissions.KeepsUserPermission
        | keeps_permissions.KeepsInstructorPermission,
    )

    @inject
    def __init__(self, service: UserService = Provider[UserService], **kwargs):
        super().__init__(**kwargs)
        self.__user_service = service

    def get(self, request):
        user_id = request.user.get("sub") if request.user else None
        workspace_id = self.request.user.get("client_id")

        user_resume = self.__user_service.get_user_activities_resume(user_id=user_id, workspace_id=workspace_id)

        return Response(user_resume)


class UserMissionEnrollmentResumeViewSet(APIView):
    """
    A viewset that provides user mission enrollments resume
    """

    permission_classes = (
        keeps_permissions.KeepsSuperAdminPermission
        | keeps_permissions.KeepsPlatformAdminPermission
        | keeps_permissions.KeepsCuratorPermission
        | keeps_permissions.KeepsContentPermission
        | keeps_permissions.KeepsUserPermission
        | keeps_permissions.KeepsInstructorPermission,
    )

    @inject
    def __init__(self, service: UserService = Provider[UserService], **kwargs):
        super().__init__(**kwargs)
        self.__user_service: UserService = service

    def get(self, request):
        user_id = request.user.get("sub") if request.user else None
        workspace_id = self.request.user.get("client_id")

        user_resume = self.__user_service.get_user_mission_enrollments_resume(
            user_id=user_id, workspace_id=workspace_id
        )

        return Response(user_resume)
