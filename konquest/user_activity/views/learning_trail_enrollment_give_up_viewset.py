from authentication.keeps_permissions import ALL_PERMISSIONS
from injector import Provider, inject
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.status import HTTP_204_NO_CONTENT
from user_activity.serializers.learning_trail_enrollment_give_up_serializer import (
    LearningTrailEnrollmentGiveUpSerializer,
)
from user_activity.services.learning_trail_enrollment_give_up_service import LearningTrailEnrollmentGiveUpService
from utils.utils import load_request_user


class LearningTrailEnrollmentGiveUpViewSet(viewsets.ViewSet):
    """
    A viewset that provides the standard actions
    """

    permission_classes = ALL_PERMISSIONS
    serliazer_class = LearningTrailEnrollmentGiveUpSerializer

    @inject
    def __init__(
        self, service: LearningTrailEnrollmentGiveUpService = Provider[LearningTrailEnrollmentGiveUpService], **kwargs
    ):
        super().__init__(**kwargs)
        self._service = service

    def create(self, request, *args, **kwargs):
        """
        Learning Trail Enrollment GiveUp

        All user mission enrollments linked to the trail will be updated with the same give_up_comment

        ---
            body data:
                'give_up_comment': 'str'
        """
        user = load_request_user(request)
        serializer = self.serliazer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        comment = serializer.validated_data["give_up_comment"]

        self._service.give_up_enrollment(kwargs["pk"], comment, user)

        return Response(None, status=HTTP_204_NO_CONTENT)
