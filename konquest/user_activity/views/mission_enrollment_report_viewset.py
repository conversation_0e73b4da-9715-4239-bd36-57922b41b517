from authentication.keeps_permissions import MANAGED_MISSION_PERMISSIONS, KeepsBasePermission
from injector import Provider, inject
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK
from user_activity.serializers.sync_attendance_serializer import MissionEnrollmentReportSerializer
from user_activity.services.report_service import MissionEnrollmentReportService
from utils.aws import S3Client


class MissionEnrollmentReportViewSet(viewsets.ModelViewSet):
    permission_classes = MANAGED_MISSION_PERMISSIONS
    serializer_class = MissionEnrollmentReportSerializer

    @inject
    def __init__(
        self,
        service: MissionEnrollmentReportService = Provider[MissionEnrollmentReportService],
        s3_client: S3Client = Provider[S3Client],
        **kwargs,
    ):
        super().__init__(**kwargs)
        self._service = service
        self._kp_permissions = KeepsBasePermission()
        self._s3_client = s3_client

    def create(self, request, *args, **kwargs):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        serializer = self.serializer_class(data=self.request.data)
        serializer.is_valid(raise_exception=True)
        mission_id = serializer.validated_data["mission_id"]
        report_url = self._service.generate_mission_enrollments(mission_id, workspace_id)

        return Response(data=report_url, status=HTTP_200_OK)
