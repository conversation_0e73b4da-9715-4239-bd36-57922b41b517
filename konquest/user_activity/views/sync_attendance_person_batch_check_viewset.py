from authentication.keeps_permissions import MANAGED_MISSION_PERMISSIONS, KeepsBasePermission
from injector import inject
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK
from user_activity.serializers.person_serializer import AttendanceCheckPersonSerializer
from user_activity.tasks.check_in_batch_force_task import check_in_batch_force


class SyncAttendancePersonBatchCheckViewSet(viewsets.ModelViewSet):
    permission_classes = MANAGED_MISSION_PERMISSIONS
    serializer_class = AttendanceCheckPersonSerializer

    @inject
    def __init__(
        self,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self._kp_permission = KeepsBasePermission()

    def create(self, request, *args, **kwargs):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        user_id = self.request.user.get("sub")
        token = self.request.META.get("HTTP_AUTHORIZATION")
        role = self._kp_permission.get_priority_user_role_for_sync_missions(token, workspace_id)
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        persons = serializer.validated_data["persons"]
        date_id = serializer.validated_data["date_id"]
        check_in_batch_force.delay(persons, date_id, role, user_id, workspace_id, token)

        return Response(status=HTTP_200_OK)
