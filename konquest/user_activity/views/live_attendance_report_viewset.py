from injector import Provider, inject
from mission.models.mission import LIVE
from user_activity.services.mission_enrollment_attendance_service import MissionEnrollmentAttendanceService
from user_activity.views.bases.sync_attendance_report_viewset_base import SyncAttendanceReportViewSetBase
from utils.aws import S3Client


class LiveAttendanceReportViewSet(SyncAttendanceReportViewSetBase):
    @inject
    def __init__(
        self,
        service: MissionEnrollmentAttendanceService = Provider[MissionEnrollmentAttendanceService],
        s3_client: S3Client = Provider[S3Client],
        **kwargs,
    ):
        super().__init__(service, s3_client, LIVE, **kwargs)
