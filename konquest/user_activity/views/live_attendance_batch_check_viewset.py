from authentication.keeps_permissions import KeepsBasePermission
from injector import Provider, inject
from user_activity.models import LiveAttendance
from user_activity.services.mission_enrollment_attendance_service import MissionEnrollmentAttendanceService
from user_activity.views.bases.sync_attendance_batch_check_viewset_base import SyncAttendanceBatchCheckViewSetBase


class LiveAttendanceBatchCheckViewSet(SyncAttendanceBatchCheckViewSetBase):
    @inject
    def __init__(
        self, service: MissionEnrollmentAttendanceService = Provider[MissionEnrollmentAttendanceService], **kwargs
    ):
        super().__init__(service, LiveAttendance, **kwargs)
        self._kp_permission = KeepsBasePermission()
