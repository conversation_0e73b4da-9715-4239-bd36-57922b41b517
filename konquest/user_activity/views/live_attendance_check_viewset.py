from injector import Provider, inject
from user_activity.models import LiveAttendance
from user_activity.services.mission_enrollment_attendance_service import MissionEnrollmentAttendanceService
from user_activity.views.bases.sync_attendance_check_viewset import SyncAttendanceCheckViewSetBase


class LiveAttendanceCheckViewSet(SyncAttendanceCheckViewSetBase):
    @inject
    def __init__(
        self, service: MissionEnrollmentAttendanceService = Provider[MissionEnrollmentAttendanceService], **kwargs
    ):
        super().__init__(service=service, model=LiveAttendance, **kwargs)
