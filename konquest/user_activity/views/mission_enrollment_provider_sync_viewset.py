from authentication import keeps_permissions
from injector import Provider, inject
from rest_framework import viewsets
from rest_framework.response import Response
from user_activity.dtos.mission_enrollment_provider_dto import MissionEnrollmentProviderDto
from user_activity.serializers.mission_enrollment_provider_sync_serializer import (
    MissionEnrollmentProviderSyncSerializer,
)
from user_activity.services.mission_enrollment_provider_sync_service import (
    MissionEnrollmentProviderSyncService,
)
from user_activity.tasks.mission_enrollment_provider_sync import mission_enrollment_provider_sync


class MissionEnrollmentProviderSyncViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    permission_classes = (keeps_permissions.KeepsIntegrationPermission,)
    serializer_class = MissionEnrollmentProviderSyncSerializer

    @inject
    def __init__(
        self, service: MissionEnrollmentProviderSyncService = Provider[MissionEnrollmentProviderSyncService], **kwargs
    ):
        super().__init__(**kwargs)
        self._service = service

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer_class()(data=request.data, many=True)
        serializer.is_valid(raise_exception=True)
        enrollments = [MissionEnrollmentProviderDto(**enrollment) for enrollment in serializer.validated_data]
        mission_enrollment_provider_sync.delay(enrollments)
        return Response(status=200)
