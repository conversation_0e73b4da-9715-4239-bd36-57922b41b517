from authentication.keeps_permissions import MANAGED_PERMISSIONS
from constants import REG<PERSON><PERSON><PERSON>RY_COMPLIANCE_CYCLE
from injector import Provider, inject
from rest_framework import status, viewsets
from rest_framework.response import Response
from user_activity.serializers.learning_trail_batch_enrollment_input import LearningTrailBatchEnrollmentInputSerializer
from user_activity.services import LearningTrailBatchEnrollmentService
from user_activity.tasks.learning_trail_enrollment_task import enroll_users


class LearningTrailBatchEnrollmentViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides create enrollment batch in many learning trails

    ---
        body:
        {"users": [user_id_list], "learning_trails": [learning_trail_id_list] "goal_date": "YYYY-MM-DD HH:MM:SS"}
    """

    permission_classes = MANAGED_PERMISSIONS
    serializer_class = LearningTrailBatchEnrollmentInputSerializer

    @inject
    def __init__(
        self, service: LearningTrailBatchEnrollmentService = Provider[LearningTrailBatchEnrollmentService], **kwargs
    ):
        super().__init__(**kwargs)
        self.service = service

    def create(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data

        users = data.get("users")
        learning_trail_ids = data.get("learning_trails")
        goal_date = data.get("goal_date")
        required = data.get("required")
        workspace_id = self.request.user.get("client_id")
        regulatory_compliance_cycle_id = data.get(REGULATORY_COMPLIANCE_CYCLE)

        enroll_users.delay(
            trails=learning_trail_ids,
            user_ids=users,
            workspace_id=workspace_id,
            goal_date=goal_date,
            regulatory_compliance_cycle_id=regulatory_compliance_cycle_id,
            required=required
        )

        return Response(None, status=status.HTTP_202_ACCEPTED)
