from typing import Union

from authentication.keeps_permissions import ALL_PERMISSIONS, KeepsBasePermission
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK
from user_activity.models import LiveAttendance, PresentialAttendance
from user_activity.serializers.sync_attendance_serializer import AttendanceCheckSerializer, LiveAttendanceSerializer
from user_activity.services.mission_enrollment_attendance_service import MissionEnrollmentAttendanceService


class SyncAttendanceCheckViewSetBase(viewsets.ModelViewSet):
    permission_classes = ALL_PERMISSIONS
    serializer_class = AttendanceCheckSerializer

    def __init__(
        self,
        service: MissionEnrollmentAttendanceService,
        model: Union[type(LiveAttendance), type(PresentialAttendance)],
        **kwargs,
    ):
        super().__init__(**kwargs)
        self._service = service
        self._kp_permission = KeepsBasePermission()
        self._model = model

    def create(self, request, *args, **kwargs):
        attendance_id = kwargs["pk"]
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        user_id = self.request.user.get("sub")
        token = self.request.META.get("HTTP_AUTHORIZATION")
        role = self._kp_permission.get_priority_user_role_for_sync_missions(token, workspace_id)
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        presented = serializer.validated_data["presented"]

        attendance = self._model.objects.get(id=attendance_id)

        attendance = self._service.check(attendance, presented, role, user_id, workspace_id)

        return Response(data=LiveAttendanceSerializer(attendance).data, status=HTTP_200_OK)
