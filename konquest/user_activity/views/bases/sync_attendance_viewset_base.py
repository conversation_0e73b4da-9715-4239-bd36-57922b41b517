from authentication.keeps_permissions import ALL_PERMISSIONS, KeepsBasePermission
from django_filters.rest_framework import DjangoFilter<PERSON>ackend
from mission.models.mission import LIVE, PRESENTIAL
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
from user_activity.models import LiveAttendance
from user_activity.models.presential_attendance import PresentialAttendance
from user_activity.selectors.sync_attendance_selector import SyncAttendanceSelector


class SyncAttendanceViewSetBase(viewsets.ModelViewSet):
    filter_backends = (
        DjangoFilterBackend,
        SearchFilter,
        OrderingFilter,
    )
    search_fields = (
        "enrollment__user__name",
        "enrollment__user__email",
    )
    ordering_fields = (
        "enrollment__user__name",
        "date__start_at",
    )
    ordering = "enrollment__user__name"
    permission_classes = ALL_PERMISSIONS

    def __init__(self, selector: SyncAttendanceSelector, mission_model: str, **kwargs):
        super().__init__(**kwargs)
        self._service = selector
        self._kp_permissions = KeepsBasePermission()
        self._mission_model = mission_model

        if self._mission_model not in [LIVE, PRESENTIAL]:
            raise ValueError(f"Isn't possible manager attendances for {self._mission_model} mission model")

    def get_queryset(self):
        if getattr(self, "swagger_fake_view", False):
            models = {LIVE: LiveAttendance, PRESENTIAL: PresentialAttendance}
            return models[self._mission_model].objects.none()
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        user_id = self.request.user.get("sub")
        token = self.request.META.get("HTTP_AUTHORIZATION")
        role = self._kp_permissions.get_priority_user_role_for_sync_missions(token, workspace_id)
        return self._service.get_allowed_attendances(user_id, workspace_id, self._mission_model, role)
