from abc import ABC
from typing import Union

from authentication.keeps_permissions import ALL_PERMISSIONS, KeepsBasePermission
from custom.keeps_exception_handler import KeepsNotFoundError
from django.utils.translation import gettext as _
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK
from user_activity.models import LiveAttendance, PresentialAttendance
from user_activity.serializers.sync_attendance_serializer import AttendanceBatchCheckSerializer
from user_activity.services.mission_enrollment_attendance_service import MissionEnrollmentAttendanceService


class SyncAttendanceBatchCheckViewSetBase(viewsets.ModelViewSet, ABC):
    permission_classes = ALL_PERMISSIONS
    serializer_class = AttendanceBatchCheckSerializer

    def __init__(
        self,
        service: MissionEnrollmentAttendanceService,
        model: Union[type(LiveAttendance), type(PresentialAttendance)],
        **kwargs,
    ):
        super().__init__(**kwargs)
        self._service = service
        self._kp_permission = KeepsBasePermission()
        self._model = model
        if self._model not in [LiveAttendance, PresentialAttendance]:
            raise RuntimeError(f"{self._model} is invalid SyncAttendance type")

    def create(self, request, *args, **kwargs):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        user_id = self.request.user.get("sub")
        token = self.request.META.get("HTTP_AUTHORIZATION")
        role = self._kp_permission.get_priority_user_role_for_sync_missions(token, workspace_id)
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        presented = serializer.validated_data["presented"]
        attendance_ids = serializer.validated_data["attendance_ids"]

        attendances = self._model.objects.filter(id__in=attendance_ids)
        if not attendances:
            raise KeepsNotFoundError(i18n="not_found", detail=_("not_found_attendances"))

        self._service.check_in_batch(attendances, presented, role, user_id, workspace_id)

        return Response(status=HTTP_200_OK)
