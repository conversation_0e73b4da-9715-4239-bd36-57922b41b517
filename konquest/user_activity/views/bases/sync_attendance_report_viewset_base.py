from authentication.keeps_permissions import MANAGED_PERMISSIONS, KeepsBasePermission
from mission.models.mission import LIVE, PRESENTIAL
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK
from user_activity.serializers.sync_attendance_serializer import AttendanceReportSerializer
from user_activity.services.mission_enrollment_attendance_service import MissionEnrollmentAttendanceService
from utils.aws import S3Client


class SyncAttendanceReportViewSetBase(viewsets.ModelViewSet):
    permission_classes = MANAGED_PERMISSIONS
    serializer_class = AttendanceReportSerializer

    def __init__(self, service: MissionEnrollmentAttendanceService, s3_client: S3Client, mission_model: str, **kwargs):
        super().__init__(**kwargs)
        self._kp_permissions = KeepsBasePermission()
        self._s3_client = s3_client
        self._service = service
        self._mission_model = mission_model
        if self._mission_model not in [LIVE, PRESENTIAL]:
            raise ValueError(f"Isn't possible generate attendance report to {self._mission_model} mission model")

    def create(self, request, *args, **kwargs):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        user_id = self.request.user.get("sub")
        token = self.request.META.get("HTTP_AUTHORIZATION")
        role = self._kp_permissions.get_priority_user_role_for_sync_missions(token, workspace_id)
        serializer = self.serializer_class(data=self.request.data)
        serializer.is_valid(raise_exception=True)
        mission_id = serializer.validated_data["mission_id"]
        report_url = self._service.generate_report(mission_id, user_id, workspace_id, self._mission_model, role)

        return Response(data=report_url, status=HTTP_200_OK)
