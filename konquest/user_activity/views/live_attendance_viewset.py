from injector import Provider, inject
from mission.models.mission import LIVE
from user_activity.selectors.sync_attendance_selector import SyncAttendanceSelector
from user_activity.serializers.sync_attendance_serializer import LiveAttendanceListSerializer
from user_activity.views.bases.sync_attendance_viewset_base import SyncAttendanceViewSetBase


class LiveAttendanceViewSet(SyncAttendanceViewSetBase):
    filterset_fields = (
        "date_id",
        "date__live_id",
        "enrollment_id",
        "presented",
        "date__live__mission_id",
        "date__allow_self_attendance",
    )

    @inject
    def __init__(self, selector: SyncAttendanceSelector = Provider[SyncAttendanceSelector], **kwargs):
        super().__init__(selector, LIVE, **kwargs)

    def get_serializer_class(self):
        return LiveAttendanceListSerializer
