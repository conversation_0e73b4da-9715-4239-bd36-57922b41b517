from injector import Provider, inject
from mission.models.mission import PRESENTI<PERSON>
from user_activity.selectors.sync_attendance_selector import SyncAttendanceSelector
from user_activity.serializers.sync_attendance_serializer import PresentialAttendanceListSerializer
from user_activity.views.bases.sync_attendance_viewset_base import SyncAttendanceViewSetBase


class PresentialAttendanceViewSet(SyncAttendanceViewSetBase):
    filterset_fields = (
        "date_id",
        "enrollment_id",
        "presented",
        "date__presential_id",
        "date__presential__mission_id",
        "date__allow_self_attendance",
    )

    @inject
    def __init__(self, selector: SyncAttendanceSelector = Provider[SyncAttendanceSelector], **kwargs):
        super().__init__(selector, PRESENTIAL, **kwargs)

    def get_serializer_class(self):
        return PresentialAttendanceListSerializer
