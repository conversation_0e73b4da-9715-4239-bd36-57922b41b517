from constants import ENROLLMENT_COMPLETED, ENROLLMENT_REPROVED
from django.db import models
from learning_trail.models.learning_trail import LearningTrail
from user_activity.models.enrollment import Enrollment
from user_activity.models.enums.enrollment_status_enum import EnrollmentStatusEnum


class LearningTrailEnrollment(Enrollment):
    learning_trail = models.ForeignKey(LearningTrail, verbose_name="Learning Trail", on_delete=models.CASCADE)
    status = models.CharField(
        verbose_name="Status",
        max_length=20,
        choices=EnrollmentStatusEnum.trail_choices(),
        default=EnrollmentStatusEnum.ENROLLED.value,
    )

    @property
    def is_done(self):
        return self.status in [ENROLLMENT_COMPLETED, ENROLLMENT_REPROVED]

    class Meta:
        verbose_name_plural = "Learning Trail Enrollments"
        db_table = "learning_trail_enrollment"
