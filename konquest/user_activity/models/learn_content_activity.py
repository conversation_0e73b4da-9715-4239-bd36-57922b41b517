import uuid
from datetime import <PERSON><PERSON><PERSON>

from account.models import User, Workspace
from django.db import models
from django.db.models import Sum
from mission.models import MissionStageContent
from pulse.models import Pulse
from user_activity.models.mission_enrollment import MissionEnrollment
from utils.models import BaseModel

ACTIONS = (("WATCH", "WATCH"), ("LISTEN", "LISTEN"), ("READ", "READ"), ("VIEW", "VIEW"))


class LearnContentActivity(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    user = models.ForeignKey(User, verbose_name="Stage", on_delete=models.SET_NULL, null=True, blank=True)
    action = models.CharField(verbose_name="Action", max_length=20, choices=ACTIONS, null=False, blank=False)

    mission_stage_content = models.ForeignKey(
        MissionStageContent, verbose_name="Mission Stage Content", on_delete=models.SET_NULL, null=True, blank=True
    )
    mission_enrollment = models.ForeignKey(
        MissionEnrollment, verbose_name="Mission Enrollment", on_delete=models.SET_NULL, null=True, blank=True
    )

    pulse = models.ForeignKey(Pulse, verbose_name="Pulse", on_delete=models.SET_NULL, null=True, blank=True)

    workspace = models.ForeignKey(Workspace, verbose_name="Workspace", on_delete=models.SET_NULL, null=True)

    time_start = models.DateTimeField(verbose_name="Time Start")
    time_stop = models.DateTimeField(verbose_name="Time Stop", null=True, blank=True)

    time_in = models.DurationField(verbose_name="Time consuming content", null=True, blank=True)

    @property
    def total_pulse_consumption(self):
        if not self.pulse or not self.pulse.duration_time or not self.time_in:
            return 0
        time_in: timedelta = LearnContentActivity.objects.filter(pulse=self.pulse, user=self.user).aggregate(
            Sum("time_in")
        )["time_in__sum"]
        total_seconds = round(time_in.total_seconds())
        return min(round(total_seconds / self.pulse.duration_time, 2), 1)

    class Meta:
        verbose_name_plural = "User Learn Content Activities"
        db_table = "learn_content_activity"
