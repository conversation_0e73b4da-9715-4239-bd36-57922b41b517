import uuid

from account.models import User
from django.db import models
from django.utils.translation import gettext as _
from mission.models import MissionStageContent
from user_activity.models import MissionEnrollment
from utils.models import BaseModel

STARTED = "STARTED"
COMPLETED = "COMPLETED"

STATUS_USER_STAGE_CONTENT = (
    (STARTED, STARTED),
    (COMPLETED, COMPLETED),
)


class UserMissionContent(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    content = models.ForeignKey(MissionStageContent, verbose_name="Stage Content", on_delete=models.CASCADE)
    user = models.ForeignKey(User, verbose_name="User", on_delete=models.PROTECT)
    mission_enrollment = models.ForeignKey(
        MissionEnrollment, verbose_name="Mission Enrollment", on_delete=models.CASCADE, null=True
    )
    status = models.Char<PERSON>ield(verbose_name="Status", max_length=20, choices=STATUS_USER_STAGE_CONTENT, default=STARTED)

    class Meta:
        unique_together = ("content", "user", "mission_enrollment")
        verbose_name_plural = _("User Mission Contents")
        db_table = "user_mission_content"
