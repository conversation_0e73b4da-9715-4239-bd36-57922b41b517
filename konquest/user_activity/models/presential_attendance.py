import uuid

from django.db import models
from mission.models import PresentialMissionDates
from utils.models import BaseModel


class PresentialAttendance(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    date = models.ForeignKey(PresentialMissionDates, verbose_name="Presential Mission Date", on_delete=models.CASCADE)
    presented = models.BooleanField(verbose_name="Presented", blank=True, null=True)
    enrollment = models.ForeignKey("MissionEnrollment", verbose_name="Mission Enrollment", on_delete=models.CASCADE)

    class Meta:
        unique_together = ("date", "enrollment")
        verbose_name_plural = "Presential Attendance"
        db_table = "presential_attendance"
