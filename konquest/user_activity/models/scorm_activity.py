import uuid

from account.models import User
from django.db import models
from mission.models import MissionStage<PERSON>ontent
from user_activity.models import MissionEnrollment
from utils.models import BaseModel


class ScormActivity(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, verbose_name="Stage", on_delete=models.SET_NULL, null=True, blank=True)
    enrollment = models.ForeignKey(
        MissionEnrollment, verbose_name="Enrollment", on_delete=models.SET_NULL, null=True, blank=True
    )
    content = models.ForeignKey(
        MissionStageContent, verbose_name="Mission Stage Content", on_delete=models.SET_NULL, null=True, blank=True
    )

    cmi = models.JSONField()

    class Meta:
        verbose_name_plural = "Scorm Mission Activities"
        db_table = "scorm_activity"
        unique_together = ("enrollment", "user", "content")
