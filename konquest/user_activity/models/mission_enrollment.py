from typing import Optional

from django.db import models
from django.utils.translation import gettext_noop
from mission.models import Mission
from user_activity.models.enrollment import Enrollment

ENROLLED = gettext_noop("ENROLLED")
STARTED = gettext_noop("STARTED")
INACTIVATED = gettext_noop("INACTIVATED")
COMPLETED = gettext_noop("COMPLETED")
PENDING_VALIDATION = gettext_noop("PENDING_VALIDATION")
REPROVED = gettext_noop("REPROVED")
REFUSED = gettext_noop("REFUSED")
EXPIRED = gettext_noop("EXPIRED")
GIVE_UP = gettext_noop("GIVE_UP")
REQUEST_EXTENSION = gettext_noop("REQUEST_EXTENSION")
WAITING_APPROVAL = gettext_noop("WAITING_APPROVAL")
WAITING_VACANCIES = gettext_noop("WAITING_VACANCIES")

STATUS_IN_PROGRESS = [
    GIVE_UP,
    ENR<PERSON>LED,
    STARTED,
    PENDING_VALIDATION,
    REFUSED,
    REQUEST_EXTENSION,
    WAITING_APPROVAL,
    WAITING_VACANCIES,
]

SYNC_STATUS_ACCEPTS = [STARTED, ENROLLED, WAITING_APPROVAL]
SYNC_STATUS_APPROVED = [STARTED, ENROLLED]
STATUS_DONE = [COMPLETED, REPROVED]

STATUS_MISSION_ENROLLMENT = (
    (INACTIVATED, INACTIVATED),
    (ENROLLED, ENROLLED),
    (STARTED, STARTED),
    (COMPLETED, COMPLETED),
    (PENDING_VALIDATION, PENDING_VALIDATION),
    (REPROVED, REPROVED),
    (REFUSED, REFUSED),
    (EXPIRED, EXPIRED),
    (REQUEST_EXTENSION, REQUEST_EXTENSION),
    (WAITING_APPROVAL, WAITING_APPROVAL),
    (WAITING_VACANCIES, WAITING_VACANCIES),
    (GIVE_UP, GIVE_UP),
)


class MissionEnrollment(Enrollment):
    mission = models.ForeignKey(Mission, verbose_name="Mission", on_delete=models.CASCADE)
    certificate_provider_url = models.TextField(verbose_name="Certificate Provider", null=True, blank=True)
    approve_msg = models.TextField(verbose_name="Approve Message", null=True, blank=True)
    learning_trail_enrollment = models.ForeignKey(
        "user_activity.LearningTrailEnrollment",
        verbose_name="Learning Trail Enrollment",
        on_delete=models.SET_NULL,
        null=True,
    )

    total_mission_questions = models.IntegerField(verbose_name="Total mission questions", null=True, blank=True)
    total_correct_answers = models.IntegerField(verbose_name="Total correct answers", null=True, blank=True)
    enrolled_count = models.IntegerField(verbose_name="Enrolled Count", default=1)

    @property
    def in_progress(self) -> bool:
        return self.status in STATUS_IN_PROGRESS

    @property
    def evaluated(self) -> bool:
        return self.evaluation.exists()

    @property
    def actual_stage(self) -> Optional[str]:
        last_user_stage_consumed = self.usermissionstage_set.filter(status="COMPLETED").order_by("stage__order").first()
        return last_user_stage_consumed.stage.name if last_user_stage_consumed else None

    @property
    def is_done(self):
        return self.status in STATUS_DONE

    class Meta:
        verbose_name_plural = "Missions Enrollments"
        db_table = "mission_enrollment"
