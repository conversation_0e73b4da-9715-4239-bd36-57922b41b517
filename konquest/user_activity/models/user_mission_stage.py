import uuid

from account.models import User
from django.db import models
from django.utils.translation import gettext as _
from mission.models import MissionStage
from user_activity.models import MissionEnrollment
from utils.models import BaseModel

STARTED = "STARTED"
COMPLETED = "COMPLETED"

STATUS_USER_STAGE = (
    (STARTED, STARTED),
    (COMPLETED, COMPLETED),
)


class UserMissionStage(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    stage = models.ForeignKey(MissionStage, verbose_name="Stage", on_delete=models.CASCADE)
    user = models.ForeignKey(User, verbose_name="User", on_delete=models.PROTECT)
    mission_enrollment = models.ForeignKey(
        MissionEnrollment, verbose_name="Mission Enrollment", on_delete=models.CASCADE, null=True
    )
    status = models.CharField(verbose_name="Status", max_length=20, choices=STATUS_USER_STAGE, default=STARTED)

    class Meta:
        verbose_name_plural = _("User Mission Stages")
        db_table = "user_mission_stage"
