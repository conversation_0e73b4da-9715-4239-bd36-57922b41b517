import uuid

from account.models import User, Workspace
from django.core.validators import Max<PERSON><PERSON>ueValida<PERSON>, MinValueValidator
from django.db import models
from user_activity.models.enums.enrollment_status_enum import EnrollmentStatusEnum
from utils.models import BaseModel


class Enrollment(BaseModel):
    status_enum = EnrollmentStatusEnum

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, verbose_name="User", on_delete=models.PROTECT)
    workspace = models.ForeignKey(Workspace, verbose_name="Workspace", on_delete=models.CASCADE, null=True)

    points = models.FloatField(verbose_name="Points", null=True, blank=True)
    performance = models.FloatField(verbose_name="Performance", null=True, blank=True)
    start_date = models.DateTimeField(verbose_name="Start Date", auto_now_add=True, null=True, blank=True)
    end_date = models.DateTimeField(verbose_name="End Date", blank=True, null=True)
    goal_date = models.DateField(verbose_name="Goal Date", blank=False, null=False)
    give_up = models.BooleanField(verbose_name="Give up", default=False, blank=True)
    give_up_comment = models.TextField(verbose_name="Give up Comment", blank=True, null=True)

    status = models.CharField(
        verbose_name="Status", max_length=20, choices=status_enum.choices(), default=status_enum.ENROLLED.value
    )
    required = models.BooleanField(verbose_name="Required", default=False)
    normative = models.BooleanField(verbose_name="Normative", default=False)

    progress = models.FloatField(
        verbose_name="Progress",
        null=True,
        blank=True,
        default=0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
    )
    certificate_url = models.TextField(verbose_name="Certificate", null=True, blank=True)
    assessment_type = models.CharField(verbose_name="Assessment Type", max_length=7, null=True, blank=True)

    class Meta:
        abstract = True
