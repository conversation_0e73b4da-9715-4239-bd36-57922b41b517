import uuid

from django.db import models
from mission.models import LiveMissionDates
from utils.models import BaseModel


class LiveAttendance(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    date = models.ForeignKey(LiveMissionDates, verbose_name="Live Mission Date", on_delete=models.CASCADE)
    presented = models.BooleanField(verbose_name="Presented", blank=True, null=True)
    enrollment = models.ForeignKey("MissionEnrollment", verbose_name="Mission Enrollment", on_delete=models.CASCADE)

    class Meta:
        unique_together = ("date", "enrollment")
        verbose_name_plural = "Live Attendance"
        db_table = "live_attendance"
