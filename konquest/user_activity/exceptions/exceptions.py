from custom.keeps_exception_handler import KeepsServiceError
from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop

USER_MISSION_ENROLLMENT_IN_PROGRESS_ALREADY_EXISTS = gettext_noop("user_mission_enrollment_in_progress_already_exists")


class MissionEnrollmentInProgressAlreadyExists(KeepsServiceError):
    def __init__(self):
        super().__init__(
            USER_MISSION_ENROLLMENT_IN_PROGRESS_ALREADY_EXISTS, _(USER_MISSION_ENROLLMENT_IN_PROGRESS_ALREADY_EXISTS)
        )
