from unittest.mock import MagicMock, <PERSON>ck

import mock
from custom.message_connection_service import Message<PERSON>onnectionService
from django.test import TestCase
from rest_clients.regulatory_compliance_client_abc import RegulatoryComplianceClientABC
from user_activity.dtos.regulatory_compliance_cycle_enrollment import EnrollmentCycleCreateDTO
from user_activity.services.regulatory_compliance_service import RegulatoryComplianceService


class TestRegulatoryComplianceService(TestCase):
    @mock.patch("pika.BlockingConnection.__init__", return_value=None)
    def setUp(self, blocking_connection_constructor: mock.MagicMock):
        self.mock_client = Mock(spec=RegulatoryComplianceClientABC)
        self.message_broker_url = "mock_url"
        self.regulatory_compliance_queue = "mock_queue"
        self.new_enrollment_cycle_task_name = "mock_task_name"

    def test_create_enrollment_cycle(self):
        message_connection_service = MagicMock(spec=MessageConnectionService)
        self.service = RegulatoryComplianceService(
            message_connection_service=message_connection_service,
            new_enrollment_cycle_task_name=self.new_enrollment_cycle_task_name,
        )

        enrollment = EnrollmentCycleCreateDTO(enrollment_id="test_enrollment", cycle_id="test_cycle")

        self.service.create_enrollment_cycle(enrollment)

        message_connection_service.send.assert_called_with(enrollment.__dict__, self.new_enrollment_cycle_task_name)
