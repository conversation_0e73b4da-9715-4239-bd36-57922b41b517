from constants import ENROLLMENT_COMPLETED, ENROLLMENT_REPROVED
from django.test import TestCase
from mission.models import LiveMission, LiveMissionDates, Mission, PresentialMission, PresentialMissionDates
from mission.models.mission import LIVE, PRESENTIAL
from model_mommy import mommy
from user_activity.models import LiveAttendance, MissionEnrollment, PresentialAttendance
from user_activity.module import ATTENDANCE_MODELS_MAP
from user_activity.services.mission_enrollment_close_service import MissionEnrollmentCloseService


class TestMissionEnrollmentCloseService(TestCase):
    def setUp(self):
        self.service = MissionEnrollmentCloseService(ATTENDANCE_MODELS_MAP)

    def test_complete_presential_enrollment_with_full_attendance(self):
        mission = mommy.make(Mission, mission_model=PRESENTIAL)
        presential_mission = mommy.make(PresentialMission, mission=mission)
        enrollment = mommy.make(MissionEnrollment, mission=mission)
        mission_date = mommy.make(PresentialMissionDates, presential=presential_mission)
        mommy.make(PresentialAttendance, date=mission_date, enrollment=enrollment, presented=True)

        enrollment = self.service.close_enrollment(enrollment)

        self.assertEqual(enrollment.status, ENROLLMENT_COMPLETED)

    def test_reprove_presential_enrollment_without_attendance(self):
        mission = mommy.make(Mission, mission_model=PRESENTIAL)
        presential_mission = mommy.make(PresentialMission, mission=mission)
        enrollment = mommy.make(MissionEnrollment, mission=mission)
        mommy.make(PresentialMissionDates, presential=presential_mission)

        enrollment = self.service.close_enrollment(enrollment)

        self.assertEqual(enrollment.status, ENROLLMENT_REPROVED)

    def test_complete_live_enrollment_with_full_attendance(self):
        mission = mommy.make(Mission, mission_model=LIVE)
        live_mission = mommy.make(LiveMission, mission=mission)
        enrollment = mommy.make(MissionEnrollment, mission=mission)
        mission_date = mommy.make(LiveMissionDates, live=live_mission)
        mommy.make(LiveAttendance, date=mission_date, enrollment=enrollment, presented=True)

        enrollment = self.service.close_enrollment(enrollment)

        self.assertEqual(enrollment.status, ENROLLMENT_COMPLETED)

    def test_reprove_live_enrollment_without_attendance(self):
        mission = mommy.make(Mission, mission_model=LIVE)
        live_mission = mommy.make(LiveMission, mission=mission)
        enrollment = mommy.make(MissionEnrollment, mission=mission)
        mommy.make(LiveMissionDates, live=live_mission)

        enrollment = self.service.close_enrollment(enrollment)

        self.assertEqual(enrollment.status, ENROLLMENT_REPROVED)
