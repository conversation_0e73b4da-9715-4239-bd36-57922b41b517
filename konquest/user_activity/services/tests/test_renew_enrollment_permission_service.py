from unittest.mock import MagicMock, patch

import pytest
from account.models import Workspace
from custom.keeps_exception_handler import (
    NOT_PERMISSION_TO_RENEW_ENROLLMENT_BLOCKED_BY_WORKSPACE,
    KeepsNoPermissionToRenewEnrollment,
)
from django.test import TestCase
from django.utils.translation import gettext as _
from mission.models import Mission
from user_activity.models import MissionEnrollment
from user_activity.models.mission_enrollment import REPROVED
from user_activity.services.rewew_enrollment.renew_enrollment_permission_service import RenewEnrollmentPermissionService


class MissionEnrollmentServiceTest(TestCase):
    def setUp(self):
        self.service = RenewEnrollmentPermissionService()
        self.mission = MagicMock(spec=Mission)
        self.workspace = MagicMock(spec=Workspace)
        self.new_enrollment = MagicMock(
            spec=MissionEnrollment, mission=self.mission, workspace=self.workspace, status=None
        )

    def setup_mocks(self, mock_filter, last_enrollment_status=None, mission_settings=None, workspace_settings=None):
        if last_enrollment_status is not None:
            last_enrollment = MagicMock(spec=MissionEnrollment, status=last_enrollment_status)
        else:
            last_enrollment = None
        mock_filter.return_value.order_by.return_value.first.return_value = last_enrollment

        if mission_settings:
            for attr, value in mission_settings.items():
                setattr(self.mission, attr, value)

        if workspace_settings:
            for attr, value in workspace_settings.items():
                setattr(self.workspace, attr, value)

    @patch("user_activity.models.MissionEnrollment.objects.filter")
    def test_permission_chain_last_enrollment_not_exists(self, mock_filter):
        self.setup_mocks(mock_filter)

        result = self.service.has_permission(self.new_enrollment, False)
        self.assertTrue(result)

    @patch("user_activity.models.MissionEnrollment.objects.filter")
    def test_permission_chain_workspace_blocks_reenrollment(self, mock_filter):
        self.setup_mocks(
            mock_filter,
            last_enrollment_status=REPROVED,
            mission_settings={"allow_self_enrollment_renewal": True, "allow_self_reproved_enrollment_renewal": True},
            workspace_settings={"block_reenrollment": True},
        )

        with pytest.raises(KeepsNoPermissionToRenewEnrollment) as exception:
            self.service.has_permission(self.new_enrollment, False)
            self.assertEqual(exception.value, _(NOT_PERMISSION_TO_RENEW_ENROLLMENT_BLOCKED_BY_WORKSPACE))

    @patch("user_activity.models.MissionEnrollment.objects.filter")
    def test_permission_chain_admin_can_renew(self, mock_filter):
        self.setup_mocks(
            mock_filter,
            last_enrollment_status=REPROVED,
            mission_settings={"allow_self_enrollment_renewal": True, "allow_self_reproved_enrollment_renewal": True},
            workspace_settings={"block_reenrollment": False},
        )

        result = self.service.has_permission(self.new_enrollment, True)
        self.assertTrue(result)

    @patch("user_activity.models.MissionEnrollment.objects.filter")
    def test_permission_chain_user_reproved_and_mission_allows_reproved_renewal(self, mock_filter):
        self.setup_mocks(
            mock_filter,
            last_enrollment_status=REPROVED,
            mission_settings={"allow_self_enrollment_renewal": True, "allow_self_reproved_enrollment_renewal": True},
            workspace_settings={"block_reenrollment": False},
        )

        result = self.service.has_permission(self.new_enrollment, False)
        self.assertTrue(result)

    @patch("user_activity.models.MissionEnrollment.objects.filter")
    def test_permission_chain_user_reproved_and_mission_disallows_reproved_renewal(self, mock_filter):
        self.setup_mocks(
            mock_filter,
            last_enrollment_status=REPROVED,
            mission_settings={"allow_self_enrollment_renewal": True, "allow_self_reproved_enrollment_renewal": False},
            workspace_settings={"block_reenrollment": False},
        )

        result = self.service.has_permission(self.new_enrollment, False)
        self.assertFalse(result)

    @patch("user_activity.models.MissionEnrollment.objects.filter")
    def test_permission_chain_user_not_reproved_and_mission_allows_renewal(self, mock_filter):
        self.setup_mocks(
            mock_filter,
            last_enrollment_status=REPROVED,
            mission_settings={"allow_self_enrollment_renewal": True, "allow_self_reproved_enrollment_renewal": True},
            workspace_settings={"block_reenrollment": False},
        )

        result = self.service.has_permission(self.new_enrollment, False)
        self.assertTrue(result)

    @patch("user_activity.models.MissionEnrollment.objects.filter")
    def test_permission_chain_user_not_reproved_and_mission_disallows_renewal(self, mock_filter):
        self.setup_mocks(
            mock_filter,
            last_enrollment_status=REPROVED,
            mission_settings={"allow_self_enrollment_renewal": False, "allow_self_reproved_enrollment_renewal": True},
            workspace_settings={"block_reenrollment": False},
        )

        result = self.service.has_permission(self.new_enrollment, False)
        self.assertFalse(result)
