from unittest.mock import MagicMock

from account.models import User, Workspace
from django.test import TestCase
from learning_trail.models import LearningTrail, LearningTrailStep
from mission.models import Mission
from model_mommy import mommy
from user_activity.models import LearningTrailEnrollment
from user_activity.services.learning_trail_enrollment_finish_service import (
    DEFAULT_PERFORMANCE,
    LearningTrailEnrollmentFinishService,
)


class TestLearningTrailEnrollmentFinishService(TestCase):
    def setUp(self) -> None:
        self._service = LearningTrailEnrollmentFinishService(MagicMock(), MagicMock())
        self._trail = mommy.make(LearningTrail)
        self._mission = mommy.make(Mission)
        self._workspace = mommy.make(Workspace)
        mommy.make(LearningTrailStep, mission=self._mission, learning_trail=self._trail)
        self._user = mommy.make(User)

    def test_ignore_missions_with_not_mandatory_development_status(self):
        trail_enrollment = mommy.make(
            LearningTrailEnrollment, learning_trail=self._trail, user=self._user, workspace=self._workspace
        )

        score = self._service.process(trail_enrollment)

        self.assertEqual(score.performance, DEFAULT_PERFORMANCE)
