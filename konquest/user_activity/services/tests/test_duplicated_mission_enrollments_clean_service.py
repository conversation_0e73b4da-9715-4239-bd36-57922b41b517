from unittest.mock import MagicMock

from account.models import User, Workspace
from django.test import TestCase
from mission.models import Mission
from model_mommy import mommy
from user_activity.models import MissionEnrollment
from user_activity.services import MissionEnrollmentService
from user_activity.services.duplicated_mission_enrollments_clean_service import DuplicatedMissionEnrollmentsCleanService


class DuplicateMissionEnrollmentsCleanServiceTest(TestCase):
    def setUp(self):
        self.mission_enrollment_service = MagicMock(spec=MissionEnrollmentService)
        self.clean_service = DuplicatedMissionEnrollmentsCleanService(self.mission_enrollment_service)

    def test_clean_enrollments(self):
        user = mommy.make(User)
        mission = mommy.make(Mission)
        workspace = mommy.make(Workspace)
        mommy.make(MissionEnrollment, user=user, mission=mission, workspace=workspace)
        duplicated_enrollment = mommy.make(MissionEnrollment, user=user, mission=mission, workspace=workspace)

        self.clean_service.clean_enrollments()

        self.mission_enrollment_service.verify_integrate_after_create.assert_called_once_with(duplicated_enrollment.id)
