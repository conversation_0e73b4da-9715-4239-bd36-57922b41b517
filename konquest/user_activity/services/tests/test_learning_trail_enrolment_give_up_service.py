from unittest.mock import MagicMock

import pytest
from account.models import User, Workspace
from constants import ENR<PERSON>LMENT_COMPLETED, ENROLLMENT_GIVE_UP, TRAIL_ENROLLMENT_GAVE_UP_EVENT
from custom.exceptions.unable_to_give_up_enrollment_finished import UnableToGiveUpEnrollmentFinished
from custom.exceptions.unable_to_give_up_mandatory_enrollment import UnableToGiveUpMandatoryEnrollment
from custom.keeps_exception_handler import KeepsNoPermissionToEditLearningTrailEnrollment
from django.test import TestCase
from model_mommy import mommy
from observer.event_manager import EventManager
from user_activity.models import LearningTrailEnrollment
from user_activity.services.learning_trail_enrollment_give_up_service import LearningTrailEnrollmentGiveUpService


class TestLearningTrailEnrollmentGiveUpService(TestCase):
    def setUp(self) -> None:
        self.user = mommy.make(User)
        self.workspace = mommy.make(Workspace)
        self.event_manager = MagicMock(spec=EventManager)
        self.service = LearningTrailEnrollmentGiveUpService(
            self.event_manager,
        )

    def test_should_give_up(self):
        enrollment = mommy.make(LearningTrailEnrollment, user=self.user, workspace=self.workspace)
        self.user.workspace_id = self.workspace.id
        comment = "DESISTOOOOOOOOOO"

        enrollment = self.service.give_up_enrollment(enrollment.id, comment, self.user)

        self.assertEqual(enrollment.status, ENROLLMENT_GIVE_UP)
        self.assertTrue(enrollment.give_up)
        self.assertEqual(enrollment.give_up_comment, comment)
        self.event_manager.notify.assert_called_with(TRAIL_ENROLLMENT_GAVE_UP_EVENT, (enrollment.id,))

    def test_should_raise_no_permission_to_edit_learning_trail_enrollment(self):
        enrollment = mommy.make(LearningTrailEnrollment, workspace=self.workspace)
        self.user.workspace_id = self.workspace.id
        comment = "DESISTOOOOOOOOOO"

        with pytest.raises(KeepsNoPermissionToEditLearningTrailEnrollment):
            self.service.give_up_enrollment(enrollment.id, comment, self.user)

        self.event_manager.assert_not_called()

    def test_should_raise_unable_to_give_up_enrollment_finished(self):
        enrollment = mommy.make(
            LearningTrailEnrollment, user=self.user, workspace=self.workspace, status=ENROLLMENT_COMPLETED
        )
        self.user.workspace_id = self.workspace.id
        comment = "DESISTOOOOOOOOOO"

        with pytest.raises(UnableToGiveUpEnrollmentFinished):
            self.service.give_up_enrollment(enrollment.id, comment, self.user)

        self.event_manager.notify.assert_not_called()

    def test_should_raise_unable_to_give_up_mandatory_enrollment(self):
        enrollment = mommy.make(LearningTrailEnrollment, user=self.user, workspace=self.workspace, required=True)
        self.user.workspace_id = self.workspace.id
        comment = "DESISTOOOOOOOOOO"

        with pytest.raises(UnableToGiveUpMandatoryEnrollment):
            self.service.give_up_enrollment(enrollment.id, comment, self.user)

        self.event_manager.notify.assert_not_called()
