from unittest.mock import MagicMock

from account.models import User, Workspace
from django.test import TestCase
from learning_trail.models import LearningTrail
from model_mommy import mommy
from user_activity.models import LearningTrailEnrollment
from user_activity.services import LearningTrailEnrollmentService
from user_activity.services.duplicated_trail_enrollments_clean_service import DuplicatedTrailEnrollmentsCleanService


class DuplicateTrailEnrollmentsCleanServiceTest(TestCase):
    def setUp(self):
        self.trail_enrollment_service = MagicMock(spec=LearningTrailEnrollmentService)
        self.clean_service = DuplicatedTrailEnrollmentsCleanService(self.trail_enrollment_service)

    def test_clean_enrollments(self):
        user = mommy.make(User)
        learning_trail = mommy.make(LearningTrail)
        workspace = mommy.make(Workspace)
        mommy.make(LearningTrailEnrollment, user=user, learning_trail=learning_trail, workspace=workspace)
        duplicated_enrollment = mommy.make(
            LearningTrailEnrollment, user=user, learning_trail=learning_trail, workspace=workspace
        )

        self.clean_service.clean_enrollments()

        self.trail_enrollment_service.verify_integrate_after_create.assert_called_once_with(duplicated_enrollment.id)
