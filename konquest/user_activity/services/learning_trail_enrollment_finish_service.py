from typing import List

from constants import ENROLLMENT_COMPLETED, ENR<PERSON>LMENT_REPROVED
from custom.discord_webhook import DiscordWebhookLogger
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import Avg, Exists, OuterRef, QuerySet, Sum
from django.utils.timezone import now
from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop
from learning_trail.models import LearningTrailStep
from mission.models.mission import LIVE, PRESENTIAL
from mission.models.mission_development_status_enum import CLOSED, DONE
from pulse.models import Pulse
from user_activity.application.use_cases.generate_certificate.generate_trail_certificate_use_case import (
    GenerateTrailCertificateUseCase,
)
from user_activity.dtos.finish_score_base import FinishScoreBase
from user_activity.models import LearningTrailEnrollment, MissionEnrollment
from user_activity.services.abstracts.enrollment_finish_service import EnrollmentFinishService

DEFAULT_MISSIONS_POINTS = 0
DEFAULT_PULSES_POINTS = 0
DEFAULT_MISSION_ENROLLMENT_AVG = 0
DEFAULT_PERFORMANCE = 1
USER_DONT_COMPLETED_ALL_LEARNING_TRAIL_MISSIONS = gettext_noop("user_dont_completed_all_learning_trail_missions")


class LearningTrailEnrollmentFinishService(EnrollmentFinishService):
    def __init__(
        self,
        generate_certificate_use_case: GenerateTrailCertificateUseCase,
        webhook_logger: DiscordWebhookLogger,
        mission_development_status_mandatory: List[str] = None,
    ):
        if mission_development_status_mandatory is None:
            mission_development_status_mandatory = [DONE, CLOSED]
        self._generate_certificate_use_case = generate_certificate_use_case
        self._webhook_logger = webhook_logger
        self.mission_development_status_mandatory = mission_development_status_mandatory

    def process(self, enrollment: LearningTrailEnrollment) -> FinishScoreBase:
        steps = LearningTrailStep.objects.filter(learning_trail=enrollment.learning_trail)

        score = self._compute_missions_score(enrollment, steps)
        total_pulses_point = self._compute_pulses_points(steps)

        total_points = total_pulses_point + score.total_available_score
        score = FinishScoreBase(
            performance=score.performance, total_awarded_score=total_points, total_available_score=total_points
        )
        self.update_enrollment(enrollment, score)
        return score

    def update_enrollment(self, enrollment: LearningTrailEnrollment, score: FinishScoreBase):
        enrollment = super().update_enrollment(enrollment, score)
        try:
            self._generate_certificate_use_case.execute(enrollment.id)
        except Exception as error:
            self._webhook_logger.emit_short_message(
                f"Error to generate trail certiticate. Enrollment id: {enrollment.id}", error
            )

    def _compute_missions_score(self, enrollment: LearningTrailEnrollment, steps: QuerySet) -> FinishScoreBase:
        trail_missions = steps.filter(
            mission_id__isnull=False, mission__development_status__in=self.mission_development_status_mandatory
        )
        if not trail_missions.exists():
            return FinishScoreBase(performance=DEFAULT_PERFORMANCE)

        missions_to_exclude = trail_missions.filter(
            ~Exists(MissionEnrollment.objects.filter(user=enrollment.user, mission_id=OuterRef("mission_id"))),
            mission__mission_model__in=[LIVE, PRESENTIAL],
            mission__development_status=CLOSED,
        ).values_list("mission__id", flat=True)
        if missions_to_exclude:
            trail_missions = trail_missions.exclude(mission_id__in=missions_to_exclude)

        missions_completed_by_user = MissionEnrollment.objects.filter(
            user=enrollment.user,
            mission_id__in=trail_missions.values_list("mission_id", flat=True),
            workspace=enrollment.workspace,
            status=ENROLLMENT_COMPLETED,
        )
        if trail_missions.count() > missions_completed_by_user.values("mission_id").distinct().count():
            raise ValidationError(_(USER_DONT_COMPLETED_ALL_LEARNING_TRAIL_MISSIONS))
        total_missions_point = missions_completed_by_user.aggregate(sum_p=Sum("points")).get("sum_p")
        total_missions_point = total_missions_point or DEFAULT_MISSIONS_POINTS
        performance = missions_completed_by_user.aggregate(avg=Avg("performance")).get("avg")
        return FinishScoreBase(
            performance=performance or DEFAULT_MISSION_ENROLLMENT_AVG,
            total_awarded_score=total_missions_point,
            total_available_score=total_missions_point,
        )

    @staticmethod
    def _compute_pulses_points(trail_steps: QuerySet) -> int:
        trail_pulse = trail_steps.filter(pulse_id__isnull=False)
        total_pulses_point = (
            Pulse.objects.filter(id__in=trail_pulse.values_list("pulse_id", flat=True))
            .aggregate(sum=Sum("points"))
            .get("sum")
        )
        total_pulses_point = total_pulses_point or DEFAULT_PULSES_POINTS
        return total_pulses_point

    # TODO: DEV-19967
    @transaction.atomic()
    def finish_manually(self, enrollment: LearningTrailEnrollment, performance: float) -> LearningTrailEnrollment:
        enrollment.end_date = now()
        enrollment.status = ENROLLMENT_COMPLETED
        return self.change_performance(enrollment, performance)

    @staticmethod
    def change_performance(enrollment: LearningTrailEnrollment, performance: float) -> LearningTrailEnrollment:
        if not enrollment.is_done:
            raise ValidationError(_("LEARNING_TRAIL_ENROLLMENT_INVALID_STATUS"))
        minimum_performance = enrollment.workspace.min_performance_certificate
        trail = enrollment.learning_trail
        trail_points = trail.points if trail.points else 0
        enrollment.performance = performance
        enrollment.points = trail_points * performance
        enrollment.status = ENROLLMENT_COMPLETED if performance >= minimum_performance else ENROLLMENT_REPROVED
        enrollment.save()

        return enrollment
