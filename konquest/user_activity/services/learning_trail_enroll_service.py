from account.models import User
from authentication.keeps_permissions import ADMIN, SUPER_ADMIN
from config.settings import LEARNING_TRAIL_OPEN_FOR_COMPANY
from constants import ENR<PERSON>LMENT_ENROLLED, ENROLLMENT_EXPIRED, <PERSON><PERSON><PERSON>LMENT_STARTED, REQUIRED
from custom.exceptions.trail_creator_cannot_be_enrolled import Trail<PERSON><PERSON>CannotBeEnrolled
from custom.exceptions.unable_to_enroll_another_user import UnableToEnrollAnotherUser
from custom.exceptions.unable_to_enroll_in_a_inactive_trail import UnableToEnrollInAInactiveTrail
from custom.exceptions.user_already_enrolled_in_the_trail import UserAlreadyEnrolledInTheTrail
from custom.exceptions.user_is_not_linked_to_any_trail_group import UserIsNotLinkedToAnyTrailGroup
from custom.keeps_exception_handler import (
    KeepsEnrollServiceCalledWithInvalidParamsError,
    KeepsNoPermissionToRenewEnrollment,
)
from django.db import transaction
from django.db.models import Q
from learning_trail.models import LearningTrail
from observer.event_manager import EventManager
from user_activity.dtos.regulatory_compliance_cycle_enrollment import Enrollment<PERSON><PERSON><PERSON>reateDTO
from user_activity.events_types import ENROLLED_IN_TRAIL_BY_ADMIN
from user_activity.models import LearningTrailEnrollment
from user_activity.services.regulatory_compliance_service import RegulatoryComplianceService
from user_activity.services.rewew_enrollment.renew_enrollment_permission_service import RenewEnrollmentPermissionService


class LearningTrailEnrollService:
    def __init__(
        self,
        event_manager: EventManager,
        regulatory_compliance_service: RegulatoryComplianceService,
        renew_enrollment_permission_service: RenewEnrollmentPermissionService,
    ):
        self._regulatory_compliance_service = regulatory_compliance_service
        self._event_manager = event_manager
        self._renew_enrollment_permission_service = renew_enrollment_permission_service

    @staticmethod
    def _user_are_the_manager(user: User, trail: LearningTrail) -> bool:
        return trail.user_creator == user

    def _has_permission_to_enroll(self, enrollment: LearningTrailEnrollment, by_admin: bool, is_admin: bool) -> bool:
        trail = enrollment.learning_trail
        user = enrollment.user
        open_for_workspace = trail.learning_trail_type.name == LEARNING_TRAIL_OPEN_FOR_COMPANY

        if not trail.learningtrailworkspace_set.filter(workspace_id=enrollment.workspace_id).exists():
            return False
        if open_for_workspace or by_admin or is_admin:
            return True

        return self._user_linked_in_some_trail_group(enrollment.workspace_id, trail, user)

    @staticmethod
    def _user_linked_in_some_trail_group(workspace_id: str, trail: LearningTrail, user: User) -> bool:
        groups = trail.grouplearningtrail_set.filter(group__workspace_id=workspace_id).values_list(
            "group_id", flat=True
        )
        return user.groupuser_set.filter(group_id__in=groups).exists()

    def get_another_enrollment_in_progress_query_set(self, enrollment: LearningTrailEnrollment):
        return LearningTrailEnrollment.objects.filter(
            Q(user=enrollment.user, learning_trail=enrollment.learning_trail, workspace=enrollment.workspace)
            & (Q(status__in=[ENROLLMENT_ENROLLED, ENROLLMENT_STARTED]) | Q(status=ENROLLMENT_EXPIRED))
        )

    def _has_another_enrollment_in_progress(self, enrollment: LearningTrailEnrollment) -> bool:
        return self.get_another_enrollment_in_progress_query_set(enrollment).exists()

    @staticmethod
    def _get_konquest_user() -> User:
        konquest_user = User()
        konquest_user.role = ADMIN
        return konquest_user

    @transaction.atomic()
    def enroll(
        self,
        data: dict,
        action_user: User = None,
        regulatory_compliance_cycle_id: str = None,
        triggered_by_internal_service: bool = False,
    ) -> LearningTrailEnrollment:
        if not action_user and not triggered_by_internal_service:
            raise KeepsEnrollServiceCalledWithInvalidParamsError()
        if triggered_by_internal_service:
            action_user = self._get_konquest_user()

        enrollment = LearningTrailEnrollment(**data)
        enrolled_by_other_user = str(enrollment.user_id) != str(action_user.id)
        actor_is_admin = action_user.role in [ADMIN, SUPER_ADMIN]
        default_required_value = actor_is_admin and enrolled_by_other_user

        if enrolled_by_other_user and not actor_is_admin:
            raise UnableToEnrollAnotherUser()

        enrollment.required = data.get(REQUIRED, default_required_value)
        enrollment.normative = bool(regulatory_compliance_cycle_id)
        if not enrollment.learning_trail.is_active:
            raise UnableToEnrollInAInactiveTrail()
        if self._user_are_the_manager(enrollment.user, enrollment.learning_trail):
            raise TrailCreatorCannotBeEnrolled()
        if not self._has_permission_to_enroll(enrollment, enrolled_by_other_user, actor_is_admin):
            raise UserIsNotLinkedToAnyTrailGroup()

        already_enrolled = self._has_another_enrollment_in_progress(enrollment)
        if already_enrolled:
            raise UserAlreadyEnrolledInTheTrail()

        if not self._renew_enrollment_permission_service.has_permission(enrollment, enrolled_by_other_user):
            raise KeepsNoPermissionToRenewEnrollment()

        enrollment.save()
        if regulatory_compliance_cycle_id:
            self._create_enrollment_cycle(enrollment, regulatory_compliance_cycle_id)

        if enrolled_by_other_user:
            transaction.on_commit(lambda: self._event_manager.notify(ENROLLED_IN_TRAIL_BY_ADMIN, (enrollment.id,)))
        return enrollment

    def _create_enrollment_cycle(self, enrollment: LearningTrailEnrollment, regulatory_compliance_cycle_id: str):
        enrollment_cycle = EnrollmentCycleCreateDTO(
            enrollment_id=enrollment.id, cycle_id=regulatory_compliance_cycle_id
        )
        self._regulatory_compliance_service.create_enrollment_cycle(enrollment_cycle)
