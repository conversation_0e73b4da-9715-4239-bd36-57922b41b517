from account.models import User
from constants import ENROLLMENT_COMPLETED, RULE_CAN_CHANGE_LEARNING_TRAIL_ENROLLMENT, TRAIL_ENROLLMENT_GAVE_UP_EVENT
from custom.exceptions.unable_to_give_up_enrollment_finished import UnableToGiveUpEnrollmentFinished
from custom.exceptions.unable_to_give_up_mandatory_enrollment import UnableToGiveUpMandatoryEnrollment
from custom.keeps_exception_handler import KeepsNoPermissionToEditLearningTrailEnrollment
from django.db import transaction
from django.utils.timezone import now
from observer.event_manager import EventManager
from rules import test_rule
from user_activity.models import LearningTrailEnrollment
from user_activity.models.mission_enrollment import GIVE_UP


class LearningTrailEnrollmentGiveUpService:
    def __init__(self, event_manager: EventManager):
        self._event_manager = event_manager

    @transaction.atomic()
    def give_up_enrollment(self, enrollment_id: str, comment: str, request_user: User) -> LearningTrailEnrollment:
        enrollment = LearningTrailEnrollment.objects.get(id=enrollment_id)

        self._check_permissions(enrollment, request_user)
        self._validate_enrollment(enrollment)

        self._mark_enrollment_as_give_up(enrollment, comment)
        self._event_manager.notify(TRAIL_ENROLLMENT_GAVE_UP_EVENT, (enrollment_id,))

        return enrollment

    def _check_permissions(self, enrollment: LearningTrailEnrollment, request_user):
        if not test_rule(RULE_CAN_CHANGE_LEARNING_TRAIL_ENROLLMENT, request_user, enrollment):
            raise KeepsNoPermissionToEditLearningTrailEnrollment()

    def _validate_enrollment(self, enrollment: LearningTrailEnrollment):
        if enrollment.status == ENROLLMENT_COMPLETED:
            raise UnableToGiveUpEnrollmentFinished()
        if enrollment.required:
            raise UnableToGiveUpMandatoryEnrollment()

    def _mark_enrollment_as_give_up(self, enrollment: LearningTrailEnrollment, comment: str):
        enrollment.status = GIVE_UP
        enrollment.give_up = True
        enrollment.give_up_comment = comment
        enrollment.deleted_date = now()
        enrollment.save()
