import gettext
import pathlib
import uuid
from datetime import date, datetime, timedelta, timezone
from typing import List, Optional, Sequence, Union

from account.models import User, Workspace
from authentication.keeps_permissions import ADMIN, SUPER_ADMIN
from config import settings
from config.celery import app
from constants import ENROLLMENT_ENROLLED, ENROLLMENT_STARTED, MISSION_ENROLLMENT_RESTARTED_EVENT, REQUIRED
from custom.discord_webhook import DiscordWebhookLogger
from custom.exceptions.not_allowed_send_certificate_to_integrated_mission import (
    NotAllowedSendCertificateIntegratedMission,
)
from custom.exceptions.unable_to_enroll_another_user import UnableToEnrollAnotherUser
from custom.exceptions.unable_to_enroll_user_without_permission import UnableToEnrollUserWithoutPermission
from custom.exceptions.unable_to_finish_internal_enrollment import UnableToFinishInternalEnrollmentBySyncFinish
from custom.keeps_exception_handler import (
    KeepsBadRequestError,
    KeepsEnrollmentLinkedInTrail,
    KeepsEnrollServiceCalledWithInvalidParamsError,
    KeepsNoPermissionToRenewEnrollment,
    KeepsRuntimeError,
    KeepsServiceError,
)
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import Q, QuerySet
from django.utils.timezone import now
from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop
from learning_trail.models import LearningTrailStep
from mission.models import LiveMission, Mission, MissionContributor, MissionTypeEnum, PresentialMission
from mission.models.mission import EXTERNAL_PROVIDER, INTERNAL, LIVE, PRESENTIAL, SCORM
from mission.models.mission_development_status_enum import DONE
from mission.task_names import UPDATE_ENROLLMENTS_ACCEPTED
from myaccount.application.services.myaccount_service import MyAccountService
from myaccount.domain.repositories.myaccount_respository import UserHasPermissionDTO
from observer.event_manager import EventManager
from rest_clients.kontent import KontentClient
from user_activity.dtos.mission_enrollments_check import MissionEnrollmentsCheck
from user_activity.dtos.person import Person
from user_activity.dtos.regulatory_compliance_cycle_enrollment import EnrollmentCycleCreateDTO
from user_activity.exceptions.exceptions import MissionEnrollmentInProgressAlreadyExists
from user_activity.models import LearningTrailEnrollment, LiveAttendance, MissionEnrollment, PresentialAttendance
from user_activity.models.mission_enrollment import (
    COMPLETED,
    EXPIRED,
    GIVE_UP,
    INACTIVATED,
    PENDING_VALIDATION,
    REFUSED,
    REPROVED,
    REQUEST_EXTENSION,
    STATUS_DONE,
    STATUS_IN_PROGRESS,
    SYNC_STATUS_ACCEPTS,
    WAITING_APPROVAL,
    WAITING_VACANCIES,
)
from user_activity.services.mission_enrollment_finish_service import MissionEnrollmentFinishService
from user_activity.services.regulatory_compliance_service import RegulatoryComplianceService
from user_activity.services.rewew_enrollment.renew_enrollment_permission_service import RenewEnrollmentPermissionService
from user_activity.task_names import (
    CREATE_SYNC_ENROLLMENT_ATTENDANCES,
    DELETE_SYNC_ENROLLMENT_ATTENDANCES,
    NOTIFY_SYNC_ENROLLMENT_REFUSED,
    PROMOTE_WAITING_VACANCIES_ENROLLMENTS,
    UPDATE_ENROLLED_COUNT,
    UPDATE_SYNC_REMAINING_SEATS,
)
from user_activity.tasks.notifications import (
    notify_enrolled_in_a_new_mission,
    notify_internal_mission_enrollment_approved_by_admin,
    notify_sync_enrollment_approved,
    send_sync_enrollment_approved_email,
)
from utils.aws.aws_s3 import S3Client
from utils.csv_processor import CSVProcessor

UNABLE_TO_APPROVE_AN_ENROLLMENT_WAITING_FOR_VACANCIES = gettext_noop(
    "unable_to_approve_an_enrollment_waiting_for_vacancies"
)
INVALID_ENROLLMENT_STATUS_TO_SEND_ENROLLMENT_APPROVED_EMAIL = gettext_noop(
    "invalid_enrollment_status_to_send_enrollment_approved_email"
)
USER_WAS_NOT_PRESENT_ON_ANY_DAY = gettext_noop("user_was_not_present_on_any_day")
THE_MISSION_DID_NOT_END = gettext_noop("the_mission_did_not_end")
ENROLLMENT_ALREADY_FINISHED = gettext_noop("enrollment_already_finished")
UNABLE_TO_ENROLL_IN_AN_IN_INACTIVE_MISSION = gettext_noop("unable_to_enroll_in_an_in_inactive_mission")
UNABLE_TO_RESTART_INACTIVATED_ENROLLMENT = gettext_noop("unable_to_restart_inactivated_enrollment")
MISSION_ENROLLMENT_INVALID_STATUS = gettext_noop("mission_enrollment_invalid_status")
ENROLLMENT_DOES_NOT_HAVE_REQUIRED_MISSION_FIELD_EQUAL_TO_TRUE = gettext_noop(
    "mission_enrollment_does_not_have_required_mission_field_equal_to_true"
)
MISSION_CONTRIBUTOR_OR_CREATOR_CANNOT_BE_ENROLLED = gettext_noop("mission_contributor_or_creator_cannot_be_enrolled")
USER_IS_NOT_LINKED_TO_ANY_MISSION_GROUP = gettext_noop("user_is_not_linked_to_any_mission_group")
ALREADY_ENROLLED_IN_THE_MISSION = gettext_noop("already_enrolled_in_the_mission")
INVALID_PERFORMANCE = gettext_noop("invalid_performance")


# pylint: disable=R0904
class MissionEnrollmentService:
    sync_models = Union[LiveMission, PresentialMission]

    def __init__(
        self,
        finish_service: MissionEnrollmentFinishService,
        aws_s3_client: S3Client,
        kontent_client: KontentClient,
        event_manager: EventManager,
        regulatory_compliance_service: RegulatoryComplianceService,
        renew_enrollment_permission_service: RenewEnrollmentPermissionService,
        discord_webhook_logger: DiscordWebhookLogger,
        myaccount_service: MyAccountService,
        active_mission_development_status: List[str] = None
    ):
        if active_mission_development_status is None:
            active_mission_development_status = [DONE]
        self._aws_s3_client = aws_s3_client
        self._kontent_client = kontent_client
        self._enroll_by_mission_model: dict = {
            EXTERNAL_PROVIDER: self._enroll_in_external_provider,
            LIVE: self._enroll_in_sync,
            PRESENTIAL: self._enroll_in_sync,
            INTERNAL: self._enroll_in_internal,
            SCORM: self._enroll_in_internal,
        }
        self._internal_finish_service = finish_service
        self._event_manager = event_manager
        self._active_mission_development_status = active_mission_development_status
        self._regulatory_compliance_service = regulatory_compliance_service
        self._renew_enrollment_permission_service = renew_enrollment_permission_service
        self.discord_webhook_logger = discord_webhook_logger
        self._myaccount_service: MyAccountService = myaccount_service

    @staticmethod
    def _get_konquest_user() -> User:
        konquest_user = User()
        konquest_user.role = ADMIN
        return konquest_user

    @transaction.atomic()
    def enroll_user(
        self,
        data: dict,
        action_user: User = None,
        regulatory_compliance_cycle_id: str = None,
        triggered_by_internal_service: bool = False,
    ) -> MissionEnrollment:
        if not action_user and not triggered_by_internal_service:
            raise KeepsEnrollServiceCalledWithInvalidParamsError()
        if triggered_by_internal_service:
            action_user = self._get_konquest_user()

        self.set_goal_date(data)
        enrollment = MissionEnrollment(**data)
        actor_is_admin = action_user.role in [ADMIN, SUPER_ADMIN]
        enrolled_by_other_user = str(enrollment.user_id) != str(action_user.id)
        default_required_value = actor_is_admin and enrolled_by_other_user

        if not self._myaccount_service.has_access(
            UserHasPermissionDTO(enrollment.user_id, enrollment.workspace_id)
        ):
            raise UnableToEnrollUserWithoutPermission()

        if enrolled_by_other_user and not actor_is_admin:
            raise UnableToEnrollAnotherUser()

        enrollment.required = data.get(REQUIRED, default_required_value)
        mission_model = enrollment.mission.mission_model
        enrollment.normative = bool(data.get("regulatory_compliance_cycle_id"))

        if not enrollment.mission.is_active:
            raise KeepsServiceError(
                UNABLE_TO_ENROLL_IN_AN_IN_INACTIVE_MISSION, _(UNABLE_TO_ENROLL_IN_AN_IN_INACTIVE_MISSION)
            )
        if mission_model not in self._enroll_by_mission_model:
            raise KeepsRuntimeError(
                f"Mission Model {mission_model} are no mapped in MissionEnrolmentService",
            )

        if self._user_are_the_manager(enrollment.user, enrollment.mission):
            raise KeepsServiceError(
                MISSION_CONTRIBUTOR_OR_CREATOR_CANNOT_BE_ENROLLED, _(MISSION_CONTRIBUTOR_OR_CREATOR_CANNOT_BE_ENROLLED)
            )
        if not self._has_permission_to_enroll(enrollment, enrolled_by_other_user, actor_is_admin):
            raise KeepsServiceError(USER_IS_NOT_LINKED_TO_ANY_MISSION_GROUP, _(USER_IS_NOT_LINKED_TO_ANY_MISSION_GROUP))
        if not self._renew_enrollment_permission_service.has_permission(enrollment, enrolled_by_other_user):
            raise KeepsNoPermissionToRenewEnrollment()
        enrollment = self._enroll_by_mission_model[mission_model](enrollment, enrolled_by_other_user)
        if enrolled_by_other_user:
            transaction.on_commit(lambda: notify_enrolled_in_a_new_mission.delay(enrollment.id))
        if regulatory_compliance_cycle_id:
            self._create_enrollment_cycle(enrollment, regulatory_compliance_cycle_id)
        return enrollment

    def set_goal_date(self, data: dict) -> dict:
        if not data.get("goal_date"):
            workspace_id = data.get("workspace_id") if data.get("workspace_id") else data.get("workspace").id
            mission_id = data.get("mission_id") if data.get("mission_id") else data.get("mission").id
            data["goal_date"] = self._get_goal_date_to_enrollment(workspace_id=workspace_id, mission_id=mission_id)
        return data

    def _get_goal_date_to_enrollment(self, workspace_id, mission_id) -> date:
        if mission_id:
            mission = Mission.objects.get(id=mission_id)
            if mission.enrollment_goal_duration_days:
                return self._get_future_date_from_days(mission.enrollment_goal_duration_days)
        if workspace_id:
            workspace = Workspace.objects.get(id=workspace_id)
            if workspace.enrollment_goal_duration_days:
                return self._get_future_date_from_days(workspace.enrollment_goal_duration_days)

    def _get_future_date_from_days(self, days: int) -> date:
        return date.today() + timedelta(days=days) if days else None

    def _create_enrollment_cycle(self, enrollment: MissionEnrollment, regulatory_compliance_cycle_id: str):
        enrollment_cycle = EnrollmentCycleCreateDTO(
            enrollment_id=enrollment.id, cycle_id=regulatory_compliance_cycle_id
        )
        self._regulatory_compliance_service.create_enrollment_cycle(enrollment_cycle)

    def _override_new_enrollment_status(self, enrollment):
        mission_avaible_to_consumption = (
            enrollment.mission.development_status in self._active_mission_development_status
        )
        if mission_avaible_to_consumption:
            return enrollment
        enrollment.status = INACTIVATED
        return enrollment

    def _enroll_in_internal(self, enrollment: MissionEnrollment, by_admin: bool) -> MissionEnrollment:
        already_enrolled = self.has_another_enrollment_in_progress(enrollment)
        enrollment = self._override_new_enrollment_status(enrollment)
        if already_enrolled:
            raise MissionEnrollmentInProgressAlreadyExists()
        enrollment.save()
        return enrollment

    def _enroll_in_external_provider(self, enrollment: MissionEnrollment, by_admin: bool) -> MissionEnrollment:
        already_enrolled = self.has_another_enrollment_in_progress(enrollment)
        if already_enrolled:
            raise MissionEnrollmentInProgressAlreadyExists()
        enrollment.status = ENROLLMENT_STARTED
        enrollment = self._override_new_enrollment_status(enrollment)
        enrollment.save()
        return enrollment

    @staticmethod
    def _get_sync_mission_goal_date(mission: Mission) -> date:
        last_date = mission.sync.dates.filter().order_by("-end_at").first()
        if last_date:
            return last_date.end_at
        return mission.created_date

    def _enroll_in_sync(self, enrollment: MissionEnrollment, by_admin: bool) -> MissionEnrollment:
        sync_mission = enrollment.mission.sync
        enrollment.goal_date = self._get_sync_mission_goal_date(enrollment.mission)
        if self._already_enrolled(enrollment):
            raise ValidationError(_(ALREADY_ENROLLED_IN_THE_MISSION))
        are_vacancies_in_mission = self._are_vacancies_in_mission(sync_mission)
        approve_enrollment = sync_mission.allow_any_enrollment or by_admin

        if are_vacancies_in_mission:
            enrollment.status = ENROLLMENT_ENROLLED if approve_enrollment else WAITING_APPROVAL
        else:
            enrollment.status = WAITING_VACANCIES
        enrollment.save()

        if enrollment.status == ENROLLMENT_ENROLLED:
            app.send_task(name=CREATE_SYNC_ENROLLMENT_ATTENDANCES, args=(enrollment.id,))
        app.send_task(UPDATE_SYNC_REMAINING_SEATS, args=(enrollment.mission_id,))
        return enrollment

    def _are_vacancies_in_mission(self, sync: sync_models) -> bool:
        if not sync.seats:
            return True
        return self.calc_remaining_seats(sync) > 0

    @staticmethod
    def _user_are_the_manager(user: User, mission: Mission) -> bool:
        mission_contributor = MissionContributor.objects.filter(mission=mission, user=user).exists()
        mission_owner = mission.user_creator == user
        return mission_contributor or mission_owner

    @staticmethod
    # TODO: https://app.clickup.com/t/86dv8gynp duplicated code
    def calc_remaining_seats(sync: sync_models) -> int:
        enrollments_in_progress = MissionEnrollment.objects.filter(
            mission_id=sync.mission_id, status__in=SYNC_STATUS_ACCEPTS + STATUS_DONE
        )
        return sync.seats - enrollments_in_progress.count()

    @staticmethod
    def _already_enrolled(enrollment: MissionEnrollment) -> bool:
        return MissionEnrollment.objects.filter(user=enrollment.user, mission=enrollment.mission).exists()

    def _has_permission_to_enroll(self, enrollment: MissionEnrollment, by_admin: bool, is_admin: bool) -> bool:
        mission = enrollment.mission
        user = enrollment.user
        mission_open_for_workspace = str(mission.mission_type.id) != MissionTypeEnum.CLOSE_FOR_COMPANY.value

        if not mission.missionworkspace_set.filter(workspace_id=enrollment.workspace_id).exists():
            return False
        if mission_open_for_workspace or by_admin or is_admin:
            return True

        user_linked_in_some_mission_group = self._user_linked_in_some_mission_group(enrollment, mission, user)
        user_enrolled_in_trails = self._user_enrolled_in_some_mission_trail(mission, user)
        return user_enrolled_in_trails or user_linked_in_some_mission_group

    @staticmethod
    def _user_linked_in_some_mission_group(enrollment, mission, user):
        groups = mission.groupmission_set.filter(group__workspace_id=enrollment.workspace_id).values_list(
            "group_id", flat=True
        )
        return user.groupuser_set.filter(group_id__in=groups).exists()

    @staticmethod
    def _user_enrolled_in_some_mission_trail(mission, user):
        trails = mission.learning_trail_step.filter().values_list("learning_trail_id", flat=True)
        return user.learningtrailenrollment_set.filter(
            learning_trail_id__in=trails, status__in=[ENROLLMENT_ENROLLED, ENROLLMENT_STARTED]
        ).exists()

    @staticmethod
    def get_duplicated_enrollment_queryset(enrollment: MissionEnrollment) -> QuerySet:
        queryset = MissionEnrollment.objects.filter(
            Q(user=enrollment.user, mission=enrollment.mission, workspace=enrollment.workspace)
            & (Q(status__in=STATUS_IN_PROGRESS) | Q(status__in=[EXPIRED, INACTIVATED]))
        )
        if enrollment.pk:
            return queryset.exclude(id=enrollment.id)
        return queryset

    def has_another_enrollment_in_progress(self, enrollment: MissionEnrollment) -> bool:
        return self.get_duplicated_enrollment_queryset(enrollment).exists()

    def finish_sync(self, enrollment: MissionEnrollment) -> MissionEnrollment:
        if not enrollment.mission.is_sync:
            raise UnableToFinishInternalEnrollmentBySyncFinish()
        if enrollment.status == COMPLETED:
            raise ValidationError(_(ENROLLMENT_ALREADY_FINISHED))
        if self._sync_mission_in_progress(enrollment):
            raise ValidationError(_(THE_MISSION_DID_NOT_END))
        if self._not_presented_in_any_period(enrollment):
            raise ValidationError(_(USER_WAS_NOT_PRESENT_ON_ANY_DAY))

        enrollment.status = COMPLETED
        enrollment.end_date = now()
        enrollment.performance = 1
        enrollment.points = 1000
        enrollment.save()
        return enrollment

    @staticmethod
    def close_sync_enrollments(enrollment: MissionEnrollment):
        able_to_complete = enrollment.status in [ENROLLMENT_ENROLLED, ENROLLMENT_STARTED]
        if not able_to_complete:
            enrollment.status = REFUSED
            return enrollment
        dates_count = enrollment.mission.sync.sync_dates.count()
        attendances_count = LiveAttendance.objects.filter(presented=True, enrollment=enrollment).count()
        if dates_count == attendances_count:
            enrollment.status = COMPLETED
            enrollment.performance = 1
            enrollment.points = 1000
        else:
            enrollment.status = REPROVED
            enrollment.performance = 0
            enrollment.points = 0
        enrollment.end_date = now()
        return enrollment

    def retake(self, enrollment: MissionEnrollment, goal_date: str) -> MissionEnrollment:
        if not goal_date:
            goal_date = self._get_goal_date_to_enrollment(enrollment.workspace.id, enrollment.mission.id)
        started_the_mission = enrollment.usermissioncontent_set.filter().exists()
        enrollment.status = ENROLLMENT_STARTED if started_the_mission else ENROLLMENT_ENROLLED
        enrollment.give_up = False
        enrollment.give_up_comment = None
        if not enrollment.required:
            enrollment.goal_date = goal_date

        enrollment.save()
        return enrollment

    @staticmethod
    def _not_presented_in_any_period(enrollment: MissionEnrollment) -> bool:
        if enrollment.mission.mission_model == LIVE:
            return not LiveAttendance.objects.filter(presented=True, enrollment=enrollment).exists()
        if enrollment.mission.mission_model == PRESENTIAL:
            return not PresentialAttendance.objects.filter(presented=True, enrollment=enrollment).exists()
        return True

    @staticmethod
    def _sync_mission_in_progress(enrollment: MissionEnrollment) -> bool:
        return enrollment.mission.sync.dates.filter(end_at__gte=now()).exists()

    def approval_sync(self, enrollment: MissionEnrollment, approved: bool) -> MissionEnrollment:
        if enrollment.status == WAITING_VACANCIES:
            raise ValidationError(_(UNABLE_TO_APPROVE_AN_ENROLLMENT_WAITING_FOR_VACANCIES))
        if approved:
            return self._approve_sync(enrollment)

        return self._refuse_sync(enrollment)

    @staticmethod
    def _refuse_sync(enrollment: MissionEnrollment) -> MissionEnrollment:
        if enrollment.status == REFUSED:
            return enrollment
        enrollment.status = REFUSED
        enrollment.save()

        def send_tasks():
            app.send_task(PROMOTE_WAITING_VACANCIES_ENROLLMENTS, args=(enrollment.mission_id,))
            app.send_task(NOTIFY_SYNC_ENROLLMENT_REFUSED, args=(enrollment.id,))
            app.send_task(DELETE_SYNC_ENROLLMENT_ATTENDANCES, args=(enrollment.id,))

        transaction.on_commit(send_tasks)

        return enrollment

    @staticmethod
    def _approve_sync(enrollment: MissionEnrollment) -> MissionEnrollment:
        if enrollment.status in [ENROLLMENT_ENROLLED, ENROLLMENT_STARTED]:
            return enrollment
        enrollment.status = ENROLLMENT_ENROLLED
        notify_sync_enrollment_approved.delay(enrollment.id)
        app.send_task(name=CREATE_SYNC_ENROLLMENT_ATTENDANCES, args=(enrollment.id,))
        enrollment.save()
        return enrollment

    @staticmethod
    def send_sync_enrollment_approved_email(enrollment: MissionEnrollment) -> None:
        if enrollment.status not in [ENROLLMENT_ENROLLED, ENROLLMENT_STARTED]:
            raise ValidationError(_(INVALID_ENROLLMENT_STATUS_TO_SEND_ENROLLMENT_APPROVED_EMAIL))
        send_sync_enrollment_approved_email.delay(enrollment.id)

    def send_sync_enrollment_approved_email_in_batch(self, enrollment_ids: Sequence[str], workspace_id: str) -> None:
        enrollments = MissionEnrollment.objects.filter(workspace_id=workspace_id, id__in=enrollment_ids)
        for enrollment in enrollments:
            self.send_sync_enrollment_approved_email(enrollment)

    @transaction.atomic()
    def batch_approval_sync(self, mission_id: str, enrollment_ids: Sequence[str], workspace_id: str, approved: bool):
        enrollments = MissionEnrollment.objects.filter(Q(workspace_id=workspace_id) & ~Q(status=WAITING_VACANCIES))
        if mission_id:
            enrollments = enrollments.filter(mission_id=mission_id, workspace_id=workspace_id)
        else:
            enrollments = enrollments.filter(id__in=enrollment_ids, workspace_id=workspace_id)
        for enrollment in enrollments:
            self.approval_sync(enrollment, approved)

    @transaction.atomic()
    def batch_delete(
        self, mission_id: Optional[str], enrollment_ids: Optional[Sequence[str]], workspace_id: str
    ) -> None:
        enrollments = MissionEnrollment.objects.filter(workspace_id=workspace_id)
        if mission_id:
            enrollments.filter(mission_id=mission_id).delete()
            return
        for enrollment_id in enrollment_ids:
            enrollments.get(id=enrollment_id, workspace_id=workspace_id).delete()

    def delete_enrollment(self, enrollment: MissionEnrollment, force: bool = False):
        enrollment_linked_in_trail = self._is_linked_in_trail(enrollment)
        if enrollment_linked_in_trail and not force:
            raise KeepsEnrollmentLinkedInTrail()

        enrollment.deleted = True
        enrollment.deleted_date = now()
        enrollment.save()

        if enrollment.mission.mission_model == LIVE:
            LiveAttendance.objects.filter(enrollment=enrollment).delete()
        elif enrollment.mission.mission_model == PRESENTIAL:
            PresentialAttendance.objects.filter(enrollment=enrollment).delete()

        app.send_task(
            name=UPDATE_ENROLLED_COUNT,
            args=(
                enrollment.user_id,
                enrollment.mission_id,
                enrollment.workspace_id,
            ),
        )

    @staticmethod
    def _is_linked_in_trail(enrollment: MissionEnrollment):
        mission_trails = LearningTrailStep.objects.filter(mission_id=enrollment.mission_id).values_list(
            "learning_trail_id", flat=True
        )
        return LearningTrailEnrollment.objects.filter(
            user_id=enrollment.user_id, learning_trail_id__in=mission_trails, workspace_id=enrollment.workspace_id
        ).exists()

    def external_review(self, enrollment: MissionEnrollment, proof_file: str) -> MissionEnrollment:
        if enrollment.is_done:
            raise ValidationError(_(MISSION_ENROLLMENT_INVALID_STATUS))
        if enrollment.mission.is_integration:
            raise NotAllowedSendCertificateIntegratedMission()
        extension_file = pathlib.Path(proof_file).suffix
        file_path = None
        content_type = None
        if extension_file == ".pdf":
            file_path = f"certificates_provider/{uuid.uuid4()}.pdf"
            content_type = "application/pdf"
        if extension_file == ".png":
            file_path = f"certificates_provider/{uuid.uuid4()}.png"
            content_type = "image/png"

        response_upload = self._aws_s3_client.send_file_path(proof_file, file_path, content_type)
        status = PENDING_VALIDATION
        approve_msg = self._generate_approve_message(status, enrollment, enrollment.user)

        if enrollment.approve_msg:
            approve_msg = f"{enrollment.approve_msg}\n\n\n\n{approve_msg}"

        enrollment.status = status
        enrollment.certificate_provider_url = response_upload["url"]
        enrollment.approve_msg = approve_msg
        enrollment.save(update_fields=["status", "certificate_provider_url", "approve_msg"])
        return enrollment

    def approve_external_enrollment(self, enrollment: MissionEnrollment, reviewer: User, performance: float) -> None:
        if enrollment.status not in [PENDING_VALIDATION, REFUSED]:
            raise ValidationError(_(MISSION_ENROLLMENT_INVALID_STATUS))
        performance = performance if performance is not None else 1
        if not 0.0 < performance <= 1.0:
            raise ValidationError(_(INVALID_PERFORMANCE))

        mission_points = enrollment.mission.points
        workspace_min_performance = enrollment.workspace.min_performance_certificate
        mission_min_performance = enrollment.mission.minimum_performance
        min_performance = workspace_min_performance
        if mission_min_performance > 0:
            min_performance = mission_min_performance
        status = COMPLETED if performance >= min_performance else REPROVED
        enrollment.points = mission_points
        enrollment.status = status
        enrollment.performance = performance
        enrollment.end_date = date.today()
        enrollment.approve_msg = (
            f"{enrollment.approve_msg}\n\n\n\n{self._generate_approve_message(status, enrollment, reviewer)}"
        )
        enrollment.save(update_fields=["status", "approve_msg", "points", "performance", "end_date"])

    def refuse_external_enrollment(self, enrollment: MissionEnrollment, reject_comment: str, reviewer: User) -> None:
        if enrollment.status != PENDING_VALIDATION:
            raise ValidationError(_(MISSION_ENROLLMENT_INVALID_STATUS))
        status = REFUSED
        approve_msg = self._generate_approve_message(status, enrollment, reviewer, reject_comment)
        enrollment.status = status
        enrollment.approve_msg = f"{enrollment.approve_msg}\n\n\n\n{approve_msg}"
        enrollment.save(update_fields=["status", "approve_msg"])

    @staticmethod
    def request_extension(enrollment: MissionEnrollment):
        if enrollment.status != EXPIRED:
            raise KeepsBadRequestError(
                i18n="mission_enrollment_is_not_expired",
                detail="mission enrollment is not expired to request extension",
            )

        enrollment.status = REQUEST_EXTENSION
        enrollment.save(update_fields=["status"])

        return enrollment

    @staticmethod
    def extend_deadline(enrollment: MissionEnrollment, new_goal_date: str):
        if not enrollment.required:
            raise ValidationError(_(ENROLLMENT_DOES_NOT_HAVE_REQUIRED_MISSION_FIELD_EQUAL_TO_TRUE))
        if enrollment.status not in STATUS_IN_PROGRESS and enrollment.status != EXPIRED:
            raise ValidationError(_(MISSION_ENROLLMENT_INVALID_STATUS))
        enrollment.status = ENROLLMENT_STARTED
        enrollment.goal_date = new_goal_date
        enrollment.save(update_fields=["status", "goal_date"])

        return enrollment

    def restart(self, enrollment: MissionEnrollment, **kwargs):
        goal_date = kwargs.get("goal_date", None)
        required_mission = kwargs.get("required", False)
        if enrollment.status == INACTIVATED:
            raise ValidationError(_(UNABLE_TO_RESTART_INACTIVATED_ENROLLMENT))
        if enrollment.in_progress:
            raise ValidationError(_(MISSION_ENROLLMENT_INVALID_STATUS))

        enrollment.status = ENROLLMENT_STARTED
        enrollment.goal_date = goal_date
        enrollment.required = required_mission
        enrollment.performance = None
        enrollment.points = None
        enrollment.end_date = None
        enrollment.certificate_url = None
        enrollment.certificate_provider_url = None
        enrollment.save()

        self._event_manager.notify(MISSION_ENROLLMENT_RESTARTED_EVENT, (enrollment.id,))

        return enrollment

    @staticmethod
    def change_performance(enrollment: MissionEnrollment, performance: float) -> MissionEnrollment:
        if not enrollment.is_done:
            raise ValidationError(_(MISSION_ENROLLMENT_INVALID_STATUS))

        mission_points = enrollment.mission.points if enrollment.mission.points else 0
        workspace_min_performance = enrollment.workspace.min_performance_certificate
        mission_min_performance = enrollment.mission.minimum_performance
        min_performance = workspace_min_performance
        if mission_min_performance > 0:
            min_performance = mission_min_performance
        enrollment.performance = performance
        enrollment.certificate_url = None
        enrollment.points = mission_points * performance
        enrollment.status = COMPLETED if performance >= min_performance else REPROVED

        if enrollment.status == COMPLETED:
            notify_internal_mission_enrollment_approved_by_admin.delay(enrollment.id)

        enrollment.save()
        return enrollment

    @staticmethod
    def slack_webhook_error_attachment(error):
        return [{"color": "danger", "fields": [{"title": "Error", "value": error, "short": False}]}]

    @staticmethod
    def _generate_approve_message(status, enrollment_instance, user_action, reject_comment=None):
        language = enrollment_instance.mission.language
        _locale = gettext.translation("notification", localedir=settings.EMAIL_LOCALE_PATH, languages=[language])
        _locale.install()
        _locale.info()

        today_date = date.today()
        if language in ["pt-BR", "es"]:
            today_date = today_date.strftime("%d/%m/%Y")
        if language in ["en"]:
            today_date = today_date.strftime("%m/%d/%Y")

        message_action = None
        if status == "PENDING_VALIDATION":
            message_action = _locale.gettext("i18n_sent_to_certificate")

        if status == "REFUSED":
            message_action = f"{_locale.gettext('i18n_reprove_certificate')}\n\n{reject_comment}"

        if status == "COMPLETED":
            message_action = f"{_locale.gettext('i18n_approve_certificate')}"

        return f"{today_date} - {user_action.name}({user_action.id}) {message_action}"

    @transaction.atomic()
    def give_up(self, enrollment: MissionEnrollment, comment: str) -> MissionEnrollment:
        if enrollment.required:
            raise KeepsServiceError("not_allowed_to_give_up_a_mandatory_enrollment")
        if not enrollment.in_progress:
            raise KeepsServiceError("not_allowed_to_give_up_a_completed_enrollment")

        if enrollment.mission.is_sync:
            app.send_task(PROMOTE_WAITING_VACANCIES_ENROLLMENTS, args=(enrollment.mission_id,))
            app.send_task(DELETE_SYNC_ENROLLMENT_ATTENDANCES, args=(enrollment.id,))

        enrollment.give_up_comment = comment
        enrollment.give_up = True
        enrollment.status = GIVE_UP
        enrollment.save()
        return enrollment

    @staticmethod
    def active_mission_enrollments(mission_id: str) -> QuerySet:
        enrollments = MissionEnrollment.objects.filter(mission_id=mission_id, status=INACTIVATED)
        for enrollment in enrollments:
            started_the_mission = enrollment.usermissioncontent_set.filter().exists()
            enrollment.status = ENROLLMENT_STARTED if started_the_mission else ENROLLMENT_ENROLLED
        MissionEnrollment.objects.bulk_update(enrollments, fields=["status"])
        return enrollments

    def promote_waiting_vacancies_enrollments(self, sync: sync_models) -> None:
        mission_id = sync.mission_id
        enrollments = self._list_waiting_vacancies_enrollments_to_promote(sync)

        for enrollment in enrollments:
            enrollment.status = ENROLLMENT_ENROLLED if sync.allow_any_enrollment else WAITING_APPROVAL

        MissionEnrollment.objects.bulk_update(enrollments, fields=["status"])
        app.send_task(UPDATE_SYNC_REMAINING_SEATS, args=(mission_id,))

    def _list_waiting_vacancies_enrollments_to_promote(self, sync: sync_models) -> QuerySet:
        if not sync.seats:
            all_enrollments = MissionEnrollment.objects.filter(
                mission_id=sync.mission_id, status=WAITING_VACANCIES
            ).order_by("created_date")
            return all_enrollments
        remaining_seats = self.calc_remaining_seats(sync)
        enrollments = MissionEnrollment.objects.filter(mission_id=sync.mission_id, status=WAITING_VACANCIES).order_by(
            "created_date"
        )[:remaining_seats]
        return enrollments

    @staticmethod
    def inactive_mission_enrollments(mission_id: str) -> QuerySet:
        enrollments = MissionEnrollment.objects.filter(mission_id=mission_id, status__in=STATUS_IN_PROGRESS)
        for enrollment in enrollments:
            enrollment.status = INACTIVATED
        MissionEnrollment.objects.bulk_update(enrollments, fields=["status"])
        return enrollments

    def update_sync_remaining_seats(self, sync: sync_models) -> None:
        sync.remaining_seats = self.calc_remaining_seats(sync) if sync.seats else 0
        sync.save()
        app.send_task(UPDATE_ENROLLMENTS_ACCEPTED, args=(sync.mission_id,))

    @transaction.atomic()
    def finish_manually(self, enrollment: MissionEnrollment, performance: float) -> MissionEnrollment:
        enrollment.end_date = datetime.now(timezone.utc)
        enrollment.status = COMPLETED
        return self.change_performance(enrollment, performance)

    def verify_integrate_after_create(self, id: str):
        enrollment = MissionEnrollment.objects.get(id=id)
        duplicated_enrollment = self.get_duplicated_enrollment_queryset(enrollment).first()
        if not duplicated_enrollment:
            return

        most_recent = enrollment
        if duplicated_enrollment.created_date > enrollment.created_date:
            most_recent = duplicated_enrollment

        self.delete_enrollment(most_recent, True)

        self.discord_webhook_logger.emit_warning_message(
            "DELETED DUPLICATED MISSION ENROLLMENT", f"Deleted enrollment id: {most_recent.id}."
        )

    def get_users_by_email(self, emails):
        users = User.objects.filter(email__in=emails)
        return users

    def get_missions_enrollments_by_users(self, users, mission_id):
        enrollments = MissionEnrollment.objects.select_related("user").filter(mission__id=mission_id, user__in=users)
        return enrollments

    def mission_enrollments_check_emails(self, persons_list: List[Person], mission_id) -> MissionEnrollmentsCheck:
        not_registered = []
        registered = []
        enrolled = []
        emails = [person.email for person in persons_list]
        users = self.get_users_by_email(emails)
        users_emails_registered = users.values_list("email", flat=True)
        enrollments = self.get_missions_enrollments_by_users(users, mission_id).values_list("user", flat=True)
        # exclude those that have already been identified
        persons_list = [person for person in persons_list if person.email not in users_emails_registered]

        for user in users:
            email = user.email
            if user.id in enrollments:
                enrolled.append({"email": email, "name": user.name})
            else:
                registered.append({"email": email, "name": user.name})

        for account in persons_list:
            email = account.email
            if email not in users_emails_registered:
                name = account.name
                name = name if name else email
                not_registered.append({"email": email, "name": name})
        return MissionEnrollmentsCheck(
            not_registered=[Person(**person) for person in not_registered],
            registered=[Person(**person) for person in registered],
            enrolled=[Person(**person) for person in enrolled],
        )

    def check_enrollments_by_sheet(self, file, mission_id):
        self.csv_processor = CSVProcessor()
        persons_list = self.csv_processor.extract_emails_and_names_from_csv(file)
        return self.mission_enrollments_check_emails(persons_list, mission_id)

    def force_enroll_person(self, email, name, mission_id, workspace_id, token):
        user = User.objects.filter(email=email).first()
        if not user:
            user_data = {"email": email, "name": name}
            user = self._myaccount_service.create_user(user_data, token, workspace_id)
            user, _ = User.objects.get_or_create(id=user["id"], defaults=user_data)
        enrollment = MissionEnrollment.objects.filter(user=user, mission_id=mission_id).first()
        if not enrollment:
            enrollment = self.enroll_user(
                {
                    "user_id": user.id,
                    "mission_id": mission_id,
                    "workspace_id": workspace_id,
                },
                triggered_by_internal_service=True,
            )
        return enrollment
