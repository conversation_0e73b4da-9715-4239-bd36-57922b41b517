import uuid
from dataclasses import dataclass
from datetime import datetime
from typing import Sequence

import pytz
from config import settings
from custom.keeps_exception_handler import KeepsServiceError
from django.core.exceptions import ValidationError
from django.db.models import QuerySet, Sum
from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop
from learn_content.models import Answer, Question
from mission.models import Mission, MissionStageContent
from mission.models.mission_content_type_enum import CONTENT, HTML, SCORM
from rest_clients.kontent import KontentClient
from user_activity.dtos.finish_score_base import FinishScoreBase
from user_activity.models import MissionEnrollment, UserMissionContent
from user_activity.models.mission_enrollment import INACTIVATED, STATUS_IN_PROGRESS
from user_activity.services.abstracts.enrollment_finish_service import EnrollmentFinishService

USER_CONSUME_NOT_ENOUGH_TO_FINISH = gettext_noop("user_consume_not_enough_to_finish")
MISSION_ENROLLMENT_ALREADY_FINISHED = gettext_noop("mission_enrollment_already_finished")
MISSION_NOT_ENOUGH_DATA_TO_FINISH = gettext_noop("mission_not_enough_data_to_finish")
MISSION_ENROLLMENT_INACTIVATED = gettext_noop("mission_enrollment_inactivated")
CONTENT_LEARN_CONTENT_FIELD = "mission_stage_content__learn_content_uuid"


# pylint: disable=too-many-instance-attributes
@dataclass
class FinishScore(FinishScoreBase):
    content_awarded_score: int = 0
    contents_available_time: int = 0
    contents_consume_time: int = 0
    content_available_score: int = 0
    exam_available_score: int = 0
    exam_awarded_score: int = 0
    total_correct_answers: int = 0
    total_mission_questions: int = 0


class MissionEnrollmentFinishService(EnrollmentFinishService):
    def __init__(self, kontent_client: KontentClient):
        self._kontent_client = kontent_client
        self._score_calculate_functions = {
            "CONTENT": self.calc_contents_score,
            "QUIZ": self._calc_quizzes_score,
            "FULL": self._calc_full_score,
        }

    def process(self, enrollment: MissionEnrollment) -> FinishScore:
        already_finished = enrollment.status not in STATUS_IN_PROGRESS
        if enrollment.status == INACTIVATED:
            raise KeepsServiceError(MISSION_ENROLLMENT_INACTIVATED, _(MISSION_ENROLLMENT_INACTIVATED))
        if already_finished:
            raise KeepsServiceError(MISSION_ENROLLMENT_ALREADY_FINISHED, _(MISSION_ENROLLMENT_ALREADY_FINISHED))

        try:
            calculate_function = self._score_calculate_functions[enrollment.mission.assessment_type]
        except KeyError as exc:
            raise ValueError(f"Assessment Type {enrollment.assessment_type} not mapped in the service") from exc

        score = calculate_function(enrollment)
        score = self._compute_answers_statics(enrollment, score)
        if not score.total_available_score:
            raise KeepsServiceError(MISSION_NOT_ENOUGH_DATA_TO_FINISH, _(MISSION_NOT_ENOUGH_DATA_TO_FINISH))

        min_performance = enrollment.mission.minimum_performance or enrollment.workspace.min_performance_certificate
        enrollment_status = "COMPLETED" if score.performance >= min_performance else "REPROVED"

        self._update_enrollment(enrollment, score, enrollment_status)
        return score

    def _compute_answers_statics(self, enrollment: MissionEnrollment, score: FinishScore) -> FinishScore:
        mission_questions = self._get_mission_questions(enrollment.mission_id)
        score.total_correct_answers = self._get_correct_answers(enrollment, mission_questions).count()
        score.total_mission_questions = mission_questions.count()
        return score

    @staticmethod
    def _update_enrollment(enrollment: MissionEnrollment, score: FinishScore, new_status: str):
        enrollment.performance = score.performance
        enrollment.assessment_type = enrollment.mission.assessment_type
        enrollment.points = score.total_awarded_score
        enrollment.total_mission_questions = score.total_mission_questions
        enrollment.total_correct_answers = score.total_correct_answers
        enrollment.end_date = datetime.now(pytz.utc)
        enrollment.status = new_status
        enrollment.save()

    def calc_contents_score(self, enrollment: MissionEnrollment, include_quiz_score: bool = True) -> FinishScore:
        if self._mission_only_have_scorm_contents(enrollment.mission_id):
            return self._calc_contents_score_in_scorm_or_genially_mission(enrollment)
        score = FinishScore()
        contents = self._load_mission_contents(enrollment.mission)
        available_duration, available_score = self._sum_contents_duration_and_points(contents) if contents else (0, 0)
        consumed_duration = self._sum_consumed_seconds(enrollment, contents)
        user_not_consume = len(contents) > 0 and not consumed_duration
        if user_not_consume:
            raise KeepsServiceError(USER_CONSUME_NOT_ENOUGH_TO_FINISH, _(USER_CONSUME_NOT_ENOUGH_TO_FINISH))

        if include_quiz_score:
            quiz_score = self._calc_quizzes_score(enrollment, False)
            score.exam_awarded_score = quiz_score.exam_awarded_score
            score.exam_available_score = quiz_score.exam_available_score

        awarded_score = int(available_score * (consumed_duration / available_duration)) if available_duration else 0
        score.contents_consume_time = consumed_duration
        score.contents_available_time = available_duration
        score.content_available_score = available_score
        score.content_awarded_score = awarded_score
        score.total_awarded_score = awarded_score
        score.total_available_score = available_score
        score.performance = self.partial_performance(score)

        return score

    @staticmethod
    def _mission_only_have_scorm_contents(mission_id: uuid) -> bool:
        return not MissionStageContent.objects.filter(content_type=CONTENT, stage__mission_id=mission_id).exists()

    def _calc_contents_score_in_scorm_or_genially_mission(self, enrollment: MissionEnrollment) -> FinishScore:
        score = FinishScore()
        contents = self._load_mission_contents(enrollment.mission, [SCORM, HTML])
        available_duration, available_score = self._sum_contents_duration_and_points(contents) if contents else (0, 0)
        if self._user_not_consume_all_contents(enrollment, contents):
            raise ValidationError(_(USER_CONSUME_NOT_ENOUGH_TO_FINISH))

        score.contents_consume_time = available_duration
        score.contents_available_time = available_duration
        score.content_available_score = available_score
        score.content_awarded_score = available_score
        score.total_awarded_score = available_score
        score.total_available_score = available_score
        score.performance = self.partial_performance(score)

        return score

    def _calc_quizzes_score(self, enrollment: MissionEnrollment, include_contents_score: bool = True) -> FinishScore:
        score = FinishScore()
        questions = self._get_mission_questions(enrollment.mission_id)
        correct_answers = self._get_correct_answers(enrollment, questions)

        if include_contents_score:
            try:
                contents_score = self.calc_contents_score(enrollment, False)
            except ValidationError:
                contents_score = FinishScore()
            score.contents_consume_time = contents_score.contents_consume_time
            score.contents_available_time = contents_score.contents_available_time
            score.content_available_score = contents_score.content_available_score
            score.content_awarded_score = contents_score.content_awarded_score

        awarded_score = correct_answers.aggregate(
            points_sum=Sum("exam_has_question__points"),
        )["points_sum"]
        available_score = questions.aggregate(points_sum=Sum("points"))["points_sum"]
        score.exam_awarded_score = int(awarded_score) if awarded_score else 0
        score.exam_available_score = int(available_score) if available_score else 0
        score.total_available_score = score.exam_available_score
        score.total_awarded_score = score.exam_awarded_score
        score.performance = self.partial_performance(score)

        return score

    @staticmethod
    def _get_correct_answers(enrollment: MissionEnrollment, questions: QuerySet) -> QuerySet:
        return Answer.objects.filter(exam_has_question_id__in=questions, enrollment=enrollment, is_ok=True)

    @staticmethod
    def _get_mission_questions(mission_id: uuid) -> QuerySet:
        exam_ids = MissionStageContent.objects.filter(stage__mission_id=mission_id, content_type="EXAM").values(
            "learn_content_uuid"
        )
        questions = Question.objects.filter(exam_id__in=exam_ids)
        return questions

    def _calc_full_score(self, enrollment: MissionEnrollment) -> FinishScore:
        contents = self.calc_contents_score(enrollment, False)
        quizzes = self._calc_quizzes_score(enrollment, False)

        score = FinishScore(
            exam_awarded_score=quizzes.exam_awarded_score,
            exam_available_score=quizzes.exam_available_score,
            content_awarded_score=contents.content_awarded_score,
            content_available_score=contents.content_available_score,
            contents_available_time=contents.contents_available_time,
            contents_consume_time=contents.contents_consume_time,
            total_awarded_score=contents.total_awarded_score + quizzes.total_awarded_score,
            total_available_score=contents.total_available_score + quizzes.total_available_score,
        )
        score.performance = self.partial_performance(score)

        return score

    @staticmethod
    def _sum_contents_duration_and_points(contents: Sequence[dict]) -> tuple:
        sum_duration = 0
        sum_points = 0
        for content in contents:
            duration = content.get("duration")
            points = content.get("points")
            sum_duration += int(duration) if duration else 1
            sum_points += int(points) if points else 1
        return sum_duration, sum_points

    def _load_mission_contents(self, mission: Mission, content_types: Sequence[str] = None) -> Sequence[dict]:
        if content_types is None:
            content_types = [CONTENT]
        stage_contents = MissionStageContent.objects.filter(stage__mission=mission, content_type__in=content_types)
        learn_content_ids = [str(content.learn_content_uuid) for content in stage_contents]
        return self._kontent_client.get_docs(settings.KEEPS_SECRET_TOKEN_INTEGRATION, learn_content_ids)

    @staticmethod
    def _sum_consumed_seconds(enrollment: MissionEnrollment, contents: Sequence[dict]) -> int:
        available_kontent_content_ids = [content["id"] for content in contents]
        consume_by_content = (
            enrollment.learncontentactivity_set.filter(
                mission_stage_content__learn_content_uuid__in=available_kontent_content_ids
            )
            .values(CONTENT_LEARN_CONTENT_FIELD)
            .annotate(sum_time_in=Sum("time_in"))
        )
        consumed_seconds = 0
        for consume in consume_by_content:
            content_duration = next(
                content for content in contents if str(content["id"]) == str(consume[CONTENT_LEARN_CONTENT_FIELD])
            ).get("duration")
            content_duration = content_duration if content_duration else 1
            sum_time_in = consume["sum_time_in"]
            sum_time_in = sum_time_in.total_seconds() if sum_time_in else 0
            consumed_seconds += min(sum_time_in, int(content_duration))
        return consumed_seconds

    @staticmethod
    def _user_not_consume_all_contents(enrollment: MissionEnrollment, contents: Sequence[dict]) -> bool:
        content_ids = [content["id"] for content in contents]
        count_contents = len(content_ids)
        count_consumed_contents = UserMissionContent.objects.filter(
            mission_enrollment=enrollment, content__learn_content_uuid__in=content_ids
        ).count()
        return count_consumed_contents < count_contents
