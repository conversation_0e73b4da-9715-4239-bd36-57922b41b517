from typing import List, Optional

from django.db.models import QuerySet, Sum
from injector import inject
from learning_trail.models import LearningTrailStep
from pulse.models import Pulse
from user_activity.dtos.pulse_consume_tracking import PulseConsumeTracking
from user_activity.dtos.trail_enrollment_tracking import TrailEnrollmentTracking
from user_activity.models import LearnContentActivity, LearningTrailEnrollment
from user_activity.selectors.mission_enrollment_selector import MissionEnrollmentSelector


class LearningTrailEnrollmentTrackingService:
    EMPTY_PULSE_TRACKING = PulseConsumeTracking(
        consume_duration=0,
        content_duration=0,
    )

    @inject
    def __init__(self, mission_enrollment_selector: MissionEnrollmentSelector):
        self._mission_enrollment_selector = mission_enrollment_selector

    def load_step_pulse(self, enrollment: LearningTrailEnrollment, pulse: Pulse) -> Optional[PulseConsumeTracking]:
        if self._is_not_linked_in_trail(enrollment, pulse):
            return None

        activities = self._build_pulse_activites_queryset(enrollment, pulse.id)
        if not activities.exists():
            return self.EMPTY_PULSE_TRACKING
        time_in = activities.aggregate(Sum("time_in"))["time_in__sum"]

        return PulseConsumeTracking(
            consume_duration=time_in.seconds,
            content_duration=int(pulse.duration_time) if pulse.duration_time else 0,
            first_access=activities.first().time_start,
            last_access=activities.last().time_stop,
        )

    @staticmethod
    def _is_not_linked_in_trail(enrollment, pulse) -> bool:
        not_linked_in_trail = not LearningTrailStep.objects.filter(
            learning_trail_id=enrollment.learning_trail_id, pulse_id=pulse.id
        ).exists()
        return not_linked_in_trail

    @staticmethod
    def _build_pulse_activites_queryset(enrollment: LearningTrailEnrollment, pulse_id: str) -> QuerySet:
        return LearnContentActivity.objects.filter(user_id=enrollment.user_id, pulse_id=pulse_id).order_by("time_start")

    def load(self, enrollment: LearningTrailEnrollment) -> Optional[List[TrailEnrollmentTracking]]:
        steps = (
            LearningTrailStep.objects.filter(learning_trail_id=enrollment.learning_trail_id)
            .select_related("pulse")
            .select_related("mission")
            .order_by("order")
        )
        tracking_steps = []
        for step in steps:
            tracking_steps.append(self._build_step_tracking(step, enrollment))
        return tracking_steps

    def _build_step_tracking(
        self, step: LearningTrailStep, enrollment: LearningTrailEnrollment
    ) -> TrailEnrollmentTracking:
        if step.pulse:
            return self._build_step_pulse_consume_tracking(step)
        return self._build_step_mission_consume_tracking(step, enrollment)

    @staticmethod
    def _build_step_pulse_consume_tracking(step: LearningTrailStep) -> TrailEnrollmentTracking:
        return TrailEnrollmentTracking(id=str(step.pulse_id), name=step.pulse.name, step_type="PULSE")

    def _build_step_mission_consume_tracking(
        self, step: LearningTrailStep, trail_enrollment: LearningTrailEnrollment
    ) -> TrailEnrollmentTracking:
        enrollment = self._mission_enrollment_selector.get_actual_user_enrollment(
            trail_enrollment.user_id, step.mission_id, trail_enrollment.workspace_id
        )
        return TrailEnrollmentTracking(
            id=str(step.mission_id),
            name=step.mission.name,
            step_type="MISSION",
            mission_enrollment_id=enrollment.id if enrollment else None,
        )
