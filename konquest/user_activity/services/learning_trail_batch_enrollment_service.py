from account.models import User
from custom.keeps_exception_handler import KeepsBadRequestError, KeepsError
from django.db import IntegrityError
from learning_trail.models import LearningTrail
from user_activity.models import LearningTrailEnrollment


# pylint: disable=R0903
class LearningTrailBatchEnrollmentService:
    """Deprecated. New version: learning_trail_batch_enrollment_service_v2.py"""

    def __init__(self):
        self._enrollment_errors = {"enrollment_errors": []}

    def enroll(self, learning_trail_id_list: list, user_id_list: list, workspace_id: str, goal_date: str):
        if not user_id_list or not learning_trail_id_list:
            self._enrollment_errors["enrollment_errors"].append(
                {
                    "error": {
                        "i18n": "not_found_learning_trails_or_users",
                        "detail": "not found learning trails or users",
                    }
                }
            )
            return self._enrollment_errors

        for learning_trail_id in learning_trail_id_list:
            for user_id in user_id_list:
                user = User.objects.filter(id=user_id).first()
                learning_trail = LearningTrail.objects.filter(id=learning_trail_id).first()

                try:
                    self._create_enrollment(user, learning_trail, workspace_id, goal_date)
                except Exception as error:
                    self._format_exception_data(error, learning_trail_id, learning_trail, user_id, user)

        return self._enrollment_errors

    def _format_exception_data(
        self, error: Exception, learning_trail_id: str, learning_trail: LearningTrail, user_id: str, user: User
    ):
        user_id__email = {"id": user_id}
        learning_trail_id__name = {"id": learning_trail_id}
        if user:
            user_id__email.update({"email": user.email})
        if learning_trail:
            learning_trail_id__name.update({"name": learning_trail.name})

        self._enrollment_errors["enrollment_errors"].append(
            {
                "user": user_id__email,
                "learning_trail": learning_trail_id__name,
                "error": {"i18n": error.i18n, "detail": error.detail},
            }
        )

    @staticmethod
    def _create_enrollment(
        user: User, learning_trail: LearningTrail, workspace_id: str, goal_date: str
    ) -> LearningTrailEnrollment:
        if user == learning_trail.user_creator:
            raise KeepsBadRequestError(
                detail="learning trail creator user cannot be enrolled",
                i18n="learning_trail_creator_user_cannot_be_enrolled",
            )

        try:
            enrollment = LearningTrailEnrollment.objects.create(
                learning_trail=learning_trail, workspace_id=workspace_id, user=user, goal_date=goal_date, required=True
            )
            return enrollment
        except IntegrityError as exc:
            raise KeepsBadRequestError(
                detail="this enrollment already exists", i18n="learning_trail_enrollment_already_exists"
            ) from exc
        except Exception as exc:
            raise KeepsError(
                detail="unexpected error happened", i18n="unexpected_error_happened", status_code=500
            ) from exc
