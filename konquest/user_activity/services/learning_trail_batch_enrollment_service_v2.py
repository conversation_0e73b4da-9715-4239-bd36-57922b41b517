from dataclasses import dataclass
from typing import List, Optional

from account.models import User
from custom.custom_exception_handler import transform_object_does_not_exits_exception
from custom.exceptions.keeps_permission_error import KeepsPermissionError
from custom.exceptions.user_already_enrolled_in_the_trail import UserAlreadyEnrolledInTheTrail
from custom.keeps_exception_handler import KeepsServiceError
from django.core.exceptions import ObjectDoesNotExist
from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop
from learning_trail.models import LearningTrail, LearningTrailWorkspace
from user_activity.dtos.batch_trail_enroll_result import BatchTrailEnrollError, BatchTrailEnrollResult
from user_activity.dtos.error_info import ErrorInfo
from user_activity.services.learning_trail_enroll_service import LearningTrailEnrollService

NOT_FOUND_TRAILS_OR_USERS = gettext_noop("not_found_learning_trails_or_users")
AN_UNEXPECTED_ERROR_HAS_OCCURRED = gettext_noop("an_unexpected_error_has_occurred")


@dataclass
class EnrollmentsBatch:
    trail_ids: List[str]
    workspace_id: str
    user_ids: List[str]
    action_user: Optional[User] = None
    goal_date: Optional[str] = None
    required: Optional[bool] = True
    regulatory_compliance_cycle_id: Optional[str] = None
    triggered_by_internal_service: Optional[bool] = False


class LearningTrailBatchEnrollmentService:
    def __init__(self, trail_enrollment_service: LearningTrailEnrollService):
        self._trail_enrollment_service = trail_enrollment_service

    def enroll(self, batch_input: EnrollmentsBatch) -> BatchTrailEnrollResult:
        trail_ids = (
            LearningTrailWorkspace.objects.filter(
                learning_trail_id__in=batch_input.trail_ids, workspace_id=batch_input.workspace_id
            )
            .all()
            .values_list("learning_trail_id", flat=True)
        )
        enrollment_errors = BatchTrailEnrollResult([])
        errors = enrollment_errors.enrollment_errors
        if not batch_input.user_ids or not trail_ids:
            errors.append(
                BatchTrailEnrollError(error=ErrorInfo(i18n=NOT_FOUND_TRAILS_OR_USERS, detail=NOT_FOUND_TRAILS_OR_USERS))
            )
        for trail_id in trail_ids:
            error_info = self._enroll_users_in_trail(
                batch_input.goal_date,
                trail_id,
                batch_input.user_ids,
                batch_input.workspace_id,
                required=batch_input.required,
                action_user=batch_input.action_user,
                regulatory_compliance_cycle_id=batch_input.regulatory_compliance_cycle_id,
                triggered_by_internal_service=batch_input.triggered_by_internal_service,
            )
            errors.extend(error_info) if error_info else None

        return enrollment_errors

    def _enroll_users_in_trail(
        self,
        goal_date: str,
        trail_id: str,
        user_ids: List[str],
        workspace_id: str,
        required: bool = True,
        action_user: User = None,
        triggered_by_internal_service: bool = False,
        **kwargs,
    ) -> List[BatchTrailEnrollError]:
        errors = []
        users = User.objects.filter(id__in=user_ids).filter()
        for user in users:
            trail = LearningTrail.objects.filter(id=trail_id).first()
            enrollment = {
                "user_id": user.id,
                "learning_trail_id": trail_id,
                "workspace_id": workspace_id,
                "goal_date": goal_date,
                "required": required,
            }
            try:
                self._trail_enrollment_service.enroll(
                    enrollment,
                    action_user,
                    regulatory_compliance_cycle_id=kwargs.get("regulatory_compliance_cycle_id"),
                    triggered_by_internal_service=triggered_by_internal_service,
                )
            except (
                KeepsServiceError,
                User.DoesNotExist,
                LearningTrail.DoesNotExist,
                KeepsPermissionError,
                UserAlreadyEnrolledInTheTrail,
            ) as exception:
                errors.append(
                    self._format_exception(
                        exception=exception, trail_id=trail_id, trail=trail, user_id=user.id, user=user
                    )
                )

        return errors

    def _format_exception(
        self, exception: Exception, trail_id: str, trail: Optional[LearningTrail], user_id: str, user: User
    ) -> BatchTrailEnrollError:
        user_data = {"id": user_id, "email": user.email} if user else {"id": user_id}
        trail_data = {"id": trail_id, "name": trail.name} if trail else {"id": trail_id}

        error_info = self._format_error_info(exception)

        return BatchTrailEnrollError(user=user_data, trail=trail_data, error=error_info)

    @staticmethod
    def _format_error_info(exception: Exception) -> ErrorInfo:
        if isinstance(exception, KeepsServiceError):
            return ErrorInfo(i18n=exception.msg, detail=exception.description)
        if isinstance(exception, ObjectDoesNotExist):
            return ErrorInfo(**transform_object_does_not_exits_exception(exception))
        if isinstance(exception, KeepsPermissionError):
            return ErrorInfo(i18n="not_allowed", detail=exception.message)

        return ErrorInfo(i18n=AN_UNEXPECTED_ERROR_HAS_OCCURRED, detail=_(AN_UNEXPECTED_ERROR_HAS_OCCURRED))
