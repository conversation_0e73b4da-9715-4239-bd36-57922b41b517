from abc import ABC, abstractmethod
from datetime import datetime

import pytz
from constants import <PERSON><PERSON><PERSON><PERSON>ENT_COMPLETED, ENROLLMENT_REPROVED
from user_activity.dtos.finish_score_base import FinishScoreBase
from user_activity.models.enrollment import Enrollment
from utils.utils import truncate_decimal_value


class EnrollmentFinishService(ABC):
    @abstractmethod
    def process(self, enrollment: Enrollment) -> FinishScoreBase:
        raise NotImplementedError("'process' method not implemented")

    @staticmethod
    def partial_performance(score: FinishScoreBase) -> float:
        if not score.total_available_score:
            return 0
        score_final = score.total_awarded_score / score.total_available_score
        return min([truncate_decimal_value(score_final), 1])

    @staticmethod
    def _get_minimum_performance(enrollment: Enrollment) -> float:
        return enrollment.workspace.min_performance_certificate

    def update_enrollment(self, enrollment: Enrollment, score: FinishScoreBase):
        minimum_performance = self._get_minimum_performance(enrollment)
        enrollment.performance = score.performance
        enrollment.points = score.total_awarded_score
        enrollment.status = ENROLLMENT_COMPLETED if score.performance >= minimum_performance else ENROLLMENT_REPROVED
        enrollment.end_date = datetime.now(pytz.utc)
        enrollment.save()
        return enrollment
