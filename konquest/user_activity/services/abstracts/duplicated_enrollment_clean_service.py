from abc import ABC
from typing import Union

from django.db import models
from django.db.models import Count
from user_activity.services import LearningTrailEnrollmentService, MissionEnrollmentService


class DuplicatedEnrollmentCleanService(ABC):
    model: models.Model
    object_id_field: str

    def __init__(self, enrollment_service: Union[LearningTrailEnrollmentService, MissionEnrollmentService]):
        self._enrollment_service = enrollment_service

    def clean_enrollments(self):
        enrollments = (
            self.model.objects.filter(
                end_date__isnull=True,
            )
            .values(self.object_id_field, "user_id", "workspace_id")
            .annotate(count=Count("id"))
            .filter(count__gt=1)
        )

        for enrollment in enrollments:
            filter_kwargs = {
                self.object_id_field: enrollment[self.object_id_field],
                "user_id": enrollment["user_id"],
                "workspace_id": enrollment["workspace_id"],
                "end_date__isnull": True,
            }
            newest_enrollment = self.model.objects.filter(**filter_kwargs).order_by("-created_date").first()
            if not newest_enrollment:
                continue

            self._enrollment_service.verify_integrate_after_create(newest_enrollment.id)
