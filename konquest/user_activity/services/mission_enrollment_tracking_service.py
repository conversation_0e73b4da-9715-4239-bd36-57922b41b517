import uuid
from typing import Sequence

from config import settings
from django.db.models import Sum
from learn_content.models import Answer, Exam, Question
from mission.models import MissionStageContent
from mission.models.mission_content_type_enum import CONTENT, EXAM, HTML, SCORM
from rest_clients.kontent import KontentClient
from user_activity.models import MissionEnrollment


class MissionEnrollmentTrackingResumeService:
    def __init__(self, kontent_client: KontentClient):
        self._learn_contents = []
        self._kontent_client = kontent_client
        self.enrollment = None

    def load(self, enrollment: MissionEnrollment) -> Sequence[dict]:
        contents = MissionStageContent.objects.filter(stage__mission_id=enrollment.mission_id).order_by(
            "stage__order", "order"
        )
        self._learn_contents = self._kontent_client.get_docs(
            settings.KEEPS_SECRET_TOKEN_INTEGRATION, [content.learn_content_uuid for content in contents]
        )
        return self._load_tracking_by_content(contents, enrollment)

    def load_by_enrollment_id(self, enrollment_id: str) -> Sequence[dict]:
        enrollment = MissionEnrollment.objects.get(id=enrollment_id)
        return self.load(enrollment)

    def _load_tracking_by_content(
        self, contents: Sequence[MissionStageContent], enrollment: MissionEnrollment
    ) -> Sequence[dict]:
        tracking = []
        for content in contents:
            content_tracking = self._get_consume_tracking(content, enrollment)
            tracking.append(content_tracking)
        return tracking

    def _get_consume_tracking(self, content: MissionStageContent, enrollment: MissionEnrollment) -> dict:
        learn_content = self._get_learn_content(content.learn_content_uuid)
        if content.content_type in [CONTENT, SCORM, HTML]:
            return self.get_content_consume(content, learn_content, enrollment)
        if content.content_type == EXAM:
            return self.get_exam_consume(content, enrollment)
        return {}

    def _get_learn_content(self, learn_content_uuid: uuid) -> dict:
        try:
            return dict(
                list(
                    filter(lambda _learn_content: _learn_content["id"] == str(learn_content_uuid), self._learn_contents)
                )[0]
            )
        except IndexError:
            return {}

    @staticmethod
    def get_exam_consume(content: MissionStageContent, enrollment: MissionEnrollment):
        exam = Exam.objects.filter(id=content.learn_content_uuid)
        questions = Question.objects.filter(exam_id__in=exam)
        answers = Answer.objects.filter(exam_has_question_id__in=questions, enrollment=enrollment).order_by(
            "created_date"
        )
        if enrollment.is_done:
            answers = answers.filter(created_date__lte=enrollment.end_date)
        if answers.first():
            first_access = answers.first().created_date
            last_access = answers.last().created_date
        else:
            first_access = None
            last_access = None

        consume_data = {
            "content": None,
            "name": content.name,
            "first_access": first_access,
            "last_access": last_access,
            "consume_duration": None,
            "content_duration": None,
            "total_questions": questions.count(),
            "total_correct_answers": answers.filter(is_ok=True).count(),
        }

        return consume_data

    # TODO: DEV-19997
    def get_content_consume(
        self, mission_stage_content: MissionStageContent, learn_content: dict, enrollment: MissionEnrollment
    ):
        consume = enrollment.learncontentactivity_set.filter(
            mission_stage_content=mission_stage_content,
        ).order_by("created_date")
        if enrollment.is_done:
            consume = consume.filter(time_start__lte=enrollment.end_date)
        if consume:
            first_access = consume.first().created_date
            last_access = consume.last().created_date
        else:
            first_access = None
            last_access = None
        cleaned_learn_content = self._clean_learn_content(learn_content)
        consume_duration = consume.aggregate(sum_d=Sum("time_in")).get("sum_d")
        consume_data = {
            "content": cleaned_learn_content,
            "name": mission_stage_content.name,
            "first_access": first_access,
            "last_access": last_access,
            "consume_duration": consume_duration.seconds if consume_duration else 0,
            "content_duration": learn_content.get("duration", 0),
            "total_questions": 0,
            "total_correct_answers": 0,
        }

        return consume_data

    @staticmethod
    def _clean_learn_content(learn_content: dict) -> dict:
        return {
            "id": learn_content.get("id"),
            "duration": learn_content.get("duration"),
            "content_type": learn_content.get("content_type"),
        }
