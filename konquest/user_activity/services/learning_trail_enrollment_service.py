from datetime import datetime
from typing import List, Sequence

from account.models import User
from constants import (
    <PERSON><PERSON><PERSON>LMENT_COMPLETED,
    ENROLLMENT_ENROLLED,
    ENROLLMENT_REPROVED,
    ENROLLMENT_STARTED,
    RULE_CAN_CHANGE_LEARNING_TRAIL_ENROLLMENT,
)
from custom.discord_webhook import Discord<PERSON>ebho<PERSON><PERSON>ogger
from custom.exceptions.unable_to_give_up_enrollment_finished import UnableToGiveUpEnrollmentFinished
from custom.exceptions.unable_to_give_up_mandatory_enrollment import UnableToGiveUpMandatoryEnrollment
from custom.keeps_exception_handler import (
    KeepsNoPermissionToEditLearningTrailEnrollment,
    UnableToRestartEnrollmentInvalidStatus,
)
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import Count, Exists, OuterRef
from django.utils.timezone import now
from learn_content.models import Answer, Exam
from learning_trail.models import LearningTrailStep
from mission.models import Mission
from mission.models.mission import LIVE, PRESENTIAL
from mission.models.mission_development_status_enum import CLOSED, DONE
from pulse.models import Pulse
from rules import test_rule
from user_activity.models import LearnContentActivity, LearningTrailEnrollment, MissionEnrollment
from user_activity.models.mission_enrollment import COMPLETED, ENROLLED, GIVE_UP, PENDING_VALIDATION, STARTED
from user_activity.services.learning_trail_enroll_service import LearningTrailEnrollService
from user_activity.services.learning_trail_enrollment_finish_service import LearningTrailEnrollmentFinishService
from user_activity.services.mission_batch_enrollment_service import EnrollmentsBatch, MissionBatchEnrollmentService
from user_activity.services.mission_enrollment_service import MissionEnrollmentService

MAX_ENROLLMENT_PROGRESS = 1


class LearningTrailEnrollmentService:
    def __init__(
        self,
        mission_enrollment_service: MissionEnrollmentService,
        mission_batch_enrollment_service: MissionBatchEnrollmentService,
        finish_service: LearningTrailEnrollmentFinishService,
        enroll_service: LearningTrailEnrollService,
        discord_webhook_logger: DiscordWebhookLogger,
        mission_development_status_mandatory: List[str] = None,
    ):
        if mission_development_status_mandatory is None:
            mission_development_status_mandatory = [DONE, CLOSED]
        self._mission_enrollment_service = mission_enrollment_service
        self._mission_batch_enrollment_service = mission_batch_enrollment_service
        self._finish_service = finish_service
        self.mission_development_status_mandatory = mission_development_status_mandatory
        self.enroll_service = enroll_service
        self.discord_webhook_logger = discord_webhook_logger

    @transaction.atomic()
    def delete_enrollment(self, enrollment: LearningTrailEnrollment):
        mission_ids = LearningTrailStep.objects.filter(
            learning_trail=enrollment.learning_trail, mission_id__isnull=False
        ).values_list("mission_id", flat=True)

        mission_enrollments = MissionEnrollment.objects.filter(
            mission_id__in=mission_ids,
            user_id=enrollment.user_id,
            workspace_id=enrollment.workspace_id,
            created_date__gte=enrollment.created_date,
        ).exclude(status__in=[COMPLETED, ENROLLMENT_REPROVED, PENDING_VALIDATION])

        for mission_enrollment in mission_enrollments:
            self._mission_enrollment_service.delete_enrollment(mission_enrollment, True)

        enrollment.deleted_date = now()
        enrollment.deleted = True
        enrollment.save()

    def to_give_up(self, enrollment_id: str, comment: str, request_user: User) -> LearningTrailEnrollment:
        enrollment = LearningTrailEnrollment.objects.get(id=enrollment_id)
        if not test_rule(RULE_CAN_CHANGE_LEARNING_TRAIL_ENROLLMENT, request_user, enrollment):
            raise KeepsNoPermissionToEditLearningTrailEnrollment()
        if enrollment.status == ENROLLMENT_COMPLETED:
            raise UnableToGiveUpEnrollmentFinished()
        if enrollment.required:
            raise UnableToGiveUpMandatoryEnrollment()

        self._update_instance_to_give_up(comment, enrollment)
        self._give_up_mission_enrollments_linked(comment, enrollment)

        return enrollment

    @staticmethod
    def _give_up_mission_enrollments_linked(comment: str, enrollment: LearningTrailEnrollment):
        missions = LearningTrailStep.objects.filter(
            learning_trail=enrollment.learning_trail, mission_id__isnull=False
        ).values_list("mission_id", flat=True)
        MissionEnrollment.objects.filter(
            user=enrollment.user,
            mission_id__in=missions,
            status=ENROLLMENT_STARTED,
            workspace_id=enrollment.workspace_id,
        ).update(give_up=True, give_up_comment=comment)

    @staticmethod
    def _update_instance_to_give_up(comment, enrollment):
        enrollment.status = GIVE_UP
        enrollment.give_up = True
        enrollment.give_up_comment = comment
        enrollment.save()

    def restart(self, enrollment: LearningTrailEnrollment, **kwargs):
        goal_date = kwargs.get("goal_date", None)
        if enrollment.status in [ENROLLED, STARTED]:
            raise UnableToRestartEnrollmentInvalidStatus(enrollment.status)

        enrollment.status = STARTED
        enrollment.goal_date = goal_date
        enrollment.performance = None
        enrollment.points = None
        enrollment.end_date = None
        enrollment.certificate_url = None
        enrollment.give_up = False
        enrollment.give_up_comment = None
        enrollment.save()

        return enrollment

    @staticmethod
    def retake(enrollment_instance: LearningTrailEnrollment, goal_date: str):
        enrollment_instance.status = "STARTED"
        enrollment_instance.give_up = False
        enrollment_instance.goal_date = goal_date
        enrollment_instance.end_date = None
        enrollment_instance.give_up_comment = None
        enrollment_instance.performance = None
        enrollment_instance.points = None
        enrollment_instance.certificate_url = None
        enrollment_instance.save()

        lt_missions = LearningTrailStep.objects.filter(
            learning_trail=enrollment_instance.learning_trail, mission_id__isnull=False
        ).values_list("mission_id", flat=True)
        MissionEnrollment.objects.filter(
            user=enrollment_instance.user, mission_id__in=lt_missions, status="STARTED"
        ).update(
            give_up=False, end_date=None, give_up_comment=None, performance=None, points=None, certificate_url=None
        )

        return enrollment_instance

    def update_progress(self, enrollment: LearningTrailEnrollment) -> LearningTrailEnrollment:
        enrollment.progress = self._calc_progress(enrollment)
        enrollment.status = STARTED if enrollment.status == ENROLLED else enrollment.status
        if enrollment.progress != MAX_ENROLLMENT_PROGRESS:
            enrollment.save()
            return enrollment

        try:
            self._finish_service.process(enrollment)
        except ValidationError:
            pass

        enrollment.save()
        return enrollment

    def enroll_users_in_new_step_mission(self, learning_trail_id: str, mission_id: str):
        trail_enrollments = LearningTrailEnrollment.objects.filter(
            learning_trail_id=learning_trail_id, status__in=[ENROLLMENT_STARTED, ENROLLMENT_ENROLLED]
        )
        mission_enrollments_already_created = MissionEnrollment.objects.filter(
            user_id__in=trail_enrollments.values_list("user_id", flat=True), mission_id=mission_id
        )
        trail_enrollments = trail_enrollments.exclude(
            user_id__in=mission_enrollments_already_created.values_list("user_id", flat=True)
        )

        for enrollment in trail_enrollments:
            enrollments_batch = EnrollmentsBatch(
                mission_ids=[mission_id],
                user_ids=[enrollment.user_id],
                workspace_id=enrollment.workspace_id,
                goal_date=enrollment.goal_date,
                required=enrollment.required,
                triggered_by_internal_service=True,
            )
            self._mission_batch_enrollment_service.enroll(enrollments_batch)

    def _calc_progress(self, enrollment: LearningTrailEnrollment) -> float:
        steps = LearningTrailStep.objects.filter(learning_trail=enrollment.learning_trail)

        missions_to_exclude = steps.filter(
            ~Exists(MissionEnrollment.objects.filter(user=enrollment.user, mission_id=OuterRef("mission_id"))),
            mission__mission_model__in=[LIVE, PRESENTIAL],
            mission__development_status=CLOSED,
        ).values_list("mission__id", flat=True)
        if missions_to_exclude:
            steps = steps.exclude(mission_id__in=missions_to_exclude)

        mission_ids = steps.filter(
            mission_id__isnull=False, mission__development_status__in=self.mission_development_status_mandatory
        ).values_list("mission_id", flat=True)
        pulses_ids = steps.filter(pulse_id__isnull=False).values_list("pulse_id", flat=True)
        count_trail_objects = len(mission_ids) + len(pulses_ids)
        if not count_trail_objects:
            return MAX_ENROLLMENT_PROGRESS

        count_completed_missions = self._count_missions_enrollments_completed(enrollment, mission_ids)
        count_pulses_consumed = self._count_pulses_consumed(enrollment, pulses_ids)
        count_pulses_quises_completed = self._count_pulses_quizzes_completed(enrollment, pulses_ids)
        count_objects_consumed = count_completed_missions + count_pulses_consumed + count_pulses_quises_completed
        return (
            round(count_objects_consumed / count_trail_objects, 4) if count_trail_objects else MAX_ENROLLMENT_PROGRESS
        )

    @staticmethod
    def _count_pulses_consumed(enrollment: LearningTrailEnrollment, pulses_ids: Sequence[str]) -> int:
        pulses_activities = LearnContentActivity.objects.filter(
            pulse_id__in=pulses_ids, user_id=enrollment.user_id
        ).distinct("pulse_id")
        return Pulse.objects.filter(id__in=pulses_activities.values_list("pulse_id", flat=True)).count()

    @staticmethod
    def _count_pulses_quizzes_completed(enrollment: LearningTrailEnrollment, quiz_ids: Sequence[str]) -> int:
        quizzes = Exam.objects.filter(pulse_id__in=quiz_ids)
        questions_by_quizzes = {quiz.id: quiz.questions.all() for quiz in quizzes}
        all_questions_id = [question_id for questions in questions_by_quizzes.values() for question_id in questions]
        completed_answers = Answer.objects.filter(exam_has_question_id__in=all_questions_id, user_id=enrollment.user_id)
        quiz_completed_counts = completed_answers.values("exam_has_question__exam_id").annotate(
            completed_count=Count("exam_has_question")
        )

        # ruff: noqa: E731
        is_quiz_completed = lambda quiz_count: quiz_count["completed_count"] == len(
            questions_by_quizzes[quiz_count["exam_has_question__exam_id"]]
        )

        quizzes_with_all_questions_answered = [
            quiz_count["exam_has_question__exam_id"]
            for quiz_count in quiz_completed_counts
            if is_quiz_completed(quiz_count)
        ]
        return len(quizzes_with_all_questions_answered)

    @staticmethod
    def _count_missions_enrollments_completed(enrollment: LearningTrailEnrollment, mission_ids: Sequence[str]) -> int:
        completed_enrollments = MissionEnrollment.objects.filter(
            mission_id__in=mission_ids, user=enrollment.user, workspace=enrollment.workspace, status=COMPLETED
        ).distinct("mission_id", "user_id")
        return Mission.objects.filter(id__in=completed_enrollments.values_list("mission_id", flat=True)).count()

    @staticmethod
    def extend_deadline(trail_enrollment: LearningTrailEnrollment, new_goal_date: datetime):
        steps = LearningTrailStep.objects.filter(learning_trail=trail_enrollment.learning_trail).values_list(
            "mission_id", flat=True
        )
        enrollments = MissionEnrollment.objects.filter(mission_id__in=steps, user_id=trail_enrollment.user.id).exclude(
            status__in=[COMPLETED, ENROLLMENT_REPROVED, PENDING_VALIDATION]
        )
        enrollments.update(goal_date=new_goal_date)
        trail_enrollment.goal_date = new_goal_date
        trail_enrollment.save()

    def verify_integrate_after_create(self, id: str):
        enrollment = LearningTrailEnrollment.objects.get(id=id)
        duplicated_enrollment = self.enroll_service.get_another_enrollment_in_progress_query_set(enrollment).first()
        if not duplicated_enrollment:
            return

        most_recent = enrollment
        if duplicated_enrollment.created_date > enrollment.created_date:
            most_recent = duplicated_enrollment

        self.delete_enrollment(most_recent)

        self.discord_webhook_logger.emit_warning_message(
            "DELETED DUPLICATED LEARNING TRAIL ENROLLMENT", f"Deleted enrollment id: {most_recent.id}."
        )
