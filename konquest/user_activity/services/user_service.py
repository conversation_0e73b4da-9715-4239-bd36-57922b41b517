from account.models.user import User
from cachetools import TTLCache
from custom.keeps_exception_handler import KeepsNotFoundError
from django.db import transaction
from django.db.models import Avg, Q, QuerySet
from django.utils import timezone
from learning_trail.models import LearningTrail
from mission.models import Mission, MissionWorkspace
from pulse.services.channel_service import ChannelService
from user_activity.models import LearnContentActivity, LearningTrailEnrollment, MissionEnrollment

SESSION_CACHE_SIZE = 10000
SESSION_CACHE_TTL = 10 * 60  # 10 minutes


class UserService:
    user_cache = None

    def __init__(self, channel_service: ChannelService):
        self.__channel_service = channel_service
        self.__user = None
        self.__workspace_id = None

        if UserService.user_cache is None:
            UserService.user_cache = TTLCache(maxsize=SESSION_CACHE_SIZE, ttl=SESSION_CACHE_TTL)

    @staticmethod
    def update_user_access_time(user_id):
        # skip recent updates
        exists = UserService.user_cache.get(user_id)
        if exists:
            return

        with transaction.atomic():
            obj = User.objects.get(pk=user_id)
            obj.last_access_date = timezone.now()
            obj.save()

        UserService.user_cache[user_id] = user_id

    def get_user_mission_enrollments_resume(self, user_id: str, workspace_id: str, date_range: tuple = None):
        self.check_user_id(user_id)
        enrollments = MissionEnrollment.objects.filter(workspace_id=workspace_id, user_id=user_id)
        return self.get_enrollments_resume(enrollments, date_range)

    def get_user_trail_enrollments_resume(self, user_id: str, workspace_id: str, date_range: tuple = None):
        self.check_user_id(user_id)
        enrollments = LearningTrailEnrollment.objects.filter(workspace_id=workspace_id, user_id=user_id)
        return self.get_enrollments_resume(enrollments, date_range)

    @staticmethod
    def get_enrollments_resume(enrollments: QuerySet, date_range: tuple = None):
        enrollments_enrolled = enrollments.filter(status="ENROLLED", give_up=False)
        enrollments_started = enrollments.filter(status="STARTED", give_up=False)
        enrollments_completed = enrollments.filter(status="COMPLETED")
        enrollments_give_up = enrollments.filter(give_up=True)
        if date_range:
            invalid_date_range_exc = ValueError("date_range must me a single tuple: (less_date, greatest_date)")
            try:
                small_date = date_range[0]
                greatest_date = date_range[1]
            except IndexError as exc:
                raise invalid_date_range_exc from exc

            if not small_date or not greatest_date:
                raise invalid_date_range_exc

            enrollments = enrollments.filter(
                Q(created_date__gte=small_date, created_date__lte=greatest_date)
                | Q(start_date__gte=small_date, start_date__lte=greatest_date)
                | Q(
                    end_date__gte=small_date,
                    end_date__lte=greatest_date,
                )
            )
            enrollments_enrolled = enrollments_enrolled.filter(
                created_date__gte=small_date,
                created_date__lte=greatest_date,
            )
            enrollments_started = enrollments_started.filter(
                start_date__gte=small_date,
                start_date__lte=greatest_date,
            )
            enrollments_completed = enrollments_completed.filter(
                end_date__gte=small_date,
                end_date__lte=greatest_date,
            )
            enrollments_give_up = enrollments_give_up.filter(
                updated_date__gte=small_date,
                updated_date__lte=greatest_date,
            )

        progress = enrollments_started.aggregate(Avg("progress"))

        data = {
            "enrollments_progress": progress,
            "enrollments": enrollments.count(),
            "enrollments_enrolled": enrollments_enrolled.count(),
            "enrollments_started": enrollments_started.count(),
            "enrollments_completed": enrollments_completed.count(),
            "enrollments_give_up": enrollments_give_up.count(),
        }

        return data

    def get_user_activities_resume(self, user_id: str, workspace_id: str):
        self.__user = User.objects.filter(id=user_id).first()
        if not self.__user:
            raise KeepsNotFoundError(i18n="user_not_found", detail="user not found")
        self.__workspace_id = workspace_id

        missions_completed = self.__get_user_missions_completed()
        trails_completed = self.__get_user_trails_completed()
        pulses_consumed = self.__get_user_pulses_already_consumed()
        missions_created = MissionWorkspace.objects.filter(workspace_id=workspace_id, mission__user_creator=self.__user)
        data = {
            "missions_completed": len(missions_completed),
            "trails_completed": len(trails_completed),
            "pulses_consumed": len(pulses_consumed),
            "missions_created": len(missions_created.distinct("mission_id", "mission__user_creator")),
        }

        return data

    def __get_user_mission_enrollments(self, status: list, **kwargs):
        give_up = kwargs.get("give_up", False)
        enrollments = MissionEnrollment.objects.filter(
            user=self.__user, workspace_id=self.__workspace_id, give_up=give_up, status__in=status
        ).order_by("start_date", "mission_id")

        return enrollments

    def __get_user_trail_enrollments(self, status: list, **kwargs):
        give_up = kwargs.get("give_up", False)
        enrollments = LearningTrailEnrollment.objects.filter(
            user=self.__user, workspace_id=self.__workspace_id, give_up=give_up, status__in=status
        ).order_by("start_date", "learning_trail_id")

        return enrollments

    def __get_user_missions_completed(self):
        enrollments_completed = self.__get_user_mission_enrollments(status=["COMPLETED"])
        missions = Mission.objects.filter(id__in=enrollments_completed.values_list("mission_id", flat=True))

        return missions

    def __get_user_trails_completed(self):
        enrollments_completed = self.__get_user_trail_enrollments(status=["COMPLETED"])
        trails = LearningTrail.objects.filter(id__in=enrollments_completed.values_list("learning_trail_id", flat=True))

        return trails

    def __get_user_pulses_already_consumed(self):
        consumed_pulses = self.get_pulses_already_consumed(self.__user.id)
        allowed_pulses = self.__channel_service.get_allowed_pulses(
            user_id=self.__user.id, workspace_id=self.__workspace_id
        )

        return allowed_pulses.filter(id__in=consumed_pulses)

    @staticmethod
    def get_pulses_already_consumed(user_id: str):
        pulse_ids = LearnContentActivity.objects.filter(user_id=user_id, pulse__isnull=False).values_list(
            "pulse_id", flat=True
        )

        return pulse_ids

    @staticmethod
    def check_user_id(user_id):
        user = User.objects.filter(id=user_id).first()
        if not user:
            raise KeepsNotFoundError(i18n="user_not_found", detail="user not found")
