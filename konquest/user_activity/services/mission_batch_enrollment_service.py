from dataclasses import dataclass
from typing import List, Optional

from account.models import User
from constants import REQUIRED
from custom.exceptions.keeps_permission_error import KeepsPermissionError
from custom.keeps_exception_handler import KeepsRuntimeError, KeepsServiceError
from django.core.exceptions import ValidationError
from mission.models import Mission
from user_activity.exceptions.exceptions import MissionEnrollmentInProgressAlreadyExists
from user_activity.services.mission_enrollment_service import MissionEnrollmentService

# pylint: disable=R0903


@dataclass
class ServiceErrorDetail:
    error: str
    user: dict
    mission: dict


@dataclass
class EnrollmentsBatch:
    mission_ids: List[str]
    user_ids: List[str]
    workspace_id: str
    action_user: Optional[User] = None
    goal_date: Optional[str] = None
    required: Optional[bool] = False
    regulatory_compliance_cycle_id: Optional[str] = None
    triggered_by_internal_service: Optional[bool] = False
    learning_trail_enrolment_id: Optional[str] = None


class MissionBatchEnrollmentService:
    def __init__(self, mission_enrollment_service: MissionEnrollmentService):
        self._enrollment_errors = {"enrollment_errors": []}
        self._errors = self._enrollment_errors["enrollment_errors"]
        self._mission_enrollment_service = mission_enrollment_service

    def enroll(self, enrollments_batch: EnrollmentsBatch):
        if not enrollments_batch.user_ids or not enrollments_batch.mission_ids:
            self._enrollment_errors["enrollment_errors"].append(
                {"error": {"i18n": "not_found_missions_or_users", "detail": "not found missions or users"}}
            )
            return self._enrollment_errors
        if not enrollments_batch.action_user and not enrollments_batch.triggered_by_internal_service:
            raise KeepsRuntimeError(
                "No action user (action_user) provided. Please provide an action user for the batch or "
                "indicate if it was triggered by an internal service (triggered_by_internal_service)."
            )

        for mission_id in enrollments_batch.mission_ids:
            for user_id in enrollments_batch.user_ids:
                user = User.objects.filter(id=user_id).first()
                mission = Mission.objects.filter(id=mission_id).first()
                enrollment = {
                    "user_id": user_id,
                    "mission_id": mission_id,
                    "workspace_id": enrollments_batch.workspace_id,
                    "goal_date": enrollments_batch.goal_date,
                    "learning_trail_enrollment_id": enrollments_batch.learning_trail_enrolment_id,
                    REQUIRED: enrollments_batch.required,
                }
                try:
                    self._mission_enrollment_service.enroll_user(
                        enrollment,
                        enrollments_batch.action_user,
                        enrollments_batch.regulatory_compliance_cycle_id,
                        enrollments_batch.triggered_by_internal_service,
                    )
                except (
                    KeepsServiceError,
                    ValidationError,
                    User.DoesNotExist,
                    Mission.DoesNotExist,
                    KeepsPermissionError,
                    MissionEnrollmentInProgressAlreadyExists,
                ) as exception:
                    exception_data = self._format_exception(
                        exception=exception, mission_id=mission_id, mission=mission, user_id=user_id, user=user
                    )
                    self._errors.append(exception_data)

        return self._enrollment_errors

    def _format_exception(
        self, exception: Exception, mission_id: str, mission: Optional[Mission], user_id: str, user: User
    ):
        user_data = {"id": user_id, "email": user.email} if user else {"id": user_id}
        mission_data = {"id": mission_id, "name": mission.name} if mission else {"id": mission_id}

        error_info = self._format_error_info(exception)

        return {"user": user_data, "mission": mission_data, "error": error_info}

    @staticmethod
    def _format_error_info(exception: Exception):
        if isinstance(exception, ValidationError):
            return {"i18n": exception.messages[0], "detail": exception.messages[0]}
        if isinstance(exception, KeepsServiceError):
            return {"i18n": exception.msg, "detail": exception.description}
        if isinstance(exception, User.DoesNotExist):
            return {"i18n": "user_not_found", "detail": "User not found"}
        if isinstance(exception, Mission.DoesNotExist):
            return {"i18n": "mission_not_found", "detail": "Mission not found"}
        if isinstance(exception, KeepsPermissionError):
            return {"i18n": "not_allowed", "detail": exception.message}

        return {"i18n": "an_unexpected_error_has_occurred", "detail": "an unexpected error has occurred"}
