import os
import uuid
from collections import OrderedDict
from typing import List, Sequence, Union

from account.models import User
from authentication.keeps_permissions import USER
from config.settings import TEMP_FOLDER
from custom.keeps_exception_handler import KeepsPermissionError, KeepsRuntimeError
from custom.types import AttendanceModelsMap
from django.db import transaction
from django.db.models import QuerySet
from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop
from mission.models import LiveMissionDates, PresentialMissionDates
from mission.models.mission import LIVE, PRESENTIAL, Mission
from mission.rules import is_mission_contributor
from mission.services.mission_service import MissionService
from pandas import DataFrame
from user_activity.dtos.person import Person
from user_activity.models import LiveAttendance, MissionEnrollment, PresentialAttendance

# pylint: disable=R0903
from user_activity.models.mission_enrollment import SYNC_STATUS_ACCEPTS
from user_activity.selectors.sync_attendance_selector import SyncAttendanceSelector
from user_activity.services.mission_enrollment_service import MissionEnrollmentService
from utils.aws import S3Client

NOT_ALLOWED_TO_ATTEND = gettext_noop("not_allowed_to_attend")
SELF_ATTENDANCE_IS_BLOCKED_TO_THIS_PERIOD = gettext_noop("self_attendance_is_blocked_to_this_period")
NAME = gettext_noop("name")
EMAIL = gettext_noop("email")
PRESENTED = gettext_noop("presented")
PERIOD = gettext_noop("period")

AttendanceTypes = Union[LiveAttendance, PresentialAttendance]


class MissionEnrollmentAttendanceService:
    def __init__(
        self,
        s3_client: S3Client,
        selector: SyncAttendanceSelector,
        mission_enrollment_service: MissionEnrollmentService,
        attendance_models: AttendanceModelsMap,
    ):
        self._s3_client = s3_client
        self._selector = selector
        self._attendance_models = attendance_models
        self.mission_enrollment_service = mission_enrollment_service

    def _check_permission_to_attend(
        self, user_id: str, attendance: AttendanceTypes, workspace_id: str, role: str
    ) -> None:
        mission_model = self._get_attendance_mission_model(attendance)
        is_user_consumer = role == USER

        user = User.objects.get(id=user_id)
        is_contributor = is_mission_contributor(user, attendance.enrollment.mission)

        blocked_self_attendance = not attendance.date.allow_self_attendance
        allowed_attendances = self._selector.get_allowed_attendances(user_id, workspace_id, mission_model, role)
        not_allowed_attendance = not allowed_attendances.filter(id=attendance.id).exists()

        if not is_contributor:
            if not_allowed_attendance:
                raise KeepsPermissionError(_(NOT_ALLOWED_TO_ATTEND))
            if blocked_self_attendance and is_user_consumer:
                raise KeepsPermissionError(_(SELF_ATTENDANCE_IS_BLOCKED_TO_THIS_PERIOD))

    @transaction.atomic()
    def check(
        self, attendance: AttendanceTypes, presented: bool, role: str, user_id: str, workspace_id: str
    ) -> AttendanceTypes:
        self._check_permission_to_attend(user_id, attendance, workspace_id, role)
        attendance.presented = presented
        attendance.save()
        return attendance

    def check_in_batch(
        self, attendances: Sequence[AttendanceTypes], presented: bool, role: str, user_id: str, workspace_id: str
    ) -> None:
        for attendance in attendances:
            self.check(attendance, presented, role, user_id, workspace_id)

    @staticmethod
    def _get_attendance_mission_model(attendance: AttendanceTypes) -> str:
        if isinstance(attendance, LiveAttendance):
            mission_model = LIVE
        elif isinstance(attendance, PresentialAttendance):
            mission_model = PRESENTIAL
        else:
            raise KeepsRuntimeError(f"The {type(attendance)} is an invalid Attendance Type")
        return mission_model

    @transaction.atomic()
    def create_attendances_to_enrollment(
        self, enrollment: MissionEnrollment
    ) -> Sequence[Union[LiveAttendance, PresentialAttendance]]:
        mission = enrollment.mission
        attendance_model = self._get_model(mission.mission_model)
        dates = mission.sync.dates.filter()

        attendances = []
        for date in dates:
            attendance = attendance_model(date=date, enrollment=enrollment, id=uuid.uuid4())
            attendances.append(attendance)
        attendance_model.objects.bulk_create(attendances, ignore_conflicts=True)
        return attendances

    @transaction.atomic()
    def create_attendances_to_date(
        self, date: Union[LiveMissionDates, PresentialMissionDates]
    ) -> Sequence[AttendanceTypes]:
        mission = date.mission
        attendance_model = self._get_model(mission.mission_model)
        enrollments = mission.missionenrollment_set.filter(status__in=SYNC_STATUS_ACCEPTS)
        attendances_already_exists = attendance_model.objects.filter(date=date, enrollment__in=enrollments)
        enrollment_attendances_to_create = enrollments.exclude(
            pk__in=attendances_already_exists.values("enrollment_id")
        )

        attendances = []
        for enrollment in enrollment_attendances_to_create:
            attendance = attendance_model(date=date, enrollment=enrollment)
            attendances.append(attendance)
        attendance_model.objects.bulk_create(attendances)
        return attendances

    def delete_attendances_enrollment(self, enrollment: MissionEnrollment) -> int:
        model = self._get_model(enrollment.mission.mission_model)
        return model.objects.filter(enrollment=enrollment).delete()

    def _get_model(self, mission_model: str):
        try:
            return self._attendance_models[mission_model]
        except KeyError as exc:
            raise KeepsRuntimeError("invalid_attendance_model_to_this_mission_model") from exc

    def generate_report(self, mission_id: str, user_id: str, workspace_id: str, mission_model: str, role: str) -> dict:
        attendances = self._selector.get_mission_attendances(mission_id, user_id, workspace_id, mission_model, role)
        file_name = os.path.join(TEMP_FOLDER, f"{uuid.uuid4()}.csv")
        data = self._build_report_data(attendances)
        data.to_csv(file_name, index=False)
        upload_response = self._s3_client.send_file_path(
            file_name, f"presential_attendances/{str(uuid.uuid4())}.csv", "text/csv"
        )
        return upload_response

    @staticmethod
    def _build_report_data(attendances: QuerySet) -> DataFrame:
        data = {NAME: [], EMAIL: [], PRESENTED: [], PERIOD: []}

        for attendance in attendances:
            data[NAME].append(attendance.enrollment.user.name)
            data[EMAIL].append(attendance.enrollment.user.email)
            data[PRESENTED].append(attendance.presented)
            data[PERIOD].append(f"{attendance.date.start_at} - {attendance.date.end_at}")

        data_frame = DataFrame(data)
        data_frame.rename({NAME: _(NAME), EMAIL: _(EMAIL), PRESENTED: _(PRESENTED), PERIOD: _(PERIOD)})
        return data_frame

    def get_valid_date(self, date_id: str) -> Union[LiveMissionDates, PresentialMissionDates]:
        date = LiveMissionDates.objects.filter(id=date_id).first()
        if not date:
            date = PresentialMissionDates.objects.filter(id=date_id).first()
        return date

    def filter_persons_not_enrolled(self, users: List[Person], mission: Mission) -> List[Person]:
        users_not_enrolled: List[Person] = []
        emails = [user.email for user in users]
        enrolled_emails = MissionEnrollment.objects.filter(mission=mission, user__email__in=emails).values_list(
            "user__email", flat=True
        )

        for user in users:
            if user.email not in enrolled_emails:
                users_not_enrolled.append(user)
        return users_not_enrolled

    def update_seats(self, date: Union[LiveMissionDates, PresentialMissionDates], seats_needed: int) -> None:
        MissionService.sync_increase_seats(date.sync_object, seats_needed)

    def enroll_persons(self, users: List[Person], mission: Mission, workspace_id: str, token: str) -> None:
        for user in users:
            self.mission_enrollment_service.force_enroll_person(
                email=user.email, name=user.name, mission_id=mission.id, workspace_id=workspace_id, token=token
            )

    def check_in_batch_force(
        self,
        persons: OrderedDict,
        date_id: str,
        role: str,
        user_id: str,
        workspace_id: str,
        token: str,
    ) -> AttendanceTypes:
        persons = [Person(**person) for person in persons]
        date = self.get_valid_date(date_id)
        mission = date.mission
        users_not_enrolled = self.filter_persons_not_enrolled(persons, mission)
        self.update_seats(date, len(users_not_enrolled))
        self.enroll_persons(users_not_enrolled, mission, workspace_id, token)
        self.create_attendances_to_date(date)
        attendances = self.get_attendances_to_persons(persons, date)
        self.check_in_batch(attendances, True, role, user_id, workspace_id)

    def get_attendances_to_persons(
        self, persons: List[Person], date: Union[LiveMissionDates, PresentialMissionDates]
    ) -> Sequence[AttendanceTypes]:
        attendance_model = self._get_model(date.mission.mission_model)
        attendances = attendance_model.objects.filter(
            enrollment__user__email__in=[person.email for person in persons], date=date
        )
        return attendances
