from typing import List

from account.models.user import User
from django.utils import timezone
from django.utils.translation import gettext_noop as _
from notification.dtos.message_v2 import MessageV2
from notification.services.notification_service_v2 import NotificationServiceV2
from user_activity.dtos.mission_enrollment_provider_dto import MissionEnrollmentProviderDto
from user_activity.models.learn_content_activity import LearnContentActivity
from user_activity.models.mission_enrollment import COMPLETED, MissionEnrollment
from user_activity.services.mission_enrollment_service import MissionEnrollmentService
from utils.task_transaction import task_transaction

EVALUATE_THE_COURSE_PROVIDER = _("evaluate_the_course_%(provider)s")
RATE_THE_COURSE_COMPLETED_BY_YOU = _("evaluate_the_course_%(course)s_completed_by_you")


class MissionEnrollmentProviderSyncService:
    def __init__(self, mission_enrollment_service: MissionEnrollmentService):
        self.mission_enrollment_service = mission_enrollment_service

    def sync_enrollment(self, enrollments_providers: List[MissionEnrollmentProviderDto]):
        for enrollment in enrollments_providers:
            user = User.objects.filter(email=enrollment.email).first()
            if user:
                mission_enrollment = self.create_enrollment(enrollment, user.id)
                if mission_enrollment.status == COMPLETED:
                    continue
                if enrollment.progress:
                    self.update_progress(mission_enrollment, enrollment.progress)
                if enrollment.certificate_url:
                    self.finish_mission(mission_enrollment, enrollment.certificate_url)

    def update_progress(self, mission_enrollment: MissionEnrollment, progress):
        mission_enrollment.progress = progress / 100
        mission_enrollment.save()

    def finish_mission(self, mission_enrollment: MissionEnrollment, certificate_url):
        if mission_enrollment.is_done:
            return
        mission_enrollment.status = COMPLETED
        mission_enrollment.performance = mission_enrollment.progress
        mission_enrollment.certificate_provider_url = certificate_url
        mission_enrollment.end_date = timezone.now()
        if mission_enrollment.mission.duration_time:
            time_in = mission_enrollment.mission.duration_time * mission_enrollment.progress
        else:
            time_in = 0
        if mission_enrollment.mission.points:
            mission_enrollment.points = mission_enrollment.mission.points * mission_enrollment.progress
        LearnContentActivity(
            user=mission_enrollment.user,
            mission_enrollment=mission_enrollment,
            action="WATCH",
            workspace=mission_enrollment.workspace,
            time_in=time_in,
            time_start=mission_enrollment.start_date,
        )
        mission_enrollment.save()
        self.notify_evaluate(
            mission_id=mission_enrollment.mission.id,
            mission_name=mission_enrollment.mission.name,
            user_id=mission_enrollment.user.id,
            workspace_id=mission_enrollment.workspace.id,
        )

    def create_enrollment(self, enrollment_provider: MissionEnrollmentProviderDto, user_id: str):
        enrollment = MissionEnrollment.objects.filter(
            user_id=user_id, mission_id=enrollment_provider.mission_id
        ).first()
        if enrollment:
            return enrollment

        mission_enrollment = self.mission_enrollment_service.enroll_user(
            {
                "user_id": user_id,
                "mission_id": enrollment_provider.mission_id,
                "workspace_id": enrollment_provider.workspace_id,
            },
            triggered_by_internal_service=True,
        )
        return mission_enrollment

    def notify_evaluate(self, mission_id: str, mission_name: str, user_id: str, workspace_id: str):
        message = MessageV2(
            title=EVALUATE_THE_COURSE_PROVIDER,
            description=RATE_THE_COURSE_COMPLETED_BY_YOU,
            title_values={"provider": "Alura"},
            description_values={"course": mission_name},
        )

        with task_transaction(self.notify_evaluate.__name__, NotificationServiceV2) as notification_service:
            notification_service.create_notification(
                user_ids=[user_id],
                type_key="MIRRORED_COURSE_EVALUATION",
                action="LIST",
                object=mission_id,
                message=message,
                workspace_id=workspace_id,
            )
