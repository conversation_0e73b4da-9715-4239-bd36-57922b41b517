import os
import uuid

from config.settings import TEMP_FOLDER
from django.db.models import QuerySet
from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop
from pandas import DataFrame
from user_activity.models import MissionEnrollment
from utils.aws import S3Client

CSV_FORMAT = "text/csv"
MISSION_ENROLLMENT_REPORT_FOLDER = "mission_enrollment_reports"
NAME = gettext_noop("name")
EMAIL = gettext_noop("email")
CREATED_DATE = gettext_noop("created_date")
START_DATE = gettext_noop("start_date")
STATUS = gettext_noop("status")
DATE_FORMAT = "date_format"


class MissionEnrollmentReportService:
    def __init__(self, s3_client: S3Client):
        self._s3_client = s3_client

    def generate_mission_enrollments(self, mission_id: str, workspace_id: str) -> dict:
        enrollments = MissionEnrollment.objects.filter(mission_id=mission_id, workspace_id=workspace_id)
        file_name = os.path.join(TEMP_FOLDER, f"{uuid.uuid4()}.csv")
        data = self._build_enrollments_report_data(enrollments)
        data.to_csv(file_name, index=False)
        upload_response = self._s3_client.send_file_path(
            file_name, f"{MISSION_ENROLLMENT_REPORT_FOLDER}/{str(uuid.uuid4())}.csv", CSV_FORMAT
        )
        return upload_response

    @staticmethod
    def _build_enrollments_report_data(enrollments: QuerySet) -> DataFrame:
        data = {NAME: [], EMAIL: [], CREATED_DATE: [], STATUS: []}
        date_format = _(DATE_FORMAT)

        for enrollment in enrollments:
            data[NAME].append(enrollment.user.name)
            data[EMAIL].append(enrollment.user.email)
            data[CREATED_DATE].append(enrollment.created_date.strftime(date_format))
            data[STATUS].append(_(enrollment.status))

        data_frame = DataFrame(data)
        for key in data:
            data_frame.rename(columns={key: _(key)}, inplace=True)
        return data_frame
