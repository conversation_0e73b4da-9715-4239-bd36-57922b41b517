from constants import ENR<PERSON>LMENT_ENROLLED, ENROLLMENT_STARTED
from custom.types import AttendanceModelsMap
from django.utils.timezone import now
from user_activity.models import MissionEnrollment
from user_activity.models.mission_enrollment import COMPLETED, REFUSED, REPROVED


class MissionEnrollmentCloseService:
    def __init__(self, attendance_models: AttendanceModelsMap):
        self._attendance_models = attendance_models

    def close_enrollment(self, enrollment: MissionEnrollment):
        able_to_complete = enrollment.status in [ENROLLMENT_ENROLLED, ENROLLMENT_STARTED]
        if not able_to_complete:
            enrollment.status = REFUSED
            return enrollment
        dates_count = enrollment.mission.sync.sync_dates.count()

        attendance_model = self._attendance_models[enrollment.mission.mission_model]
        attendances_count = attendance_model.objects.filter(presented=True, enrollment=enrollment).count()

        enrollment.end_date = now()

        if dates_count == attendances_count:
            return self._complete_enrollment(enrollment)
        return self._reprove_enrollment(enrollment)

    def _reprove_enrollment(self, enrollment: MissionEnrollment) -> MissionEnrollment:
        enrollment.status = REPROVED
        enrollment.performance = 0
        enrollment.points = 0
        return enrollment

    def _complete_enrollment(self, enrollment: MissionEnrollment) -> MissionEnrollment:
        enrollment.status = COMPLETED
        enrollment.performance = 1
        enrollment.points = 1000
        return enrollment
