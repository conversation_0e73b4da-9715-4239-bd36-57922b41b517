from account.models import User
from constants import RULE_CAN_CHANGE_MISSION_ENROLLMENT
from custom.keeps_exception_handler import KeepsNoPermissionToEditLearningTrailEnrollment
from rules import test_rule
from user_activity.models import MissionEnrollment


class MissionEnrollmentUpdateService:
    def update_enrollment(self, enrollment_id: str, validated_data: dict, request_user: User) -> MissionEnrollment:
        enrollment = MissionEnrollment.objects.get(id=enrollment_id)

        if not test_rule(RULE_CAN_CHANGE_MISSION_ENROLLMENT, request_user, enrollment):
            raise KeepsNoPermissionToEditLearningTrailEnrollment()
        enrollment.update(**validated_data)

        enrollment.save()
        return enrollment
