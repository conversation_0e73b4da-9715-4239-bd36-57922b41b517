from uuid import UUID

from celery import shared_task
from config import settings
from django.db.models import QuerySet
from django.forms import model_to_dict
from django.utils.translation import gettext_noop as _
from mission.models.mission import LIVE, PRESENTIAL
from myaccount.application.services.myaccount_service import MyAccountService
from notification.services.notification_service import Message
from notification.services.notification_service_v2 import NotificationServiceV2
from pytz import timezone
from user_activity.models import MissionEnrollment
from user_activity.notifications.enrolled_in_trail_by_admin_notification import EnrolledInTrailByAdminNotification
from user_activity.notifications.mission_enrollment_restarted_notification import MissionEnrollmentRestartedNotification
from user_activity.task_names import NOTIFY_SYNC_ENROLLMENT_REFUSED
from user_activity.tasks.learning_trail_enrollment_expiring_notification_task import (
    LearningTrailEnrollmentExpiringNotificationTask,
)
from utils.email_service.notification import notify_users
from utils.task_transaction import task_transaction

YOU_HAVE_A_NEW_CERTIFICATE_TO_REVIEW = _("you_have_a_new_certificate_to_review")
YOUR_ENROLLMENT_HAS_BEEN_REVIEWED = _("your_enrollment_has_been_reviewed")
YOUR_ENROLLMENT_WAS_REFUSED = _("your_enrollment_was_refused")
YOUR_ENROLLMENT_WAS_APPROVED = _("your_enrollment_was_approved")
YOU_HAVE_A_MISSION_ENROLLMENT_REQUEST_EXTENSION_TO_REVIEW = _(
    "you_have_a_mission_enrollment_request_extension_to_review"
)
YOUR_MISSION_ENROLLMENT_HAS_EXPIRED = _("your_mission_enrollment_has_expired")
YOU_HAVE_BEEN_ENROLLED_IN_A_MISSION = _("you_have_been_enrolled_in_a_mission")
YOUR_MISSION_ENROLLMENT_DEADLINE_HAS_BEEN_EXTENDED = _("your_mission_enrollment_deadline_has_been_extended")
YOU_HAVE_BEEN_ENROLLED_IN_A_TRAIL_AND_ENROLLED_IN_ALL_YOUR_MISSIONS = _(
    "you_have_been_enrolled_in_a_trail_and_enrolled_in_all_your_missions"
)
YOUR_MISSION_ENROLLMENT_EXPIRES_TODAY = _("your_mission_enrollment_expires_today")
YOUR_MISSION_ENROLLMENT_EXPIRES_TOMORROW = _("your_mission_enrollment_expires_tomorrow")
YOUR_MISSION_ENROLLMENT_EXPIRES_IN_X_DAYS = _("your_mission_enrollment_expires_in_%(days)s_days")


@shared_task(ignore_result=True)
def notify_new_mission_enrollment_certificate_to_review(mission_enrollment_id: UUID) -> None:
    with task_transaction(
        notify_new_mission_enrollment_certificate_to_review.__name__, MyAccountService
    ) as myaccount_service:
        myaccount_service: MyAccountService
        enrollment = MissionEnrollment.objects.get(id=mission_enrollment_id)
        workspace_admins = myaccount_service.list_all_admin_users(str(enrollment.workspace_id))

    with task_transaction("notify_new_mission_enrollment_certificate_to_review", NotificationServiceV2) as service:
        message = Message(title=YOU_HAVE_A_NEW_CERTIFICATE_TO_REVIEW, description=enrollment.user.name)

        for user in workspace_admins:
            service.create_notification(
                user_ids=[user.id],
                type_key="NEW_EXTERNAL_ENROLLMENT_CERTIFICATE_TO_REVIEW",
                action="LIST",
                message=message,
                object=mission_enrollment_id,
                workspace_id=enrollment.workspace_id,
            )
        _send_new_mission_enrollment_certificate_email(enrollment, workspace_admins)


def _send_new_mission_enrollment_certificate_email(enrollment: MissionEnrollment, users: QuerySet) -> None:
    workspace = enrollment.workspace
    email_data = {
        "user_name": enrollment.user.name,
        "user_email": enrollment.user.email,
        "mission": enrollment.mission.name,
        "workspace_name": workspace.name,
        "mission_vertical_cover_image": enrollment.mission.thumb_image,
        "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
            enrollment.workspace.hash_id, enrollment.mission.id
        ),
    }
    users_data = [model_to_dict(user) for user in users]
    notify_users.delay(
        email_data=email_data,
        message_key="new_enrollment_to_review",
        workspace_id=workspace.id,
        users_receivers=users_data,
    )


@shared_task(ignore_result=True)
def notify_enrollment_certificate_reviewed(enrollment_id: UUID) -> None:
    with task_transaction("notify_enrollment_certificate_reviewed", NotificationServiceV2) as service:
        enrollment = MissionEnrollment.objects.get(id=enrollment_id)
        message = Message(title=YOUR_ENROLLMENT_HAS_BEEN_REVIEWED, description=enrollment.mission.name)
        service.create_notification(
            user_ids=[enrollment.user_id],
            type_key="EXTERNAL_ENROLLMENT_REVIEWED",
            action="LIST",
            message=message,
            object=enrollment.mission_id,
            workspace_id=enrollment.workspace_id,
        )


@shared_task(name=NOTIFY_SYNC_ENROLLMENT_REFUSED, ignore_result=True)
def notify_sync_enrollment_refused(enrollment_id: UUID) -> None:
    with task_transaction("notify_sync_enrollment_refused", NotificationServiceV2) as service:
        enrollment = MissionEnrollment.objects.get(id=enrollment_id)
        message = Message(title=YOUR_ENROLLMENT_WAS_REFUSED, description=enrollment.mission.name)
        service.create_notification(
            user_ids=[enrollment.user_id],
            type_key="MISSION_ENROLLMENT_REFUSED",
            action="ENROLLED",
            message=message,
            object=enrollment.mission_id,
            workspace_id=enrollment.workspace_id,
        )


@shared_task(ignore_result=True)
def notify_sync_enrollment_approved(enrollment_id: UUID) -> None:
    with task_transaction("notify_sync_enrollment_approved", NotificationServiceV2) as service:
        enrollment = MissionEnrollment.objects.get(id=enrollment_id)
        message = Message(title=YOUR_ENROLLMENT_WAS_APPROVED, description=enrollment.mission.name)
        service.create_notification(
            user_ids=[enrollment.user_id],
            type_key="MISSION_ENROLLMENT_APPROVED",
            action="ENROLLED",
            message=message,
            object=enrollment.mission_id,
            workspace_id=enrollment.workspace_id,
        )
        send_sync_enrollment_approved_email.delay(enrollment.id)


@shared_task(ignore_result=True)
def send_sync_enrollment_approved_email(enrollment_id: str) -> None:
    with task_transaction("send_sync_enrollment_approved_email"):
        enrollment: MissionEnrollment = MissionEnrollment.objects.get(id=enrollment_id)
        mission = enrollment.mission
        first_date = mission.sync.dates.filter().order_by("start_at").first()
        start_date = None
        start_time = None
        if first_date:
            start_date = first_date.start_at.date()
            start_time = first_date.start_at.time()
        email_data = {
            "mission_name": mission.name,
            "mission_start_date": start_date,
            "mission_start_time": start_time,
            "mission_seats": mission.sync.seats,
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
                enrollment.workspace.hash_id, mission.id
            ),
            "user_name": enrollment.user.name,
            "mission_vertical_cover_image": mission.thumb_image,
        }
        if mission.mission_model == PRESENTIAL:
            email_data.update({"mission_address": mission.sync.address})
        if mission.mission_model == LIVE:
            email_data.update({"mission_url": mission.sync.url})
        user_data = model_to_dict(enrollment.user)
        message_key = f"{enrollment.mission.mission_model}_enrollment_accepted".lower()
        notify_users(
            email_data=email_data,
            message_key=message_key,
            users_receivers=[user_data],
            workspace_id=enrollment.workspace_id,
        )


@shared_task(ignore_result=True)
def notify_mission_enrollment_request_extension(mission_enrollment_id: UUID):
    with task_transaction(
        notify_mission_enrollment_request_extension.__name__, MyAccountService
    ) as myaccount_service:
        myaccount_service: MyAccountService
        enrollment = MissionEnrollment.objects.get(id=mission_enrollment_id)
        admins = myaccount_service.list_all_admin_users(str(enrollment.workspace_id))

    with task_transaction("notify_mission_enrollment_request_extension", NotificationServiceV2) as service:
        enrollment = MissionEnrollment.objects.get(id=mission_enrollment_id)
        message = Message(
            title=YOU_HAVE_A_MISSION_ENROLLMENT_REQUEST_EXTENSION_TO_REVIEW, description=enrollment.user.name
        )

        for admin in admins:
            service.create_notification(
                user_ids=[admin.id],
                type_key="USER_REQUESTED_MISSION_ENROLLMENT_DEADLINE_EXTENSION",
                action="ANALYSE_EXTENSION_REQUEST",
                message=message,
                object=mission_enrollment_id,
                workspace_id=enrollment.workspace_id,
            )


@shared_task(ignore_result=True)
def notify_mission_enrollment_has_expired(enrollment_id: UUID) -> None:
    with task_transaction("notify_mission_enrollment_has_expired", NotificationServiceV2) as service:
        enrollment = MissionEnrollment.objects.get(id=enrollment_id)
        message = Message(title=YOUR_MISSION_ENROLLMENT_HAS_EXPIRED, description=enrollment.mission.name)
        service.create_notification(
            user_ids=[enrollment.user_id],
            type_key="MISSION_ENROLLMENT_HAS_EXPIRED",
            action="EXPIRED",
            message=message,
            object=enrollment.mission_id,
            workspace_id=enrollment.workspace_id,
        )


@shared_task(ignore_result=True)
def notify_enrolled_in_a_new_mission(mission_enrollment_id: UUID) -> None:
    with task_transaction("notify_enrolled_in_a_new_mission", NotificationServiceV2) as service:
        enrollment = MissionEnrollment.objects.get(id=mission_enrollment_id)
        message = Message(title=YOU_HAVE_BEEN_ENROLLED_IN_A_MISSION, description=enrollment.mission.name)
        service.create_notification(
            user_ids=[enrollment.user_id],
            type_key="ENROLLED_IN_A_MISSION",
            action="ENROLLED",
            object=enrollment.mission_id,
            message=message,
            workspace_id=enrollment.workspace_id,
        )
        _send_enrolled_in_a_new_mission_email(enrollment)


def _send_enrolled_in_a_new_mission_email(enrollment: MissionEnrollment) -> None:
    mission_model = enrollment.mission.mission_model
    if mission_model == LIVE:
        _send_user_enrolled_in_a_live_mission_email(enrollment)
    if mission_model == PRESENTIAL:
        _send_user_enrolled_in_a_presential_mission_email(enrollment)


def _send_user_enrolled_in_a_presential_mission_email(enrollment: MissionEnrollment) -> None:
    mission = enrollment.mission
    user = enrollment.user
    presential = mission.presential
    start_at = presential.dates.filter().order_by("start_at").first().start_at.astimezone(timezone(user.time_zone))
    email_data = {
        "mission_address": presential.address,
        "mission_name": mission.name,
        "mission_start_date": start_at.date(),
        "mission_start_time": start_at.time(),
        "mission_seats": presential.seats,
        "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
            enrollment.workspace.hash_id, mission.id
        ),
        "user_name": user.name,
        "workspace_name": enrollment.workspace.name,
        "mission_vertical_cover_image": mission.thumb_image,
    }
    receivers = [model_to_dict(user)]
    notify_users.delay(email_data, "user_enrolled_in_a_presential_mission", enrollment.workspace_id, receivers)


def _send_user_enrolled_in_a_live_mission_email(enrollment: MissionEnrollment) -> None:
    mission = enrollment.mission
    user = enrollment.user
    live = mission.live
    start_at = live.dates.filter().order_by("start_at").first().start_at.astimezone(timezone(user.time_zone))
    email_data = {
        "mission_name": mission.name,
        "mission_start_date": start_at.date(),
        "mission_start_time": start_at.time(),
        "mission_seats": live.seats,
        "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
            enrollment.workspace.hash_id, mission.id
        ),
        "user_name": user.name,
        "workspace_name": enrollment.workspace.name,
        "mission_vertical_cover_image": mission.thumb_image,
        "mission_url": mission.sync.url,
    }
    receivers = [model_to_dict(user)]
    notify_users.delay(email_data, "user_enrolled_in_a_live_mission", enrollment.workspace_id, receivers)


@shared_task(ignore_result=True)
def notify_mission_enrollment_deadline_extended(mission_enrollment_id: UUID) -> None:
    with task_transaction("notify_mission_enrollment_deadline_extended", NotificationServiceV2) as service:
        enrollment = MissionEnrollment.objects.get(id=mission_enrollment_id)
        message = Message(title=YOUR_MISSION_ENROLLMENT_DEADLINE_HAS_BEEN_EXTENDED, description=enrollment.mission.name)
        service.create_notification(
            user_ids=[enrollment.user_id],
            type_key="MISSION_ENROLLMENT_DEADLINE_HAS_BEEN_EXTENDED",
            action="DEADLINE_EXTENDED",
            object=enrollment.mission_id,
            message=message,
            workspace_id=enrollment.workspace_id,
        )


@shared_task(ignore_result=True)
def notify_mission_enrollment_is_expiring(mission_enrollment_id: UUID, days_remaining: int) -> None:
    with task_transaction("notify_mission_enrollment_is_expiring", NotificationServiceV2) as service:
        enrollment = MissionEnrollment.objects.get(id=mission_enrollment_id)
        title_values = {}
        if days_remaining == 0:
            title = YOUR_MISSION_ENROLLMENT_EXPIRES_TODAY
        elif days_remaining == 1:
            title = YOUR_MISSION_ENROLLMENT_EXPIRES_TOMORROW
        else:
            title = YOUR_MISSION_ENROLLMENT_EXPIRES_IN_X_DAYS
            title_values = {"days": days_remaining}
        message = Message(
            title=title,
            title_values=title_values,
            description=enrollment.mission.name,
        )

        service.create_notification(
            user_ids=[enrollment.user_id],
            type_key="MISSION_ENROLLMENT_IS_EXPIRING",
            action="EXPIRING",
            workspace_id=enrollment.workspace_id,
            object=enrollment.mission_id,
            message=message,
        )


@shared_task(ignore_result=True)
def notify_mission_enrollment_restarted(mission_enrollment_id: UUID) -> None:
    with task_transaction(
        notify_mission_enrollment_restarted.__name__, MissionEnrollmentRestartedNotification
    ) as service:
        service.send(mission_enrollment_id)


@shared_task(ignore_result=True)
def notify_all_trail_enrollments_expiring(remaining_days: int) -> None:
    with task_transaction(
        notify_all_trail_enrollments_expiring.__name__, LearningTrailEnrollmentExpiringNotificationTask
    ) as service:
        service.notify_enrollments(remaining_days)


@shared_task(ignore_result=True)
def notify_enrolled_in_trail_by_admin(mission_enrollment_id: UUID) -> None:
    with task_transaction(notify_enrolled_in_trail_by_admin.__name__, EnrolledInTrailByAdminNotification) as service:
        service.send(mission_enrollment_id)


@shared_task(ignore_result=True)
def notify_internal_mission_enrollment_approved_by_admin(mission_enrollment_id: UUID) -> None:
    with task_transaction(
        notify_internal_mission_enrollment_approved_by_admin.__name__, NotificationServiceV2
    ) as service:
        enrollment = MissionEnrollment.objects.get(id=mission_enrollment_id)
        message = Message(
            title=_("your_reproved_mission_enrollment_has_been_manually_reviewed_and_approved"),
            description=enrollment.mission.name,
        )

        service.create_notification(
            user_ids=[enrollment.user_id],
            type_key="REPROVED_ENROLLMENT_HAS_BEEN_MANUALLY_APPROVED",
            action="MANUALLY_APPROVED",
            workspace_id=enrollment.workspace_id,
            object=enrollment.mission_id,
            message=message,
        )
