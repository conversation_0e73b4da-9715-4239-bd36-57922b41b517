from celery import shared_task
from user_activity.models import MissionEnrollment
from user_activity.task_names import UPDATE_ENROLLED_COUNT
from utils.task_transaction import task_transaction


@shared_task(name=UPDATE_ENROLLED_COUNT)
def update_enrolled_count(user_id: str, mission_id: str, workspace_id: str):
    with task_transaction(update_enrolled_count.__name__):
        enrollments = MissionEnrollment.objects.filter(
            mission_id=mission_id, workspace_id=workspace_id, user_id=user_id
        )
        enrolled_count = enrollments.count()
        for enrollment in enrollments:
            enrollment.enrolled_count = enrolled_count
            enrollment.save()
