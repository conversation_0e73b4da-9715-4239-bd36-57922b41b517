from celery import shared_task
from learn_content.models import Answer
from user_activity.services.mission_enrollment_progress_service import MissionEnrollmentProgressService
from utils.task_transaction import task_transaction


@shared_task
def compute_progress_by_question_answer(answer_id: str):
    with task_transaction(compute_progress_by_question_answer.__name__, MissionEnrollmentProgressService) as service:
        answer = Answer.objects.get(id=answer_id)
        service.compute_question_answered(answer)
