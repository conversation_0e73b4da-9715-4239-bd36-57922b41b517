from celery import shared_task
from user_activity.models import LearnContentActivity
from user_activity.services.mission_enrollment_progress_service import MissionEnrollmentProgressService
from utils.task_transaction import task_transaction


@shared_task
def compute_progress_by_activity(activity_id: str):
    with task_transaction(compute_progress_by_activity.__name__, MissionEnrollmentProgressService) as service:
        activity = LearnContentActivity.objects.get(id=activity_id)
        service.compute_content_consumed(activity)
