from celery import shared_task
from config import settings
from user_activity.services import MissionEnrollmentService
from utils.task_transaction import task_transaction


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True, countdown=1)
def verify_mission_enrollment_integrate_after_create(id: str):
    """
    This task is designed to eliminate any duplicate enrollments that occur due to race conditions
    """
    with task_transaction(
        verify_mission_enrollment_integrate_after_create.__name__, MissionEnrollmentService
    ) as service:
        service: MissionEnrollmentService
        service.verify_integrate_after_create(id)
