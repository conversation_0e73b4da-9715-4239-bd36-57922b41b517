from datetime import date, datetime, timedelta

from constants import ENROLLMENT_COMPLETED, ENROLLMENT_EXPIRED, ENROLLMENT_REPROVED
from django.db.models import Duration<PERSON>ield, ExpressionWrapper, F, Q
from user_activity.models import LearningTrailEnrollment
from user_activity.notifications.learning_trail_enrollment_expiring_notification import (
    LearningTrailEnrollmentExpiringNotification,
)


class LearningTrailEnrollmentExpiringNotificationTask:
    def __init__(self, notification_service: LearningTrailEnrollmentExpiringNotification):
        self._notification_service = notification_service

    def notify_enrollments(self, remaining_days: int):
        enrollments = self.get_enrollments_expiring(remaining_days)
        for enrollment in enrollments:
            self._notification_service.send(enrollment)

    @staticmethod
    def get_enrollments_expiring(remaining_days: int):
        today_date = date.today()
        expiration_date = today_date + timedelta(days=remaining_days)
        expiration_datetime = datetime.combine(expiration_date, datetime.max.time())
        completed_status = [ENROLLMENT_EXPIRED, ENROLLMENT_COMPLETED, ENROLLMENT_REPROVED]

        enrollments_expiring = (
            LearningTrailEnrollment.objects.filter(
                Q(required=True, goal_date__lte=expiration_datetime) & ~Q(status__in=completed_status)
            )
            .annotate(days_r=ExpressionWrapper(F("goal_date") - today_date, output_field=DurationField()))
            .filter(days_r__gte=timedelta(days=remaining_days), days_r__lte=timedelta(days=remaining_days + 1))
        )
        return enrollments_expiring
