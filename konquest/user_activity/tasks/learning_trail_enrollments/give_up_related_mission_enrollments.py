from celery import shared_task
from config import settings
from user_activity.models import LearningTrailEnrollment, MissionEnrollment
from user_activity.models.mission_enrollment import STATUS_IN_PROGRESS
from user_activity.services import MissionEnrollmentService
from user_activity.task_names import GIVE_UP_RELATED_MISSION_ENROLLMENTS_TASK
from utils.task_transaction import task_transaction


@shared_task(name=GIVE_UP_RELATED_MISSION_ENROLLMENTS_TASK, queue=settings.CELERY_QUEUE)
def give_up_related_mission_enrollments(trail_enrollment_id: str):
    with task_transaction(give_up_related_mission_enrollments.__name__, MissionEnrollmentService) as service:
        trail_enrollment = LearningTrailEnrollment.objects.get(pk=trail_enrollment_id)
        mission_enrollments = MissionEnrollment.objects.filter(
            learning_trail_enrollment_id=trail_enrollment_id, status__in=STATUS_IN_PROGRESS
        )
        for enrollment in mission_enrollments:
            service.give_up(enrollment, trail_enrollment.give_up_comment)
