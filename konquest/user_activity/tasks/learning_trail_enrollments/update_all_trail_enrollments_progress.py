from celery import shared_task
from config import settings
from constants import <PERSON><PERSON><PERSON><PERSON>ENT_ENR<PERSON>LED, ENR<PERSON>LMENT_STARTED
from user_activity.models import LearningTrailEnrollment
from user_activity.task_names import UPDATE_ALL_TRAIL_ENROLLMENTS_PROGRESS_TASK
from user_activity.tasks.learning_trail_enrollment_task import update_enrollments_progress
from utils.task_transaction import task_transaction


@shared_task(name=UPDATE_ALL_TRAIL_ENROLLMENTS_PROGRESS_TASK, queue=settings.CELERY_QUEUE)
def update_all_trail_enrollments_progress(learning_trail_id: str):
    with task_transaction(update_all_trail_enrollments_progress.__name__):
        enrollment_ids = LearningTrailEnrollment.objects.filter(
            learning_trail_id=learning_trail_id, status__in=[ENROLLMENT_STARTED, ENROLLMENT_ENROLLED]
        ).values_list("id", flat=True)
        for enrollment_id in enrollment_ids:
            update_enrollments_progress.delay(enrollment_id)
