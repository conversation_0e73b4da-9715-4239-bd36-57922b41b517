import json
import uuid
from typing import List, Optional

from celery import shared_task
from config import settings
from constants import ENROLL_TRAIL_USERS_TASK
from custom.discord_webhook import DiscordWebhookLogger
from django.db import IntegrityError
from learning_trail.models import LearningTrailStep
from user_activity.dtos.batch_trail_enroll_result import BatchTrailEnrollResult
from user_activity.models import LearningTrailEnrollment, MissionEnrollment, UserMissionStage
from user_activity.models.mission_enrollment import STATUS_IN_PROGRESS
from user_activity.services import (
    LearningTrailBatchEnrollmentService,
    LearningTrailEnrollmentService,
    MissionBatchEnrollmentService,
    MissionEnrollmentService,
)
from user_activity.services.learning_trail_batch_enrollment_service_v2 import EnrollmentsBatch as TrailEnrollmentsBatch
from user_activity.services.mission_batch_enrollment_service import EnrollmentsBatch as MissionEnrollmentsBatch
from utils.task_transaction import task_transaction

DISCORD_WEBHOOK = DiscordWebhookLogger


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def enroll_users_in_the_learning_trail_missions(
    learning_trail_id: str, user_id: str, workspace_id: str, goal_date: str
) -> None:
    """
    Enroll one or many users in the missions of a LearningTrail
    """
    with task_transaction(
        enroll_users_in_the_learning_trail_missions.__name__, MissionBatchEnrollmentService
    ) as mission_batch_enrollment:
        mission_batch_enrollment: MissionBatchEnrollmentService
        trail_enrollment = LearningTrailEnrollment.objects.filter(
            learning_trail_id=learning_trail_id, user_id=user_id
        ).first()

        if not trail_enrollment:
            raise IntegrityError("user_not_enrolled_in_the_learning_trail")

        trail_missions = LearningTrailStep.objects.filter(learning_trail_id=learning_trail_id, mission_id__isnull=False)
        trail_mission_ids = trail_missions.values_list("mission_id", flat=True)
        mission_already_consumed_ids = MissionEnrollment.objects.filter(
            mission_id__in=trail_mission_ids, workspace_id=workspace_id, user_id=user_id
        ).values_list("mission_id", flat=True)
        missions_to_enroll = trail_missions.exclude(mission_id__in=mission_already_consumed_ids).values_list(
            "mission_id", flat=True
        )

        batch_enrolment = MissionEnrollmentsBatch(
            mission_ids=missions_to_enroll,
            user_ids=[user_id],
            workspace_id=workspace_id,
            goal_date=goal_date,
            required=trail_enrollment.required,
            learning_trail_enrolment_id=trail_enrollment.id,
            triggered_by_internal_service=True,
        )
        mission_batch_enrollment.enroll(batch_enrolment)


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def enroll_users_in_new_step_mission(learning_trail_id, mission_id):
    with task_transaction(enroll_users_in_new_step_mission.__name__, LearningTrailEnrollmentService) as service:
        service.enroll_users_in_new_step_mission(learning_trail_id, mission_id)


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def delete_step_mission_enrollments(learning_trail_id: uuid, mission_id: uuid):
    with task_transaction(
        delete_step_mission_enrollments.__name__, MissionEnrollmentService
    ) as mission_enrollment_service:
        users = (
            LearningTrailEnrollment.objects.filter(
                learning_trail_id=learning_trail_id, status__in=["ENROLLED", "STARTED"]
            )
            .all()
            .values_list("user_id", flat=True)
        )
        mission_enrollments = MissionEnrollment.objects.filter(
            mission_id=mission_id, user_id__in=users, status__in=STATUS_IN_PROGRESS
        ).all()

        for mission_enrollment in mission_enrollments:
            user_mission_stage = UserMissionStage.objects.filter(mission_enrollment=mission_enrollment).first()
            if not user_mission_stage:
                mission_enrollment_service.delete_enrollment(mission_enrollment, True)


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def update_enrollments_progress(enrollment_id: uuid):
    """
    This Task calc the progress of the learning trail enrollment,
    if the result are 1, the status of the enrollment will update to COMPLETED
    """
    with task_transaction("update_enrollments_progress", LearningTrailEnrollmentService) as service:
        enrollment = LearningTrailEnrollment.objects.get(id=enrollment_id)
        service.update_progress(enrollment)


@shared_task(name=ENROLL_TRAIL_USERS_TASK, queue=settings.CELERY_QUEUE)
def enroll_users(
    trails: List[str],
    user_ids: List[str],
    workspace_id: str,
    goal_date: Optional[str] = None,
    required: Optional[bool] = False,
    regulatory_compliance_cycle_id: Optional[str] = None,
):
    with task_transaction(enroll_users.__name__, LearningTrailBatchEnrollmentService) as service:
        batch_input = TrailEnrollmentsBatch(
            user_ids=user_ids,
            trail_ids=trails,
            workspace_id=workspace_id,
            goal_date=goal_date,
            required=required,
            regulatory_compliance_cycle_id=regulatory_compliance_cycle_id,
            triggered_by_internal_service=True,
        )

        result: BatchTrailEnrollResult = service.enroll(batch_input)

        if result and result.enrollment_errors:
            error_details = json.dumps(
                [error.__dict__ for error in result.enrollment_errors], indent=2, ensure_ascii=False
            )
            DISCORD_WEBHOOK.emit_warning_message(
                ENROLL_TRAIL_USERS_TASK,
                f"Errors encountered during batch enrollment:\n```json\n{error_details}\n```"
            )
