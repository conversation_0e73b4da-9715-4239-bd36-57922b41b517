from typing import List

from celery import shared_task
from django.conf import settings
from user_activity.dtos.mission_enrollment_provider_dto import MissionEnrollmentProviderDto
from user_activity.services.mission_enrollment_provider_sync_service import MissionEnrollmentProviderSyncService
from utils.task_transaction import task_transaction


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def mission_enrollment_provider_sync(enrollments_providers: List[MissionEnrollmentProviderDto]):
    with task_transaction(mission_enrollment_provider_sync.__name__, MissionEnrollmentProviderSyncService) as service:
        service.sync_enrollment(enrollments_providers)
