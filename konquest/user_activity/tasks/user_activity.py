from celery import shared_task
from config import settings
from user_activity.models import LearnContentActivity
from utils.task_transaction import task_transaction


def _fit_time_and_speed(speed, time_in_previous, time_stop_previous, time_stop_now):
    time_stop_previous = time_stop_previous.replace(tzinfo=None)
    time_stop_now = time_stop_now.replace(tzinfo=None)
    time_in_now = time_stop_now - time_stop_previous
    time_in_fit_speed = time_in_previous + time_in_now * speed
    return time_in_fit_speed


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def update_learning_activity(activity_id, time_stop, speed):
    with task_transaction(update_learning_activity.__name__):
        instance = LearnContentActivity.objects.get(id=activity_id)
        if instance.time_stop.replace(tzinfo=None) < time_stop:
            time_in = _fit_time_and_speed(
                speed=speed,
                time_in_previous=instance.time_in,
                time_stop_previous=instance.time_stop,
                time_stop_now=time_stop,
            )

            instance.time_stop = time_stop
            instance.time_in = time_in
            instance.save()
