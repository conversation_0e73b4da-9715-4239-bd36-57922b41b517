from collections import OrderedDict

from celery import shared_task
from config.settings import CELERY_QUEUE
from user_activity.services.mission_enrollment_attendance_service import MissionEnrollmentAttendanceService
from utils.task_transaction import task_transaction


@shared_task(queue=CELERY_QUEUE, ignore_result=True)
def check_in_batch_force(
    persons: OrderedDict,
    date_id: str,
    role: str,
    user_id: str,
    workspace_id: str,
    token: str,
):
    with task_transaction("check_in_batch_force", MissionEnrollmentAttendanceService) as service:
        service.check_in_batch_force(persons, date_id, role, user_id, workspace_id, token)
