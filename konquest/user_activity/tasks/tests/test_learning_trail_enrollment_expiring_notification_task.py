from datetime import timedelta

from django.test import TestCase
from django.utils.timezone import now
from mission.notifications.tests.test_minimum_performance_updated_notification import NotificationServiceStub
from mock import mock
from model_mommy import mommy
from user_activity.models import LearningTrailEnrollment
from user_activity.notifications.learning_trail_enrollment_expiring_notification import (
    LearningTrailEnrollmentExpiringNotification,
)
from user_activity.tasks.learning_trail_enrollment_expiring_notification_task import (
    LearningTrailEnrollmentExpiringNotificationTask,
)


class TestLearningTrailEnrollmentExpiringNotificationTask(TestCase):
    def setUp(self) -> None:
        self._service = LearningTrailEnrollmentExpiringNotification(NotificationServiceStub())
        self._task = LearningTrailEnrollmentExpiringNotificationTask(self._service)
        self._enrollment_expiring = mommy.make(
            LearningTrailEnrollment, required=True, goal_date=now() + timedelta(days=5)
        )

    @mock.patch.object(LearningTrailEnrollmentExpiringNotification, "send")
    def test_should_notify_enrollments(self, send_notification: mock.MagicMock):
        self._task.notify_enrollments(5)

        send_notification.assert_called_once_with(self._enrollment_expiring)
