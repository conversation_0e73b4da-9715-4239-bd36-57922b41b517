from account.models import User, Workspace
from constants import ENROLLMENT_STARTED
from django.test import TestCase
from mock import mock
from model_mommy import mommy
from user_activity.models import LearningTrailEnrollment, MissionEnrollment
from user_activity.services import MissionEnrollmentService
from user_activity.tasks.learning_trail_enrollments.give_up_related_mission_enrollments import (
    give_up_related_mission_enrollments,
)


class TestGiveUpRelatedMissionEnrollmentsTask(TestCase):
    def setUp(self) -> None:
        self.user = mommy.make(User)
        self.workspace = mommy.make(Workspace)
        self.trail_enrollment = mommy.make(LearningTrailEnrollment, give_up_comment="COMMENT")

    @mock.patch.object(MissionEnrollmentService, "give_up")
    def test_give_up_related_mission_enrollments(self, give_up_mock: mock.MagicMock):
        enrollment_in_progress = mommy.make(
            MissionEnrollment, learning_trail_enrollment_id=self.trail_enrollment.id, status=ENROLLMENT_STARTED
        )
        # Any other enrollment
        mommy.make(MissionEnrollment, status=ENROLLMENT_STARTED)

        give_up_related_mission_enrollments(self.trail_enrollment.id)

        give_up_mock.assert_called_once_with(enrollment_in_progress, self.trail_enrollment.give_up_comment)
