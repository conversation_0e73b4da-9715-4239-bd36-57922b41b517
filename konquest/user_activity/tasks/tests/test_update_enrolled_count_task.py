from account.models import User, Workspace
from django.test import TestCase
from mission.models import Mission
from model_mommy import mommy
from user_activity.models import MissionEnrollment
from user_activity.tasks.mission_enrollments.update_enrolled_count import update_enrolled_count


class TestUpdateEnrolledCountTask(TestCase):
    def setUp(self) -> None:
        self.user = mommy.make(User)
        self.mission = mommy.make(Mission)
        self.another_mission = mommy.make(Mission)
        self.workspace = mommy.make(Workspace)
        self.older_enrollment = mommy.make(
            MissionEnrollment, user=self.user, mission=self.mission, workspace=self.workspace
        )

    def test_calc_two_enrollments(self):
        new_enrollment = mommy.make(MissionEnrollment, user=self.user, mission=self.mission, workspace=self.workspace)

        update_enrolled_count(self.user.id, self.mission.id, self.workspace.id)

        new_enrollment.refresh_from_db()
        self.older_enrollment.refresh_from_db()
        expected_enrolled_count = 2
        self.assertEqual(new_enrollment.enrolled_count, expected_enrolled_count)
        self.assertEqual(self.older_enrollment.enrolled_count, expected_enrolled_count)

    def test_another_enrollment_cannot_be_updated(self):
        new_enrollment = mommy.make(
            MissionEnrollment, user=self.user, mission=self.another_mission, workspace=self.workspace
        )

        update_enrolled_count(self.user.id, self.mission.id, self.workspace.id)

        new_enrollment.refresh_from_db()
        self.older_enrollment.refresh_from_db()
        expected_enrolled_count = 1
        self.assertEqual(new_enrollment.enrolled_count, expected_enrolled_count)
        self.assertEqual(self.older_enrollment.enrolled_count, expected_enrolled_count)
