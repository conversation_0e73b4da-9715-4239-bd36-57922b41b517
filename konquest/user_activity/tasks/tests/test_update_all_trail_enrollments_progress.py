import uuid

from constants import <PERSON><PERSON><PERSON><PERSON>ENT_ENROLLED, <PERSON><PERSON><PERSON><PERSON>ENT_STARTED
from django.test import TestCase
from mock import mock
from user_activity.models import LearningTrailEnrollment
from user_activity.tasks.learning_trail_enrollments.update_all_trail_enrollments_progress import (
    update_all_trail_enrollments_progress,
)


class TestUpdateAllTrailEnrollmentsProgress(TestCase):
    def setUp(self) -> None:
        self.trail_id = uuid.uuid4()

    @mock.patch.object(LearningTrailEnrollment, "objects")
    @mock.patch("user_activity.tasks.learning_trail_enrollment_task.update_enrollments_progress.delay")
    def test_update_all_trail_enrollments_progress(
        self, update_enrollments_progress: mock.MagicMock, objects: mock.MagicMock
    ):
        mission_enrollment_ids = [uuid.uuid4(), uuid.uuid4(), uuid.uuid4()]
        objects.filter.return_value.values_list.return_value = mission_enrollment_ids

        update_all_trail_enrollments_progress(self.trail_id)

        objects.filter.assert_called_with(
            learning_trail_id=self.trail_id, status__in=[ENROLLMENT_STARTED, ENR<PERSON>LMENT_ENROLLED]
        )
        update_enrollments_progress.assert_any_call(mission_enrollment_ids[0])
        update_enrollments_progress.assert_any_call(mission_enrollment_ids[1])
        update_enrollments_progress.assert_any_call(mission_enrollment_ids[2])
