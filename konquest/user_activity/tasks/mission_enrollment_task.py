import uuid
from typing import List, Optional

from celery import shared_task
from config import settings
from django.db.models import Q
from django.utils.timezone import now
from mission.models import LiveMissionDates, Mission, PresentialMissionDates
from user_activity.models import MissionEnrollment
from user_activity.models.mission_enrollment import COMPLETED, ENR<PERSON>LED, REPROVED, STARTED
from user_activity.services import MissionBatchEnrollmentService, MissionEnrollmentService
from user_activity.services.mission_batch_enrollment_service import EnrollmentsBatch
from user_activity.services.mission_enrollment_attendance_service import MissionEnrollmentAttendanceService
from user_activity.services.mission_enrollment_close_service import MissionEnrollmentCloseService
from user_activity.services.mission_enrollment_progress_service import MissionEnrollmentProgressService
from user_activity.task_names import (
    ACTIVE_MISSION_ENROLLMENTS,
    CLOSE_SYNC_ENROLLMENTS,
    CREATE_SYNC_ENROLLMENT_ATTENDANCES,
    DEACTIVATE_MISSION_ENROLLMENTS,
    DELETE_SYNC_ENROLLMENT_ATTENDANCES,
    ENROLL_USERS_TASK,
    PROMOTE_WAITING_VACANCIES_ENROLLMENTS,
    UPDATE_SYNC_REMAINING_SEATS,
)
from utils.task_transaction import task_transaction


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def update_mission_enrollment_progress(enrollment_id: str):
    with task_transaction(update_mission_enrollment_progress.__name__, MissionEnrollmentProgressService) as service:
        enrollment = MissionEnrollment.objects.get(id=enrollment_id)
        enrollment.progress = service.calc(enrollment)
        if enrollment.status == ENROLLED:
            enrollment.status = STARTED
            enrollment.start_date = now()
        enrollment.save()
        return enrollment


@shared_task(name=PROMOTE_WAITING_VACANCIES_ENROLLMENTS, queue=settings.CELERY_QUEUE, ignore_result=True)
def promote_waiting_vacancies_enrollments(mission_id: str):
    with task_transaction(promote_waiting_vacancies_enrollments.__name__, MissionEnrollmentService) as service:
        sync = Mission.objects.get(id=mission_id).sync
        service.promote_waiting_vacancies_enrollments(sync)


@shared_task(name=UPDATE_SYNC_REMAINING_SEATS, queue=settings.CELERY_QUEUE, ignore_result=True)
def update_sync_remaining_seats(mission_id: uuid) -> None:
    """
    Check up the SyncMission remaining seats (LiveMission or PresentialMission) taking into account
    all approved enrollments and enrollments waiting for approval for the mission.
    """
    with task_transaction("update_sync_remaining_seats", MissionEnrollmentService) as service:
        sync = Mission.objects.get(id=mission_id).sync
        service: MissionEnrollmentService
        service.update_sync_remaining_seats(sync)


@shared_task(name=CREATE_SYNC_ENROLLMENT_ATTENDANCES, queue=settings.CELERY_QUEUE, ignore_result=True)
def create_sync_enrollment_attendances(enrollment_id: str):
    with task_transaction("create_sync_enrollment_attendances", MissionEnrollmentAttendanceService) as service:
        enrollment = MissionEnrollment.objects.get(id=enrollment_id)
        service.create_attendances_to_enrollment(enrollment)


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def create_live_enrollment_attendances_for_new_date(date_id: str):
    with task_transaction("create_live_attendances_for_new_date", MissionEnrollmentAttendanceService) as service:
        live_date = LiveMissionDates.objects.get(id=date_id)
        service.create_attendances_to_date(live_date)


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def create_presential_enrollment_attendances_for_new_date(date_id: str):
    with task_transaction("create_presential_attendances_for_new_date", MissionEnrollmentAttendanceService) as service:
        presential_date = PresentialMissionDates.objects.get(id=date_id)
        service.create_attendances_to_date(presential_date)


@shared_task(name=DELETE_SYNC_ENROLLMENT_ATTENDANCES, queue=settings.CELERY_QUEUE, ignore_result=True)
def delete_sync_enrollment_attendances(enrollment_id: str):
    with task_transaction("delete_sync_enrollment_attendances", MissionEnrollmentAttendanceService) as service:
        enrollment = MissionEnrollment.objects.get(id=enrollment_id)
        service.delete_attendances_enrollment(enrollment)


@shared_task(name=ACTIVE_MISSION_ENROLLMENTS, queue=settings.CELERY_QUEUE, ignore_result=True)
def active_mission_enrollments(mission_id: str):
    with task_transaction("active_mission_enrollments", MissionEnrollmentService) as service:
        service.active_mission_enrollments(mission_id)


@shared_task(name=DEACTIVATE_MISSION_ENROLLMENTS, queue=settings.CELERY_QUEUE, ignore_result=True)
def inactive_mission_enrollments(mission_id: str):
    with task_transaction("inactive_mission_enrollments", MissionEnrollmentService) as service:
        service.inactive_mission_enrollments(mission_id)


@shared_task(name=ENROLL_USERS_TASK, queue=settings.CELERY_QUEUE)
def enroll_users(
    mission_ids: List[str],
    user_ids: List[str],
    workspace_id: str,
    goal_date: Optional[str] = None,
    required: bool = False,
    regulatory_compliance_cycle_id: Optional[str] = None,
):
    with task_transaction(enroll_users.__name__, MissionBatchEnrollmentService) as service:
        batch = EnrollmentsBatch(
            mission_ids=mission_ids,
            user_ids=user_ids,
            workspace_id=workspace_id,
            goal_date=goal_date,
            required=required,
            regulatory_compliance_cycle_id=regulatory_compliance_cycle_id,
            triggered_by_internal_service=True,
        )
        enrollment_errors = service.enroll(batch)
        return enrollment_errors


@shared_task(name=CLOSE_SYNC_ENROLLMENTS, queue=settings.CELERY_QUEUE)
def close_sync_enrollments(mission_id: str):
    with task_transaction(close_sync_enrollments.__name__, MissionEnrollmentCloseService) as enrollment_close_service:
        enrollment_close_service: MissionEnrollmentCloseService
        enrollments = MissionEnrollment.objects.filter(Q(mission_id=mission_id), ~Q(status__in=[COMPLETED, REPROVED]))
        updated_enrollments = []

        for enrollment in enrollments:
            updated_enrollments.append(enrollment_close_service.close_enrollment(enrollment))

        MissionEnrollment.objects.bulk_update(
            updated_enrollments, fields=["status", "end_date", "performance", "points"]
        )
