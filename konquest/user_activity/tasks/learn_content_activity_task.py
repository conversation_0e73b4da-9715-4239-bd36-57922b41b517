from celery import shared_task
from config.settings import CELERY_QUEUE
from pulse.models.pulse import Pulse
from utils.task_transaction import task_transaction


@shared_task(queue=CELERY_QUEUE, ignore_result=True)
def update_pulse_views(pulse_id):
    with task_transaction("update_pulse_views"):
        pulse = Pulse.objects.get(id=pulse_id)
        pulse.views = pulse.views + 1 if pulse.views else 1
        pulse.save()
