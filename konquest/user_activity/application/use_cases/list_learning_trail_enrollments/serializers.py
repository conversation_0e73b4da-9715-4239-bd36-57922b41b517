# -*- coding: utf-8 -*-
import math

from account.models import User
from learning_trail.models import LearningTrail
from rest_framework.serializers import ModelSerializer, SerializerMethodField
from user_activity.models import LearningTrailEnrollment


class UserSerializer(ModelSerializer):
    class Meta:
        model = User
        fields = ("id", "name", "avatar")


class LearningTrailSerializer(ModelSerializer):
    user_creator = UserSerializer()
    is_owner = SerializerMethodField()
    count_missions = SerializerMethodField()
    count_pulses = SerializerMethodField()
    users_enrolled = SerializerMethodField()
    users_finished = SerializerMethodField()

    @staticmethod
    def get_users_finished(obj):
        """
        Count users are completed the Learning Trail
        """
        return sum(1 for enrollment in obj.learningtrailenrollment_set.all() if enrollment.status == "COMPLETED")

    @staticmethod
    def get_users_enrolled(obj):
        """
        Count users are enrolled in the Learning Trail
        """
        return len(obj.learningtrailenrollment_set.all())

    @staticmethod
    def get_count_missions(obj):
        """
        Count mission in Learning Trail
        """
        return sum(1 for step in obj.learningtrailstep_set.all() if step.mission_id is not None)

    @staticmethod
    def get_count_pulses(obj):
        """
        Count pulses in Learning Trail
        """
        return sum(1 for step in obj.learningtrailstep_set.all() if step.pulse_id is not None)

    def get_is_owner(self, obj):
        """
        Check if user logged is owner
        """
        user = self.context["request"].user

        return str(obj.user_creator.id) == user["sub"]

    class Meta:
        model = LearningTrail
        fields = (
            "id",
            "name",
            "expiration_date",
            "is_owner",
            "count_missions",
            "count_pulses",
            "users_enrolled",
            "users_finished",
            "user_creator",
        )


class ListLearningTrailEnrollmentsSerializer(ModelSerializer):
    user = UserSerializer()
    learning_trail = LearningTrailSerializer()

    class Meta:
        model = LearningTrailEnrollment
        depth = 1
        fields = (
            "id",
            "points",
            "performance",
            "start_date",
            "end_date",
            "goal_date",
            "give_up",
            "required",
            "progress",
            "certificate_url",
            "assessment_type",
            "status",
            "user",
            "learning_trail",
        )

    def to_representation(self, instance):
        data = super().to_representation(instance)
        for key, value in data.items():
            if isinstance(value, float) and (math.isinf(value) or math.isnan(value)):
                data[key] = 0
        return data
