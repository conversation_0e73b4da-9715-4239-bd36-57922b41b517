from typing import Optional

import pytz
from custom.translations.translation_keys import DEFAULT_TIME
from user_activity.domain.entities.certificate_enrollment import CertificateEnrollment
from user_activity.domain.entities.enrollment_type_enum import EnrollmentType
from user_activity.domain.repositories.certificate_mission_content_repository import CertificateContentRepository
from user_activity.domain.repositories.certificate_repository import (
    CertificateRepository,
)
from user_activity.domain.repositories.i_learning_trail_enrollment_repository import ILearningTrailEnrollmentRepository
from user_activity.models import LearningTrailEnrollment
from utils.utils import format_time_duration


class GenerateTrailCertificateUseCase:
    def __init__(
        self,
        certificate_file_generator_repository: CertificateRepository,
        certificate_content_repository: CertificateContentRepository,
        enrollment_repository: ILearningTrailEnrollmentRepository
    ) -> None:
        self.certificate_file_generator_repository = certificate_file_generator_repository
        self.certificate_content_repository = certificate_content_repository
        self.enrollment_repository = enrollment_repository

    def execute(self, enrollment_id: str) -> Optional[str]:
        enrollment = self.enrollment_repository.get_by_id(enrollment_id)

        certificate_enrollment = self.to_entity(enrollment)
        if not certificate_enrollment.is_valid():
            return
        if certificate_enrollment.certificate_url:
            return certificate_enrollment.certificate_url

        certificate_url = self.certificate_file_generator_repository.generate(certificate_enrollment)

        self.enrollment_repository.update_certificate_url(enrollment, certificate_url)

        return certificate_url.value

    def to_entity(self, enrollment: LearningTrailEnrollment) -> CertificateEnrollment:
        timezone_user = pytz.timezone(enrollment.user.time_zone)
        end_date = (
            enrollment.end_date.astimezone(timezone_user).strftime("%d/%m/%Y") if enrollment.end_date else DEFAULT_TIME
        )

        return CertificateEnrollment(
            enrollment_id=enrollment.id,
            user_name=enrollment.user.name,
            user_time_zone=enrollment.user.time_zone,
            performance=f"{int((enrollment.performance or 0) * 100)}%",
            status=enrollment.status,
            type=EnrollmentType.LEARNING_TRAIL,
            end_date=end_date,
            workspace_id=enrollment.workspace_id,
            certificate_url=enrollment.certificate_url,
            course_name=enrollment.learning_trail.name,
            course_language=enrollment.learning_trail.language,
            course_duration=format_time_duration(enrollment.learning_trail.duration_time),
            summary=self.certificate_content_repository.get_contents(enrollment.id),
        )
