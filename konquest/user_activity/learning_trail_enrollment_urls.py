from django.urls import path
from user_activity.views.learning_trail_batch_enrollment_viewset import LearningTrailBatchEnrollmentViewSet
from user_activity.views.learning_trail_enrollment_give_up_viewset import LearningTrailEnrollmentGiveUpViewSet
from user_activity.views.learning_trail_enrollment_manual_finish_viewset import (
    LearningTrailEnrollmentManualFinishViewSet,
)
from user_activity.views.learning_trail_enrollment_tracking_pulse_consume_viewset import (
    LearningTrailEnrollmentTrackingPulseConsumeViewSet,
)
from user_activity.views.learning_trail_enrollment_tracking_viewset import LearningTrailEnrollmentTrackingViewSet
from user_activity.views.learning_trail_enrollment_viewset import (
    LearningTrailEnrollmentBatchViewSet as LearningTrailEnrollmentBatchOldViewSet,
)
from user_activity.views.learning_trail_enrollment_viewset import (
    LearningTrailEnrollmentCertificateViewSet,
    LearningTrailEnrollmentRestartViewSet,
    LearningTrailEnrollmentRetakeViewSet,
    LearningTrailEnrollmentViewSet,
)
from user_activity.views.learning_trail_extend_deadline_viewset import TrailEnrollmentExtendDeadlineViewSet

_READ_ONLY = {"get": "list"}
_SAVE_ONLY = {"post": "create"}

_LIST = {"get": "list", "post": "create"}

_DETAIL = {"get": "retrieve", "put": "update", "patch": "partial_update", "delete": "destroy"}


urlpatterns = [
    path("", LearningTrailEnrollmentViewSet.as_view(_LIST), name="learning-trail-enrollments-list"),
    path("/<uuid:pk>", LearningTrailEnrollmentViewSet.as_view(_DETAIL), name="learning-trail-enrollment-details"),
    path(
        "/<uuid:pk>/give-up",
        LearningTrailEnrollmentGiveUpViewSet.as_view(_SAVE_ONLY),
        name="learning-trail-enrollment-give-up",
    ),
    path(
        "/<uuid:pk>/retake",
        LearningTrailEnrollmentRetakeViewSet.as_view(_SAVE_ONLY),
        name="learning-trail-enrollment-retake",
    ),
    path(
        "/<uuid:pk>/restart",
        LearningTrailEnrollmentRestartViewSet.as_view(_SAVE_ONLY),
        name="learning-trail-enrollment-restart",
    ),
    path(
        "/<uuid:pk>/certificates",
        LearningTrailEnrollmentCertificateViewSet.as_view(_LIST),
        name="learning-trail-enrollment-certificate",
    ),
    path(
        "/<uuid:pk>/manual-finish",
        LearningTrailEnrollmentManualFinishViewSet.as_view(_SAVE_ONLY),
        name="learning-trail-enrollment-manual-finish",
    ),
    path(
        "/batch", LearningTrailEnrollmentBatchOldViewSet.as_view(_SAVE_ONLY), name="learning-trail-enrollment-batch-old"
    ),
    path("/batch/v2", LearningTrailBatchEnrollmentViewSet.as_view(_SAVE_ONLY), name="learning-trail-enrollment-batch"),
    path(
        "/<uuid:pk>/tracking",
        LearningTrailEnrollmentTrackingViewSet.as_view(_READ_ONLY),
        name="learning-trail-enrollment-tracking",
    ),
    path(
        "/<uuid:pk>/tracking/pulse/<uuid:pulse_id>",
        LearningTrailEnrollmentTrackingPulseConsumeViewSet.as_view(_READ_ONLY),
        name="learning-trail-enrollment-tracking-pulse-consume",
    ),
    path(
        "/<uuid:pk>/extend-deadline",
        TrailEnrollmentExtendDeadlineViewSet.as_view(_SAVE_ONLY),
        name="trail-enrollment-extend-deadline",
    ),
]
