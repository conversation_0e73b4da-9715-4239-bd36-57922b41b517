import rules
from account.models import User
from authentication.keeps_permissions import ADMIN_ROLES
from constants import RULE_CAN_CHANGE_LEARNING_TRAIL_ENROLLMENT, RULE_CAN_CHANGE_MISSION_ENROLLMENT
from mission.models.mission import LIVE, PRESENTIAL
from mission.rules import is_mission_contributor as check_mission_contributor
from user_activity.models import MissionEnrollment


@rules.predicate
def is_enrollment_user(user: User, enrollment: MissionEnrollment):
    return str(user.id) == str(enrollment.user_id)


@rules.predicate
def is_workspace_admin(user: User, enrollment: MissionEnrollment):
    is_workspace_enrolment = str(enrollment.workspace_id) == str(user.workspace_id)
    return user.role in ADMIN_ROLES and is_workspace_enrolment


@rules.predicate
def is_instructor(user: User, enrollment: MissionEnrollment):
    if enrollment.mission.mission_model in [LIVE, PRESENTIAL]:
        return enrollment.mission.sync.instructors.filter(id=user.id).exists()
    return False


@rules.predicate
def is_mission_contributor(user: User, enrollment: MissionEnrollment):
    return check_mission_contributor(user, enrollment.mission)


rules.add_rule(
    RULE_CAN_CHANGE_MISSION_ENROLLMENT, is_workspace_admin | is_enrollment_user | is_instructor | is_mission_contributor
)
rules.add_rule(RULE_CAN_CHANGE_LEARNING_TRAIL_ENROLLMENT, is_workspace_admin | is_enrollment_user)
