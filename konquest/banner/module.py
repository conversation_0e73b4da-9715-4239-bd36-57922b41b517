from account.services import WorkspaceService
from banner.application.use_cases.create_banner_use_case import CreateBannerUseCase
from banner.application.use_cases.get_banner_use_case import GetBannerUseCase
from banner.application.use_cases.get_recommendations_use_case import GetRecommendationsUseCase
from banner.domain.repositories.banners_repository import BannersRepository
from banner.infrastructure.repositories.banner_recommendations_repository import BannerRecommendationsRepository
from banner.infrastructure.repositories.banners_repository import BannersORMRepository
from banner.infrastructure.repositories.course_validator import CourseValidator
from banner.infrastructure.repositories.enrich_banner_with_titles_repository import EnrichBannerWithTitlesRepository
from banner.infrastructure.repositories.learning_trail_validator import LearningTrailValidator
from banner.infrastructure.repositories.mission_recommendations_repository import MissionRecommendationsRepository
from injector import Module, provider, singleton
from learning_trail.services import LearningTrailService
from mission.repositories.mission.utils import MissionRepository
from mission.services.mission_recommendation import MissionRecommendationService
from user_activity.selectors.mission_enrollment_selector import MissionEnrollmentSelector


class BannerModule(Module):

    @provider
    @singleton
    def banners_repository(self) -> BannersRepository:
        return BannersORMRepository()

    @provider
    @singleton
    def learning_trail_validator(self, learning_trail_service: LearningTrailService) -> LearningTrailValidator:
        return LearningTrailValidator(learning_trail_service)

    @provider
    @singleton
    def course_validator(self) -> CourseValidator:
        return CourseValidator(MissionRepository())

    @provider
    @singleton
    def create_banner_use_case(
        self,
        banners_repository: BannersRepository,
        course_validator: CourseValidator,
        learning_trail_validator: LearningTrailValidator
    ) -> CreateBannerUseCase:
        return CreateBannerUseCase(banners_repository, course_validator, learning_trail_validator)

    @provider
    @singleton
    def get_banner_use_case(
        self,
        banners_repository: BannersRepository,
        enrich_banner_with_titles_repository: EnrichBannerWithTitlesRepository,
    ) -> GetBannerUseCase:
        return GetBannerUseCase(banners_repository, enrich_banner_with_titles_repository)

    @provider
    @singleton
    def banner_recommendations_repository(
        self,
        banners_repository: BannersRepository,
        courses_repository: MissionRepository,
        trail_repository: LearningTrailService,
        mission_enrollment_repository: MissionEnrollmentSelector
    ) -> BannerRecommendationsRepository:
        return BannerRecommendationsRepository(
            banners_repository,
            courses_repository,
            trail_repository,
            mission_enrollment_repository
        )

    @provider
    @singleton
    def mission_recommendations_repository(
        self, mission_service: MissionRecommendationService
    ) -> MissionRecommendationsRepository:
        return MissionRecommendationsRepository(mission_service)

    @provider
    @singleton
    def get_recommendations_use_case(
        self,
        workspace_service: WorkspaceService,
        banner_strategy: BannerRecommendationsRepository,
        missions_strategy: MissionRecommendationsRepository,
    ) -> GetRecommendationsUseCase:
        return GetRecommendationsUseCase(workspace_service, banner_strategy, missions_strategy)

    @provider
    @singleton
    def enrich_banner_with_titles_repository(
        self,
        courses_repository: MissionRepository,
        trail_repository: LearningTrailService,
    ) -> EnrichBannerWithTitlesRepository:
        return EnrichBannerWithTitlesRepository(courses_repository, trail_repository)
