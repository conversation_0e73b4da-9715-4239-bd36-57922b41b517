from drf_yasg.utils import swagger_auto_schema
from injector import Provider, inject
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from authentication.keeps_permissions import ALL_PERMISSIONS
from banner.application.use_cases.get_recommendations_use_case import GetRecommendationsUseCase
from banner.infrastructure.serializers.recommendations_serializer import RecommendationSerializer
from utils.swagger import authorization_parameters
from utils.utils import Utils


class RecommendationsView(APIView):
    permission_classes = ALL_PERMISSIONS

    @inject
    def __init__(
        self,
        use_case: GetRecommendationsUseCase = Provider[GetRecommendationsUseCase],
        **kwargs
    ):
        super().__init__(**kwargs)
        self.use_case = use_case

    @swagger_auto_schema(
        manual_parameters=authorization_parameters,
        responses={status.HTTP_200_OK: RecommendationSerializer(many=True)}
    )
    def get(self, request):
        user_id = request.user["sub"]
        workspace_id = request.user["client_id"]
        language = Utils.get_user_language(request.META.get("HTTP_AUTHORIZATION", ""))

        recommendations = self.use_case.execute(user_id, workspace_id, language)
        data = RecommendationSerializer(recommendations, many=True).data
        return Response(data)
