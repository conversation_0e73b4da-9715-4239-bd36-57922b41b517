from drf_yasg.utils import swagger_auto_schema
from injector import Provider, inject
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from authentication.keeps_permissions import ADMIN_PERMISSIONS
from banner.application.use_cases.create_banner_use_case import CreateBannerUseCase
from banner.infrastructure.serializers.banner_serializer import BannerInputSerializer
from banner.presentation.mappers.banner_request_mapper import BannerRequestMapper
from utils.swagger import authorization_parameters


class BannersView(APIView):
    permission_classes = ADMIN_PERMISSIONS

    @inject
    def __init__(self, use_case: CreateBannerUseCase = Provider[CreateBannerUseCase], **kwargs):
        super().__init__(**kwargs)
        self.use_case = use_case

    @swagger_auto_schema(
        manual_parameters=authorization_parameters,
        request_body=BannerInputSerializer,
        responses={status.HTTP_201_CREATED: BannerInputSerializer()}
    )
    def post(self, request):
        serializer = BannerInputSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        workspace_id = request.user["client_id"]

        dto = BannerRequestMapper.to_create_banner_dto(serializer.validated_data, workspace_id)
        domain_banner = self.use_case.execute(dto)

        return Response(BannerInputSerializer(domain_banner).data, status=status.HTTP_201_CREATED)
