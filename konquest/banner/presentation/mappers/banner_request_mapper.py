from banner.application.dto.banner_dto import CreateBannerDTO, LearningResourceDTO


class BannerRequestMapper:
    @staticmethod
    def to_create_banner_dto(validated_data: dict, workspace_id: str) -> CreateBannerDTO:
        learning_resources_dto = []

        for lr in validated_data['learning_resources']:
            dto = LearningResourceDTO(
                resource_type=lr['resource_type'],
                resource_id=lr.get('resource_id'),
                external_resource_title=lr.get('external_resource_title'),
                external_resource_url=lr.get('external_resource_url'),
                external_resource_image=lr.get('external_resource_image'),
                order=lr['order']
            )
            learning_resources_dto.append(dto)

        return CreateBannerDTO(
            start_date=validated_data.get('start_date'),
            end_date=validated_data.get('end_date'),
            workspace_id=workspace_id,
            learning_resources=learning_resources_dto
        )
