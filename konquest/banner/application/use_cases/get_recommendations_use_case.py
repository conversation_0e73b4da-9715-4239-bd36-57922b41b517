from typing import List

from account.services import WorkspaceService
from banner.domain.entities.recommendation import Recommendation
from banner.domain.repositories.recommendations_repository import RecommendationStrategy


class GetRecommendationsUseCase:
    def __init__(
        self,
        workspace_repository: WorkspaceService,
        banner_strategy: RecommendationStrategy,
        mission_strategy: RecommendationStrategy,
    ):
        self.banner_strategy = banner_strategy
        self.mission_strategy = mission_strategy
        self.workspace_repository = workspace_repository

    def execute(self, user_id: str, workspace_id: str, language: str) -> List[Recommendation]:
        workspace = self.workspace_repository.get_workspace(workspace_id)
        if workspace.banner_mode != "RECOMMENDATION" or not workspace.banner_mode:
            return self.mission_strategy.get_recommendations(user_id, workspace_id, language)

        banner_recommendations = self.banner_strategy.get_recommendations(user_id, workspace_id, language)
        if banner_recommendations:
            return banner_recommendations

        return self.mission_strategy.get_recommendations(user_id, workspace_id, language)
