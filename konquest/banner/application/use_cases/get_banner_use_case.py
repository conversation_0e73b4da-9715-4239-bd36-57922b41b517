from banner.domain.entities.banner import Banner
from banner.domain.repositories.banners_repository import BannersRepository
from banner.infrastructure.repositories.enrich_banner_with_titles_repository import EnrichBannerWithTitlesRepository
from custom.exceptions.banner_exceptions import BannerNotFoundError


class GetBannerUseCase:
    def __init__(
        self,
        banners_repository: BannersRepository,
        enrich_banner_with_titles_repository: EnrichBannerWithTitlesRepository
    ):
        self.repository = banners_repository
        self.enrich_banner_with_titles_repository = enrich_banner_with_titles_repository

    def execute(self, workspace_id: str) -> Banner | None:
        banner = self.repository.get_by_workspace(workspace_id)

        if not banner:
            raise BannerNotFoundError("Not found any banner for the workspace")

        banner = self.enrich_banner_with_titles_repository.enrich(banner)
        return banner
