from banner.application.dto.banner_dto import Create<PERSON>annerD<PERSON>
from banner.domain.entities.banner import Banner
from banner.domain.repositories.banners_repository import BannersRepository
from banner.domain.repositories.resource_validator import LearningResourceValidator
from banner.infrastructure.mappers.banner_mapper import BannerMapper
from custom.exceptions.banner_exceptions import BannerInvalidResourceError


class CreateBannerUseCase:
    def __init__(
        self,
        repository: BannersRepository,
        course_validator: LearningResourceValidator,
        learning_trail_validator: LearningResourceValidator,
    ):
        self.repository = repository
        self.course_validator = course_validator
        self.learning_trail_validator = learning_trail_validator

    def execute(self, dto: CreateBannerDTO) -> Banner:
        existing_banner = self.repository.get_by_workspace(dto.workspace_id)
        banner = BannerMapper.from_dto(dto)
        resource_validators = {
            'COURSE': self.course_validator,
            'LEARNING_TRAIL': self.learning_trail_validator
        }
        for resource in dto.learning_resources:
            validator = resource_validators.get(resource.resource_type)
            if validator and not validator.is_valid(resource, dto.workspace_id):
                raise BannerInvalidResourceError(
                    f"Invalid resource: type={resource.resource_type}, id={resource.resource_id}"
                )

        if existing_banner:
            banner.id = existing_banner.id
            return self.repository.update(banner)

        return self.repository.save(banner)
