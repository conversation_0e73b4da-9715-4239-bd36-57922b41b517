from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional
from uuid import UUID


@dataclass
class LearningResourceDTO:
    resource_type: str
    resource_id: Optional[UUID]
    external_resource_title: Optional[str]
    external_resource_url: Optional[str]
    external_resource_image: Optional[str]
    order: int


@dataclass
class CreateBannerDTO:
    start_date: Optional[datetime]
    end_date: Optional[datetime]
    workspace_id: UUID | str
    learning_resources: List[LearningResourceDTO] = field(default_factory=list)


@dataclass
class DeleteDTO:
    workspace_id: UUID | str
