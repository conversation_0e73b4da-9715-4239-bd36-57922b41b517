import uuid

from django.db import models

from account.models import Workspace
from utils.models import BaseModel


class Banner(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    start_date = models.DateTimeField(null=True, blank=True)
    end_date = models.DateTimeField(null=True, blank=True)
    workspace = models.ForeignKey(Workspace, verbose_name="Workspace", on_delete=models.CASCADE, null=True)

    class Meta:
        db_table = "banner"
