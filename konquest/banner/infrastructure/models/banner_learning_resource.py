import uuid

from django.db import models

from utils.models import BaseModel


class ResourceType(models.TextChoices):
    COURSE = 'COURSE', 'Course'
    LEARNING_TRAIL = 'LEARNING_TRAIL', 'Learning Trail'
    EXTERNAL_CONTENT = 'EXTERNAL_CONTENT', 'External Content'
    EVENT = 'EVENT', 'Event'


class BannerLearningResource(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    banner = models.ForeignKey('banner.Banner', on_delete=models.CASCADE, related_name='learning_resources')
    resource_type = models.CharField(max_length=50, choices=ResourceType.choices)
    resource_id = models.UUIDField(null=True, blank=True)
    external_resource_title = models.CharField(max_length=255, null=True, blank=True)
    external_resource_url = models.URLField(null=True, blank=True)
    external_resource_image = models.TextField(null=True, blank=True)
    order = models.IntegerField()

    class Meta:
        db_table = "banner_learning_resources"
