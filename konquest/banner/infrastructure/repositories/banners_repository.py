from uuid import UUID

from django.db import transaction

from banner.domain.entities.banner import Banner as DomainBanner
from banner.domain.repositories.banners_repository import BannersRepository
from banner.infrastructure.mappers.banner_mapper import BannerMapper
from banner.infrastructure.models import BannerLearningResource
from banner.infrastructure.models.banner import Banner as ORMBanner


class BannersORMRepository(BannersRepository):
    @transaction.atomic()
    def save(self, banner: DomainBanner) -> DomainBanner:
        orm_banner = BannerMapper.to_orm(banner)
        orm_banner.save()

        resources = BannerMapper.learning_resources_to_orm(banner, orm_banner)
        BannerLearningResource.objects.bulk_create(resources)

        return BannerMapper.to_domain(orm_banner)

    @transaction.atomic()
    def update(self, banner: DomainBanner) -> DomainBanner:
        orm_banner = ORMBanner.objects.get(id=banner.id)
        BannerLearningResource.objects.filter(banner=orm_banner).delete()
        orm_banner.delete()

        return self.save(banner)

    def get(self, banner_id: UUID) -> DomainBanner:
        orm_banner = ORMBanner.objects.prefetch_related('learning_resources').get(id=banner_id)
        return BannerMapper.to_domain(orm_banner)

    def list_by_user(self, user_id: int) -> list[DomainBanner]:
        orm_banners = ORMBanner.objects.filter(workspace__user_id=user_id).prefetch_related('learning_resources')
        return [BannerMapper.to_domain(b) for b in orm_banners]

    def get_by_workspace(self, workspace_id: UUID) -> DomainBanner | None:
        try:
            orm_banner = ORMBanner.objects.prefetch_related('learning_resources').get(workspace_id=workspace_id)
            return BannerMapper.to_domain(orm_banner)
        except ORMBanner.DoesNotExist:
            return None

    def delete(self, banner_id: UUID) -> None:
        orm_banner = ORMBanner.objects.get(id=banner_id)
        orm_banner.delete()
