from typing import List, Optional
from uuid import UUID

from banner.domain.entities.recommendation import Enrollment, Recommendation
from banner.domain.repositories.banners_repository import BannersRepository
from banner.domain.repositories.recommendations_repository import RecommendationStrategy
from banner.infrastructure.models.banner_learning_resource import ResourceType
from django.core.exceptions import ObjectDoesNotExist
from learning_trail.services import LearningTrailService
from mission.repositories.mission.utils import MissionRepository
from user_activity.models import LearningTrailEnrollment
from user_activity.selectors.mission_enrollment_selector import MissionEnrollmentSelector


class BannerRecommendationsRepository(RecommendationStrategy):
    def __init__(
        self,
        repository: BannersRepository,
        course_repository: MissionRepository,  # (legacy)
        trail_repository: LearningTrailService,   # (legacy)
        mission_enrollment_repository: MissionEnrollmentSelector,  # (legacy)
    ):
        self.repository = repository
        self.course_repository = course_repository
        self.trail_repository = trail_repository
        self.mission_enrollment_repository = mission_enrollment_repository

    def get_recommendations(self, user_id: str, workspace_id: str, language: str) -> List[Recommendation]:
        banner = self.repository.get_by_workspace(workspace_id)
        if not banner:
            return []

        recommendations = []

        for resource in banner.learning_resources:
            resource_data = self._get_resource_data(resource, workspace_id)
            enrollment = self._get_enrollment(user_id, resource.resource_type, resource.resource_id, workspace_id)

            recommendations.append(
                Recommendation(
                    id=resource.resource_id,
                    title=resource_data.get("title", resource.external_resource_title),
                    holder_image=resource_data.get("image", resource.external_resource_image),
                    external_resource_url=resource_data.get("url", resource.external_resource_url),
                    resource_type=resource.resource_type,
                    enrollment=enrollment
                )
            )

        return recommendations

    def _get_resource_data(self, resource, workspace_id: str) -> dict:
        if resource.resource_type in (ResourceType.COURSE.value, ResourceType.EVENT.value):
            try:
                course = self.course_repository.get_workspace_missions(workspace_id).get(id=resource.resource_id)
                url = getattr(course.external, "course_url", None) if hasattr(course, "external") else None
                return {
                    "title": course.name,
                    "image": course.holder_image,
                    "url": url
                }
            except ObjectDoesNotExist:
                return {}

        elif resource.resource_type == "LEARNING_TRAIL":
            try:
                trail = self.trail_repository.get_trails_workspace(workspace_id).get(id=resource.resource_id)
                return {
                    "title": trail.name,
                    "image": trail.holder_image,
                    "url": None
                }
            except ObjectDoesNotExist:
                return {}

        return {}

    def _get_enrollment(
        self, user_id: str, resource_type: str, resource_id: UUID, workspace_id: str
    ) -> Optional[Enrollment]:
        if resource_type == "COURSE":
            enrollment = self.mission_enrollment_repository.get_actual_user_enrollment(
                user_id, resource_id, workspace_id
            )
            if enrollment:
                return Enrollment(
                    id=enrollment.id,
                    status=enrollment.status,
                    goal_date=enrollment.goal_date
                )
        elif resource_type == "LEARNING_TRAIL":
            enrollment = LearningTrailEnrollment.objects.filter(user_id=user_id, workspace_id=workspace_id).first()
            if enrollment:
                return Enrollment(
                    id=enrollment.id,
                    status=enrollment.status,
                    goal_date=enrollment.goal_date
                )
