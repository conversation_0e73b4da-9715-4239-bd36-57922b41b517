from banner.domain.entities.recommendation import Enrollment, Recommendation
from banner.domain.repositories.recommendations_repository import RecommendationStrategy
from banner.infrastructure.models.banner_learning_resource import ResourceType
from mission.models.mission import EXTERNAL_PROVIDER, INTERNAL, SCORM
from mission.services.mission_recommendation import MissionRecommendationService
from user_activity.models import MissionEnrollment


class MissionRecommendationsRepository(RecommendationStrategy):
    def __init__(self, mission_service: MissionRecommendationService):
        self.service = mission_service

    def get_recommendations(self, user_id: str, workspace_id: str, language: str) -> [Recommendation]:
        missions = self.service.get_recommendations(user_id, workspace_id, language)
        missions = missions.filter(mission_model__in=[INTERNAL, EXTERNAL_PROVIDER, SCORM])[:3]
        enrollments = MissionEnrollment.objects.filter(
            user_id=user_id, workspace_id=workspace_id, mission_id__in=[mission.id for mission in missions]
        ).order_by("-end_date")
        enrollment_map = {e.mission_id: e for e in enrollments}

        return [
            Recommendation(
                id=mission.id,
                title=mission.name,
                holder_image=mission.holder_image,
                external_resource_url=mission.external.course_url if hasattr(mission, "external") else None,
                resource_type=ResourceType.COURSE.value,
                enrollment=self._build_enrollment(enrollment_map.get(mission.id))
            )
            for mission in missions
        ]

    def _build_enrollment(self, enrollment):
        if not enrollment:
            return None
        return Enrollment(
            id=enrollment.id,
            status=enrollment.status,
            goal_date=enrollment.goal_date
        )
