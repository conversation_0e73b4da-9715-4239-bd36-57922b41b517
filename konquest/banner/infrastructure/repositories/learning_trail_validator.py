from banner.application.dto.banner_dto import LearningResourceDTO
from banner.domain.repositories.resource_validator import LearningResourceValidator
from learning_trail.models import LearningTrail
from learning_trail.services import LearningTrailService


class LearningTrailValidator(LearningResourceValidator):
    def __init__(self, trail_service: LearningTrailService):
        self.trail_service = trail_service

    def is_valid(self, resource: LearningResourceDTO, workspace_id: str) -> bool:
        try:
            trail = self.trail_service.get_trails_workspace(workspace_id).get(id=resource.resource_id)
        except LearningTrail.DoesNotExist:
            return False
        return trail.is_active
