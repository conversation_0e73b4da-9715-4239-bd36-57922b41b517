from banner.application.dto.banner_dto import LearningResourceDTO
from banner.domain.repositories.resource_validator import LearningResourceValidator
from mission.models import Mission
from mission.models.mission_development_status_enum import DONE
from mission.repositories.mission.utils import MissionRepository


class CourseValidator(LearningResourceValidator):
    def __init__(self, course_repository: MissionRepository):
        self.course_repository = course_repository

    def is_valid(self, resource: LearningResourceDTO, workspace_id: str) -> bool:
        try:
            course = self.course_repository.get_workspace_missions(workspace_id).get(id=resource.resource_id)
        except Mission.DoesNotExist:
            return False
        return course.development_status == DONE
