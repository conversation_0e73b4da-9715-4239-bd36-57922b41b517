from banner.domain.entities.banner import Banner
from banner.infrastructure.models.banner_learning_resource import ResourceType
from django.core.exceptions import ObjectDoesNotExist
from learning_trail.services import LearningTrailService
from mission.repositories.mission.utils import MissionRepository


class EnrichBannerWithTitlesRepository:
    """
    EnrichBannerWithTitlesRepository is responsible for enriching the banner learning resources with the titles
    course_repository and trail_repository are deprecated dependencies and will be removed in future releases
    """
    def __init__(self, course_repository: MissionRepository, trail_repository: LearningTrailService):
        self.course_repository = course_repository
        self.trail_repository = trail_repository

    def enrich(self, banner: Banner) -> Banner:
        for resource in banner.learning_resources:
            resource.title = self.get_resource_title(
                resource, banner.workspace, self.course_repository, self.trail_repository
            )
        return banner

    def get_resource_title(self, resource, workspace_id, course_repository, trail_repository) -> str:
        if resource.resource_type in (ResourceType.COURSE.value, ResourceType.EVENT.value):
            try:
                course = course_repository.get_workspace_missions(workspace_id).get(id=resource.resource_id)
                return course.name
            except ObjectDoesNotExist:
                return ""

        elif resource.resource_type == ResourceType.LEARNING_TRAIL.value:
            try:
                trail = trail_repository.get_trails_workspace(workspace_id).get(id=resource.resource_id)
                return trail.name
            except ObjectDoesNotExist:
                return ""

        return ""
