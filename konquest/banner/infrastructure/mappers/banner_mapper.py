from banner.application.dto.banner_dto import CreateBannerDTO
from banner.domain.entities.banner import Banner as DomainBanner
from banner.domain.entities.banner import LearningResource
from banner.infrastructure.models.banner import Banner as ORMBanner
from banner.infrastructure.models.banner_learning_resource import BannerLearningResource


class BannerMapper:
    @staticmethod
    def from_dto(dto: CreateBannerDTO) -> DomainBanner:
        learning_resources = [
            LearningResource(
                resource_type=lr.resource_type,
                resource_id=lr.resource_id,
                external_resource_title=lr.external_resource_title,
                external_resource_url=lr.external_resource_url,
                external_resource_image=lr.external_resource_image,
                order=lr.order
            )
            for lr in dto.learning_resources
        ]

        return DomainBanner(
            start_date=dto.start_date,
            end_date=dto.end_date,
            workspace=dto.workspace_id,
            learning_resources=learning_resources
        )

    @staticmethod
    def to_domain(orm_banner: ORMBanner) -> DomainBanner:
        learning_resources = [
            LearningResource(
                resource_type=resource.resource_type,
                resource_id=resource.resource_id,
                external_resource_title=resource.external_resource_title,
                external_resource_url=resource.external_resource_url,
                external_resource_image=resource.external_resource_image,
                order=resource.order
            )
            for resource in orm_banner.learning_resources.all()
        ]

        return DomainBanner(
            id=orm_banner.id,
            start_date=orm_banner.start_date,
            end_date=orm_banner.end_date,
            workspace=orm_banner.workspace.id,
            learning_resources=learning_resources
        )

    @staticmethod
    def to_orm(domain_banner: DomainBanner) -> ORMBanner:
        orm_banner = ORMBanner(
            id=domain_banner.id,
            start_date=domain_banner.start_date,
            end_date=domain_banner.end_date,
            workspace_id=domain_banner.workspace
        )

        return orm_banner

    @staticmethod
    def learning_resources_to_orm(domain_banner: DomainBanner, orm_banner: ORMBanner):
        return [
            BannerLearningResource(
                banner=orm_banner,
                resource_type=lr.resource_type,
                resource_id=lr.resource_id,
                external_resource_title=lr.external_resource_title,
                external_resource_url=lr.external_resource_url,
                external_resource_image=lr.external_resource_image,
                order=lr.order
            )
            for lr in domain_banner.learning_resources
        ]
