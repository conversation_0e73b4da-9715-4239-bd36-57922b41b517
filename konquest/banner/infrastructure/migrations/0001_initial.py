# Generated by Django 5.0.4 on 2025-06-02 14:04

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('account', '0024_alter_user_country'),
    ]

    operations = [
        migrations.CreateModel(
            name='Banner',
            fields=[
                ('created_date', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='Updated Date')),
                ('deleted_date', models.DateTimeField(blank=True, null=True, verbose_name='Deleted Date')),
                ('deleted', models.BooleanField(default=False, verbose_name='Deleted')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('start_date', models.DateTimeField(blank=True, null=True)),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('workspace', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='account.workspace', verbose_name='Workspace')),
            ],
            options={
                'db_table': 'banner',
            },
        ),
        migrations.CreateModel(
            name='BannerLearningResource',
            fields=[
                ('created_date', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='Updated Date')),
                ('deleted_date', models.DateTimeField(blank=True, null=True, verbose_name='Deleted Date')),
                ('deleted', models.BooleanField(default=False, verbose_name='Deleted')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('resource_type', models.CharField(choices=[('COURSE', 'Course'), ('LEARNING_TRAIL', 'Learning Trail'), ('EXTERNAL_CONTENT', 'External Content'), ('EVENT', 'Event')], max_length=50)),
                ('resource_id', models.UUIDField(blank=True, null=True)),
                ('external_resource_title', models.CharField(blank=True, max_length=255, null=True)),
                ('external_resource_url', models.URLField(blank=True, null=True)),
                ('external_resource_image', models.TextField(blank=True, null=True)),
                ('order', models.IntegerField()),
                ('banner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='learning_resources', to='banner.banner')),
            ],
            options={
                'db_table': 'banner_learning_resources',
            },
        ),
    ]
