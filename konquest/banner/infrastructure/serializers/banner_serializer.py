from rest_framework import serializers

from banner.infrastructure.models.banner_learning_resource import ResourceType


class BannerLearningResourceInputSerializer(serializers.Serializer):
    resource_type = serializers.ChoiceField(choices=ResourceType.choices)
    resource_id = serializers.UUIDField(required=False, allow_null=True)
    external_resource_title = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    external_resource_url = serializers.URLField(required=False, allow_blank=True, allow_null=True)

    external_resource_image = serializers.URLField(required=False, allow_blank=True, allow_null=True)

    order = serializers.IntegerField()


class BannerInputSerializer(serializers.Serializer):
    id = serializers.ReadOnlyField()
    start_date = serializers.DateTimeField(required=False, allow_null=True)
    end_date = serializers.DateTimeField(required=False, allow_null=True)
    learning_resources = BannerLearningResourceInputSerializer(many=True)
