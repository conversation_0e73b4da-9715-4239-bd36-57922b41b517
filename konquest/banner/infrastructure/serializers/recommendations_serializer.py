# presentation/serializers/recommendation_serializer.py

from rest_framework import serializers


class EnrollmentSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    status = serializers.CharField()
    goal_date = serializers.DateField()


class RecommendationSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    external_resource_url = serializers.URLField()
    title = serializers.CharField()
    holder_image = serializers.URLField()
    resource_type = serializers.CharField()
    enrollment = EnrollmentSerializer(allow_null=True)
