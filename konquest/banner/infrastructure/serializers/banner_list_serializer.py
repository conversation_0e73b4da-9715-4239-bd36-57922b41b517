from banner.infrastructure.models.banner_learning_resource import ResourceType
from rest_framework import serializers


class BannerLearningResourceListSerializer(serializers.Serializer):
    resource_type = serializers.ChoiceField(choices=ResourceType.choices)
    resource_id = serializers.UUIDField(allow_null=True)
    external_resource_title = serializers.CharField(allow_blank=True, allow_null=True)
    external_resource_url = serializers.URLField(allow_blank=True, allow_null=True)
    external_resource_image = serializers.URLField(allow_blank=True, allow_null=True)
    order = serializers.IntegerField()
    title = serializers.CharField(allow_blank=True, allow_null=True)


class BannerListSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    start_date = serializers.DateTimeField(allow_null=True)
    end_date = serializers.DateTimeField(allow_null=True)
    learning_resources = BannerLearningResourceListSerializer(many=True)
