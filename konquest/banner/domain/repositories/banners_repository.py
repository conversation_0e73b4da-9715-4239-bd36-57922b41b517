from abc import ABC

from banner.domain.entities.banner import Banner


class BannersRepository(ABC):
    def save(self, banner: Banner) -> Banner:
        raise NotImplementedError()

    def update(self, banner: Banner) -> Banner:
        raise NotImplementedError()

    def get(self, banner_id: str) -> Banner:
        raise NotImplementedError()

    def delete(self, banner_id: str) -> Banner:
        raise NotImplementedError()

    def get_by_workspace(self, workspace_id: str) -> Banner:
        raise NotImplementedError()
