from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional, Union
from uuid import UUID


@dataclass
class LearningResource:
    resource_type: str
    resource_id: Optional[UUID]
    external_resource_title: Optional[str]
    external_resource_url: Optional[str]
    external_resource_image: Optional[str]
    order: int
    title: Optional[str] = ""


@dataclass
class Banner:
    start_date: Optional[datetime]
    end_date: Optional[datetime]
    workspace: UUID
    learning_resources: List[LearningResource] = field(default_factory=list)
    id: Union[UUID, None] = None
