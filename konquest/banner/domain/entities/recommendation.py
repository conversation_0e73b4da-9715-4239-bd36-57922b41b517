# domain/entities/recommendation.py

import datetime
import uuid
from dataclasses import dataclass
from typing import Optional


@dataclass
class Enrollment:
    id: uuid.UUID
    status: str
    goal_date: datetime.date


@dataclass
class Recommendation:
    id: uuid.UUID
    external_resource_url: Optional[str]
    title: str
    holder_image: Optional[str]
    resource_type: str
    enrollment: Optional[Enrollment] = None
