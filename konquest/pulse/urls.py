from django.urls import path
from pulse.views.pulse_bookmark_viewset import PulseBookmarkViewSet
from pulse.views.pulse_comment_viewset import PulseCommentsViewSet, PulseCommentViewSet
from pulse.views.pulse_priority_viewset import PulsesPriorityViewSet
from pulse.views.pulse_rating_viewset import PulseRatingsViewSet, PulseRatingViewSet
from pulse.views.pulse_recommendation_viewset import PulseRecommendationViewSet
from pulse.views.pulse_type_viewset import PulseTypeViewSet
from pulse.views.pulse_viewset import PulseViewSet

_READY_ONLY = {"get": "list"}

_LIST = {"get": "list", "post": "create"}

_DETAIL = {"get": "retrieve", "put": "update", "patch": "partial_update", "delete": "destroy"}

_ONLY_DELETE = {"delete": "destroy"}

urlpatterns = [
    path("", PulseViewSet.as_view(_LIST), name="pulses"),
    path("/<uuid:pk>", PulseViewSet.as_view(_DETAIL), name="pulses-detail"),
    path("/dashboard/priority", PulsesPriorityViewSet.as_view(_READY_ONLY), name="pulses-dashboard-priority-list"),
    path("/comments", PulseCommentViewSet.as_view(_LIST), name="pulse-comment-list"),
    path("/comments/<uuid:pk>", PulseCommentViewSet.as_view(_DETAIL), name="pulse-comment-detail"),
    path("/<uuid:pulse_uuid>/comments", PulseCommentsViewSet.as_view(_READY_ONLY), name="pulse-comments"),
    path("/ratings", PulseRatingViewSet.as_view(_LIST), name="pulse-rating-list"),
    path("/ratings/<uuid:pk>", PulseRatingViewSet.as_view(_DETAIL), name="pulse-rating-detail"),
    path("/<uuid:pulse_uuid>/ratings", PulseRatingsViewSet.as_view(_READY_ONLY), name="pulse-ratings"),
    path("/types", PulseTypeViewSet.as_view(_LIST), name="pulse-type-list"),
    path("/types/<uuid:pk>", PulseTypeViewSet.as_view(_DETAIL), name="pulse-type-detail"),
    path("/bookmarks", PulseBookmarkViewSet.as_view(_LIST), name="pulse-favorite-list"),
    path("/bookmarks/<uuid:pk>", PulseBookmarkViewSet.as_view(_ONLY_DELETE), name="pulse-favorite-delete"),
    path("/my-recommendations", PulseRecommendationViewSet.as_view(_READY_ONLY), name="pulse-recommendation-list"),
]
