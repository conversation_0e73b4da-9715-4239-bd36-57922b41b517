from constants import USER_CREATOR
from django.db import transaction
from django.db.models.signals import post_save, pre_save
from pulse.models import Channel, ChannelComment, Pulse, PulseChannel, PulseComment
from pulse.tasks import notifications
from utils.signals import receiver


@receiver(post_save, sender=PulseComment)
def create_pulse_comment_notification(sender, instance, created, **kwargs):
    if created:
        transaction.on_commit(lambda: notifications.notify_new_pulse_comment.delay(instance.id))


@receiver(post_save, sender=ChannelComment)
def create_channel_comment_notification(sender, instance, created, **kwargs):
    if created:
        transaction.on_commit(lambda: notifications.notify_new_channel_comment.delay(instance.id))


@receiver(post_save, sender=PulseChannel)
def create_new_pulse_in_channel_notification(sender, instance: PulseChannel, created, **kwargs):
    if created:
        transaction.on_commit(lambda: notifications.notify_new_pulse_to_channel_users.delay(instance.id))


@receiver(post_save, sender=Channel)
def create_new_channel_user_creator_notification(sender, instance: Channel, created, **kwargs):
    update_fields = kwargs.get("update_fields") or {}
    if USER_CREATOR in update_fields:
        transaction.on_commit(lambda: notifications.notify_new_channel_user_creator.delay(instance.id))


@receiver(pre_save, sender=Pulse)
def set_pulse_status_to_processing_when_learn_content_uuid_changes(sender, instance, **kwargs):
    """
    Set pulse status to PROCESSING when learn_content_uuid is changed
    """
    if not instance.pk:
        return

    try:
        old_instance = Pulse.objects.get(pk=instance.pk)
        if str(old_instance.learn_content_uuid) != str(instance.learn_content_uuid):
            instance.status = "PROCESSING"
    except Pulse.DoesNotExist:
        pass
