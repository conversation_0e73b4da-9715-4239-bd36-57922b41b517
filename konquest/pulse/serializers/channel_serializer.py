from account.serializers.user_serializer import UserSerializer
from account.serializers.workspace_serializer import WorkspaceShortSerializer
from custom.keeps_serializer import KPSerializer
from django.db.models import Sum
from pulse.models import Channel, ChannelSubscription
from pulse.models.channel_type import ChannelTypeEnum
from pulse.serializers.channel_category_serializer import ChannelCategorySerializer
from pulse.serializers.channel_type_serializer import ChannelTypeSimpleSerializer
from rest_framework import serializers


class ChannelSubscriptionShortSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChannelSubscription
        fields = "__all__"


class ChannelBaseSerializer(serializers.ModelSerializer):
    enrolled = serializers.SerializerMethodField()
    is_owner = serializers.SerializerMethodField()
    is_contributor = serializers.SerializerMethodField()

    def get_is_owner(self, obj):
        """
        Check if user logged is owner
        """
        user = self.context["request"].user

        return bool(str(obj.user_creator.id) == user["sub"])

    def get_is_contributor(self, obj):
        """
        Check if user logged is contributor
        """
        user = self.context["request"].user

        is_contributor = obj.channelcontributor_set.filter(user_id=user["sub"]).first()
        return bool(is_contributor)

    def get_enrolled(self, obj):
        if "request" in self.context:
            user = self.context["request"].user
            return obj.channelsubscription_set.filter(user_id=user["sub"]).count() > 0

        return None


class ChannelSerializer(ChannelBaseSerializer):
    class Meta:
        model = Channel
        fields = (
            "id",
            "name",
            "description",
            "holder_image",
            "workspace",
            "channel_type",
            "channel_category",
            "created_date",
            "updated_date",
            "is_active",
            "language",
            "user_creator",
        )


class ChannelListSerializer(ChannelBaseSerializer):
    channel_category = ChannelCategorySerializer()
    channel_type = ChannelTypeSimpleSerializer()
    user_creator = serializers.SerializerMethodField()
    channel_statistics = serializers.SerializerMethodField()
    rating_avg = serializers.SerializerMethodField()
    rating_count = serializers.SerializerMethodField()
    contributors = serializers.SerializerMethodField()
    subscription = serializers.SerializerMethodField()

    def get_subscription(self, obj: Channel):
        user_id = self.context["request"].user.get("sub")
        subscription = obj.channelsubscription_set.filter(user_id=user_id).first()
        return ChannelSubscriptionShortSerializer(subscription).data if subscription else None

    @staticmethod
    def get_channel_statistics(obj):
        return {
            "total_subscribers": obj.channelsubscription_set.all().count(),
            "total_pulses": obj.pulsechannel_set.all().count(),
            "content_type_distribution": {},
        }

    def get_user_creator(self, obj):
        client_id = None

        if "request" in self.context:
            client_id = self.context["request"].user.get("client_id")

        if str(obj.channel_type.id) == ChannelTypeEnum.PUBLIC.value and client_id != str(obj.workspace.id):
            return WorkspaceShortSerializer(obj.workspace).data

        return UserSerializer(obj.user_creator).data

    @staticmethod
    def get_rating_avg(obj):
        rating = None
        sum_ratings = obj.channelrating_set.aggregate(Sum("rating"))["rating__sum"]
        count_ratings = obj.channelrating_set.all().count()

        if sum_ratings and count_ratings:
            rating = round(sum_ratings / count_ratings, 1)

        return rating

    @staticmethod
    def get_rating_count(obj):
        rating = None
        count_ratings = obj.channelrating_set.all().count()

        if count_ratings:
            rating = count_ratings

        return rating

    def get_contributors(self, obj):
        """
        List channel's contributor.

        In cases where workspace in request (client_id) is not channels's owner, MUST NOT show contributors data.
        We need protect user data when share channels between companies.

        Ex.:

        User(A) --> Workspace(A) ---> Create a public channels
        User(B) --> Workspace(A) ---> List these public channels
        User(C) --> Workspace(B) ---> List these public channels

        User(B) can visualize User(A) info
        User(C) cannot visualize User(A) info

        """
        workspace_in_session = self.context["request"].user["client_id"]

        if str(obj.workspace.id) == workspace_in_session:
            contributors = obj.channelcontributor_set.filter().all()

            data = []

            for contributor in contributors:
                data.append(
                    {"id": contributor.user.id, "name": contributor.user.name, "avatar": contributor.user.avatar}
                )

            return data

        return None

    class Meta:
        model = Channel
        fields = (
            "id",
            "name",
            "description",
            "holder_image",
            "workspace",
            "enrolled",
            "channel_type",
            "channel_category",
            "channel_statistics",
            "user_creator",
            "created_date",
            "updated_date",
            "is_active",
            "rating_avg",
            "rating_count",
            "is_owner",
            "is_contributor",
            "contributors",
            "language",
            "subscription",
        )


class ChannelChangeUserCreatorSerializer(KPSerializer):
    user_creator = serializers.UUIDField(required=True)
