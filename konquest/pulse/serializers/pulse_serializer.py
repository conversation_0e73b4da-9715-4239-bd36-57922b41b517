from account.serializers.user_serializer import UserSerializer
from account.serializers.workspace_serializer import WorkspaceShortSerializer
from django.db.models import Sum
from learn_content.serializers.exam_serializer import ExamQuestionsSerializer
from pulse.models import Channel, Pulse, PulseChannel
from pulse.models.channel_type import ChannelTypeEnum
from pulse.serializers.pulse_tag_serializer import PulseSimpleTagSerializer
from rest_framework import serializers


class PulseSerializer(serializers.ModelSerializer):
    class Meta:
        model = Pulse
        fields = [
            "id",
            "name",
            "description",
            "holder_image",
            "duration_time",
            "points",
            "views",
            "learn_content_uuid",
            "user_creator",
            "pulse_type",
            "language",
            "status",
        ]


class PulseListSerializer(serializers.ModelSerializer):
    rating_avg = serializers.SerializerMethodField()
    rating_count = serializers.SerializerMethodField()
    channels = serializers.SerializerMethodField()
    bookmark_id = serializers.SerializerMethodField()
    tags = serializers.SerializerMethodField()
    user_creator = serializers.SerializerMethodField()

    def get_user_creator(self, obj):
        client_id = None
        channel = (
            PulseChannel.objects.filter(pulse=obj).select_related("channel__channel_type", "channel__workspace").first()
        )

        if "request" in self.context:
            client_id = self.context["request"].user.get("client_id")

        if str(channel.channel.channel_type.id) == ChannelTypeEnum.PUBLIC.value and client_id != str(
            channel.channel.workspace.id
        ):
            return WorkspaceShortSerializer(channel.channel.workspace).data

        return UserSerializer(obj.user_creator).data

    def get_tags(self, obj):
        tags = [x["name"] for x in PulseSimpleTagSerializer(obj.pulsetag_set.all(), many=True).data]
        return tags

    def get_bookmark_id(self, obj):
        user = self.context["request"].user
        bookmarked = obj.pulsebookmark_set.filter(user_id=user["sub"]).first()

        if bookmarked:
            return str(bookmarked.id)

        return None

    @staticmethod
    def get_rating_avg(obj):
        rating = None
        sum_ratings = obj.pulserating_set.aggregate(Sum("rating"))["rating__sum"]
        count_ratings = obj.pulserating_set.all().count()

        if sum_ratings and count_ratings:
            rating = round(sum_ratings / count_ratings, 1)

        return rating

    @staticmethod
    def get_rating_count(obj):
        rating = None
        count_ratings = obj.pulserating_set.all().count()

        if count_ratings:
            rating = count_ratings

        return rating

    @staticmethod
    def get_channels(obj):
        channel_ids = PulseChannel.objects.filter(pulse=obj).values_list("channel_id", flat=True)
        channels_query = Channel.objects.filter(id__in=channel_ids).select_related("channel_category")
        channels = []

        for channel in channels_query:
            channels.append(
                {
                    "id": channel.id,
                    "name": channel.name,
                    "category": channel.channel_category.name,
                    "channel_category_id": channel.channel_category_id,
                    "language": channel.language,
                }
            )

        return channels

    class Meta:
        model = Pulse
        fields = "__all__"
        depth = 1


class PulseChannelListOptimizedSerializer(serializers.ModelSerializer):
    id = serializers.CharField(source="channel.id")
    name = serializers.CharField(source="channel.name")
    category = serializers.CharField(source="channel.channel_category.name")
    channel_category_id = serializers.CharField(source="channel.channel_category.id")
    language = serializers.CharField(source="channel.language")

    class Meta:
        model = PulseChannel
        fields = (
            "id",
            "name",
            "category",
            "channel_category_id",
            "language",
        )


class PulseListOptimizedSerializer(serializers.ModelSerializer):
    rating_count = serializers.ReadOnlyField()
    rating_avg = serializers.ReadOnlyField()
    tags = serializers.ReadOnlyField()
    bookmark_id = serializers.ReadOnlyField()
    channels = PulseChannelListOptimizedSerializer(source="pulsechannel_set", many=True)
    user_creator = serializers.SerializerMethodField()

    def get_user_creator(self, obj):
        return UserSerializer(obj.user_creator).data

    class Meta:
        model = Pulse
        fields = "__all__"
        depth = 1


class PulseExamSerializer(PulseSerializer):
    exam = ExamQuestionsSerializer()

    class Meta:
        model = Pulse
        fields = (
            "id",
            "name",
            "exam",
        )
