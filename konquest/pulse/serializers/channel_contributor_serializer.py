from account.models import User
from pulse.models import Channel, ChannelContributor
from rest_framework import serializers


class UserChannelContributorSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["id", "name", "email", "avatar"]


class ChannelSimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Channel
        fields = ["id", "name"]


class ChannelContributorSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChannelContributor
        fields = "__all__"


class ChannelContributorFullSerializer(serializers.ModelSerializer):
    user = UserChannelContributorSerializer()
    channel = ChannelSimpleSerializer()

    class Meta:
        model = ChannelContributor
        fields = "__all__"


class ChannelAddContributors(serializers.Serializer):
    """
    Schema to define data to create (POST) user as contributors
    """

    user_id = serializers.ListField(child=serializers.CharField())

    def update(self, instance, validated_data):
        """
        This method is empty because updating instances is not required for this serializer.
        """
        pass

    def create(self, validated_data):
        """
        This method is empty because creation instances is not required for this serializer.
        """
        pass


class ChannelErrorContributors(serializers.Serializer):
    """
    Schema to define data return when create (POST) user as contributors
    """

    user = serializers.CharField()
    detail = serializers.CharField()
    key = serializers.CharField()

    def update(self, instance, validated_data):
        """
        This method is empty because updating instances is not required for this serializer.
        """
        pass

    def create(self, validated_data):
        """
        This method is empty because creation instances is not required for this serializer.
        """
        pass


class ChannelAddReturnContributors(serializers.Serializer):
    """
    Schema to define data return when create (POST) user as contributors
    """

    success = serializers.ListField(child=ChannelContributorFullSerializer())
    errors = serializers.ListField(child=ChannelErrorContributors())

    def update(self, instance, validated_data):
        """
        This method is empty because updating instances is not required for this serializer.
        """
        pass

    def create(self, validated_data):
        """
        This method is empty because creation instances is not required for this serializer.
        """
        pass
