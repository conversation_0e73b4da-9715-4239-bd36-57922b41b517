from uuid import UUID

from celery import shared_task
from config import settings
from constants import MOD<PERSON>IE<PERSON>
from django.forms import model_to_dict
from django.utils.translation import gettext_noop as _
from notification.services.notification_service import Message
from notification.services.notification_service_v2 import NotificationServiceV2
from pulse.models import Channel, ChannelComment, ChannelSubscription, PulseChannel, PulseComment
from utils.email_service.notification import notify_users
from utils.task_transaction import task_transaction

NEW_QUIZ_ADDED_FOR_A_CHANNEL_YOU_ARE_SUBSCRIBED = _("new_quiz_added_for_a_channel_you_are_subscribed")
PULSE_PULSE_NAME_ADDED_ON_CHANNEL = _("pulse_%(pulse_name)s_added_on_channel")
NEW_COMMENT_IN_YOUR_CHANNEL = _("new_comment_in_your_channel")
NEW_PULSE_COMMENT = _("new_pulse_comment")
CHANNEL_TRANSFERRED_TO_YOU = _("channel_transferred_to_you")


@shared_task(ignore_result=True)
def notify_new_pulse_to_channel_users(pulse_channel_id: UUID):
    with task_transaction("notify_new_pulse_to_channel_users", NotificationServiceV2) as service:
        pulse_channel = PulseChannel.objects.filter(id=pulse_channel_id).first()
        is_exam = pulse_channel.pulse.pulse_type.name == "Question"
        subscriptions = ChannelSubscription.objects.filter(channel=pulse_channel.channel).all()
        if is_exam:
            object_type = "NEW_QUIZ_ADDED_IN_THE_CHANNEL"
            message = Message(
                title=NEW_QUIZ_ADDED_FOR_A_CHANNEL_YOU_ARE_SUBSCRIBED, description=pulse_channel.channel.name
            )
        else:
            object_type = "NEW_PULSE_ADDED_IN_THE_CHANNEL"
            message = Message(
                title=PULSE_PULSE_NAME_ADDED_ON_CHANNEL,
                title_values={"pulse_name": pulse_channel.pulse.name},
                description=pulse_channel.channel.name,
            )

        service.create_notification(
            user_ids=[subscription.user_id for subscription in subscriptions],
            type_key=object_type,
            action="CREATE",
            message=message,
            object=pulse_channel.pulse_id,
            workspace_id=pulse_channel.channel.workspace_id,
        )


@shared_task(ignore_result=True)
def notify_new_channel_comment(channel_comment_id: UUID):
    with task_transaction("notify_new_channel_comment", NotificationServiceV2) as service:
        comment = ChannelComment.objects.get(id=channel_comment_id)
        creator_id = comment.channel.user_creator_id
        is_owner_comment = comment.user.id == comment.channel.user_creator.id
        if is_owner_comment:
            return
        message = Message(title=NEW_COMMENT_IN_YOUR_CHANNEL, description=comment.channel.name)

        service.create_notification(
            user_ids=[creator_id],
            type_key="NEW_CHANNEL_COMMENT",
            action="COMMENT",
            message=message,
            object=comment.channel.id,
            workspace_id=comment.channel.workspace_id,
        )


@shared_task(ignore_result=True)
def notify_new_pulse_comment(pulse_comment_id: UUID):
    with task_transaction("notify_new_pulse_comment", NotificationServiceV2) as service:
        comment = PulseComment.objects.get(id=pulse_comment_id)
        creator_id = comment.pulse.user_creator_id
        is_owner_comment = comment.user_id == creator_id
        if is_owner_comment:
            return
        message = Message(title=NEW_PULSE_COMMENT, description=comment.pulse.name)
        service.create_notification(
            user_ids=[creator_id],
            type_key="NEW_PULSE_COMMENT",
            action="COMMENT",
            message=message,
            object=comment.pulse_id,
            workspace_id=comment.pulse.pulsechannel_set.first().channel.workspace_id,
        )


@shared_task(ignore_result=True)
def notify_new_channel_user_creator(channel_id: UUID):
    with task_transaction(notify_new_channel_user_creator.__name__, NotificationServiceV2) as service:
        channel = Channel.objects.get(id=channel_id)
        new_user_creator = channel.user_creator
        message = Message(title=CHANNEL_TRANSFERRED_TO_YOU, description=channel.name)
        channel = Channel.objects.get(id=channel_id)
        service.create_notification(
            user_ids=[new_user_creator.id],
            type_key="CHANNEL_TRANSFERRED_TO_YOU",
            action=MODIFIED,
            object=channel.id,
            workspace_id=channel.workspace_id,
            message=message,
        )
        _send_new_channel_to_manage_email(channel)


def _send_new_channel_to_manage_email(channel: Channel) -> None:
    workspace = channel.workspace
    email_data = {
        "user_name": channel.user_creator.name,
        "channel_name": channel.name,
        "channel_link": settings.KONQUEST_WEB_CHANNEL_DETAIL_URL.format(channel.id),
    }
    receivers = [model_to_dict(channel.user_creator)]
    notify_users.delay(email_data, "new_channel_to_manage", workspace.id, receivers)
