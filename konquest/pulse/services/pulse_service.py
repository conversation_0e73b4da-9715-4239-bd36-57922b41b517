from authentication.keeps_permissions import SUPER_ADMIN
from config.settings import KEEPS_SECRET_TOKEN_INTEGRATION
from custom.keeps_exception_handler import ChannelEditNotAuthorizedException
from django.utils.timezone import now
from pulse.models import ChannelContributor, Pulse
from rest_clients.kontent import KontentClient
from shareds.model_service import ModelService


class PulseService(ModelService):
    def __init__(self):
        super().__init__(Pulse)

    def should_change_content_uuid(self, old_content_uuid, new_content_uuid) -> bool:
        kontent_client = KontentClient()

        old_content = kontent_client.get_learn_content(KEEPS_SECRET_TOKEN_INTEGRATION, old_content_uuid)
        old_content_type = old_content.get("content_type", {}).get("name") if old_content else None

        new_content = kontent_client.get_learn_content(KEEPS_SECRET_TOKEN_INTEGRATION, new_content_uuid)
        new_content_type = new_content.get("content_type", {}).get("name") if new_content else None
        return old_content_type == new_content_type

    def destroy(self, instance_id: str, request_user: str) -> None:
        pulse = Pulse.objects.get(id=instance_id)
        channel = pulse.pulsechannel_set.filter().first().channel
        if request_user.role != SUPER_ADMIN or str(channel.workspace.id) != str(request_user.workspace_id):
            self.check_permission_to_modify_pulse(pulse, request_user.id)
        super().delete_instance(pulse)
        self.delete_related_objects(pulse)

    @staticmethod
    def delete_related_objects(pulse: Pulse):
        pulse.pulsechannel_set.update(deleted=True, deleted_date=now())
        pulse.pulserating_set.update(deleted=True, deleted_date=now())
        pulse.pulsecomment_set.update(deleted=True, deleted_date=now())
        pulse.pulsebookmark_set.update(deleted=True, deleted_date=now())
        pulse.learncontentactivity_set.update(deleted=True, deleted_date=now())
        pulse.learning_trail_step.update(deleted=True, deleted_date=now())

    @staticmethod
    def check_permission_to_modify_pulse(pulse: Pulse, user_id: str) -> None:
        channel_ids = pulse.pulsechannel_set.filter().values_list("channel_id", flat=True)
        is_user_creator = str(pulse.user_creator.id) == str(user_id)
        is_contributor = ChannelContributor.objects.filter(user_id=user_id, channel_id__in=channel_ids).exists()
        if is_user_creator or is_contributor:
            return
        raise ChannelEditNotAuthorizedException()
