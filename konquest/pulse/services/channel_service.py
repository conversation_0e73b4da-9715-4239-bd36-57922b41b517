from typing import List

from account.models import User, Workspace
from account.services.workspace_service import WorkspaceService
from authentication.keeps_permissions import ADMIN, SUPER_ADMIN
from constants import CAN_CHANGE_CHANNEL, RULE_CAN_DELETE_CHANNEL_COMMENT, USER_CREATOR
from custom.keeps_exception_handler import (
    ChannelEditNotAuthorizedException,
    KeepsNoPermissionToDeleteChannelComment,
    KeepsNotAllowedError,
    KeepsNotFoundCompanyError,
    KeepsUserCreatorIsInactive,
    KeepsUserDoesntHaveManagerPermission,
)
from django.db import transaction
from django.db.models import Case, IntegerField, Q, QuerySet, When
from django.utils.timezone import now
from group.services.group_filters import GroupFilterService
from learning_trail.models import LearningTrailStep
from myaccount.application.services.myaccount_service import MyAccountService
from myaccount.domain.repositories.myaccount_respository import User<PERSON>as<PERSON>ermissionD<PERSON>
from pulse.models import Channel, <PERSON>Comment, ChannelContributor, <PERSON>ulse, <PERSON><PERSON><PERSON>hannel
from pulse.models.channel_type import ChannelTypeEnum
from pulse.services.pulse_service import PulseService
from rules import test_rule
from shareds.model_service import ModelService


class ChannelService(ModelService):
    """
    Companies may have the following settings (check workspace model)
        allow list public channel
        allow list paid channel
        allow create public channel
        allow create paid channel

    A user can access only channels or pulses of a workspace if have permission
    (relation and role on the workspace) and the workspace need be setting on
    x-client header param on request.

    """

    def __init__(self, myaccount_service: MyAccountService):
        super().__init__(Channel)
        self._workspace_service = WorkspaceService()
        self._pulse_service = PulseService()
        self._group_service = GroupFilterService()
        self._myaccount_service = myaccount_service

    @transaction.atomic()
    def save_pulse(self, channel_id: str, data: dict) -> Pulse:
        pulse = Pulse(**data)
        pulse.save()

        self.link_new_pulse(pulse, channel_id)
        return pulse

    @transaction.atomic()
    def update_user_creator(self, channel: Channel, user_id: str, workspace_id: str, token: str) -> Channel:
        user = User.objects.get(id=user_id)
        if not user.status:
            raise KeepsUserCreatorIsInactive()
        user_has_manager_permission = self._myaccount_service.has_manager_permission(
            UserHasPermissionDTO(user_id=user_id, workspace_id=workspace_id)
        )
        if not user_has_manager_permission:
            raise KeepsUserDoesntHaveManagerPermission()
        channel.user_creator_id = user_id
        channel.save(update_fields=[USER_CREATOR])
        Pulse.objects.filter(id__in=channel.pulsechannel_set.filter().values_list("pulse_id", flat=True)).update(
            user_creator_id=user_id
        )
        return channel

    @staticmethod
    def link_new_pulse(pulse: Pulse, channel_id: str) -> PulseChannel:
        pulse_channel = PulseChannel(pulse=pulse, channel_id=channel_id)
        pulse_channel.save()
        return pulse_channel

    @transaction.atomic()
    def destroy(self, instance_id: str, request_user: User) -> None:
        channel = Channel.objects.get(id=instance_id)
        if request_user.role != SUPER_ADMIN or str(channel.workspace.id) != str(request_user.workspace_id):
            self.check_permission_to_modify_channel_instance(channel, str(request_user.id))

        pulse_ids = PulseChannel.objects.filter(channel=channel).values_list("pulse__id", flat=True)
        self.delete_trail_step_by_pulses(pulse_ids)
        self._delete_relations(channel, pulse_ids)
        super().delete_instance(channel)

    @staticmethod
    def _delete_relations(channel: Channel, pulse_ids: List[str]) -> None:
        Pulse.objects.filter(id__in=pulse_ids).update(deleted=True)
        channel.pulsechannel_set.update(deleted=True)
        channel.channelrating_set.update(deleted=True)
        channel.groupchannel_set.update(deleted=True)
        channel.channelcontributor_set.update(deleted=True)
        channel.channelsubscription_set.update(deleted=True)

    @staticmethod
    def destroy_comment(comment_id: str, user: User) -> None:
        channel_comment = ChannelComment.objects.get(id=comment_id)
        if not test_rule(RULE_CAN_DELETE_CHANNEL_COMMENT, user, channel_comment):
            raise KeepsNoPermissionToDeleteChannelComment()
        channel_comment.delete()

    def get_allowed_channels_for_user(self, workspace_id: str, user_id: str, user_language: str) -> QuerySet:
        return self.get_allowed_channels(workspace_id, user_id, user_language).filter(is_active=True)

    def get_allowed_workspace_channels(self, workspace_id, user_language):
        """
        List allowed channels by workspace.
        Including channels where workspace is owner (or their user) and external channels if allowed in their configs.
        """
        workspace = Workspace.objects.filter(pk=workspace_id).first()

        external_channel_type_allowed = []
        workspace_channel_type_allowed = [ChannelTypeEnum.OPEN_FOR_COMPANY.value, ChannelTypeEnum.PUBLIC.value]

        if workspace.allow_list_public_channel:
            external_channel_type_allowed.append(ChannelTypeEnum.PUBLIC.value)

        if workspace.allow_list_paid_channel:
            external_channel_type_allowed.append(ChannelTypeEnum.PAID.value)

        channels = Channel.objects.filter(
            Q(workspace_id=workspace.id, channel_type_id__in=workspace_channel_type_allowed)
            | Q(workspace_id=workspace.id)
            | Q(channel_type_id__in=external_channel_type_allowed)
        )
        if user_language:
            channels = self.order_query_by_user_language(channels, user_language)

        return channels.order_by("-updated_date")

    def get_allowed_channels(self, workspace_id, user_id, user_language: str = None):
        """
        - List user logged companies allowed
        - Get workspace settings
        - Build workspace filters (Of what is allowed)
        - Apply select and return.
        """
        workspace = Workspace.objects.filter(pk=workspace_id).first()

        if not workspace:
            raise KeepsNotFoundCompanyError()

        external_channel_type_allowed = []
        workspace_channel_type_allowed = [ChannelTypeEnum.OPEN_FOR_COMPANY.value, ChannelTypeEnum.PUBLIC.value]

        if workspace.allow_list_public_channel:
            external_channel_type_allowed.append(ChannelTypeEnum.PUBLIC.value)

        if workspace.allow_list_paid_channel:
            external_channel_type_allowed.append(ChannelTypeEnum.PAID.value)

        channel_group = self._group_service.channel_filter(workspace_id, user_id)
        managed_channels = self.get_allowed_channels_user_managed(workspace_id, user_id)
        channels = Channel.objects.filter(
            Q(workspace_id=workspace.id, channel_type_id__in=workspace_channel_type_allowed)
            | Q(workspace_id=workspace.id, user_creator_id=user_id)
            | Q(id__in=channel_group)
            | Q(id__in=managed_channels)
            | Q(channel_type_id__in=external_channel_type_allowed)
        )

        return self.order_query_by_user_language(channels, user_language) if user_language else channels

    def get_all_channels_by_workspace(self, workspace_id, user_language: str = None):
        """
        - List user logged companies allowed
        - Get workspace settings
        - Build workspace filters (Of what is allowed)
        - Apply select and return.
        """
        workspace = Workspace.objects.filter(pk=workspace_id).first()

        if not workspace:
            raise KeepsNotFoundCompanyError()

        channels = Channel.objects.filter(workspace_id=workspace.id)

        return self.order_query_by_user_language(channels, user_language) if user_language else channels

    def get_allowed_pulses(self, workspace_id, user_id, user_language=None, **kwargs):
        channels = self.get_allowed_channels(user_id=user_id, workspace_id=workspace_id)
        pulse_channels = PulseChannel.objects.filter(channel__in=channels).values_list("pulse_id", flat=True)
        pulse = Pulse.objects.filter(id__in=pulse_channels, **kwargs)
        if user_language:
            pulse = self.order_query_by_user_language(pulse, user_language)

        return pulse

    def get_allowed_channels_user_managed(self, workspace_id, user_id, user_language=None):
        """
        Allowed channels to manage.

        This include channels these user (user_consumer_1_id) is owner (creator) or contributor

        :param workspace_id: workspace select in session (header x-client)
        :param user_id: should be a logged user or given by param
        :param user_language: user language to order query
        :return: query
        """
        channels_contributor = ChannelContributor.objects.filter(user_id=user_id).values_list("channel_id", flat=True)
        channels = Channel.objects.filter(
            Q(user_creator=user_id, workspace=workspace_id) | Q(id__in=channels_contributor, workspace=workspace_id)
        )

        return self.order_query_by_user_language(channels, user_language) if user_language else channels

    @staticmethod
    def get_workspace_pulses(workspace_id: str) -> QuerySet:
        channel_ids = Channel.objects.filter(workspace_id=workspace_id, is_active=True).values_list("id", flat=True)
        pulse_ids = PulseChannel.objects.filter(channel_id__in=channel_ids).values_list("pulse_id", flat=True)
        return Pulse.objects.filter(id__in=pulse_ids)

    @staticmethod
    def order_query_by_user_language(base_query: QuerySet, user_language: str):
        missions = base_query.annotate(
            lang=Case(When(language=user_language, then=1), default=0, output_field=IntegerField())
        ).order_by("-lang")
        return missions

    @staticmethod
    def check_permission_to_modify_channel_instance(channel: Channel, user_id: str) -> None:
        is_user_creator = str(channel.user_creator.id) == user_id
        is_contributor = channel.channelcontributor_set.filter(user_id=user_id).exists()
        if is_user_creator or is_contributor:
            return
        raise ChannelEditNotAuthorizedException()

    @staticmethod
    def check_user_permission_to_modify_channel(channel_id, user):
        """
        Check if a user can modify an channel, if not, raise keeps NotAllowedError
        """
        user_id = str(user.id)
        channel_contributor = ChannelContributor.objects.filter(channel_id=channel_id, user_id=user_id).values_list(
            "channel_id", flat=True
        )
        is_channel_owner = Channel.objects.filter(
            Q(id=channel_id, user_creator=user_id) | Q(id__in=channel_contributor)
        ).exists()
        if (not is_channel_owner and user.role == ADMIN) or (user.role != SUPER_ADMIN and not is_channel_owner):
            raise KeepsNotAllowedError(
                i18n="not_permission_to_modify_this_channel",
                detail="You don't have permission to modify this channel or the channel not exists",
            )

    def update_channel(self, channel_id, channel_data, user) -> Channel:
        channel = Channel.objects.get(id=channel_id)
        if not test_rule(CAN_CHANGE_CHANNEL, user, channel):
            raise ChannelEditNotAuthorizedException()
        if "is_active" in channel_data:
            self.set_pulses_in_channel_status(channel_id, channel_data["is_active"])
        if "language" in channel_data:
            self.set_pulses_in_channel_language(channel_id, channel_data["language"])

        channel.update(**channel_data)
        channel.save()
        return channel

    @staticmethod
    def delete_trail_step_by_pulses(pulse_ids: dict):
        deleted_date = now()
        LearningTrailStep.objects.filter(pulse__id__in=pulse_ids).update(deleted=True, deleted_date=deleted_date)

    @staticmethod
    def get_pulses_in_channel_ids(channel_id: str):
        pulses_in_channel = PulseChannel.objects.filter(channel_id=channel_id)
        pulses_ids = pulses_in_channel.values_list("pulse_id", flat=True)
        return pulses_ids

    def set_pulses_in_channel_status(self, channel_id: str, is_active: bool):
        pulses_ids = self.get_pulses_in_channel_ids(channel_id)
        Pulse.objects.filter(id__in=pulses_ids).update(is_active=is_active)

    def set_pulses_in_channel_language(self, channel_id: str, language: str):
        pulses_ids = self.get_pulses_in_channel_ids(channel_id)
        Pulse.objects.filter(id__in=pulses_ids).update(language=language)
