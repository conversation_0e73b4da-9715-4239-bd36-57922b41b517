import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from pulse.models import PulseType


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class PulseTypeViewsetTestCase(TestCase):
    fixtures = ["workspace"]

    def setUp(self):
        self.client = APIClient()

        self.pulse_type = mommy.make(PulseType, id=uuid.uuid4())
        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"

        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.url = reverse("pulse-type-list")

    def test_pulse_types_list(self, mock_return, mock_return_roles):
        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

    def test_pulse_type_get_success(self, mock_return, mock_return_roles):
        response = self.client.get(
            reverse("pulse-type-detail", args=[str(self.pulse_type.id)]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["name"], str(self.pulse_type.name))
        self.assertEqual(response.status_code, 200)

    def test_pulse_type_get_not_found(self, mock_return, mock_return_roles):
        response = self.client.get(
            reverse("pulse-type-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_pulse_type_post_success(self, mock_return, mock_return_roles):
        data = {
            "name": "Pulse Type 1",
            "description": "Pulse Type 1",
            "image": "http://www.keeps.com.br",
            "image_cover": "http://www.keeps.com.br",
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)

    def test_pulse_type_post_required_field(self, mock_return, mock_return_roles):
        data = {"description": "Pulse Type 1"}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response_json["name"], ["This field is required."])
        self.assertEqual(response.status_code, 400)

    def test_pulse_type_patch_success(self, mock_return, mock_return_roles):
        data = {
            "name": "update name",
        }

        response = self.client.patch(
            reverse("pulse-type-detail", args=[str(self.pulse_type.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["name"], data["name"])
        self.assertEqual(response.status_code, 200)

    def test_pulse_type_put_success(self, mock_return, mock_return_roles):
        data = {
            "name": "Pulse Type 2",
            "description": "Pulse Type 2",
            "image": "http://www.keeps.com.br",
            "image_cover": "http://www.keeps.com.br",
        }

        response = self.client.put(
            reverse("pulse-type-detail", args=[str(self.pulse_type.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["name"], data["name"])
        self.assertEqual(response.status_code, 200)

    def test_pulse_type_put_not_found(self, mock_return, mock_return_roles):
        data = {"name": "Pulse Type 2", "description": "Pulse Type 2"}

        response = self.client.put(
            reverse("pulse-type-detail", args=[str(uuid.uuid4())]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_pulse_delete_success(self, mock_return, mock_return_roles):
        response = self.client.delete(
            reverse("pulse-type-detail", args=[str(self.pulse_type.id)]), **self.headers, format="json"
        )
        self.assertEqual(response.status_code, 204)

    def test_pulse_delete_not_found(self, mock_return, mock_return_roles):
        response = self.client.delete(
            reverse("pulse-type-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)
