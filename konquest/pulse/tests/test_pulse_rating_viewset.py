import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from notification.models import NotificationType
from pulse.models import PulseChannel, PulseRating, PulseType


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class PulseRatingViewsetTestCase(TestCase):
    fixtures = ["workspace", "user", "channel_type", "channel_category", "channel", "pulse_type", "pulse"]

    def setUp(self):
        self.client = APIClient()
        self.pulse_type = mommy.make(PulseType, id=uuid.uuid4())
        self.notification_type = mommy.make(NotificationType, object_type="PULSE", action="CREATE")

        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.user_id = "489fb596-478f-40b5-b717-4931f20e4cc6"

        self.pulse_1_id = "7ec337a7-07f7-47e3-aaec-0b1239eec921"

        self.pulse_2_id = "7d254172-4f0a-49ac-8a9b-319bdbf3512b"

        self.channel_id = "87060745-def8-42c5-aa91-bb37411ed2fc"
        self.channel_pulse_1 = mommy.make(PulseChannel, channel_id=self.channel_id, pulse_id=self.pulse_1_id)
        self.channel_pulse_2 = mommy.make(PulseChannel, channel_id=self.channel_id, pulse_id=self.pulse_2_id)

        self.rating = mommy.make(PulseRating, pulse_id=self.pulse_1_id, user_id=self.user_id)

        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.url = reverse("pulse-rating-list")

    def test_pulses_ratings_list(self, mock_return, mock_return_roles):
        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

    def test_pulse_rating_get_success(self, mock_return, mock_return_roles):
        response = self.client.get(
            reverse("pulse-rating-detail", args=[str(self.rating.id)]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["user"], self.user_id)
        self.assertEqual(response.status_code, 200)

    def test_pulse_rating_get_not_found(self, mock_return, mock_return_roles):
        response = self.client.get(
            reverse("pulse-rating-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_pulse_rating_post_success(self, mock_return, mock_return_roles):
        data = {"rating": 1, "pulse": self.pulse_1_id, "user": self.user_id}

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)

    def test_pulse_rating_post_required_field(self, mock_return, mock_return_roles):
        data = {"rating": 1, "pulse": self.pulse_1_id}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response_json["user"], ["This field is required."])
        self.assertEqual(response.status_code, 400)

    def test_pulse_rating_post_invalid_foreign_key(self, mock_return, mock_return_roles):
        data = {"rating": 1, "pulse": self.pulse_1_id, "user": str(uuid.uuid4())}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertIsNotNone(response_json.get("user"))
        self.assertEqual(response.status_code, 400)

    def test_pulse_rating_patch_success(self, mock_return, mock_return_roles):
        data = {
            "rating": 4,
        }

        response = self.client.patch(
            reverse("pulse-rating-detail", args=[str(self.rating.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["rating"], data["rating"])
        self.assertEqual(response.status_code, 200)

    def test_pulse_rating_put_success(self, mock_return, mock_return_roles):
        data = {"rating": 2, "pulse": self.pulse_1_id, "user": self.user_id}

        response = self.client.put(
            reverse("pulse-rating-detail", args=[str(self.rating.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["rating"], data["rating"])
        self.assertEqual(response.status_code, 200)

    def test_pulse_rating_put_not_found(self, mock_return, mock_return_roles):
        data = {"rating": 1, "pulse": self.pulse_1_id, "user": str(uuid.uuid4())}

        response = self.client.put(
            reverse("pulse-rating-detail", args=[str(uuid.uuid4())]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_pulse_delete_success(self, mock_return, mock_return_roles):
        response = self.client.delete(
            reverse("pulse-rating-detail", args=[str(self.rating.id)]), **self.headers, format="json"
        )
        self.assertEqual(response.status_code, 204)

    def test_pulse_delete_not_found(self, mock_return, mock_return_roles):
        response = self.client.delete(
            reverse("pulse-rating-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)
