import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from pulse.models import Channel, ChannelCategory, ChannelPaidConfig, ChannelType


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class ChannelPaidConfigViewsetTestCase(TestCase):
    fixtures = ["workspace"]

    def setUp(self):
        self.client = APIClient()

        self.channel_type = mommy.make(ChannelType, id=uuid.uuid4())
        self.channel_category = mommy.make(ChannelCategory, id=uuid.uuid4())
        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"

        self.channel = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="CHANNEL-TEST-1",
            channel_type=self.channel_type,
            channel_category=self.channel_category,
            workspace_id=self.workspace_id,
        )

        self.paid_config = mommy.make(ChannelPaidConfig, channel=self.channel)

        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.url = reverse("channel-paid-configs-list")

    def test_channel_paid_configs_list(self, mock_return, mock_return_roles):
        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

    def test_channel_paid_config_get_success(self, mock_return, mock_return_roles):
        response = self.client.get(
            reverse("channels-paid-configs-detail", args=[str(self.paid_config.id)]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["channel"], str(self.channel.id))
        self.assertEqual(response.status_code, 200)

    def test_channel_paid_config_get_not_found(self, mock_return, mock_return_roles):
        response = self.client.get(
            reverse("channels-paid-configs-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_channel_paid_config_post_success(self, mock_return, mock_return_roles):
        data = {"unit": 1, "value": "10.20", "channel": str(self.channel.id)}

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)

    def test_channel_paid_config_post_required_field(self, mock_return, mock_return_roles):
        data = {"unit": 1, "value": "10.20"}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response_json["channel"], ["This field is required."])
        self.assertEqual(response.status_code, 400)

    def test_channel_paid_config_post_invalid_foreign_key(
        self, mock_return, mock_return_roles
    ):
        data = {"unit": 1, "value": "10.20", "channel": str(uuid.uuid4())}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertIsNotNone(response_json.get("channel"))
        self.assertEqual(response.status_code, 400)

    def test_channel_paid_config_patch_success(self, mock_return, mock_return_roles):
        data = {"unit": 2}

        response = self.client.patch(
            reverse("channels-paid-configs-detail", args=[str(self.paid_config.id)]),
            **self.headers,
            data=data,
            format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["unit"], data["unit"])
        self.assertEqual(response.status_code, 200)

    def test_channel_paid_config_put_success(self, mock_return, mock_return_roles):
        data = {"unit": 2, "value": "10.20", "channel": str(self.channel.id)}

        response = self.client.put(
            reverse("channels-paid-configs-detail", args=[str(self.paid_config.id)]),
            **self.headers,
            data=data,
            format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["unit"], data["unit"])
        self.assertEqual(response.status_code, 200)

    def test_channel_paid_config_put_not_found(self, mock_return, mock_return_roles):
        data = {"unit": 2, "value": "10.20", "channel": str(uuid.uuid4())}

        response = self.client.put(
            reverse("channels-paid-configs-detail", args=[str(uuid.uuid4())]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_channel_paid_config_delete_success(self, mock_return, mock_return_roles):
        response = self.client.delete(
            reverse("channels-paid-configs-detail", args=[str(self.paid_config.id)]), **self.headers, format="json"
        )
        self.assertEqual(response.status_code, 204)

    def test_channel_paid_config_delete_not_found(self, mock_return, mock_return_roles):
        response = self.client.delete(
            reverse("channels-paid-configs-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)
