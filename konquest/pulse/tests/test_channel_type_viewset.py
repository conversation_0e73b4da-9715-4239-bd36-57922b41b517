import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from pulse.models import ChannelType


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class ChannelTypeViewsetTestCase(TestCase):
    fixtures = ["workspace"]

    def setUp(self):
        self.client = APIClient()

        self.channel_type = mommy.make(ChannelType, id=uuid.uuid4())
        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"

        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.url = reverse("channel-types-list")

    def test_channel_types_list(self, mock_return, mock_return_roles):
        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

    def test_channel_types_get_success(self, mock_return, mock_return_roles):
        response = self.client.get(
            reverse("channels-types-detail", args=[str(self.channel_type.id)]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["id"], str(self.channel_type.id))
        self.assertEqual(response.status_code, 200)

    def test_channel_types_get_not_found(self, mock_return, mock_return_roles):
        response = self.client.get(
            reverse("channels-types-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_channel_types_post_success(self, mock_return, mock_return_roles):
        data = {"name": "Channel Type 1", "description": "Channel Type 1", "image": "http://www.keeps.com.br"}

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)

    def test_channel_types_post_required_field(self, mock_return, mock_return_roles):
        data = {"description": "Channel Category 1"}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response_json["name"], ["This field is required."])
        self.assertEqual(response.status_code, 400)

    def test_channel_types_patch_success(self, mock_return, mock_return_roles):
        data = {"description": "bla bla bla"}

        response = self.client.patch(
            reverse("channels-types-detail", args=[str(self.channel_type.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["description"], data["description"])
        self.assertEqual(response.status_code, 200)

    def test_channel_types_put_success(self, mock_return, mock_return_roles):
        data = {
            "name": "Channel Category Update 1",
            "description": "Channel Category Update 1",
            "image": "http://www.keeps.com.br",
        }

        response = self.client.put(
            reverse("channels-types-detail", args=[str(self.channel_type.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["name"], data["name"])
        self.assertEqual(response.status_code, 200)

    def test_channel_types_put_not_found(self, mock_return, mock_return_roles):
        data = {
            "name": "Channel Category Update 1",
            "description": "Channel Category Update 1",
            "image": "http://www.keeps.com.br",
        }

        response = self.client.put(
            reverse("channels-types-detail", args=[str(uuid.uuid4())]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_channel_types_delete_success(self, mock_return, mock_return_roles):
        response = self.client.delete(
            reverse("channels-types-detail", args=[str(self.channel_type.id)]), **self.headers, format="json"
        )
        self.assertEqual(response.status_code, 204)

    def test_channel_types_delete_not_found(self, mock_return, mock_return_roles):
        response = self.client.delete(
            reverse("channels-types-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)
