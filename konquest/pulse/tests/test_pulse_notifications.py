import uuid
from unittest import mock

from django.test import TestCase

from notification.services.notification_service import Message
from pulse.models import ChannelComment, PulseChannel, PulseComment
from pulse.tasks import notifications

CREATE_NOTIFICATION = "notification.services.notification_service_v2.NotificationServiceV2.create_notification"


class PulseNotificationsTestCase(TestCase):
    fixtures = [
        "user",
        "workspace",
        "channel_type",
        "channel_category",
        "channel",
        "pulse_type",
        "pulse",
        "pulse_comment",
        "channel_comment",
        "channel_subscription",
        "pulse_channel",
    ]

    def setUp(self) -> None:
        self.user_consumer_1_id = "489fb596-478f-40b5-b717-4931f20e4cc6"
        self.user_consumer_2_id = "4b5880b1-ddaf-4b4f-b72b-1aa0cf49fcf1"
        self.channel_id = "87060745-def8-42c5-aa91-bb37411ed2fc"
        self.pulse_id = "cb91cc20-2eb5-42d0-b0f3-e9361c8018b7"
        self.pulse_comment_id = "ddf42854-ce00-4fbc-a0bf-533df5259d8d"
        self.channel_comment_id = "6a09602c-6c99-4de2-a860-aa140993d9e8"

    @mock.patch(CREATE_NOTIFICATION)
    def test_notify_new_pulse(self, create_notification: mock.MagicMock):
        pulse_channel = PulseChannel.objects.get(pulse_id=self.pulse_id, channel_id=self.channel_id)
        notifications.notify_new_pulse_to_channel_users(pulse_channel.id)
        message = Message(
            title="pulse_%(pulse_name)s_added_on_channel",
            title_values={"pulse_name": pulse_channel.pulse.name},
            description=pulse_channel.channel.name,
        )

        create_notification.assert_called_with(
            user_ids=[uuid.UUID(self.user_consumer_1_id), uuid.UUID(self.user_consumer_2_id)],
            type_key="NEW_PULSE_ADDED_IN_THE_CHANNEL",
            action="CREATE",
            message=message,
            object=pulse_channel.pulse_id,
            workspace_id=pulse_channel.channel.workspace_id,
        )

    @mock.patch(CREATE_NOTIFICATION)
    def test_notify_new_pulse_comment(self, create_notification: mock.MagicMock):
        comment = PulseComment.objects.get(id=self.pulse_comment_id)

        notifications.notify_new_pulse_comment(comment.id)
        message = Message(title="new_pulse_comment", description=comment.pulse.name)
        create_notification.assert_called_with(
            user_ids=[comment.pulse.user_creator_id],
            type_key="NEW_PULSE_COMMENT",
            action="COMMENT",
            message=message,
            object=comment.pulse_id,
            workspace_id=comment.pulse.pulsechannel_set.first().channel.workspace_id,
        )

    @mock.patch(CREATE_NOTIFICATION)
    def test_notify_new_channel_comment(self, create_notification: mock.MagicMock):
        comment = ChannelComment.objects.get(id=self.channel_comment_id)
        message = Message(title="new_comment_in_your_channel", description=comment.channel.name)

        notifications.notify_new_channel_comment(comment.id)
        create_notification.assert_called_with(
            user_ids=[comment.channel.user_creator_id],
            type_key='NEW_CHANNEL_COMMENT',
            action="COMMENT",
            message=message,
            object=comment.channel.id,
            workspace_id=comment.channel.workspace_id,
        )
