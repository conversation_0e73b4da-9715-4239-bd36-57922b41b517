import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from pulse.models import Channel, ChannelType
from pulse.models.channel_type import ChannelTypeEnum

token_en = "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
token_es = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJSYzRyNmZMREJYWU1NcE00dS10OWhPOC1ob3hxNGhrdlVtcXo1WW9hQURzIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SF_zicfB3mQuCQPC6ne5nekwn42xqFRHiSqqjYHBO-9JLghWt-c_qToa9AEJAqVxkJ9WTg-LG75mh9AlzH0FkoLLEclhBH5_ZcdMsaP-s3uXHaMGJuLg_GKuC2vWT1z2BhbRdhiX68tJN6TyVsI8xxsKW5Oj5Y41LqRyWieaHSZjJjAATpIu6BBfzyd1jN-MwBh9CNKuttANf5OnvSyy4Eq-Q_9YVXLHH2v82-EGe1LRXdCbhpFeE0esNuF1k4ERGkUu6aPudqxviKMpIzAoUHG5j5PCNu-2x5SAtbAC1gktoZZlccKVpE0t-qjmU4k0w8Fu6Z-Y3Vf3P2ZIGvfeIQ"
token_br = "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info")
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
@mock.patch("authentication.keeps_permissions.KeepsBasePermission.get_user_roles")
class ChannelLanguageViewsetTestCase(TestCase):
    fixtures = ["user", "workspace"]

    def setUp(self):
        """ """
        self.client = APIClient()

        self.user_creator_id = "70db9d57-c146-436e-a06c-ea7e01c9659d"
        self.user_consumer_id = "ac408722-9054-4427-a4e3-c93010475e6f"
        self.workspace_id = "a6f33710-c07b-4051-9a80-c30ac8f6ebf1"

        self.channel_type_public = mommy.make(ChannelType, id=ChannelTypeEnum.PUBLIC.value)
        self.create_channels()
        self.url = reverse("channels-list")

    def test_mission_list_mission_english_user(
        self, mock_user_roles, mock_return, mock_return_roles
    ):
        """ """
        self.headers = {"HTTP_X_CLIENT": self.workspace_id, "HTTP_AUTHORIZATION": token_en}
        self.client.force_authenticate(
            user={"sub": self.user_consumer_id, "client_id": self.workspace_id, "locale": "en"}
        )

        response = self.client.get(self.url, **self.headers)
        self.assertEqual(response.status_code, 200)
        response = response.json()
        self.assertEqual(len(response["results"]), 9)
        self.assertEqual(response["results"][0]["language"], "en")
        self.assertEqual(response["results"][1]["language"], "en")
        self.assertEqual(response["results"][2]["language"], "en")

    def test_mission_list_mission_spanish_user(
        self, mock_user_roles, mock_return, mock_return_roles
    ):
        """ """
        self.headers = {"HTTP_X_CLIENT": self.workspace_id, "HTTP_AUTHORIZATION": token_es}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_id})

        response = self.client.get(self.url, **self.headers)
        self.assertEqual(response.status_code, 200)
        response = response.json()
        self.assertEqual(len(response["results"]), 9)
        self.assertEqual(response["results"][0]["language"], "es")
        self.assertEqual(response["results"][1]["language"], "es")
        self.assertEqual(response["results"][2]["language"], "es")

    def test_mission_list_mission_portuguese_user(
        self, mock_user_roles, mock_return, mock_return_roles
    ):
        """ """
        self.headers = {"HTTP_X_CLIENT": self.workspace_id, "HTTP_AUTHORIZATION": token_br}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_id})

        response = self.client.get(self.url, **self.headers)
        self.assertEqual(response.status_code, 200)
        response = response.json()
        self.assertEqual(len(response["results"]), 9)
        self.assertEqual(response["results"][0]["language"], "pt-BR")
        self.assertEqual(response["results"][1]["language"], "pt-BR")
        self.assertEqual(response["results"][2]["language"], "pt-BR")

    def test_mission_list_mission_english_filter(
        self, mock_user_roles, mock_return, mock_return_roles
    ):
        """ """
        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_id})

        response = self.client.get(self.url + "?language=en", **self.headers)
        self.assertEqual(response.status_code, 200)
        response = response.json()
        self.assertEqual(len(response["results"]), 3)
        self.assertEqual(response["results"][0]["language"], "en")
        self.assertEqual(response["results"][1]["language"], "en")
        self.assertEqual(response["results"][2]["language"], "en")

    def test_mission_list_mission_spanish_filter(
        self, mock_user_roles, mock_return, mock_return_roles
    ):
        """ """
        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_id})

        response = self.client.get(self.url + "?language=es", **self.headers)
        self.assertEqual(response.status_code, 200)
        response = response.json()
        self.assertEqual(len(response["results"]), 3)
        self.assertEqual(response["results"][0]["language"], "es")
        self.assertEqual(response["results"][1]["language"], "es")
        self.assertEqual(response["results"][2]["language"], "es")

    def test_mission_list_mission_es_and_en_filter_with_spanish_user(
        self, mock_user_roles, mock_return, mock_return_roles
    ):
        """ """
        self.headers = {"HTTP_X_CLIENT": self.workspace_id, "HTTP_AUTHORIZATION": token_es}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_id})

        response = self.client.get(self.url + "?language=en,es", **self.headers)
        self.assertEqual(response.status_code, 200)
        response = response.json()
        self.assertEqual(len(response["results"]), 6)
        self.assertEqual(response["results"][0]["language"], "es")
        self.assertEqual(response["results"][1]["language"], "es")
        self.assertEqual(response["results"][2]["language"], "es")
        self.assertEqual(response["results"][3]["language"], "en")
        self.assertEqual(response["results"][4]["language"], "en")
        self.assertEqual(response["results"][5]["language"], "en")

    def test_mission_list_mission_es_and_en_filter_with_english_user(
        self, mock_user_roles, mock_return, mock_return_roles
    ):
        """ """
        self.headers = {"HTTP_X_CLIENT": self.workspace_id, "HTTP_AUTHORIZATION": token_en}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_id})

        response = self.client.get(self.url + "?language=en,es", **self.headers)
        self.assertEqual(response.status_code, 200)
        response = response.json()
        self.assertEqual(len(response["results"]), 6)
        self.assertEqual(response["results"][0]["language"], "en")
        self.assertEqual(response["results"][1]["language"], "en")
        self.assertEqual(response["results"][2]["language"], "en")
        self.assertEqual(response["results"][3]["language"], "es")
        self.assertEqual(response["results"][4]["language"], "es")
        self.assertEqual(response["results"][5]["language"], "es")

    def create_channels(self):
        for x in range(3):
            mommy.make(
                Channel,
                id=uuid.uuid4(),
                name=f"Channel {x}",
                channel_type=self.channel_type_public,
                user_creator_id=self.user_creator_id,
                workspace_id=self.workspace_id,
                language="en",
            )

        for x in range(3):
            mommy.make(
                Channel,
                id=uuid.uuid4(),
                name=f"Channel {x}",
                channel_type=self.channel_type_public,
                user_creator_id=self.user_creator_id,
                workspace_id=self.workspace_id,
                language="es",
            )

        for x in range(3):
            mommy.make(
                Channel,
                id=uuid.uuid4(),
                name=f"Channel {x}",
                channel_type=self.channel_type_public,
                user_creator_id=self.user_creator_id,
                workspace_id=self.workspace_id,
                language="pt-BR",
            )
