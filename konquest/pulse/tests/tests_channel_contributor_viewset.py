import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from account.models import User
from authentication.keeps_permissions import SUPER_ADMIN
from pulse.models import Channel, ChannelContributor


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info")
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class ChannelContributorViewsetTestCase(TestCase):
    fixtures = ["workspace", "user", "channel_category", "channel_type", "channel"]

    def setUp(self):
        self.client = APIClient()
        self.workspace_id = "1ef9e947-5746-4171-b218-0f9bfd6c6a28"
        self.workspace_2_id = "d27b7876-9936-4529-b394-f7df10fe5899"

        self.user_creator_id = "489fb596-478f-40b5-b717-4931f20e4cc6"
        self.user_contributor_id = "4b5880b1-ddaf-4b4f-b72b-1aa0cf49fcf1"
        self.user_consumer_id = "244ef26a-eb00-4f80-9800-5e976e234452"

        self.user_contributor = User.objects.get(id=self.user_contributor_id)

        self.channel_id = "e99e0814-87ef-4fdd-ae97-5c3f84e373ea"

        self.channel = Channel.objects.get(id=self.channel_id)

        mommy.make(ChannelContributor, channel=self.channel, user_id=self.user_contributor_id)

        self.channel_2_id = "66f18647-498c-49fd-beb0-3750ec31dfb5"

        self.channel_2 = Channel.objects.get(id=self.channel_2_id)

        mommy.make(ChannelContributor, channel_id=self.channel_2_id, user_id=self.user_contributor_id)

        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.url = reverse("channel-contributors-list", args=[self.channel_id])

    def test_channel_contributor_list_workspace_1(self, mock_return, mock_return_roles):
        """
        List channel contributors only for user creator filtered by workspace
        """
        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.client.force_authenticate(user={"sub": self.user_creator_id, "client_id": self.workspace_id})

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)
        self.assertEqual(response["results"][0]["channel"]["id"], self.channel_id)
        self.assertEqual(response["results"][0]["channel"]["name"], self.channel.name)
        self.assertEqual(response["results"][0]["user"]["id"], self.user_contributor_id)
        self.assertEqual(response["results"][0]["user"]["name"], self.user_contributor.name)

    def test_channel_contributor_list_workspace_2(self, mock_return, mock_return_roles):
        """
        List channel contributors only for user creator filtered by workspace
        """
        url = reverse("channel-contributors-list", args=[self.channel_2_id])
        self.headers = {"HTTP_X_CLIENT": self.workspace_2_id}
        self.client.force_authenticate(user={"sub": self.user_creator_id, "client_id": self.workspace_2_id})

        response = self.client.get(url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)
        self.assertEqual(response["results"][0]["channel"]["id"], self.channel_2_id)
        self.assertEqual(response["results"][0]["channel"]["name"], self.channel_2.name)
        self.assertEqual(response["results"][0]["user"]["id"], self.user_contributor_id)
        self.assertEqual(response["results"][0]["user"]["name"], self.user_contributor.name)

    def test_channel_contributor_add(self, mock_return, mock_return_roles):
        """
        User creator can add contributors for your own channel
        """

        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.client.force_authenticate(user={"sub": self.user_creator_id, "client_id": self.workspace_id})


        new_user = {"id": "97e1e5ce-9b33-434d-afc0-ccf71162e84f", "name": "User Fake 3"}

        url = reverse("channel-contributors-manage", args=[self.channel_id, str(new_user["id"])])
        response = self.client.post(url, **self.headers, format="json").json()

        self.assertEqual(response["channel"]["id"], self.channel_id)
        self.assertEqual(response["channel"]["name"], str(self.channel.name))
        self.assertEqual(response["user"]["id"], str(new_user["id"]))
        self.assertEqual(response["user"]["name"], str(new_user["name"]))

    def test_super_admin_can_add_contributor(self, mock_return, mock_return_roles):
        """
        Super Admin can add contributors for any Workspace Channel
        """
        admin = mommy.make(User)

        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.client.force_authenticate(user={
            "sub": admin.id,
            "role": SUPER_ADMIN,
            "client_id": self.workspace_id
        })

        new_user = {"id": "97e1e5ce-9b33-434d-afc0-ccf71162e84f", "name": "User Fake 3"}

        url = reverse("channel-contributors-manage", args=[self.channel_id, str(new_user["id"])])
        response = self.client.post(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)

    def test_channel_contributor_cant_add_contributor(self, mock_return, mock_return_roles):
        """
        user cannot add contributors for another owner's channel
        """
        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.client.force_authenticate(user={"sub": self.user_contributor_id, "client_id": self.workspace_id})


        new_user = {"id": "97e1e5ce-9b33-434d-afc0-ccf71162e84f", "name": "User Fake 3"}

        url = reverse("channel-contributors-manage", args=[self.channel_id, str(new_user["id"])])
        response = self.client.post(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 403)
        self.assertEqual(response.json()["i18n"], "not_allowed")

    def test_channel_consumer_cannot_add_contributor(self, mock_return, mock_return_roles):
        """
        User cannot add contributors for another owner's channel
        """

        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_id})


        new_user = {"id": "97e1e5ce-9b33-434d-afc0-ccf71162e84f", "name": "User Fake 3"}

        url = reverse("channel-contributors-manage", args=[self.channel_id, str(new_user["id"])])
        response = self.client.post(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 403)
        self.assertEqual(response.json()["i18n"], "not_allowed")

    def test_channel_contributor_remove(self, mock_return, mock_return_roles):
        """
        The channel creator user can remove contributors from their own channel
        """

        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.client.force_authenticate(user={"sub": self.user_creator_id, "client_id": self.workspace_id})


        url = reverse("channel-contributors-manage", args=[self.channel_id, self.user_contributor_id])
        old_response = self.client.get(self.url, **self.headers, format="json").json()
        response = self.client.delete(url, **self.headers)
        new_response = self.client.get(self.url, **self.headers, format="json").json()

        self.assertEqual(response.status_code, 204)
        self.assertEqual(len(old_response["results"]), 1)
        self.assertEqual(len(new_response["results"]), 0)

    def test_channel_contributor_cannot_remove(self, mock_return, mock_return_roles):
        """
        The channel creator user can remove contributors from their own channel
        """

        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.client.force_authenticate(user={"sub": self.user_contributor_id, "client_id": self.workspace_id})


        url = reverse("channel-contributors-manage", args=[self.channel_id, self.user_contributor_id])
        response = self.client.delete(url, **self.headers)

        self.assertEqual(response.status_code, 403)
        self.assertEqual(response.json()["detail"], "Access not allowed. Only user creator can remove contributors")

    def test_channel_list_as_contributor(self, mock_return, mock_return_roles):
        """
        checks if the general channel listing, the is_contributor field is set up correctly.
        checks if the list of missions managed by the user (owner and contributor) returns correctly filtered.
        """
        channel_new = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Not listed",
            user_creator_id=self.user_creator_id,
            channel_type_id="a58aa724-ed55-4567-9850-206b3c2f393a",
            workspace_id=self.workspace_id,
        )

        url = reverse("channels-list")
        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.client.force_authenticate(user={"sub": self.user_contributor_id, "client_id": self.workspace_id})


        all_channels = self.client.get(url, **self.headers, format="json").json()
        manage_channel = self.client.get(url + "?managed=true", **self.headers, format="json").json()

        # check list all
        results = all_channels["results"]
        self.assertEqual(len(results), 2)
        channel_ids = [result["id"] for result in results]

        self.assertIn(str(channel_new.id), channel_ids)
        self.assertIn(self.channel_id, channel_ids)

        # check filter managed
        self.assertEqual(len(manage_channel["results"]), 1)
        self.assertEqual(manage_channel["results"][0]["id"], self.channel_id)
        self.assertEqual(manage_channel["results"][0]["name"], self.channel.name)

    def test_channel_detail(self, mock_return, mock_return_roles):
        """
        checks if the general channel listing, the is_contributor field is set up correctly.
        checks if the list of missions managed by the user (owner and contributor) returns correctly filtered.
        """
        url = reverse("channels-detail", args=[self.channel_id])
        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.client.force_authenticate(user={"sub": self.user_contributor_id, "client_id": self.workspace_id})


        response = self.client.get(url, **self.headers, format="json").json()

        self.assertEqual(response["id"], self.channel_id)
        self.assertEqual(response["name"], self.channel.name)
        self.assertEqual(response["contributors"][0]["name"], self.user_contributor.name)
