import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from notification.models import NotificationType
from pulse.models import PulseChannel, PulseComment, PulseType


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class PulseCommentViewsetTestCase(TestCase):
    fixtures = ["workspace", "user", "channel_type", "channel_category", "channel", "pulse_type", "pulse"]

    def setUp(self):
        self.client = APIClient()
        self.notification_type_1 = mommy.make(NotificationType, object_type="PULSE", action="CREATE")
        self.notification_type_2 = mommy.make(NotificationType, object_type="PULSE", action="COMMENT")
        self.pulse_type = mommy.make(PulseType, id=uuid.uuid4())
        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.user_id = "489fb596-478f-40b5-b717-4931f20e4cc6"

        self.pulse_1_id = "7ec337a7-07f7-47e3-aaec-0b1239eec921"

        self.pulse_2_id = "7d254172-4f0a-49ac-8a9b-319bdbf3512b"

        self.comment = mommy.make(PulseComment, pulse_id=self.pulse_1_id, user_id=self.user_id)
        self.comment_2 = mommy.make(PulseComment, pulse_id=self.pulse_2_id, user_id=self.user_id)

        self.channel_id = "87060745-def8-42c5-aa91-bb37411ed2fc"

        self.channel_pulse_1 = mommy.make(PulseChannel, channel_id=self.channel_id, pulse_id=self.pulse_1_id)
        self.channel_pulse_2 = mommy.make(PulseChannel, channel_id=self.channel_id, pulse_id=self.pulse_2_id)

        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.url = reverse("pulse-comment-list")

    def test_pulses_comments_list(self, mock_return, mock_return_roles):
        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 2)

    def test_pulse_comment_get_success(self, mock_return, mock_return_roles):
        response = self.client.get(
            reverse("pulse-comment-detail", args=[str(self.comment.id)]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["user"], self.user_id)
        self.assertEqual(response.status_code, 200)

    def test_pulse_comment_get_not_found(self, mock_return, mock_return_roles):
        response = self.client.get(
            reverse("pulse-comment-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_pulse_comment_post_success(self, mock_return, mock_return_roles):
        data = {"comment": "comment 1 bla bla bla", "pulse": self.pulse_1_id, "user": self.user_id}

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)

    def test_pulse_comment_post_required_field(self, mock_return, mock_return_roles):
        data = {"comment": "comment 1 bla bla bla", "pulse": self.pulse_1_id}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response_json["user"], ["This field is required."])
        self.assertEqual(response.status_code, 400)

    def test_pulse_comment_post_invalid_foreign_key(self, mock_return, mock_return_roles):
        data = {"comment": "comment 1 bla bla bla", "pulse": self.pulse_1_id, "user": str(uuid.uuid4())}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertIsNotNone(response_json.get("user"))
        self.assertEqual(response.status_code, 400)

    def test_pulse_comment_patch_success(self, mock_return, mock_return_roles):
        data = {
            "comment": "comment 1 bla bla bla",
        }

        response = self.client.patch(
            reverse("pulse-comment-detail", args=[str(self.comment.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["comment"], data["comment"])
        self.assertEqual(response.status_code, 200)

    def test_pulse_comment_put_success(self, mock_return, mock_return_roles):
        data = {"comment": "comment 2 bla bla bla", "pulse": self.pulse_1_id, "user": self.user_id}

        response = self.client.put(
            reverse("pulse-comment-detail", args=[str(self.comment.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["comment"], data["comment"])
        self.assertEqual(response.status_code, 200)

    def test_pulse_comment_put_not_found(self, mock_return, mock_return_roles):
        data = {"comment": "comment 2 bla bla bla", "pulse": self.pulse_1_id, "user": str(uuid.uuid4())}

        response = self.client.put(
            reverse("pulse-comment-detail", args=[str(uuid.uuid4())]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_pulse_delete_success(self, mock_return, mock_return_roles):
        response = self.client.delete(
            reverse("pulse-comment-detail", args=[str(self.comment.id)]), **self.headers, format="json"
        )
        self.assertEqual(response.status_code, 204)

    def test_pulse_delete_not_found(self, mock_return, mock_return_roles):
        response = self.client.delete(
            reverse("pulse-comment-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_pulse_comments_get_filtered(self, mock_return, mock_return_roles):
        response = self.client.get(reverse("pulse-comments", args=[self.pulse_1_id]), **self.headers, format="json")
        response_json = response.json()
        instance = PulseComment.objects.get(id=self.comment.id)

        self.assertEqual(response_json.get("results")[0].get("user").get("id"), self.user_id)
        self.assertEqual(response_json.get("results")[0].get("comment"), instance.comment)
        self.assertEqual(response.status_code, 200)
