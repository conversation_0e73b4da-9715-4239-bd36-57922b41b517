import uuid
from unittest import mock

from django.test import TestCase, override_settings
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from authentication.keeps_permissions import ADMIN, CONTENT, SUPER_ADMIN
from group.models import Group, GroupChannel
from learning_trail.models import LearningTrail, LearningTrailStep, LearningTrailType
from notification.models import NotificationType
from pulse.models import Channel, ChannelCategory, ChannelType, Pulse, PulseChannel, PulseType
from pulse.models.channel_type import ChannelTypeEnum
from user_activity.models import LearningTrailEnrollment

GET_USER_LANGUAGE = "utils.utils.Utils.get_user_language"
GET_PRIORITY_USER_ROLE = "authentication.keeps_permissions.KeepsBasePermission.get_priority_user_role_by_token"


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class PulseViewsetTestCase(TestCase):
    fixtures = ["workspace", "user"]

    def setUp(self):
        self.client = APIClient()
        self.notification_type = mommy.make(NotificationType, object_type="PULSE", action="CREATE")
        self.pulse_type = mommy.make(PulseType, id=uuid.uuid4())

        """
        Workspace: Keeps Test
        Configs:
            allow_list_public_channel = true
            allow_list_paid_channel = true
            allow_create_public_channel = true
            allow_create_paid_channel = true

        Workspace: Bayer Test
        Configs:
            allow_list_public_channel = true
            allow_list_paid_channel = true
            allow_create_public_channel = false
            allow_create_paid_channel = false

        Workspace: Mosaic Test
        Configs:
            allow_list_public_channel = false
            allow_list_paid_channel = false
            allow_create_public_channel = false
            allow_create_paid_channel = false
        """
        self.client = APIClient()

        self.user_creator_id = "489fb596-478f-40b5-b717-4931f20e4cc6"
        self.user_contributor_id = "4b5880b1-ddaf-4b4f-b72b-1aa0cf49fcf1"
        self.user_consumer_id = "244ef26a-eb00-4f80-9800-5e976e234452"

        self.workspace_keeps_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.workspace_bayer_id = "d27b7876-9936-4529-b394-f7df10fe5899"
        self.workspace_mosaic_id = "1ef9e947-5746-4171-b218-0f9bfd6c6a28"

        self.category_id = "f4a4b3a1-573c-488d-bd8c-19556790601a"

        self.channel_type_public = mommy.make(ChannelType, id=ChannelTypeEnum.PUBLIC.value)
        self.channel_type_open_for_workspace = mommy.make(ChannelType, id=ChannelTypeEnum.OPEN_FOR_COMPANY.value)
        self.channel_type_close_for_workspace = mommy.make(ChannelType, id=ChannelTypeEnum.CLOSE_FOR_COMPANY.value)
        self.channel_type_paid = mommy.make(ChannelType, id=ChannelTypeEnum.PAID.value)
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})
        self.url = reverse("pulses")
        self.url_detail_name = "pulses-detail"

    def test_pulses_workspace_user_not_allowed_for_workspace(
        self, mock_return, mock_return_roles
    ):
        """
        User don't have access allowed for workspace in x-client header.
        """
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})

        mock_return.return_value = False

        response = self.client.get(self.url, **self.headers, format="json")
        self.assertEqual(response.status_code, 403)

    def test_pulses_workspace_keeps_list(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})


        self.create_channels_and_pulses()

        response = self.client.get(self.url, **self.headers, format="json").json()
        results = response["results"]
        self.assertEqual(len(results), 4)
        channel_names = [result["channels"][0]["name"] for result in results]

        self.assertIn("Mosaic public channel created in past", channel_names)
        self.assertIn("Keeps paid channel", channel_names)
        self.assertIn("Keeps open for workspace channel", channel_names)
        self.assertIn("Keeps public channel", channel_names)

    def test_should_list_keeps_pulses_in_recommendation(self, mock_return, mock_return_roles):
        url = reverse("pulse-recommendation-list")
        self.create_channels_and_pulses()

        response = self.client.get(url, **self.headers, format="json")

        results = response.data["results"]
        self.assertEqual(len(results), 4)
        channel_names = [result["channels"][0]["name"] for result in results]
        self.assertIn("Mosaic public channel created in past", channel_names)
        self.assertIn("Keeps paid channel", channel_names)
        self.assertIn("Keeps open for workspace channel", channel_names)
        self.assertIn("Keeps public channel", channel_names)

    def test_delete_pulse(self, mock_return, mock_return_roles):
        self.client.force_authenticate(user={"sub": self.user_creator_id, "client_id": self.workspace_keeps_id})
        pulse = self.create_pulse()
        url = reverse(self.url_detail_name, args=[pulse.id])

        response = self.client.delete(url, **self.headers, format="json")

        pulse.refresh_from_db()
        self.assertEqual(response.status_code, 204)
        self.assertEqual(pulse.deleted, True)
        exists_pulse_channel_not_deleted = pulse.pulsechannel_set.filter(deleted=False).exists()
        self.assertFalse(exists_pulse_channel_not_deleted)

    def create_pulse(self):
        keeps_public_channel = mommy.make(
            Channel,
            workspace_id=self.workspace_keeps_id,
            channel_type=self.channel_type_public,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )
        pulse = mommy.make(Pulse, name="Pulse in Channel Public", user_creator_id=self.user_creator_id)
        mommy.make(PulseChannel, channel=keeps_public_channel, pulse=pulse)
        return pulse

    def test_not_list_pulse_deleted(self, mock_return, mock_return_roles):
        keeps_public_channel = mommy.make(
            Channel,
            workspace_id=self.workspace_keeps_id,
            channel_type=self.channel_type_public,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )
        pulse = mommy.make(Pulse, name="Pulse in Channel Public", deleted=True)
        mommy.make(PulseChannel, channel=keeps_public_channel, pulse=pulse)

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 0)

    def test_pulses_workspace_keeps_list_user_creator(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_creator_id, "client_id": self.workspace_keeps_id})


        self.create_channels_and_pulses()

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 5)

    def test_pulses_workspace_bayer_list(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_bayer_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_bayer_id})


        self.create_channels_and_pulses()

        response = self.client.get(self.url, **self.headers, format="json").json()
        results = response["results"]
        self.assertEqual(len(results), 4)
        channel_names = [result["channels"][0]["name"] for result in results]

        self.assertIn("Mosaic public channel created in past", channel_names)
        self.assertIn("Bayer open for workspace channel", channel_names)
        self.assertIn("Keeps paid channel", channel_names)
        self.assertIn("Keeps public channel", channel_names)

    @mock.patch(GET_USER_LANGUAGE)
    def test_pulses_order_by_user_language(
        self, mock_get_user_language, mock_return, mock_return_roles
    ):
        mock_get_user_language.return_value = "pt-BR"
        self.headers = {"HTTP_X_CLIENT": self.workspace_bayer_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_bayer_id})


        self.create_channels_and_pulses()

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 4)

        self.assertEqual(response["results"][0]["language"], "pt-BR")

    def test_pulses_workspace_mosaic_list(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_mosaic_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_mosaic_id})

        self.create_channels_and_pulses()

        response = self.client.get(self.url, **self.headers, format="json").json()
        results = response["results"]
        self.assertEqual(len(results), 2)
        channel_names = [result["channels"][0]["name"] for result in results]

        self.assertIn("Mosaic public channel created in past", channel_names)
        self.assertIn("Mosaic open for workspace channel", channel_names)

    def test_pulse_type_filter(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})
        pulse = self.create_channels_and_pulses()

        response = self.client.get(
            self.url + f"?pulse_type={pulse.pulse.pulse_type.id}", **self.headers, format="json"
        ).json()

        self.assertEqual(len(response["results"]), 1)

    def test_pulse_search_name(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})
        pulse = self.create_channels_and_pulses()

        response = self.client.get(self.url + f"?search={pulse.pulse.name}", **self.headers, format="json").json()

        self.assertEqual(len(response["results"]), 1)

        response = self.client.get(self.url + "?search=XXXXX", **self.headers, format="json").json()

        self.assertEqual(len(response["results"]), 0)

    def test_filter_pulse_by_category(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})
        self.create_channels_and_pulses()

        response = self.client.get(
            self.url + f"?channel_category={self.category_id}", **self.headers, format="json"
        ).json()
        results = response.get("results")
        for pulse in results:
            self.assertEqual(pulse["channels"][0]["channel_category_id"], self.category_id)

    def test_filter_pulse_by_bookmarked(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})
        self.create_channels_and_pulses()

        response = self.client.get(self.url + f"?bookmarked=true", **self.headers, format="json").json()
        results = response.get("results")
        for pulse in results:
            self.assertTrue(pulse["bookmark_id"])

    def test_pulse_get_success(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})
        pulse = self.create_channels_and_pulses()

        response = self.client.get(reverse("pulses-detail", args=[str(pulse.pulse.id)]), **self.headers, format="json")
        self.assertEqual(response.status_code, 200)

    def test_pulse_get_from_learning_trail_success(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_creator_id, "client_id": self.workspace_keeps_id})
        pulse = self.create_learning_trail_inactive_channels_and_pulses()

        response = self.client.get(reverse("pulses-detail", args=[str(pulse.pulse.id)]), **self.headers, format="json")
        self.assertEqual(response.status_code, 200)

    @mock.patch(GET_PRIORITY_USER_ROLE)
    def test_pulse_get_inactive_channel_content_success(
        self, get_priority_user_role, check_role, get_token_info
    ):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_creator_id, "client_id": self.workspace_keeps_id})
        get_priority_user_role.return_value = CONTENT
        pulse = self.create_inactive_channels_and_pulses()

        response = self.client.get(reverse("pulses-detail", args=[str(pulse.pulse.id)]), **self.headers, format="json")
        self.assertEqual(response.status_code, 200)

    @mock.patch(GET_PRIORITY_USER_ROLE)
    def test_pulse_get_inactive_channel_super_admin_success(
        self, get_priority_user_role, check_role, get_token_info
    ):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})
        get_priority_user_role.return_value = SUPER_ADMIN
        pulse = self.create_inactive_channels_and_pulses()

        response = self.client.get(reverse("pulses-detail", args=[str(pulse.pulse.id)]), **self.headers, format="json")
        self.assertEqual(response.status_code, 200)

    @mock.patch(GET_PRIORITY_USER_ROLE)
    def test_admin_get_pulse_from_closed_channel(
        self, get_priority_user_role, check_role, get_token_info
    ):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})
        get_priority_user_role.return_value = ADMIN
        pulse = self.create_closed_pulse(self.workspace_keeps_id)

        response = self.client.get(reverse("pulses-detail", args=[str(pulse.id)]), **self.headers, format="json")
        self.assertEqual(response.status_code, 200)

    @mock.patch(GET_PRIORITY_USER_ROLE)
    def test_content_user_get_pulse_for_consume(
        self, get_priority_user_role, check_role, get_token_info
    ):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})
        get_priority_user_role.return_value = CONTENT
        pulse = self.create_pulse()

        response = self.client.get(reverse("pulses-detail", args=[str(pulse.id)]), **self.headers, format="json")
        self.assertEqual(response.status_code, 200)

    def test_pulse_get_not_found(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})
        self.create_channels_and_pulses()

        response = self.client.get(reverse("pulses-detail", args=[str(uuid.uuid4())]), **self.headers, format="json")
        self.assertEqual(response.status_code, 404)

    def create_closed_pulse(self, workspace_id: str) -> Pulse:
        closed_channel = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Keeps close for workspace channel",
            is_active=True,
            channel_type=self.channel_type_close_for_workspace,
            user_creator_id=self.user_creator_id,
            workspace_id=self.workspace_keeps_id,
        )

        closed_pulse = mommy.make(Pulse, name="Pulse in Channel Close for Workspace")
        mommy.make(PulseChannel, channel=closed_channel, pulse=closed_pulse)
        group = mommy.make(Group, workspace_id=workspace_id)
        mommy.make(GroupChannel, group=group, channel=closed_channel)
        return closed_pulse

    def create_inactive_channels_and_pulses(self):
        channel_category = mommy.make(ChannelCategory, id=self.category_id)
        keeps_public_channel = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Keeps public channel",
            workspace_id=self.workspace_keeps_id,
            channel_type=self.channel_type_public,
            user_creator_id=self.user_creator_id,
            is_active=False,
            channel_category=channel_category,
        )

        keeps_public_pulse = mommy.make(Pulse, name="Pulse in Channel Public", user_creator_id=self.user_creator_id)
        pulse_template = mommy.make(PulseChannel, channel=keeps_public_channel, pulse=keeps_public_pulse)

        return pulse_template

    def create_learning_trail_inactive_channels_and_pulses(self):
        learning_trail_type = mommy.make(LearningTrailType)
        learning_trail = mommy.make(LearningTrail, id=uuid.uuid4(), learning_trail_type=learning_trail_type)
        enrollment = mommy.make(
            LearningTrailEnrollment,
            learning_trail=learning_trail,
            workspace_id=self.workspace_keeps_id,
            user_id=self.user_creator_id,
        )

        channel_category = mommy.make(ChannelCategory, id=self.category_id)
        keeps_public_channel = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Keeps public channel",
            workspace_id=self.workspace_keeps_id,
            channel_type=self.channel_type_public,
            user_creator_id=self.user_creator_id,
            is_active=False,
            channel_category=channel_category,
        )

        keeps_public_pulse = mommy.make(Pulse, name="Pulse in Channel Public", user_creator_id=self.user_creator_id)
        pulse_template = mommy.make(PulseChannel, channel=keeps_public_channel, pulse=keeps_public_pulse)

        learning_trail_step = mommy.make(LearningTrailStep, learning_trail=learning_trail, pulse=keeps_public_pulse)

        return pulse_template

    def create_channels_and_pulses(self):
        """
        Keeps
            - 4 pulses (1 Public, 1 Open Workspace, 1 Close Workspace, 1 Paid)
        Bayer
            - 2 pulses (1 Open Workspace, 1 Close Workspace)
        Mosaic
            - 3 pulses (1 Public, 1 Open Workspace, 1 Close Workspace)

        """
        channel_category = mommy.make(ChannelCategory, id=self.category_id)
        keeps_public_channel = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Keeps public channel",
            workspace_id=self.workspace_keeps_id,
            channel_type=self.channel_type_public,
            user_creator_id=self.user_creator_id,
            is_active=True,
            channel_category=channel_category,
        )

        keeps_public_pulse = mommy.make(Pulse, name="Pulse in Channel Public")
        pulse_template = mommy.make(PulseChannel, channel=keeps_public_channel, pulse=keeps_public_pulse)

        keeps_open_workspace_channel = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Keeps open for workspace channel",
            is_active=True,
            workspace_id=self.workspace_keeps_id,
            channel_type=self.channel_type_open_for_workspace,
            language="pt-BR",
            user_creator_id=self.user_creator_id,
        )

        keeps_open_workspace_pulse = mommy.make(Pulse, name="Pulse in Channel Open for Workspace")
        mommy.make(PulseChannel, channel=keeps_open_workspace_channel, pulse=keeps_open_workspace_pulse)

        keeps_close_workspace_channel = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Keeps close for workspace channel",
            is_active=True,
            channel_type=self.channel_type_close_for_workspace,
            user_creator_id=self.user_creator_id,
            workspace_id=self.workspace_keeps_id,
        )

        keeps_close_workspace_pulse = mommy.make(Pulse, name="Pulse in Channel Close for Workspace")
        mommy.make(PulseChannel, channel=keeps_close_workspace_channel, pulse=keeps_close_workspace_pulse)

        keeps_paid_channel = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Keeps paid channel",
            channel_type=self.channel_type_paid,
            workspace_id=self.workspace_keeps_id,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )

        keeps_paid_pulse = mommy.make(Pulse, name="Pulse in Channel Paid")
        mommy.make(PulseChannel, channel=keeps_paid_channel, pulse=keeps_paid_pulse)

        bayer_open_workspace_channel = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Bayer open for workspace channel",
            channel_type=self.channel_type_open_for_workspace,
            workspace_id=self.workspace_bayer_id,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )

        bayer_open_workspace_pulse = mommy.make(Pulse, name="Pulse in Channel Open for Workspace")
        mommy.make(PulseChannel, channel=bayer_open_workspace_channel, pulse=bayer_open_workspace_pulse)

        bayer_close_workspace_channel = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Bayer close for workspace channel",
            channel_type=self.channel_type_close_for_workspace,
            workspace_id=self.workspace_bayer_id,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )

        bayer_close_workspace_pulse = mommy.make(Pulse, name="Pulse in Channel Close for Workspace")
        mommy.make(PulseChannel, channel=bayer_close_workspace_channel, pulse=bayer_close_workspace_pulse)

        mosaic_open_workspace_channel = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Mosaic open for workspace channel",
            channel_type=self.channel_type_open_for_workspace,
            workspace_id=self.workspace_mosaic_id,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )

        mosaic_open_workspace_pulse = mommy.make(Pulse, name="Pulse in Channel Close for Workspace")
        mommy.make(PulseChannel, channel=mosaic_open_workspace_channel, pulse=mosaic_open_workspace_pulse)

        mosaic_close_workspace_channel = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Mosaic close for workspace channel",
            channel_type=self.channel_type_close_for_workspace,
            workspace_id=self.workspace_mosaic_id,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )

        mosaic_close_workspace_pulse = mommy.make(Pulse, name="Pulse in Channel Close for Workspace")
        mommy.make(PulseChannel, channel=mosaic_close_workspace_channel, pulse=mosaic_close_workspace_pulse)

        mosaic_public_workspace_channel = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Mosaic public channel created in past",
            channel_type=self.channel_type_public,
            workspace_id=self.workspace_mosaic_id,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )

        mosaic_public_workspace_pulse = mommy.make(Pulse, name="Pulse in Channel Close for Workspace")
        mommy.make(PulseChannel, channel=mosaic_public_workspace_channel, pulse=mosaic_public_workspace_pulse)

        return pulse_template

    @override_settings(SUSPEND_SIGNALS=False)
    @mock.patch("pulse.services.pulse_service.PulseService.should_change_content_uuid")
    def test_update_pulse_sets_status_to_processing_when_learn_content_uuid_changes(
            self, mock_should_change_content_uuid, mock_return, mock_return_roles
    ):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_creator_id, "client_id": self.workspace_keeps_id})

        mock_should_change_content_uuid.return_value = True

        original_uuid = uuid.uuid4()
        pulse = mommy.make(Pulse, name="Test Pulse", status="DONE", user_creator_id=self.user_creator_id, learn_content_uuid=original_uuid)
        keeps_public_channel = mommy.make(
            Channel,
            workspace_id=self.workspace_keeps_id,
            channel_type=self.channel_type_public,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )
        mommy.make(PulseChannel, channel=keeps_public_channel, pulse=pulse)

        new_uuid = uuid.uuid4()
        update_data = {"name": "Updated Pulse Name", "learn_content_uuid": new_uuid}
        response = self.client.patch(
            reverse("pulses-detail", args=[str(pulse.id)]),
            data=update_data,
            **self.headers,
            format="json"
        )

        self.assertEqual(response.status_code, 200)
        pulse.refresh_from_db()

        self.assertEqual(pulse.status, "PROCESSING")
        self.assertEqual(pulse.name, "Updated Pulse Name")
        self.assertNotEqual(str(pulse.learn_content_uuid), str(original_uuid))
        self.assertEqual(str(pulse.learn_content_uuid), str(new_uuid))

    @override_settings(SUSPEND_SIGNALS=False)
    def test_update_pulse_keeps_status_when_learn_content_uuid_not_changed(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_creator_id, "client_id": self.workspace_keeps_id})

        original_uuid = uuid.uuid4()
        pulse = mommy.make(Pulse, name="Test Pulse", status="DONE", user_creator_id=self.user_creator_id, learn_content_uuid=original_uuid)
        keeps_public_channel = mommy.make(
            Channel,
            workspace_id=self.workspace_keeps_id,
            channel_type=self.channel_type_public,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )
        mommy.make(PulseChannel, channel=keeps_public_channel, pulse=pulse)

        update_data = {"name": "Updated Pulse Name"}
        response = self.client.patch(
            reverse("pulses-detail", args=[str(pulse.id)]),
            data=update_data,
            **self.headers,
            format="json"
        )

        self.assertEqual(response.status_code, 200)
        pulse.refresh_from_db()

        self.assertEqual(pulse.status, "DONE")
        self.assertEqual(pulse.name, "Updated Pulse Name")
        self.assertEqual(str(pulse.learn_content_uuid), str(original_uuid))

    @mock.patch("pulse.services.pulse_service.PulseService.should_change_content_uuid")
    def test_update_pulse_raises_exception_when_content_type_change_not_allowed(
        self, mock_should_change_content_uuid, mock_return, mock_return_roles
    ):
        """
        Test that update raises InvalidContentTypeChangeException when should_change_content_uuid returns False
        """
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_creator_id, "client_id": self.workspace_keeps_id})

        # Mock should_change_content_uuid to return False
        mock_should_change_content_uuid.return_value = False

        original_uuid = uuid.uuid4()
        pulse = mommy.make(Pulse, name="Test Pulse", status="DONE", user_creator_id=self.user_creator_id, learn_content_uuid=original_uuid)
        keeps_public_channel = mommy.make(
            Channel,
            workspace_id=self.workspace_keeps_id,
            channel_type=self.channel_type_public,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )
        mommy.make(PulseChannel, channel=keeps_public_channel, pulse=pulse)

        new_uuid = uuid.uuid4()
        update_data = {"name": "Updated Pulse Name", "learn_content_uuid": new_uuid}

        response = self.client.put(
            reverse("pulses-detail", args=[str(pulse.id)]),
            data=update_data,
            **self.headers,
            format="json"
        )

        self.assertEqual(response.status_code, 400)

    @mock.patch("pulse.services.pulse_service.PulseService.should_change_content_uuid")
    def test_partial_update_pulse_raises_exception_when_content_type_change_not_allowed(
        self, mock_should_change_content_uuid, mock_return, mock_return_roles
    ):
        """
        Test that partial_update raises InvalidContentTypeChangeException when should_change_content_uuid returns False
        """
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_creator_id, "client_id": self.workspace_keeps_id})

        # Mock should_change_content_uuid to return False
        mock_should_change_content_uuid.return_value = False

        original_uuid = uuid.uuid4()
        pulse = mommy.make(Pulse, name="Test Pulse", status="DONE", user_creator_id=self.user_creator_id, learn_content_uuid=original_uuid)
        keeps_public_channel = mommy.make(
            Channel,
            workspace_id=self.workspace_keeps_id,
            channel_type=self.channel_type_public,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )
        mommy.make(PulseChannel, channel=keeps_public_channel, pulse=pulse)

        new_uuid = uuid.uuid4()
        update_data = {"learn_content_uuid": new_uuid}

        response = self.client.patch(
            reverse("pulses-detail", args=[str(pulse.id)]),
            data=update_data,
            **self.headers,
            format="json"
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()["i18n"], "invalid_content_type_change")

        pulse.refresh_from_db()
        self.assertEqual(str(pulse.learn_content_uuid), str(original_uuid))

    def test_channel_list_only_active_channels(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_creator_id, "client_id": self.workspace_keeps_id})

        self.create_channels_and_pulses()
        channel_to_pulse_disabled = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Mosaic public channel created in past",
            channel_type=self.channel_type_public,
            workspace_id=self.workspace_mosaic_id,
            user_creator_id=self.user_creator_id,
            is_active=False,
        )

        pulse_disabled = mommy.make(Pulse, name="Pulse in Channel Close for Workspace", is_active=False)
        mommy.make(PulseChannel, channel=channel_to_pulse_disabled, pulse=pulse_disabled)
        response = self.client.get(self.url, **self.headers, format="json").json()
        results = response["results"]
        self.assertEqual(len(results), 5)
        for result in results:
            self.assertEqual(result["is_active"], True)
            self.assertFalse(result["id"] == pulse_disabled.id)