import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from account.models import Workspace
from pulse.models.channel_category import ChannelCategory


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
class ChannelCategoryViewsetTestCase(TestCase):
    fixtures = ["workspace"]

    def setUp(self):
        self.client = APIClient()

        self.channel_category = mommy.make(ChannelCategory, id=uuid.uuid4(), is_public=True)
        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.workspace = Workspace.objects.get(id=self.workspace_id)

        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.url = reverse("channel-categories-list")

    def test_channel_categories_list(self, mock_return):
        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

    def test_list_only_workspace_categories(self, mock_return):
        self.workspace.allow_list_public_categories = False
        self.workspace.save()
        category = mommy.make(ChannelCategory, workspace=self.workspace, is_public=False)

        response = self.client.get(self.url, **self.headers, format="json").json()

        results = response["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["id"], str(category.id))

    def test_channel_category_get_success(self, mock_return):
        response = self.client.get(
            reverse("channel-categories-detail", args=[str(self.channel_category.id)]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["id"], str(self.channel_category.id))
        self.assertEqual(response.status_code, 200)

    def test_channel_category_get_not_found(self, mock_return):
        response = self.client.get(
            reverse("channel-categories-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_channel_category_post_success(self, mock_return):
        data = {"name": "Channel Category 1", "description": "Channel Category 1", "image": "http://www.keeps.com.br"}

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)

    def test_channel_category_post_required_field(self, mock_return):
        data = {"description": "Channel Category 1", "image": "http://www.keeps.com.br"}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response_json["name"], ["This field is required."])
        self.assertEqual(response.status_code, 400)

    def test_channel_category_patch_success(self, mock_return):
        data = {"description": "bla bla bla"}

        response = self.client.patch(
            reverse("channel-categories-detail", args=[str(self.channel_category.id)]),
            **self.headers,
            data=data,
            format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["description"], data["description"])
        self.assertEqual(response.status_code, 200)

    def test_channel_category_put_success(self, mock_return):
        data = {
            "name": "Channel Category Update 1",
            "description": "Channel Category Update 1",
            "image": "http://www.keeps.com.br",
        }

        response = self.client.put(
            reverse("channel-categories-detail", args=[str(self.channel_category.id)]),
            **self.headers,
            data=data,
            format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["name"], data["name"])
        self.assertEqual(response.status_code, 200)

    def test_channel_category_put_not_found(self, mock_return):
        data = {"name": "Channel Category Update 1", "description": "Channel Category Update 1"}

        response = self.client.put(
            reverse("channel-categories-detail", args=[str(uuid.uuid4())]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_channel_category_delete_success(self, mock_return):
        response = self.client.delete(
            reverse("channel-categories-detail", args=[str(self.channel_category.id)]), **self.headers, format="json"
        )
        self.assertEqual(response.status_code, 204)

    def test_channel_category_delete_not_found(self, mock_return):
        response = self.client.delete(
            reverse("channel-categories-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)
