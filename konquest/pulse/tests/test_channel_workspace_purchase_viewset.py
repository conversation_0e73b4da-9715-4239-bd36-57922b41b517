import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from pulse.models import Channel, ChannelCategory, ChannelType, ChannelWorkspacePurchase


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class ChannelWorkspacePurchaseViewsetTestCase(TestCase):
    fixtures = ["workspace"]

    def setUp(self):
        self.client = APIClient()

        self.channel_type = mommy.make(ChannelType, id=uuid.uuid4())
        self.channel_category = mommy.make(ChannelCategory, id=uuid.uuid4())
        self.workspace_1 = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.workspace_2 = "f7d9bd2b-0d0c-45e3-bebb-70b9797ce0e5"

        self.channel_1 = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="CHANNEL-TEST-WORKSPACE-1",
            channel_type=self.channel_type,
            channel_category=self.channel_category,
            workspace_id=self.workspace_1,
        )

        self.channel_2 = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="CHANNEL-TEST-WORKSPACE-2",
            channel_type=self.channel_type,
            channel_category=self.channel_category,
            workspace_id=self.workspace_2,
        )

        self.purchase_1 = mommy.make(ChannelWorkspacePurchase, workspace_id=self.workspace_1, channel=self.channel_2)
        self.purchase_2 = mommy.make(ChannelWorkspacePurchase, workspace_id=self.workspace_2, channel=self.channel_1)

        self.headers = {"HTTP_X_CLIENT": str(self.workspace_1)}
        self.url = reverse("channel-workspace-purchase-list")

    def test_channel_purchase_purchase_workspace_1_list(self, mock_return, mock_return_roles):
        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

    def test_channel_purchase_purchase_workspace_2_list(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_2)}
        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

    def test_channel_purchase_purchase_get_success(self, mock_return, mock_return_roles):
        response = self.client.get(
            reverse("channel-workspace-purchase-detail", args=[str(self.purchase_1.id)]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["workspace"], str(self.workspace_1))
        self.assertEqual(response.status_code, 200)

    def test_channel_purchase_purchase_get_not_found(self, mock_return, mock_return_roles):
        response = self.client.get(
            reverse("channel-workspace-purchase-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_channel_purchase_post_success(self, mock_return, mock_return_roles):
        data = {
            "quantity_unit": 1,
            "value": "10",
            "channel": str(self.channel_1.id),
            "workspace": str(self.workspace_2),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)

    def test_channel_purchase_post_required_field(self, mock_return, mock_return_roles):
        data = {"quantity_unit": 1, "value": "10", "channel": str(self.channel_1.id)}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response_json["workspace"], ["This field is required."])
        self.assertEqual(response.status_code, 400)

    def test_channel_purchase_post_invalid_foreign_key(self, mock_return, mock_return_roles):
        data = {"quantity_unit": 1, "value": "10", "channel": str(self.channel_1.id), "workspace": str(uuid.uuid4())}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertIsNotNone(response_json.get("workspace"))
        self.assertEqual(response.status_code, 400)

    def test_channel_purchase_patch_success(self, mock_return, mock_return_roles):
        data = {
            "quantity_unit": 10,
        }

        response = self.client.patch(
            reverse("channel-workspace-purchase-detail", args=[str(self.purchase_1.id)]),
            **self.headers,
            data=data,
            format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["quantity_unit"], data["quantity_unit"])
        self.assertEqual(response.status_code, 200)

    def test_channel_purchase_put_success(self, mock_return, mock_return_roles):
        data = {
            "quantity_unit": 2,
            "value": "20",
            "channel": str(self.channel_2.id),
            "workspace": str(self.workspace_1),
        }

        response = self.client.put(
            reverse("channel-workspace-purchase-detail", args=[str(self.purchase_1.id)]),
            **self.headers,
            data=data,
            format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["quantity_unit"], data["quantity_unit"])
        self.assertEqual(response.status_code, 200)

    def test_channel_purchase_put_not_found(self, mock_return, mock_return_roles):
        data = {"quantity_unit": 2, "value": "20", "channel": str(self.channel_2.id)}

        response = self.client.put(
            reverse("channel-workspace-purchase-detail", args=[str(uuid.uuid4())]),
            **self.headers,
            data=data,
            format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_channel_purchase_delete_success(self, mock_return, mock_return_roles):
        response = self.client.delete(
            reverse("channel-workspace-purchase-detail", args=[str(self.purchase_1.id)]), **self.headers, format="json"
        )
        self.assertEqual(response.status_code, 204)

    def test_channel_purchase_delete_not_found(self, mock_return, mock_return_roles):
        response = self.client.delete(
            reverse("channel-workspace-purchase-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)
