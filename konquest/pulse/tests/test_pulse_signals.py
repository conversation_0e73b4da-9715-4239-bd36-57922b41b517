import uuid
from django.test import TestCase, override_settings
from model_mommy import mommy

from pulse.models import Pulse, PulseType
from account.models import User


class PulseSignalsTestCase(TestCase):
    fixtures = ["user", "workspace"]

    def setUp(self):
        self.pulse_type = mommy.make(PulseType)
        self.user_id = "489fb596-478f-40b5-b717-4931f20e4cc6"

        # Ensure the user exists in the database
        self.user = User.objects.filter(id=self.user_id).first()
        if not self.user:
            self.user = mommy.make(User, id=self.user_id, name="Test User")

        self.learn_content_uuid = uuid.uuid4()
        self.new_learn_content_uuid = uuid.uuid4()

        # Create a pulse with initial learn_content_uuid
        self.pulse = mommy.make(
            Pulse,
            learn_content_uuid=self.learn_content_uuid,
            user_creator=self.user,
            pulse_type=self.pulse_type,
            status="DONE"  # Start with DONE status
        )

    @override_settings(SUSPEND_SIGNALS=False)
    def test_pulse_status_changes_to_processing_when_learn_content_uuid_changes(self):
        """
        Test that the pulse status is set to PROCESSING when learn_content_uuid changes
        """
        # Update the pulse with a new learn_content_uuid
        self.pulse.learn_content_uuid = self.new_learn_content_uuid
        self.pulse.save()

        # Refresh from database to get updated values
        self.pulse.refresh_from_db()

        # Assert that the status has been changed to PROCESSING
        self.assertEqual(self.pulse.status, "PROCESSING")

    @override_settings(SUSPEND_SIGNALS=False)
    def test_pulse_status_remains_unchanged_when_learn_content_uuid_is_the_same(self):
        """
        Test that the pulse status is not changed when learn_content_uuid remains the same
        """
        # Set the status to DONE
        self.pulse.status = "DONE"
        self.pulse.save()

        # Update the pulse with the same learn_content_uuid
        self.pulse.name = "Updated Name"  # Change something else
        self.pulse.save()

        # Refresh from database to get updated values
        self.pulse.refresh_from_db()

        # Assert that the status has not been changed
        self.assertEqual(self.pulse.status, "DONE")