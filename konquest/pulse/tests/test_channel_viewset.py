import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from account.models import User
from account.models.language_enum import LanguageEnum
from authentication.keeps_permissions import ADMIN, CONTENT, SUPER_ADMIN, USER
from learning_trail.models import LearningTrail, LearningTrailStep
from pulse.models import (
    Channel,
    ChannelCategory,
    ChannelContributor,
    ChannelSubscription,
    ChannelType,
    Pulse,
    PulseChannel,
)
from pulse.models.channel_type import ChannelTypeEnum


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class ChannelViewsetTestCase(TestCase):
    fixtures = ["workspace", "user"]

    def setUp(self):
        """
        Workspace: Keeps Test
        Configs:
            allow_list_public_channel = true
            allow_list_paid_channel = true
            allow_create_public_channel = true
            allow_create_paid_channel = true

        Workspace: Bayer Test
        Configs:
            allow_list_public_channel = true
            allow_list_paid_channel = true
            allow_create_public_channel = false
            allow_create_paid_channel = false

        Workspace: Mosaic Test
        Configs:
            allow_list_public_channel = false
            allow_list_paid_channel = false
            allow_create_public_channel = false
            allow_create_paid_channel = false
        """
        self.client = APIClient()

        self.user_creator_id = "489fb596-478f-40b5-b717-4931f20e4cc6"
        self.user_consumer_id = "4b5880b1-ddaf-4b4f-b72b-1aa0cf49fcf1"

        self.user_creator = User.objects.get(id=self.user_creator_id)

        self.workspace_keeps_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.workspace_bayer_id = "d27b7876-9936-4529-b394-f7df10fe5899"
        self.workspace_mosaic_id = "1ef9e947-5746-4171-b218-0f9bfd6c6a28"
        self.workspace_public_not_paid_id = "1ef9e947-5746-4171-b218-0f9bfd6c6a29"
        self.workspace_paid_not_public_id = "1ef9e947-5746-4171-b218-0f9bfd6c6a30"

        self.channel_type_public = mommy.make(ChannelType, id=ChannelTypeEnum.PUBLIC.value)
        self.channel_type_open_for_workspace = mommy.make(ChannelType, id=ChannelTypeEnum.OPEN_FOR_COMPANY.value)
        self.channel_type_close_for_workspace = mommy.make(ChannelType, id=ChannelTypeEnum.CLOSE_FOR_COMPANY.value)
        self.channel_type_paid = mommy.make(ChannelType, id=ChannelTypeEnum.PAID.value)
        self.channel_id = uuid.uuid4()

        self.channel_category = mommy.make(ChannelCategory, id=uuid.uuid4(), name="Development")
        self.channel_category_2 = mommy.make(ChannelCategory, id=uuid.uuid4(), name="Project")
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})
        self.url = reverse("channels-list")

    def test_channel_workspace_user_not_allowed_for_workspace(
        self, mock_return, mock_return_roles
    ):
        """
        User don't have access allowed for workspace in x-client header.
        """
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})
        mock_return.return_value = False

        response = self.client.get(self.url, **self.headers, format="json")
        self.assertEqual(response.status_code, 403)

    def test_channel_workspace_keeps_list(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})

        self.create_channels()

        response = self.client.get(self.url, **self.headers, format="json").json()
        results = response["results"]
        self.assertEqual(len(results), 3)
        workspace_ids = [result["workspace"] for result in results]
        channel_types_ids = [result["channel_type"]["id"] for result in results]

        self.assertIn(self.workspace_mosaic_id, workspace_ids)
        self.assertIn(ChannelTypeEnum.PUBLIC.value, channel_types_ids)

        self.assertIn(self.workspace_keeps_id, workspace_ids)
        self.assertIn(ChannelTypeEnum.PAID.value, channel_types_ids)

        self.assertIn(ChannelTypeEnum.OPEN_FOR_COMPANY.value, channel_types_ids)

    def test_user_not_list_inactivated_channel(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(
            user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id, "role": USER}
        )
        channel = self.create_channels_with_inactivated()

        response = self.client.get(self.url, **self.headers, format="json").json()
        results = response["results"]
        channel_ids = [result["id"] for result in results]

        self.assertNotIn(str(channel.id), channel_ids)

    def test_admin_list_inactivated_channel(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(
            user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id, "role": ADMIN}
        )
        self._verify_list_inactivated_channel()

    def test_manager_list_inactivated_channel(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(
            user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id, "role": CONTENT}
        )

        self._verify_list_inactivated_channel()

    def _verify_list_inactivated_channel(self):
        channel = self.create_channels_with_inactivated()

        response = self.client.get(self.url, **self.headers, format="json").json()
        results = response["results"]
        channel_ids = [result["id"] for result in results]

        self.assertIn(str(channel.id), channel_ids)

    def create_channels_with_inactivated(self):
        channel = self.create_channels()
        channel.is_active = False
        channel.save()
        return channel

    def test_channel_workspace_keeps_list_with_creator(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_creator_id, "client_id": self.workspace_keeps_id})

        self.create_channels()

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 4)

    def test_channel_workspace_bayer_list(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_bayer_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_bayer_id})

        self.create_channels()

        response = self.client.get(self.url, **self.headers, format="json").json()
        results = response["results"]
        self.assertEqual(len(results), 3)
        channel_workspaces = [result["workspace"] for result in results]
        channel_types = [result["channel_type"]["id"] for result in results]

        self.assertIn(self.workspace_mosaic_id, channel_workspaces)
        self.assertIn(ChannelTypeEnum.PUBLIC.value, channel_types)

        self.assertIn(self.workspace_bayer_id, channel_workspaces)
        self.assertIn(ChannelTypeEnum.OPEN_FOR_COMPANY.value, channel_types)

        self.assertIn(self.workspace_keeps_id, channel_workspaces)
        self.assertIn(ChannelTypeEnum.PAID.value, channel_types)

    def test_channel_workspace_mosaic_list(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_mosaic_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_mosaic_id})

        self.create_channels()

        response = self.client.get(self.url, **self.headers, format="json").json()
        results = response["results"]
        self.assertEqual(len(results), 2)
        workspace_ids = [result["workspace"] for result in results]
        channel_types = [result["channel_type"]["id"] for result in results]
        self.assertIn(self.workspace_mosaic_id, workspace_ids)
        self.assertIn(ChannelTypeEnum.PUBLIC.value, channel_types)

    def test_channel_workspace_list_public_not_paid(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_public_not_paid_id}
        self.client.force_authenticate(
            user={"sub": self.user_consumer_id, "client_id": self.workspace_public_not_paid_id, "role": ADMIN}
        )

        self.create_channels()

        response = self.client.get(self.url, **self.headers, format="json").json()
        results = response["results"]
        self.assertEqual(len(results), 2)
        channel_names = [result["name"] for result in results]
        self.assertIn("Mosaic public channel created in past", channel_names)
        self.assertIn("Keeps public channel", channel_names)

    def test_channel_workspace_list_paid_not_public(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_paid_not_public_id}
        self.client.force_authenticate(
            user={"sub": self.user_consumer_id, "client_id": self.workspace_paid_not_public_id}
        )

        self.create_channels()

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)
        self.assertEqual(response["results"][0]["name"], "Keeps paid channel")
        self.assertEqual(response["results"][0]["workspace"], self.workspace_keeps_id)
        self.assertEqual(response["results"][0]["channel_type"]["id"], ChannelTypeEnum.PAID.value)

    def test_channel_list_filter_active(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(
            user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id, "role": ADMIN}
        )
        self.create_channels()

        response = self.client.get(self.url + "?is_active=false", **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)
        self.assertEqual(response["results"][0]["workspace"], self.workspace_keeps_id)
        self.assertEqual(response["results"][0]["channel_type"]["id"], ChannelTypeEnum.PUBLIC.value)

        response = self.client.get(self.url + "?is_active=true", **self.headers, format="json").json()
        results = response["results"]
        self.assertEqual(len(results), 4)
        workspace_ids = [result["workspace"] for result in results]
        channel_types = [result["channel_type"]["id"] for result in results]

        self.assertIn(self.workspace_mosaic_id, workspace_ids)
        self.assertIn(ChannelTypeEnum.PUBLIC.value, channel_types)
        self.assertIn(self.workspace_keeps_id, workspace_ids)
        self.assertIn(ChannelTypeEnum.PAID.value, channel_types)
        self.assertIn(ChannelTypeEnum.OPEN_FOR_COMPANY.value, channel_types)

    def test_channel_list_filter_channel_category(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(
            user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id, "role": ADMIN}
        )
        self.create_channels()

        response = self.client.get(
            self.url + f"?channel_category={self.channel_category.id}", **self.headers, format="json"
        ).json()
        results = response["results"]
        self.assertEqual(len(results), 4)
        workspace_ids = [result["workspace"] for result in results]
        channel_types = [result["channel_type"]["id"] for result in results]

        self.assertIn(self.workspace_mosaic_id, workspace_ids)
        self.assertIn(ChannelTypeEnum.PUBLIC.value, channel_types)
        self.assertIn(self.workspace_keeps_id, workspace_ids)
        self.assertIn(ChannelTypeEnum.PAID.value, channel_types)
        self.assertIn(self.workspace_keeps_id, workspace_ids)

        response = self.client.get(
            self.url + f"?channel_category={self.channel_category_2.id}", **self.headers, format="json"
        ).json()
        self.assertEqual(len(response["results"]), 1)
        self.assertEqual(response["results"][0]["workspace"], self.workspace_keeps_id)
        self.assertEqual(response["results"][0]["channel_type"]["id"], ChannelTypeEnum.OPEN_FOR_COMPANY.value)
        self.assertEqual(response["results"][0]["channel_category"]["name"], self.channel_category_2.name)

    def test_filter_channel_by_language(self, mock_return, mock_return_roles):
        language = "en"
        self.create_channels()

        results = (
            self.client.get(self.url + f"?language={language}", **self.headers, format="json").json().get("results")
        )

        for result in results:
            self.assertEqual(result["language"], language)

    def test_filter_channel_by_subscribed(self, mock_return, mock_return_roles):
        self.create_channels()
        mommy.make(ChannelSubscription, channel_id=self.channel_id, user_id=self.user_consumer_id)

        results = self.client.get(self.url + f"?subscribed=true", **self.headers, format="json").json().get("results")

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["id"], str(self.channel_id))

    def test_subscription_in_channel_serializer(self, mock_return, mock_return_roles):
        channel = mommy.make(Channel, channel_type=self.channel_type_public, workspace_id=self.workspace_keeps_id)
        subscription = mommy.make(ChannelSubscription, channel=channel, user_id=self.user_consumer_id)

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(response["results"][0]["subscription"]["id"], str(subscription.id))

    def test_channel_list_search_name(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})
        self.create_channels()

        response = self.client.get(self.url + "?search=Keeps", **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 2)

    def test_channel_closed_for_workspace_super_admin_get_success(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(
            user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id, "role": ADMIN}
        )
        channel = self.create_channels()
        channel.user_creator = mommy.make(User)
        channel.channel_type=self.channel_type_close_for_workspace
        channel.save()

        response = self.client.get(reverse("channels-detail", args=[str(channel.id)]), **self.headers, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json["workspace"], self.workspace_keeps_id)
        self.assertEqual(response_json["channel_category"]["name"], self.channel_category.name)

    def test_channel_get_success(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(
            user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id, "role": ADMIN}
        )
        channel = self.create_channels()

        response = self.client.get(reverse("channels-detail", args=[str(channel.id)]), **self.headers, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json["workspace"], self.workspace_keeps_id)
        self.assertEqual(response_json["channel_type"]["id"], ChannelTypeEnum.PUBLIC.value)
        self.assertEqual(response_json["channel_category"]["name"], self.channel_category.name)

    def test_contributor_can_detail_closed_channel(self, mock_return, mock_return_roles):
        self.client.force_authenticate(
            user={"sub": self.user_creator, "client_id": self.workspace_keeps_id, "role": CONTENT}
        )
        channel = mommy.make(
            Channel, channel_type=self.channel_type_close_for_workspace, workspace_id=self.workspace_keeps_id
        )
        mommy.make(ChannelContributor, channel=channel, user=self.user_creator)

        response = self.client.get(reverse("channels-detail", args=[str(channel.id)]), **self.headers, format="json")

        self.assertEqual(response.status_code, 200)

    def test_channel_get_not_found(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})
        self.create_channels()
        response = self.client.get(reverse("channels-detail", args=[str(uuid.uuid4())]), **self.headers, format="json")
        response_json = response.json()

        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_channel_post_success(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})

        data = {
            "name": "Test New Channel",
            "description": "Test New Channel",
            "channel_type": str(self.channel_type_public.id),
            "channel_category": str(self.channel_category.id),
            "holder_image": "http://image.com",
            "workspace": self.workspace_keeps_id,
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)

    def test_channel_post_required_field(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})

        data = {
            "name": "Test New Channel",
            "description": "Test New Channel",
            "channel_category": str(self.channel_category.id),
            "holder_image": "http://image.com",
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response_json["channel_type"], ["This field is required."])
        self.assertEqual(response.status_code, 400)

    def test_channel_post_invalid_foreign_key(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})

        data = {
            "name": "Test New Channel",
            "description": "Test New Channel",
            "channel_type": uuid.uuid4(),
            "channel_category": str(self.channel_category.id),
            "holder_image": "http://image.com",
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertIsNotNone(response_json.get("channel_type"))
        self.assertEqual(response.status_code, 400)

    def test_channel_patch_success(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(
            user={"sub": self.user_creator_id, "client_id": self.workspace_keeps_id, "role": ADMIN}
        )

        channel = self.create_channels()

        data = {"name": "Test Channel", "user_creator": self.user_creator_id, "name": self.user_creator.name}

        response = self.client.patch(
            reverse("channels-detail", args=[str(channel.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()
        self.assertEqual(response_json["name"], data["name"])
        self.assertEqual(response.status_code, 200)

    def test_channel_put_success(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(
            user={"sub": self.user_creator_id, "client_id": self.workspace_keeps_id, "role": ADMIN}
        )

        channel = self.create_channels()

        data = {
            "name": "Test Change Channel Name",
            "description": "Test New Channel",
            "channel_type": str(self.channel_type_public.id),
            "channel_category": str(self.channel_category.id),
            "holder_image": "http://image.com",
            "workspace": self.workspace_keeps_id,
            "user_creator": self.user_creator_id,
            "name": self.user_creator.name,
        }

        response = self.client.put(
            reverse("channels-detail", args=[str(channel.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["name"], data["name"])
        self.assertEqual(response.status_code, 200)

    def test_channel_disabled_put_success(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(
            user={"sub": self.user_creator_id, "client_id": self.workspace_keeps_id, "role": ADMIN}
        )

        channel = self.create_channels()
        pulse = mommy.make(Pulse, id=uuid.uuid4())
        mommy.make(PulseChannel, pulse=pulse, channel=channel)

        data = {
            "name": "Test Change Channel Name",
            "description": "Test New Channel",
            "channel_type": str(self.channel_type_public.id),
            "channel_category": str(self.channel_category.id),
            "holder_image": "http://image.com",
            "workspace": self.workspace_keeps_id,
            "user_creator": self.user_creator_id,
            "name": self.user_creator.name,
            "is_active": False,
        }

        response = self.client.put(
            reverse("channels-detail", args=[str(channel.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["name"], data["name"])
        self.assertEqual(response.status_code, 200)
        self.assertFalse(response_json["is_active"])
        self.assertTrue(Pulse.objects.filter(id=pulse.id, is_active=False).exists())

    def test_channel_enabled_put_success(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(
            user={"sub": self.user_creator_id, "client_id": self.workspace_keeps_id, "role": ADMIN}
        )

        channel = self.create_channels()
        pulse = mommy.make(Pulse, id=uuid.uuid4(), is_active=False)
        mommy.make(PulseChannel, pulse=pulse, channel=channel)

        data = {
            "name": "Test Change Channel Name",
            "description": "Test New Channel",
            "channel_type": str(self.channel_type_public.id),
            "channel_category": str(self.channel_category.id),
            "holder_image": "http://image.com",
            "workspace": self.workspace_keeps_id,
            "user_creator": self.user_creator_id,
            "name": self.user_creator.name,
            "is_active": True,
        }

        response = self.client.put(
            reverse("channels-detail", args=[str(channel.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["name"], data["name"])
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response_json["is_active"])
        self.assertTrue(Pulse.objects.filter(id=pulse.id, is_active=True).exists())

    def test_channel_put_not_found(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})

        data = {
            "name": "Test Change Channel Name",
            "description": "Test New Channel",
            "channel_type": str(self.channel_type_public.id),
            "channel_category": str(self.channel_category.id),
            "holder_image": "http://image.com",
            "workspace": self.workspace_keeps_id,
        }

        response = self.client.put(
            reverse("channels-detail", args=[str(uuid.uuid4())]), **self.headers, data=data, format="json"
        )
        response_json = response.json()
        self.assertEqual(response_json["detail"], "Not found.")
        self.assertEqual(response.status_code, 404)

    def test_admin_can_update_channel(
        self, mock_return, mock_return_roles
    ):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(
            user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id, "role": ADMIN}
        )

        channel = self.create_channels()

        data = {
            "name": "Test Change Channel Name",
            "description": "Test New Channel",
            "channel_type": str(self.channel_type_public.id),
            "channel_category": str(self.channel_category.id),
            "holder_image": "http://image.com",
            "workspace": self.workspace_keeps_id,
            "user_creator": self.user_creator_id,
            "name": self.user_creator.name,
        }

        response = self.client.put(
            reverse("channels-detail", args=[str(channel.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response.status_code, 200)

    def test_channel_delete_success(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(
            user={"sub": self.user_creator_id, "client_id": self.workspace_keeps_id, "role": ADMIN}
        )

        channel = self.create_channels()

        response = self.client.delete(reverse("channels-detail", args=[str(channel.id)]), **self.headers, format="json")
        self.assertEqual(response.status_code, 204)

    def test_channel_delete_not_found(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id})

        response = self.client.delete(
            reverse("channels-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "channel_not_found")
        self.assertEqual(response.status_code, 404)

    def create_channels(self):
        channel_template = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Keeps public channel",
            channel_type=self.channel_type_public,
            channel_category=self.channel_category,
            workspace_id=self.workspace_keeps_id,
            user_creator_id=self.user_creator_id,
            is_active=False,
            language="en",
        )

        mommy.make(
            Channel,
            id=self.channel_id,
            name="Keeps open for workspace channel",
            channel_type=self.channel_type_open_for_workspace,
            channel_category=self.channel_category_2,
            workspace_id=self.workspace_keeps_id,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )

        mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Keeps close for workspace channel",
            channel_type=self.channel_type_close_for_workspace,
            channel_category=self.channel_category,
            workspace_id=self.workspace_keeps_id,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )

        mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Keeps paid channel",
            channel_type=self.channel_type_paid,
            channel_category=self.channel_category,
            workspace_id=self.workspace_keeps_id,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )

        mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Bayer open for workspace channel",
            channel_type=self.channel_type_open_for_workspace,
            channel_category=self.channel_category,
            workspace_id=self.workspace_bayer_id,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )

        mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Bayer close for workspace channel",
            channel_type=self.channel_type_close_for_workspace,
            channel_category=self.channel_category,
            workspace_id=self.workspace_bayer_id,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )

        mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Mosaic open for workspace channel",
            channel_type=self.channel_type_open_for_workspace,
            channel_category=self.channel_category,
            workspace_id=self.workspace_mosaic_id,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )

        mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Mosaic close for workspace channel",
            channel_type=self.channel_type_close_for_workspace,
            channel_category=self.channel_category,
            workspace_id=self.workspace_mosaic_id,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )

        mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Mosaic public channel created in past",
            channel_type=self.channel_type_public,
            channel_category=self.channel_category,
            workspace_id=self.workspace_mosaic_id,
            user_creator_id=self.user_creator_id,
            is_active=True,
        )

        return channel_template

    def test_channel_delete_success_as_super_user(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(
            user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id, "role": SUPER_ADMIN}
        )
        channel = mommy.make(Channel, workspace_id=self.workspace_keeps_id)
        pulse = mommy.make(Pulse)
        pulse_channel = mommy.make(PulseChannel, channel=channel, pulse=pulse)

        response = self.client.delete(reverse("channels-detail", args=[str(channel.id)]), **self.headers, format="json")
        self.assertEqual(response.status_code, 204)

        channel.refresh_from_db()
        pulse_channel.refresh_from_db()
        pulse.refresh_from_db()
        self.assertTrue(channel.deleted)
        self.assertTrue(pulse_channel.deleted)
        self.assertTrue(pulse.deleted)

    def test_channel_delete_as_super_user_from_another_workspace(
        self, mock_return, mock_return_roles
    ):
        self.headers = {"HTTP_X_CLIENT": self.workspace_bayer_id}
        self.client.force_authenticate(
            user={"sub": self.user_consumer_id, "client_id": self.workspace_bayer_id, "role": SUPER_ADMIN}
        )

        channel = self.create_channels()
        response = self.client.delete(reverse("channels-detail", args=[str(channel.id)]), **self.headers, format="json")
        self.assertEqual(response.status_code, 403)

    def test_channel_delete_success_and_learning_trail_step_removal(
        self, mock_return, mock_return_roles
    ):
        self.headers = {"HTTP_X_CLIENT": self.workspace_keeps_id}
        self.client.force_authenticate(
            user={"sub": self.user_consumer_id, "client_id": self.workspace_keeps_id, "role": SUPER_ADMIN}
        )

        channel = self.create_channels()

        pulse = mommy.make(Pulse, id=uuid.uuid4())
        mommy.make(PulseChannel, channel=channel, pulse=pulse)
        learning_trail = mommy.make(LearningTrail, id=uuid.uuid4())
        mommy.make(LearningTrailStep, id=uuid.uuid4(), learning_trail=learning_trail, pulse=pulse)

        response = self.client.delete(reverse("channels-detail", args=[str(channel.id)]), **self.headers, format="json")
        self.assertEqual(response.status_code, 204)
        Learning_trail_step_count = LearningTrailStep.objects.filter(pulse=pulse, deleted=False).count()
        self.assertEqual(Learning_trail_step_count, 0)

    def test_channel_language_data(self, mock_return, mock_return_roles):
        data = [{"i18n": lang[0], "name": lang[1]} for lang in LanguageEnum.choices()]
        response = self.client.get(reverse("channel-languages-list"), **self.headers, format="json")
        response_data = response.json()

        self.assertEqual(response.status_code, 201)
        self.assertEqual(data, response_data)
