from datetime import datetime

import mock
from conftest import get_injector
from django.test import TestCase
from model_mommy import mommy

from account.models import User, Workspace
from learning_trail.models import LearningTrail, LearningTrailStep
from pulse.models import Pulse
from pulse.services.pulse_recommendation import PulseRecommendationService
from user_activity.models import LearnContentActivity, LearningTrailEnrollment

MOCK_GET_ALLOWED_PULSES = "pulse.services.channel_service.ChannelService.get_allowed_pulses"


class PulsePriorityServiceTestCase(TestCase):
    def setUp(self):
        self.workspace = mommy.make(Workspace)
        self.user = mommy.make(User, email="<EMAIL>")

        self.pulse_1 = mommy.make(Pulse, language="pt-BR")
        self.pulse_2 = mommy.make(Pulse)
        self.__service = get_injector().get(PulseRecommendationService)

    @mock.patch(MOCK_GET_ALLOWED_PULSES)
    def test_user_pulses_priority(self, mock_get_allowed_pulse):
        lt_pulse = mommy.make(Pulse)

        mock_get_allowed_pulse.return_value = Pulse.objects.filter()

        learning_trail = mommy.make(LearningTrail)
        mommy.make(LearningTrailStep, learning_trail=learning_trail, pulse=lt_pulse)
        mommy.make(LearningTrailEnrollment, user=self.user, learning_trail=learning_trail, workspace=self.workspace)

        # user pulse consume
        mommy.make(LearnContentActivity, user=self.user, pulse=self.pulse_2, time_start=datetime.today())

        priority_pulses = self.__service.user_pulses_priority(
            user_id=self.user.id, workspace_id=self.workspace.id, user_language="pt-BR"
        )

        self.assertEqual(lt_pulse, priority_pulses[0])
        self.assertIn(self.pulse_1, priority_pulses)
