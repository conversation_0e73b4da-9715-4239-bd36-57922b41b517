from authentication.keeps_permissions import (
    ALL_PERMISSIONS,
    KeepsPlatformAdminPermission,
    KeepsSuperAdminPermission,
)
from django_filters.rest_framework import Django<PERSON>ilterBackend
from injector import Provider, inject
from pulse.models import ChannelPaidConfig
from pulse.serializers.channel_paid_config_serializer import ChannelPaidConfigSerializer
from pulse.services.channel_service import ChannelService
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter
from utils.utils import Utils, swagger_safe_queryset


class ChannelPaidConfigViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    permission_classes = (KeepsPlatformAdminPermission | KeepsSuperAdminPermission,)

    @inject
    def __init__(self, service: ChannelService = Provider[ChannelService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    @swagger_safe_queryset(ChannelPaidConfig)
    def get_queryset(self):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        token = self.request.META.get("HTTP_AUTHORIZATION")
        user_language = Utils.get_user_language(token)
        channels = self._service.get_allowed_workspace_channels(workspace_id=workspace_id, user_language=user_language)
        return ChannelPaidConfig.objects.filter(channel__in=channels)

    @staticmethod
    def get_serializer_class():
        return ChannelPaidConfigSerializer


class ChannelPaidConfigsViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    permission_classes = ALL_PERMISSIONS

    @inject
    def __init__(self, service: ChannelService = Provider[ChannelService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    @swagger_safe_queryset(ChannelPaidConfig)
    def get_queryset(self):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        token = self.request.META.get("HTTP_AUTHORIZATION")
        user_language = Utils.get_user_language(token)
        channels = self._service.get_allowed_workspace_channels(workspace_id=workspace_id, user_language=user_language)
        return ChannelPaidConfig.objects.filter(channel__in=channels)

    def get_serializer_class(self):
        return ChannelPaidConfigSerializer
