# -*- coding: utf-8 -*-

from account.models import Workspace
from authentication.keeps_permissions import IsAuthenticatedWithoutXClient
from django.db.models import Q
from django_filters.rest_framework import DjangoFilterBackend
from pulse.models import ChannelCategory
from pulse.serializers.channel_category_serializer import ChannelCategorySerializer
from rest_framework import viewsets
from rest_framework.filters import <PERSON>ing<PERSON>ilt<PERSON>, SearchFilter
from utils.utils import swagger_safe_queryset


class ChannelCategoryViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    permission_classes = (IsAuthenticatedWithoutXClient,)

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    filterset_fields = ("name",)
    search_fields = ("name",)
    ordering = ("name",)

    @swagger_safe_queryset(ChannelCategory)
    def get_queryset(self):
        workspace_id = self.request.user.get("client_id")
        workspace = Workspace.objects.get(id=workspace_id)
        filters = Q(workspace_id=workspace_id)
        if workspace.allow_list_public_categories:
            filters = filters | Q(is_public=True)
        return ChannelCategory.objects.filter(filters)

    @staticmethod
    def get_serializer_class():
        return ChannelCategorySerializer
