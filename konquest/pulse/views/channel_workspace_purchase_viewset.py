# -*- coding: utf-8 -*-

from authentication.keeps_permissions import ALL_PERMISSIONS
from django_filters.rest_framework import DjangoFilterBackend
from pulse.models import ChannelWorkspacePurchase
from pulse.serializers.channel_workspace_purchase_serializer import ChannelWorkspacePurchaseSerializer
from rest_framework import viewsets
from rest_framework.filters import <PERSON><PERSON><PERSON><PERSON><PERSON>, SearchFilter
from utils.utils import swagger_safe_queryset


class ChannelWorkspacePurchaseViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    search_fields = ("channel", "created_date")
    ordering_fields = (
        "created_date",
        "channel",
    )
    ordering = ("created_date",)

    permission_classes = ALL_PERMISSIONS

    @swagger_safe_queryset(ChannelWorkspacePurchase)
    def get_queryset(self):
        workspace_uuid = self.request.user.get("client_id") if self.request.user else None
        return ChannelWorkspacePurchase.objects.filter(workspace_id=workspace_uuid)

    def get_serializer_class(self):
        return ChannelWorkspacePurchaseSerializer
