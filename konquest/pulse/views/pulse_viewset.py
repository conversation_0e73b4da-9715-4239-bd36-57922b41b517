from authentication import keeps_permissions
from authentication.keeps_permissions import KeepsBasePermission
from django_filters.rest_framework import DjangoFilter<PERSON><PERSON>end
from injector import Provider, inject
from pulse.exceptions import InvalidContentTypeChangeException
from pulse.filters.pulse_filters import PulseFilter
from pulse.models import Pulse
from pulse.selectors.channel_selector import ChannelSelector
from pulse.serializers.pulse_serializer import PulseListSerializer, PulseSerializer
from pulse.services.pulse_service import PulseService
from pulse.views.pulse_filters import PulseTypeFilterBackend, PulseUserCreatorFilterBackend
from rest_framework import status, viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
from rest_framework.response import Response
from utils.utils import load_request_user, swagger_safe_queryset


class PulseViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (
        DjangoFilterBackend,
        SearchFilter,
        OrderingFilter,
        PulseUser<PERSON>reator<PERSON>ilterBackend,
        PulseTypeFilterBackend,
    )
    filterset_class = PulseFilter
    search_fields = (
        "name",
        "description",
        "language",
    )
    ordering_fields = (
        "name",
        "created_date",
        "language",
    )

    permission_classes = (
        keeps_permissions.KeepsPlatformAdminPermission
        | keeps_permissions.KeepsSuperAdminPermission
        | keeps_permissions.KeepsCuratorPermission
        | keeps_permissions.KeepsUserPermission
        | keeps_permissions.KeepsInstructorPermission,
    )

    @inject
    def __init__(
        self,
        pulse_service: PulseService = Provider[PulseService],
        selector: ChannelSelector = Provider[ChannelSelector],
        **kwargs,
    ):
        super().__init__(**kwargs)
        self._pulse_service = pulse_service
        self._selector = selector
        self._kp_permission = KeepsBasePermission()

    @swagger_safe_queryset(Pulse)
    def get_queryset(self):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        token = self.request.META.get("HTTP_AUTHORIZATION")
        user_id = self.request.user.get("sub")

        role = self._kp_permission.get_priority_user_role_by_token(token, workspace_id)
        return self._selector.get_allowed_pulses(user_id, workspace_id, role)

    def get_serializer_class(self):
        return PulseListSerializer if self.request.method == "GET" else PulseSerializer

    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        response = super().create(request, *args, **kwargs)
        return response

    def _validate_content_type_change(self, instance, serializer):
        new_learn_content_uuid = serializer.validated_data.get("learn_content_uuid")
        if new_learn_content_uuid and not self._pulse_service.should_change_content_uuid(
            instance.learn_content_uuid, new_learn_content_uuid
        ):
            raise InvalidContentTypeChangeException()

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=False)
        serializer.is_valid(raise_exception=True)

        self._validate_content_type_change(instance, serializer)
        self.perform_update(serializer)
        return Response(serializer.data)

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        self._validate_content_type_change(instance, serializer)
        self.perform_update(serializer)
        return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):
        user = load_request_user(request)
        pulse_id = self.kwargs["pk"]
        self._pulse_service.destroy(pulse_id, user)
        return Response(status=status.HTTP_204_NO_CONTENT)
