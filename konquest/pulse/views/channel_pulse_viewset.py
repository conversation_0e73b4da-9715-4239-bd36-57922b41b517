from authentication import keeps_permissions
from authentication.keeps_permissions import ADMIN, ALL_PERMISSIONS
from constants import CAN_CHANGE_CHANNEL, HTTP_AUTHORIZATION
from custom.keeps_exception_handler import ChannelEditNotAuthorizedException
from django.db import IntegrityError
from django_filters.rest_framework import DjangoFilter<PERSON>ackend
from injector import Provider, inject
from pulse.models import Channel, Pulse, PulseChannel
from pulse.serializers.channel_pulse_serializer import Channel<PERSON>ulseListSerializer, ChannelPulseSerializer
from pulse.services.channel_service import ChannelService
from rest_framework import status, viewsets
from rest_framework.filters import OrderingFilter, SearchFilter
from rest_framework.generics import get_object_or_404
from rest_framework.response import Response
from rules import test_rule
from utils.utils import Utils, load_request_user, swagger_safe_queryset


# Todo: Refatorar módulo de Canais https://app.clickup.com/t/3004080/DEV-27723
class ChannelPulseViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = ("pulse", "channel__is_active")
    search_fields = ("pulse__name",)
    ordering_fields = ("pulse__created_date", "channel__created_date", "pulse__updated_date")
    ordering = ("-pulse__updated_date",)

    permission_classes = ALL_PERMISSIONS

    @inject
    def __init__(self, service: ChannelService = Provider[ChannelService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    def _get_request_user_data(self, request):
        user = load_request_user(request)
        user_id = str(user.id)
        workspace_id = request.user.get("client_id") if request.user else None
        token = request.META.get(HTTP_AUTHORIZATION)
        return user, user_id, workspace_id, token

    def _get_allowed_channels(self, workspace_id, user_id, token):
        roles = keeps_permissions.KeepsBasePermission().get_user_roles(token, workspace_id)
        if ADMIN in roles:
            user_language = Utils.get_user_language(token)
            return self._service.get_allowed_workspace_channels(workspace_id, user_language)
        return self._service.get_allowed_channels(workspace_id, user_id)

    @swagger_safe_queryset(PulseChannel)
    def get_queryset(self):
        _, user_id, workspace_id, token = self._get_request_user_data(self.request)
        channels = self._get_allowed_channels(workspace_id, user_id, token)
        return PulseChannel.objects.filter(channel__in=channels, channel_id=self.kwargs.get("pk"))

    def get_serializer_class(self):
        return ChannelPulseListSerializer if self.request.method == "GET" else ChannelPulseSerializer

    def create(self, request, *args, **kwargs):
        user, user_id, workspace_id, _ = self._get_request_user_data(request)
        channel = get_object_or_404(
            self._service.get_allowed_channels(workspace_id, user_id),
            id=self.kwargs["pk"]
        )
        pulse = get_object_or_404(Pulse, id=request.data["pulse_id"])

        if not test_rule(CAN_CHANGE_CHANNEL, user, channel):
            raise ChannelEditNotAuthorizedException()

        pulse.language = channel.language
        pulse.save()

        try:
            pulse_channel = PulseChannel.objects.create(channel=channel, pulse=pulse)
            return Response(ChannelPulseSerializer(pulse_channel).data, status=status.HTTP_201_CREATED)
        except IntegrityError as e:
            return Response(str(e), status=status.HTTP_409_CONFLICT)

    def destroy(self, request, *args, **kwargs):
        user, _, _, _ = self._get_request_user_data(request)
        channel = get_object_or_404(Channel, id=self.kwargs["pk"])
        pulse = get_object_or_404(Pulse, id=request.data["pulse_id"])

        if not test_rule(CAN_CHANGE_CHANNEL, user, channel):
            raise ChannelEditNotAuthorizedException()

        PulseChannel.objects.filter(channel=channel, pulse=pulse).delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
