import json

from authentication import keeps_permissions
from authentication.keeps_permissions import ADMI<PERSON>, MANAGER_ROLES, SUPER_ADMIN
from django_filters.rest_framework import DjangoFilterBackend
from injector import Provider, inject
from pulse.filters.channel_filter import ChannelFilter
from pulse.models import Channel
from pulse.serializers.channel_filters import ChannelCategoryFilterBackend, ChannelLanguageFilterBackend
from pulse.serializers.channel_serializer import ChannelListSerializer, ChannelSerializer
from pulse.services.channel_service import ChannelService
from rest_framework import status, viewsets
from rest_framework.filters import OrderingFilter, SearchFilter
from rest_framework.generics import get_object_or_404
from rest_framework.response import Response
from utils.utils import Utils, action_permission, load_request_user, swagger_safe_queryset


class ChannelViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (
        DjangoFilterBackend,
        SearchFilter,
        OrderingFilter,
        ChannelCategoryFilterBackend,
        ChannelLanguageFilterBackend,
    )
    filterset_class = ChannelFilter
    search_fields = ("name", "description")
    ordering_fields = (
        "name",
        "created_date",
    )

    permission_classes = (
        keeps_permissions.KeepsPlatformAdminPermission
        | keeps_permissions.KeepsSuperAdminPermission
        | keeps_permissions.KeepsCuratorPermission
        | keeps_permissions.KeepsUserPermission
        | keeps_permissions.KeepsInstructorPermission,
    )

    @inject
    def __init__(self, channel_service: ChannelService = Provider[ChannelService], **kwargs):
        super().__init__(**kwargs)
        self._channel_service = channel_service

    @swagger_safe_queryset(Channel)
    def get_queryset(self):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        token = self.request.META.get("HTTP_AUTHORIZATION")
        user_language = Utils.get_user_language(token)
        user_id = self.request.user.get("sub")
        role = self.request.user.get("role")
        managed = json.loads(self.request.GET.get("managed", "false").lower())

        if managed:
            return self._channel_service.get_allowed_channels_user_managed(workspace_id, user_id, user_language)
        if role == ADMIN:
            return self._channel_service.get_allowed_workspace_channels(workspace_id, user_language)
        if role == SUPER_ADMIN:
            return self._channel_service.get_all_channels_by_workspace(workspace_id, user_language)
        if role in MANAGER_ROLES:
            return self._channel_service.get_allowed_channels(workspace_id, user_id, user_language)

        return self._channel_service.get_allowed_channels_for_user(workspace_id, user_id, user_language)

    def get_serializer_class(
        self,
    ):
        return ChannelSerializer if self.request.method in ["POST", "PUT", "PATCH"] else ChannelListSerializer

    @action_permission(keeps_permissions.MANAGED_PERMISSIONS)
    def create(self, request, *args, **kwargs):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        request.data["user_creator"] = request.user["sub"]
        request.data["workspace"] = workspace_id
        serializer = self.get_serializer_class()(data=request.data)
        serializer.is_valid(raise_exception=True)

        self.perform_create(serializer)

        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    def update(self, request, *args, **kwargs):
        if isinstance(request.data.get("user_creator"), dict):
            request.data["user_creator"] = request.data["user_creator"].get("id")
        channel_id = self.kwargs.get("pk")
        user = load_request_user(request)
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        if self.request.user.get("role") == SUPER_ADMIN:
            channel = get_object_or_404(
                self._channel_service.get_all_channels_by_workspace(workspace_id), id=channel_id
            )
        else:
            channel = get_object_or_404(
                self._channel_service.get_allowed_channels(workspace_id, user.id), id=channel_id
            )
        serializer = self.get_serializer_class()(channel, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        channel = self._channel_service.update_channel(channel.id, serializer.validated_data, user)
        return Response(self.get_serializer_class()(channel).data)

    def destroy(self, request, *args, **kwargs):
        user = load_request_user(request)
        channel_id = self.kwargs["pk"]
        self._channel_service.destroy(channel_id, user)
        return Response(status=status.HTTP_204_NO_CONTENT)
