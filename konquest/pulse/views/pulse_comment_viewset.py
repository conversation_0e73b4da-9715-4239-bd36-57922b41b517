from authentication.keeps_permissions import (
    KeepsCuratorPermission,
    KeepsPlatformAdminPermission,
    KeepsSuperAdminPermission,
    KeepsUserPermission,
)
from django_filters.rest_framework import DjangoFilterBackend
from injector import Provider, inject
from pulse.models import PulseComment
from pulse.serializers.pulse_comment_serializer import Pulse<PERSON>ommentListSerializer, PulseCommentSerializer
from pulse.services.channel_service import ChannelService
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
from utils.utils import swagger_safe_queryset


class PulseCommentViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    permission_classes = (
        KeepsPlatformAdminPermission
        | KeepsSuperAdminPermission
        | KeepsCuratorPermission
        | KeepsUserPermission,
    )

    @inject
    def __init__(self, service: ChannelService = Provider[ChannelService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    @swagger_safe_queryset(PulseComment)
    def get_queryset(self):
        user_id = self.request.user.get("sub") if self.request.user else None
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        pulses = self._service.get_allowed_pulses(workspace_id=workspace_id, user_id=user_id)
        return PulseComment.objects.filter(pulse__in=pulses)

    @staticmethod
    def get_serializer_class():
        return PulseCommentSerializer


class PulseCommentsViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    permission_classes = (
        KeepsPlatformAdminPermission
        | KeepsSuperAdminPermission
        | KeepsCuratorPermission
        | KeepsUserPermission,
    )

    @inject
    def __init__(self, service: ChannelService = Provider[ChannelService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    @swagger_safe_queryset(PulseComment)
    def get_queryset(self):
        user_id = self.request.user.get("sub") if self.request.user else None
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        pulses = self._service.get_allowed_pulses(
            workspace_id=workspace_id, user_id=user_id, id=self.kwargs.get("pulse_uuid")
        )
        return PulseComment.objects.filter(pulse__in=pulses)

    def get_serializer_class(self):
        return PulseCommentListSerializer if self.request.method == "GET" else PulseCommentSerializer
