from authentication.keeps_permissions import ALL_PERMISSIONS
from django_filters.rest_framework import DjangoFilterB<PERSON>end
from injector import Provider, inject
from pulse.models import PulseBookmark
from pulse.serializers.pulse_bookmark_serializer import PulseBookmarkListSerializer, PulseBookmarkSerializer
from pulse.services.channel_service import ChannelService
from pulse.views.pulse_filters import PulseTypeBookmarkFilterBackend
from rest_framework import viewsets
from rest_framework.filters import Order<PERSON><PERSON><PERSON><PERSON>, SearchFilter
from utils.utils import swagger_safe_queryset


class PulseBookmarkViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter, PulseTypeBookmarkFilterBackend)
    filterset_fields = ("user", "pulse", "pulse__name", "pulse__description", "pulse__pulse_type")
    search_fields = ("pulse__name", "pulse__description", "pulse__pulse_type__name")
    ordering_fields = (
        "user",
        "pulse",
    )
    ordering = ("created_date",)

    permission_classes = ALL_PERMISSIONS

    @inject
    def __init__(self, service: ChannelService = Provider[ChannelService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    @swagger_safe_queryset(PulseBookmark)
    def get_queryset(self):
        user_id = self.request.user.get("sub") if self.request.user else None
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        pulses = self._service.get_allowed_pulses(workspace_id=workspace_id, user_id=user_id)
        return PulseBookmark.objects.filter(pulse__in=pulses, user_id=user_id)

    def get_serializer_class(self):
        return PulseBookmarkListSerializer if self.request.method == "GET" else PulseBookmarkSerializer
