from pulse.models import Channel, ChannelSubscription
from pulse.serializers.my_channel_serializer import MyChannelSerializer
from rest_framework.response import Response
from rest_framework.views import APIView


class MyChannelViewSet(APIView):
    """
    A viewset that provides the standard actions
    """

    def get(self, request):
        user = request.user.get("sub") if request.user else None
        workspace = self.request.user.get("client_id")

        channels_subscription = ChannelSubscription.objects.filter(user_id=user).values_list("channel_id")

        data = {
            "owner": Channel.objects.filter(user_creator_id=user, workspace_id=workspace),
            "subscriptions": Channel.objects.filter(id__in=channels_subscription, workspace_id=workspace),
            "contributor": {},
        }

        my_channels_serializer = MyChannelSerializer(instance=data)

        return Response(my_channels_serializer.data)
