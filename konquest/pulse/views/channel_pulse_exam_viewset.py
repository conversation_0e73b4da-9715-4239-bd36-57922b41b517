from authentication import keeps_permissions
from injector import Provider, inject
from learn_content.services.exam_service import ExamService
from pulse.serializers.pulse_serializer import PulseExamSerializer, PulseSerializer
from pulse.services.channel_service import ChannelService
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.status import HTTP_201_CREATED
from utils.utils import load_request_user


class ChannelPulseExamViewSet(viewsets.ViewSet):
    permission_classes = keeps_permissions.MANAGED_PERMISSIONS

    @inject
    def __init__(
        self,
        channel_service: ChannelService = Provider[ChannelService],
        exam_service: ExamService = Provider[ExamService],
        **kwargs
    ):
        super().__init__(**kwargs)
        self._exam_service = exam_service
        self._channel_service = channel_service

    def create(self, request, *args, **kwargs):
        user = load_request_user(request)
        channel_id = self.kwargs.get("pk")
        self._channel_service.check_user_permission_to_modify_channel(channel_id, user)

        serializer = PulseExamSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        exam_data = data.pop("exam")
        data["user_creator_id"] = user.id
        data["pulse_type_id"] = "7a41a8e0-ee37-4d0b-ad4f-35bada67134d"

        pulse = self._exam_service.save_pulse_exam(channel_id, data, exam_data, user.id)

        return Response(data=PulseSerializer(pulse).data, status=HTTP_201_CREATED)
