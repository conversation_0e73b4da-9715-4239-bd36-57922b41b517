from authentication.keeps_permissions import ALL_PERMISSIONS
from django.db.models import Prefetch
from django_filters.rest_framework import DjangoFilterBackend
from injector import Provider, inject
from pulse.models import Pulse, PulseChannel
from pulse.selectors.channel_selector import ChannelSelector
from pulse.serializers.pulse_serializer import PulseListOptimizedSerializer
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON><PERSON><PERSON>, SearchFilter
from utils.utils import load_request_user, swagger_safe_queryset


class PulseRecommendationViewSet(viewsets.ReadOnlyModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    permission_classes = ALL_PERMISSIONS

    @inject
    def __init__(self, selector: ChannelSelector = Provider[ChannelSelector], **kwargs):
        super().__init__(**kwargs)
        self._selector = selector

    @swagger_safe_queryset(Pulse)
    def get_queryset(self):
        user_id = self.request.user.get("sub") if self.request.user else None
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        user = load_request_user(self.request)
        pulses = self._selector.get_allowed_pulses(user_id, workspace_id, user.role)

        query = (
            Pulse.objects.filter(id__in=pulses)
            .order_by("?")
            .select_related("user_creator", "pulse_type")
            .extra(
                select={
                    "rating_avg": """
                        SELECT AVG(pr.rating)
                        FROM pulse_rating pr
                        WHERE pr.pulse_id = pulse.id
                    """,
                    "rating_count": """
                        SELECT COUNT(*)
                        FROM pulse_rating pr
                        WHERE pr.pulse_id = pulse.id
                    """,
                    "tags": """
                        SELECT ARRAY(
                            SELECT name
                            FROM pulse_tag pt
                            WHERE pt.pulse_id = pulse.id
                        ) as tags
                    """,
                    "bookmark_id": """
                        SELECT pb.id
                        FROM pulse_bookmark pb
                        WHERE pb.pulse_id = pulse.id
                        LIMIT 1
                    """,
                }
            )
        )

        prefetch_channels = Prefetch("pulsechannel_set", queryset=PulseChannel.objects.select_related("channel"))
        query = query.prefetch_related(prefetch_channels)

        return query

    def get_serializer_class(self):
        return PulseListOptimizedSerializer
