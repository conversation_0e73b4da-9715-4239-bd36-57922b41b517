from account.models.language_enum import LanguageEnum
from authentication.keeps_permissions import IsAuthenticatedWithoutXClient
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
from rest_framework.response import Response


class ChannelLanguageViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    permission_classes = (IsAuthenticatedWithoutXClient,)
    queryset = LanguageEnum

    def list(self, request, *args, **kwargs):
        data = [{"i18n": lang[0], "name": lang[1]} for lang in LanguageEnum.choices()]
        return Response(data, status=201)
