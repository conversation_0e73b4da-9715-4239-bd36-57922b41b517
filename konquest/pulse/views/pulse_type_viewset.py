# -*- coding: utf-8 -*-

from authentication.keeps_permissions import IsAuthenticatedWithoutXClient
from django_filters.rest_framework import DjangoFilterBackend
from pulse.models import PulseType
from pulse.serializers.pulse_type_serializer import PulseTypeSerializer
from rest_framework import viewsets
from rest_framework.filters import OrderingFilter, SearchFilter


class PulseTypeViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    permission_classes = (IsAuthenticatedWithoutXClient,)

    def get_queryset(self):
        return PulseType.objects.all()

    @staticmethod
    def get_serializer_class():
        return PulseTypeSerializer
