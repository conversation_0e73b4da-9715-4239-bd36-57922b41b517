from account.models import User
from authentication.keeps_permissions import (
    KeepsBasePermission,
    KeepsCuratorPermission,
    KeepsPlatformAdminPermission,
    KeepsSuperAdminPermission,
    KeepsUserPermission,
)
from django_filters.rest_framework import DjangoFilterBackend
from injector import Provider, inject
from pulse.models import Channel
from pulse.models.channel_subscription import ChannelSubscription
from pulse.serializers.channel_filters import ChannelCategoryBookmarkFilterBackend
from pulse.serializers.channel_subscription_serializer import (
    ChannelIDSubscriptionSerializer,
    ChannelSubscriptionListSerializer,
    ChannelSubscriptionSerializer,
)
from pulse.services.channel_service import ChannelService
from rest_framework import status, viewsets
from rest_framework.filters import OrderingFilter, SearchFilter
from rest_framework.generics import get_object_or_404
from rest_framework.response import Response
from utils.utils import Utils, swagger_safe_queryset


class ChannelSubscriptionViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, ChannelCategoryBookmarkFilterBackend)
    filterset_fields = ("channel", "user", "channel__user_creator", "channel__is_active")
    search_fields = ("channel__name", "user__name")
    ordering_fields = (
        "created_date",
        "channel",
    )
    ordering = ("created_date",)

    permission_classes = (
        KeepsPlatformAdminPermission
        | KeepsSuperAdminPermission
        | KeepsCuratorPermission
        | KeepsUserPermission,
    )

    @inject
    def __init__(self, service: ChannelService = Provider[ChannelService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    @swagger_safe_queryset(ChannelSubscription)
    def get_queryset(self):
        workspace_id = self.request.user.get("client_id")
        token = self.request.META.get("HTTP_AUTHORIZATION")
        user_id = self.request.user.get("sub")
        roles = KeepsBasePermission().get_user_roles(token, workspace_id)
        user_language = Utils.get_user_language(token)

        if "admin" in roles:
            channels = self._service.get_allowed_workspace_channels(workspace_id, token)
            return ChannelSubscription.objects.filter(channel__in=channels)

        channels = self._service.get_allowed_channels(workspace_id, user_id, user_language)
        return ChannelSubscription.objects.filter(channel__in=channels)

    def get_serializer_class(self):
        return ChannelSubscriptionListSerializer if self.request.method == "GET" else ChannelSubscriptionSerializer


class ChannelIdSubscriptionViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = ("channel", "user", "channel__user_creator", "channel__is_active")
    search_fields = ("channel__name", "user__name")
    ordering_fields = (
        "created_date",
        "channel",
    )
    ordering = ("created_date",)

    permission_classes = (
        KeepsPlatformAdminPermission
        | KeepsSuperAdminPermission
        | KeepsCuratorPermission
        | KeepsUserPermission,
    )

    @inject
    def __init__(self, service: ChannelService = Provider[ChannelService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    @swagger_safe_queryset(ChannelSubscription)
    def get_queryset(self):
        channel_id = str(self.kwargs["channel_uuid"])
        workspace_id = self.request.user.get("client_id")
        user_id = self.request.user.get("sub")
        channels = self._service.get_allowed_channels(workspace_id=workspace_id, user_id=user_id)
        return ChannelSubscription.objects.filter(channel__in=channels, channel_id=channel_id)

    def get_serializer_class(self):
        return ChannelIDSubscriptionSerializer if self.request.method == "GET" else ChannelSubscriptionSerializer

    def create(self, request, *args, **kwargs):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        user_id = request.data["user"] if "user" in request.data else self.request.user.get("sub")
        channel_id = self.kwargs.get("channel_uuid")

        get_object_or_404(self._service.get_allowed_channels(workspace_id, user_id), id=channel_id)
        instance = ChannelSubscription.objects.filter(user=user_id, channel=channel_id).first()

        if instance:
            response = {"non_field_errors": ["The fields user, channel must make a unique set."]}
            return Response(response, status=status.HTTP_409_CONFLICT)

        user = User.objects.filter(id=user_id).first()
        channel = Channel.objects.filter(id=channel_id).first()
        instance = ChannelSubscription()
        instance.user = user
        instance.channel = channel
        instance.save()
        serializer = self.get_serializer(instance=instance)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def destroy(self, request, *args, **kwargs):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        user_id = request.data["user"] if "user" in request.data else self.request.user.get("sub")
        channel_id = self.kwargs.get("channel_uuid")

        get_object_or_404(self._service.get_allowed_channels(workspace_id, user_id), id=channel_id)
        instance = ChannelSubscription.objects.filter(user=user_id, channel=str(channel_id)).first()

        if not instance:
            return Response(status=status.HTTP_404_NOT_FOUND)

        instance.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
