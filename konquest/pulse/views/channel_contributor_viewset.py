from authentication.keeps_permissions import (
    KeepsCuratorPermission,
    KeepsPlatformAdminPermission,
    KeepsSuperAdminPermission,
    KeepsUserPermission,
)
from constants import CAN_CHANGE_CHANNEL_CONTRIBUTOR
from custom.keeps_exception_handler import (
    ChannelEditNotAuthorizedException,
    KeepsClientHeaderNotFoundCompanyError,
    KeepsNotAllowedError,
)
from django.db.models import Q
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import no_body, swagger_auto_schema
from injector import Provider, inject
from pulse.models import Channel, ChannelContributor
from pulse.serializers.channel_contributor_serializer import (
    ChannelContributorFullSerializer,
    ChannelContributorSerializer,
)
from pulse.services.channel_service import ChannelService
from rest_framework import status, viewsets
from rest_framework.filters import OrderingFilter, SearchFilter
from rest_framework.response import Response
from rules import test_rule
from utils.utils import load_request_user, swagger_safe_queryset


class ChannelContributorViewSet(viewsets.ModelViewSet):
    """
    List users contributors of channels.

    Only user creator can list all channel's contributors.
    If user logged is not the channel creator, the return will be empty array (without error)

    ---
        filters:
        channel, user, channel__name, user__name

        search:
        channel, user, channel__name, user__name

        order fields:
        channel__name, user__name, updated_date

        default order: user__name
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = ("channel", "user", "channel__name", "user__name")
    search_fields = ("channel", "user", "channel__name", "user__name")
    ordering_fields = ("channel__name", "user__name", "updated_date")
    ordering = ("user__name",)

    permission_classes = (
        KeepsPlatformAdminPermission
        | KeepsSuperAdminPermission
        | KeepsCuratorPermission
        | KeepsUserPermission,
    )

    @inject
    def __init__(self, service: ChannelService = Provider[ChannelService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    @swagger_safe_queryset(ChannelContributor)
    def get_queryset(self):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        user_id = self.request.user.get("sub")
        channel_id = self.kwargs["pk"]

        channels_allowed = self._service.get_allowed_channels(workspace_id=workspace_id, user_id=user_id)

        if not workspace_id:
            raise KeepsClientHeaderNotFoundCompanyError()

        return ChannelContributor.objects.filter(Q(channel__in=channels_allowed) & Q(channel_id=channel_id))

    def get_serializer_class(self):
        return ChannelContributorFullSerializer


class ChannelContributorManagerViewSet(viewsets.ModelViewSet):
    """
    manage channel contributors
    """

    permission_classes = (
        KeepsPlatformAdminPermission
        | KeepsSuperAdminPermission
        | KeepsCuratorPermission
        | KeepsUserPermission,
    )

    @inject
    def __init__(self, service: ChannelService = Provider[ChannelService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    def get_queryset(self):
        return ChannelContributor.objects.filter()

    def get_serializer_class(self):
        return ChannelContributorFullSerializer

    @swagger_auto_schema(request_body=no_body)
    def create(self, request, *args, **kwargs):
        """
        Add contributor (user) to the channels


        - Only channel user creator can add contributors
        - Contributors should add contents but not manage others contributors
        """
        channel_id = self.kwargs["pk"]
        user_contributor_id = self.kwargs["user"]

        channel = Channel.objects.get(id=channel_id)
        if not test_rule(CAN_CHANGE_CHANNEL_CONTRIBUTOR, load_request_user(self.request), channel):
            raise ChannelEditNotAuthorizedException()

        serializer = ChannelContributorSerializer(data={"user": user_contributor_id, "channel": channel_id})
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)

        return Response(ChannelContributorFullSerializer(serializer.instance).data, status=status.HTTP_200_OK)

    @swagger_auto_schema(request_body=no_body)
    def destroy(self, request, *args, **kwargs):
        """
        Remove channel's contributor (user)

        Only channel user creator can remove contributors
        """
        channel_id = self.kwargs["pk"]
        user_contributor_id = self.kwargs["user"]
        user_creator_id = self.request.user.get("sub")

        mission = Channel.objects.filter(user_creator_id=user_creator_id, id=channel_id).first()

        if not mission:
            raise KeepsNotAllowedError(
                i18n="channel_remove_contributor_not_allowed",
                detail="Access not allowed. Only user creator can remove contributors",
            )

        instance = ChannelContributor.objects.filter(channel_id=channel_id, user_id=user_contributor_id)
        instance.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
