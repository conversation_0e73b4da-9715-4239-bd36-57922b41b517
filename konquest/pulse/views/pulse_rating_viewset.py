from authentication.keeps_permissions import (
    KeepsCuratorPermission,
    KeepsPlatformAdminPermission,
    KeepsSuperAdminPermission,
    KeepsUserPermission,
)
from django_filters.rest_framework import DjangoFilterBackend
from injector import Provider, inject
from pulse.models import PulseRating
from pulse.serializers.pulse_rating_serializer import PulseRatingListSerializer, PulseRatingSerializer
from pulse.services.channel_service import ChannelService
from rest_framework import status, viewsets
from rest_framework.filters import Ordering<PERSON><PERSON><PERSON>, SearchFilter
from rest_framework.generics import get_object_or_404
from rest_framework.response import Response
from utils.utils import swagger_safe_queryset


class PulseRatingViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    permission_classes = (
        KeepsPlatformAdminPermission
        | KeepsSuperAdminPermission
        | KeepsCuratorPermission
        | KeepsUserPermission,
    )

    @inject
    def __init__(self, service: ChannelService = Provider[ChannelService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    @swagger_safe_queryset(PulseRating)
    def get_queryset(self):
        user_id = self.request.user.get("sub") if self.request.user else None
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        pulses = self._service.get_allowed_pulses(workspace_id=workspace_id, user_id=user_id)

        query = PulseRating.objects.filter(pulse__in=pulses).extra(
            select={
                "rating_avg": """
                    SELECT ROUND((SUM(rating)* 1.0) / COUNT(*), 1)
                    FROM pulse_rating pr
                    WHERE pr.pulse_id = pulse_rating.pulse_id
                """,
                "rating_count": """
                    SELECT COUNT(*)
                    FROM pulse_rating pr
                    WHERE pr.pulse_id = pulse_rating.pulse_id
                """,
            }
        )

        return query

    @staticmethod
    def get_serializer_class():
        return PulseRatingSerializer

    def create(self, request, *args, **kwargs):
        user_id = self.request.user.get("sub") if self.request.user else None
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        if "user" in request.data and "pulse" in request.data and "rating" in request.data:
            get_object_or_404(
                self._service.get_allowed_pulses(workspace_id=workspace_id, user_id=user_id), id=request.data["pulse"]
            )

            instance = PulseRating.objects.all().filter(user=request.data["user"], pulse=request.data["pulse"]).first()

            if not instance:
                return super().create(request, *args, **kwargs)

            instance.rating = request.data["rating"]
            instance.save()
            serializer = self.get_serializer(instance=instance)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        serializer = self.get_serializer(data=request.data)
        return serializer.is_valid(raise_exception=True)


class PulseRatingsViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    permission_classes = (KeepsUserPermission,)

    @inject
    def __init__(self, service: ChannelService = Provider[ChannelService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    @swagger_safe_queryset(PulseRating)
    def get_queryset(self):
        user_id = self.request.user.get("sub") if self.request.user else None
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        pulses = self._service.get_allowed_pulses(workspace_id=workspace_id, user_id=user_id)
        return PulseRating.objects.filter(pulse__in=pulses)

    def get_serializer_class(self):
        return PulseRatingListSerializer if self.request.method == "GET" else PulseRatingSerializer
