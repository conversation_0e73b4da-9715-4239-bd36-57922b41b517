
from authentication.keeps_permissions import ALL_PERMISSIONS
from constants import CAN_CHANGE_CHANNEL_USER_CREATOR
from custom.keeps_exception_handler import ChannelEditNotAuthorizedException
from injector import Provider, inject
from pulse.models import Channel
from pulse.serializers.channel_serializer import ChannelChangeUserCreatorSerializer
from pulse.services.channel_service import ChannelService
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK
from rules import test_rule
from utils.utils import load_request_user


class ChannelChangeUserCreatorViewSet(viewsets.ModelViewSet):
    permission_classes = ALL_PERMISSIONS
    serializer_class = ChannelChangeUserCreatorSerializer

    @inject
    def __init__(self, channel_service: ChannelService = Provider[ChannelService], **kwargs):
        super().__init__(**kwargs)
        self._channel_service = channel_service

    def create(self, request, *args, **kwargs):
        channel_id = self.kwargs.get("pk")
        channel = Channel.objects.get(id=channel_id)
        token = self.request.META.get("HTTP_AUTHORIZATION")
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        user_creator = serializer.validated_data["user_creator"]

        user = load_request_user(request)
        if not test_rule(CAN_CHANGE_CHANNEL_USER_CREATOR, user, channel):
            raise ChannelEditNotAuthorizedException()
        self._channel_service.update_user_creator(channel, user_creator, workspace_id, token)

        return Response(status=HTTP_200_OK)
