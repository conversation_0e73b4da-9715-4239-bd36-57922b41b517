from authentication.keeps_permissions import (
    KeepsCuratorPermission,
    KeepsPlatformAdminPermission,
    KeepsSuperAdminPermission,
    KeepsUserPermission,
)
from django_filters.rest_framework import DjangoFilterBackend
from injector import Provider, inject
from pulse.models import ChannelComment
from pulse.serializers.channel_comment_serializer import Channel<PERSON><PERSON><PERSON>L<PERSON><PERSON>erializer, ChannelCommentSerializer
from pulse.services.channel_service import ChannelService
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter
from rest_framework.response import Response
from rest_framework.status import HTTP_204_NO_CONTENT
from utils.utils import Utils, load_request_user, swagger_safe_queryset


class ChannelCommentViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    permission_classes = (
        KeepsPlatformAdminPermission
        | KeepsSuperAdminPermission
        | KeepsCuratorPermission
        | KeepsUserPermission,
    )

    @inject
    def __init__(self, service: ChannelService = Provider[ChannelService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    @swagger_safe_queryset(ChannelComment)
    def get_queryset(self):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        token = self.request.META.get("HTTP_AUTHORIZATION")
        user_language = Utils.get_user_language(token)
        channels = self._service.get_allowed_workspace_channels(workspace_id=workspace_id, user_language=user_language)
        return ChannelComment.objects.filter(channel__in=channels)

    @staticmethod
    def get_serializer_class(**kwargs):
        return ChannelCommentSerializer

    def destroy(self, request, *args, **kwargs):
        channel_id = self.kwargs.get("pk")
        user = load_request_user(request)
        self._service.destroy_comment(channel_id, user)
        return Response(status=HTTP_204_NO_CONTENT)


class ChannelCommentsViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    permission_classes = (
        KeepsPlatformAdminPermission
        | KeepsSuperAdminPermission
        | KeepsCuratorPermission
        | KeepsUserPermission,
    )

    @inject
    def __init__(self, service: ChannelService = Provider[ChannelService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    def get_queryset(self):
        channel_comment = self.kwargs.get("channel_uuid")
        return ChannelComment.objects.filter(channel_id=channel_comment)

    def get_serializer_class(self):
        return ChannelCommentListSerializer if self.request.method == "GET" else ChannelCommentSerializer
