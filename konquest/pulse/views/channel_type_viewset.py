# -*- coding: utf-8 -*-
from account.models import Workspace
from authentication.keeps_permissions import IsAuthenticatedWithoutXClient
from django.db.models import Q
from django_filters.rest_framework import DjangoFilterBackend
from pulse.models import ChannelType
from pulse.models.channel_type import ChannelTypeEnum
from pulse.serializers.channel_type_serializer import ChannelTypeSerializer
from rest_framework import viewsets
from rest_framework.filters import <PERSON><PERSON><PERSON><PERSON><PERSON>, SearchFilter
from utils.utils import swagger_safe_queryset


class ChannelTypeViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    permission_classes = (IsAuthenticatedWithoutXClient,)

    @swagger_safe_queryset(ChannelType)
    def get_queryset(self):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        workspace = Workspace.objects.get(id=workspace_id)
        exclude_channel_type = []

        if not workspace.allow_create_paid_channel:
            exclude_channel_type.append(ChannelTypeEnum.PAID.value)

        if not workspace.allow_create_public_channel:
            exclude_channel_type.append(ChannelTypeEnum.PUBLIC.value)

        return ChannelType.objects.filter(~Q(id__in=exclude_channel_type))

    @staticmethod
    def get_serializer_class():
        return ChannelTypeSerializer
