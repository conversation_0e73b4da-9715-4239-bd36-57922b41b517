from authentication import keeps_permissions
from injector import Provider, inject
from pulse.models import Pulse
from pulse.serializers.pulse_serializer import PulseListSerializer
from pulse.services.pulse_recommendation import PulseRecommendationService
from rest_framework import viewsets
from utils.utils import Utils, swagger_safe_queryset


class PulsesPriorityViewSet(viewsets.ReadOnlyModelViewSet):
    """
    A viewset that provides the pulses priority to user consume,
    following the service order.
    """

    permission_classes = (
        keeps_permissions.KeepsPlatformAdminPermission
        | keeps_permissions.KeepsSuperAdminPermission
        | keeps_permissions.KeepsCuratorPermission
        | keeps_permissions.KeepsUserPermission
        | keeps_permissions.KeepsInstructorPermission,
    )

    serializer_class = PulseListSerializer

    @inject
    def __init__(self, service: PulseRecommendationService = Provider[PulseRecommendationService], **kwargs):
        super().__init__(**kwargs)
        self.__service = service

    @swagger_safe_queryset(Pulse)
    def get_queryset(self):
        user_id = self.request.user.get("sub")
        workspace_id = self.request.user.get("client_id")
        token = self.request.META.get("HTTP_AUTHORIZATION")
        user_language = Utils.get_user_language(token)

        return self.__service.user_pulses_priority(
            user_id=user_id, workspace_id=workspace_id, user_language=user_language
        )
