from django_filters.rest_framework import Django<PERSON>ilt<PERSON><PERSON><PERSON>end
from injector import Provider, inject
from pulse.models import ChannelRating
from pulse.serializers.channel_rating_serializer import ChannelRatingListSerializer, ChannelRatingSerializer
from pulse.services.channel_service import ChannelService
from rest_framework import status, viewsets
from rest_framework.filters import Order<PERSON><PERSON><PERSON><PERSON>, SearchFilter
from rest_framework.generics import get_object_or_404
from rest_framework.response import Response
from utils.utils import swagger_safe_queryset


class ChannelRatingViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    @inject
    def __init__(self, service: ChannelService = Provider[ChannelService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    @swagger_safe_queryset(ChannelRating)
    def get_queryset(self):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        user_id = self.request.user.get("sub")
        channels = self._service.get_allowed_channels(workspace_id=workspace_id, user_id=user_id)
        return ChannelRating.objects.filter(channel__in=channels)

    @staticmethod
    def get_serializer_class():
        return ChannelRatingSerializer

    def create(self, request, *args, **kwargs):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        user_id = self.request.user.get("sub")
        if "user" in request.data and "channel" in request.data and "rating" in request.data:
            get_object_or_404(
                self._service.get_allowed_channels(workspace_id, user_id),
                id=request.data["channel"],
            )

            instance = ChannelRating.objects.filter(user=request.data["user"], channel=request.data["channel"]).first()

            if not instance:
                return super().create(request, *args, **kwargs)

            instance.rating = request.data["rating"]
            instance.save()
            serializer = self.get_serializer(instance=instance)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        serializer = self.get_serializer(data=request.data)
        return serializer.is_valid(raise_exception=True)


class ChannelRatingsViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    @inject
    def __init__(self, service: ChannelService = Provider[ChannelService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    @swagger_safe_queryset(ChannelRating)
    def get_queryset(self):
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        user_id = self.request.user.get("sub")
        channels = self._service.get_allowed_channels(workspace_id=workspace_id, user_id=user_id)
        return ChannelRating.objects.filter(channel__in=channels)

    def get_serializer_class(self):
        return ChannelRatingListSerializer if self.request.method == "GET" else ChannelRatingSerializer
