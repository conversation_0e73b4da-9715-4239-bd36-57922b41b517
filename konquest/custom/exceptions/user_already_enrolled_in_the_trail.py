from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop

from custom.keeps_exception_handler import KeepsServiceError

USER_ALREADY_ENROLLED_IN_THE_TRAIL = gettext_noop("user_already_enrolled_in_the_trail")


class UserAlreadyEnrolledInTheTrail(KeepsServiceError):
    def __init__(self):
        super().__init__(USER_ALREADY_ENROLLED_IN_THE_TRAIL, _(USER_ALREADY_ENROLLED_IN_THE_TRAIL))
