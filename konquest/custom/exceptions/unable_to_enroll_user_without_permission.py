from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop

from custom.keeps_exception_handler import KeepsServiceError

UNABLE_TO_ENROLL_USER_WITHOUT_PERMISSION = gettext_noop("unable_to_enroll_user_without_permission")


class UnableToEnrollUserWithoutPermission(KeepsServiceError):
    def __init__(self):
        super().__init__(UNABLE_TO_ENROLL_USER_WITHOUT_PERMISSION, _(UNABLE_TO_ENROLL_USER_WITHOUT_PERMISSION))
