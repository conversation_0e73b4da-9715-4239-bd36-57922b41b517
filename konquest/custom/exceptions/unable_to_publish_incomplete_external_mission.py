from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop

from custom.keeps_exception_handler import KeepsServiceError

UNABLE_TO_PUBLISH_INCOMPLETE_EXTERNAL_MISSION = gettext_noop("unable_to_publish_incomplete_external_mission")


class UnableToPublishIncompleteExternalMission(KeepsServiceError):
    def __init__(self):
        super().__init__(
            UNABLE_TO_PUBLISH_INCOMPLETE_EXTERNAL_MISSION, _(UNABLE_TO_PUBLISH_INCOMPLETE_EXTERNAL_MISSION)
        )
