from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop

from custom.keeps_exception_handler import KeepsServiceError

UNABLE_TO_GIVE_UP_ENROLLMENT_FINISHED = gettext_noop("unable_to_give_up_enrollment_finished")


class UnableToGiveUpEnrollmentFinished(KeepsServiceError):
    def __init__(self):
        super().__init__(UNABLE_TO_GIVE_UP_ENROLLMENT_FINISHED, _(UNABLE_TO_GIVE_UP_ENROLLMENT_FINISHED))
