from custom.keeps_exception_handler import KeepsServiceError


class CertificateServiceError(KeepsServiceError):
    def __init__(self, i18n: str, detail: str):
        super().__init__(i18n, detail)


class EnrollmentPerformanceError(CertificateServiceError):
    def __init__(self):
        super().__init__(
            detail="Your performance on this learning trail is lower than the minimum to get a certificate",
            i18n="user_enrollment_performance_lower_then_min",
        )


class EnrollmentNotCompletedError(CertificateServiceError):
    def __init__(self):
        super().__init__(
            detail="You have not completed all attendance requirements for this event",
            i18n="user_not_have_everyone_attendance_in_event",
        )
