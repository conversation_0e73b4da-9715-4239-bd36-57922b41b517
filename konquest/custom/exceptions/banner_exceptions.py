from custom.keeps_exception_handler import KeepsServiceError


class BannerDomainError(KeepsServiceError):
    def __init__(self, i18n: str, detail: str):
        super().__init__(i18n, detail)


class BannerInvalidResourceError(BannerDomainError):
    def __init__(self, detail: str):
        super().__init__("i18n_banner_invalid_resource_error", detail)


class BannerNotFoundError(BannerDomainError):
    def __init__(self, detail: str):
        super().__init__("i18n_banner_not_found_error", detail)
