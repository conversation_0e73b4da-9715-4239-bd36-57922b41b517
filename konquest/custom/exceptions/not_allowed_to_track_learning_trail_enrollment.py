from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop

from custom.exceptions.keeps_permission_error import KeepsPermissionError

NOT_PERMISSION_TO_TRACK_LEARNING_TRAIL_ENROLLMENT = gettext_noop("not_permission_to_track_learning_trail_enrollment")


class NotAllowedToTrackLearningTrailEnrollment(KeepsPermissionError):
    def __init__(self):
        super().__init__(_(NOT_PERMISSION_TO_TRACK_LEARNING_TRAIL_ENROLLMENT))
