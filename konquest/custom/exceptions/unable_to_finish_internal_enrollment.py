from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop

from custom.keeps_exception_handler import KeepsServiceError

UNABLE_TO_FINISH_INTERNAL_ENROLLMENT_BY_SYNC_FINISH = gettext_noop(
    "unable_to_finish_internal_enrollment_by_sync_finish"
)


class UnableToFinishInternalEnrollmentBySyncFinish(KeepsServiceError):
    def __init__(self):
        super().__init__(
            UNABLE_TO_FINISH_INTERNAL_ENROLLMENT_BY_SYNC_FINISH, _(UNABLE_TO_FINISH_INTERNAL_ENROLLMENT_BY_SYNC_FINISH)
        )
