from django.utils.translation.trans_null import gettext as _
from django.utils.translation.trans_null import gettext_noop

from custom.keeps_exception_handler import KeepsServiceError

UNABLE_TO_DELETE_MISSION_TRANSACTION_WITHOUT_RELATION_LINKED = gettext_noop(
    "error.unable_to_delete_mission_transaction_without_relation_linked"
)


class UnableToDeleteMissionTransactionWithoutRelationLinked(KeepsServiceError):
    def __init__(self):
        super().__init__(
            UNABLE_TO_DELETE_MISSION_TRANSACTION_WITHOUT_RELATION_LINKED,
            _(UNABLE_TO_DELETE_MISSION_TRANSACTION_WITHOUT_RELATION_LINKED),
        )
