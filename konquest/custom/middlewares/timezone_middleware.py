import json

import pytz
from django.utils import timezone
from jose import JWSError, jws
from pytz import UTC

from constants import EN, ES, PT_BR, PT_PT
from utils.utils import get_user_language

TIMEZONES_BY_LANGUAGE = {
    PT_BR: pytz.timezone("Etc/GMT+3"),
    PT_PT: pytz.timezone("Europe/Lisbon"),
    EN: pytz.timezone("UTC"),
    ES: pytz.timezone("Chile/Continental"),
}


class TimezoneMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        token = request.META.get("HTTP_AUTHORIZATION")
        if not token:
            timezone.activate(UTC)
            return self.get_response(request)

        try:
            # SonarQube: disable=S5659
            language_timezone = TIMEZONES_BY_LANGUAGE.get(get_user_language(token).lower(), pytz.timezone("UTC"))
            token_timezone = json.loads(jws.get_unverified_claims(token).decode("utf-8")).get("time_zone")

            if token_timezone:
                timezone.activate(pytz.timezone(token_timezone))
            else:
                timezone.activate(language_timezone)

        except JWSError:  # invalid token
            timezone.activate(UTC)

        return self.get_response(request)
