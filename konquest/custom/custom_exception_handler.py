from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop
from jose import ExpiredSignatureError, JWTError
from rest_framework.response import Response
from rest_framework.views import exception_handler

from custom.keeps_exception_handler import KeepsError, KeepsPermissionError, KeepsServiceError, transform_exception

NOT_FOUND_MESSAGE_BY_MODEL = {
    "Mission": gettext_noop("mission_not_found"),
    "MissionEnrollment": gettext_noop("mission_enrollment_not_found"),
    "MissionCompany": gettext_noop("mission_company_not_found"),
    "MissionContributor": gettext_noop("mission_contributor_not_found"),
    "MissionBookmark": gettext_noop("mission_bookmark_not_found"),
    "MissionCategory": gettext_noop("mission_category_not_found"),
    "MissionComment": gettext_noop("mission_comment_not_found"),
    "MissionCompanyPurchase": gettext_noop("mission_company_purchase_not_found"),
    "MissionContentResume": gettext_noop("mission_content_resume_not_found"),
    "MissionEvaluation": gettext_noop("mission_evaluation_not_found"),
    "MissionGoal": gettext_noop("mission_goal_not_found"),
    "MissionPaidConfig": gettext_noop("mission_paid_config_not_found"),
    "MissionProvider": gettext_noop("mission_provider_not_found"),
    "MissionRating": gettext_noop("mission_rating_not_found"),
    "MissionStage": gettext_noop("mission_stage_not_found"),
    "MissionStageContent": gettext_noop("mission_stage_content_not_found"),
    "MissionTag": gettext_noop("mission_tag_not_found"),
    "MissionType": gettext_noop("mission_type_not_found"),
    "ExternalMission": gettext_noop("external_mission_not_found"),
    "PresentialAttendance": gettext_noop("presential_attendance_not_found"),
    "PresentialMission": gettext_noop("presential_mission_not_found"),
    "PresentialMissionDates": gettext_noop("presential_mission_dates_not_found"),
    "PresentialMissionInstructors": gettext_noop("presential_mission_instructors_not_found"),
    "User": gettext_noop("user_not_found"),
    "UserMissionContent": gettext_noop("user_mission_content_not_found"),
    "UserMissionStage": gettext_noop("user_mission_stage_not_found"),
    "Company": gettext_noop("company_not_found"),
    "CompanyCertificate": gettext_noop("company_certificate_not_found"),
    "Pulse": gettext_noop("pulse_not_found"),
    "PulseChannel": gettext_noop("pulse_channel_not_found"),
    "PulseBookmark": gettext_noop("pulse_bookmark_not_found"),
    "PulseComment": gettext_noop("pulse_comment_not_found"),
    "PulseRating": gettext_noop("pulse_rating_not_found"),
    "PulseTag": gettext_noop("pulse_tag_not_found"),
    "PulseType": gettext_noop("pulse_type_not_found"),
    "Channel": gettext_noop("channel_not_found"),
    "ChannelCategory": gettext_noop("channel_category_not_found"),
    "ChannelComment": gettext_noop("channel_comment_not_found"),
    "ChannelCompanyPurchase": gettext_noop("channel_company_purchase_not_found"),
    "ChannelContributor": gettext_noop("channel_contributor_not_found"),
    "ChannelPaidConfig": gettext_noop("channel_paid_config_not_found"),
    "ChannelRating": gettext_noop("channel_rating_not_found"),
    "ChannelSubscription": gettext_noop("channel_subscription_not_found"),
    "ChannelType": gettext_noop("channel_type_not_found"),
    "Group": gettext_noop("group_not_found"),
    "GroupChannel": gettext_noop("group_channel_not_found"),
    "GroupMission": gettext_noop("group_mission_not_found"),
    "GroupUser": gettext_noop("group_user_not_found"),
    "GroupLearningTrail": gettext_noop("group_learning_trail_not_found"),
    "Achievement": gettext_noop("achievement_not_found"),
    "AchievementClass": gettext_noop("achievement_class_not_found"),
    "AchievementType": gettext_noop("achievement_type_not_found"),
    "Answer": gettext_noop("answer_not_found"),
    "Notification": gettext_noop("notification_not_found"),
    "NotificationType": gettext_noop("notification_type_not_found"),
    "CustomCategory": gettext_noop("custom_category_not_found"),
    "Exam": gettext_noop("exam_not_found"),
    "GoalKeyResult": gettext_noop("goal_key_result_not_found"),
    "GoalkeyResultType": gettext_noop("goal_key_result_type_not_found"),
    "GoalType": gettext_noop("goal_type_not_found"),
    "LearnContentActivity": gettext_noop("learn_content_activity_not_found"),
    "LearningTrail": gettext_noop("learning_trail_not_found"),
    "LearningTrailCompany": gettext_noop("learning_trail_company_not_found"),
    "LearningTrailEnrollment": gettext_noop("learning_trail_enrollment_not_found"),
    "LearningTrailStep": gettext_noop("learning_trail_step_not_found"),
    "LearningTrailType": gettext_noop("learning_trail_type_not_found"),
    "LiveAttendance": gettext_noop("live_attendance_not_found"),
    "LiveMission": gettext_noop("live_mission_not_found"),
    "LiveMissionDates": gettext_noop("live_mission_dates_not_found"),
    "LiveMissionInstructors": gettext_noop("live_mission_instructors_not_found"),
    "Question": gettext_noop("question_not_found"),
    "QuestionOption": gettext_noop("question_option_not_found"),
    "ScormActivity": gettext_noop("scorm_activitys_not_found"),
}


def custom_exception_handler(exc, context):
    response = exception_handler(exc, context)
    if isinstance(exc, JWTError):
        return Response({"detail": str(exc), "status_code": 401}, status=401)
    if isinstance(exc, ExpiredSignatureError):
        return Response({"detail": str(exc), "status_code": 401}, status=401)
    if isinstance(exc, KeepsError):
        return Response(
            {"i18n": exc.i18n, "detail": exc.detail, "status_code": exc.status_code}, status=exc.status_code
        )
    if isinstance(exc, ValidationError):
        formatted_exception = transform_exception(exc)
        return Response({"i18n": "validation_error", "detail": formatted_exception.detail}, status=400)
    if isinstance(exc, ObjectDoesNotExist):
        formatted_exception = transform_object_does_not_exits_exception(exc)
        return Response(formatted_exception, status=404)
    if isinstance(exc, KeepsPermissionError):
        return Response(
            {"i18n": "not_allowed", "detail": exc.message},
            status=403,
        )
    if isinstance(exc, KeepsServiceError):
        return Response({"i18n": exc.msg, "detail": exc.description}, status=400)
    return response


def transform_object_does_not_exits_exception(exception: ObjectDoesNotExist) -> dict:
    model_name = exception.args[0].split(" ")[0]
    message = NOT_FOUND_MESSAGE_BY_MODEL.get(model_name)
    if message:
        return {"i18n": message, "detail": _(message)}
    return {
        "i18n": "_".join(str(exception).replace(".", "").split()).lower(),
        "detail": str(exception),
    }
