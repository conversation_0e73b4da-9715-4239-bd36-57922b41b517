import json
import math
import sys
import traceback
from copy import copy
from http.client import RemoteDisconnected
from typing import List, Optional

import psutil
from discord_webhook import DiscordEmbed, DiscordWebhook
from django.conf import settings
from django.http.request import UnreadablePostError
from django.utils.log import AdminEmailHandler
from django.views.debug import ExceptionReporter
from django_redis.exceptions import ConnectionInterrupted as RedisConnectionInterrupted
from requests import Timeout
from rest_framework.request import Request

from custom.keeps_exception_handler import KeepsRuntimeError
from custom.kp_log_record import KPLogRecord

EMBED_FIELD_MAX_VALUE = 1024
HALF_EMBED_FIELD_MAX_VALUE = int(EMBED_FIELD_MAX_VALUE / 2)
TITLE_MAX_CHARACTERS = 256
DESCRIPTION_MAX_CHARACTERS = 4096
RED_COLOR = "ff0000"
WARNING_COLOR = "ffcc00"


class DiscordWebhookLogger(AdminEmailHandler):
    def __init__(
        self,
        url: str = settings.DISCORD_WEBHOOK,
    ):
        super().__init__()
        self.url = url
        self.exceptions_to_ignore = [RemoteDisconnected, RedisConnectionInterrupted]

    def emit_warning_message(self, identifier: str, description: str) -> None:
        main_embed = DiscordEmbed(
            title=identifier[:TITLE_MAX_CHARACTERS], description=description[:EMBED_FIELD_MAX_VALUE]
        )
        self.execute(main_embed)

    def emit_short_message(self, identifier: str, error: BaseException):
        description = f"{traceback.format_exc()}: {error}"
        if len(description) > EMBED_FIELD_MAX_VALUE:
            description = description[:HALF_EMBED_FIELD_MAX_VALUE] + " | " + description[-HALF_EMBED_FIELD_MAX_VALUE:]

        if settings.ENVIRONMENT_TEST:
            raise error
        main_embed = DiscordEmbed(title=identifier[:TITLE_MAX_CHARACTERS], description=description)
        self.execute(main_embed)

    def emit(self, record: KPLogRecord):
        if settings.DEBUG or "test" in sys.argv:
            return
        request = record.request if hasattr(record, "request") else None
        subject = self.get_request_subject(record, request) if request else f"{record.levelname}: {record.getMessage()}"
        subject = self.format_subject(subject)

        exception_info = record.exc_info if record.exc_info else (None, record.getMessage(), None)
        exception_type = exception_info[0]
        if exception_type in self.exceptions_to_ignore:
            return

        message = self._get_short_message(exception_info, record, request)
        user = self.get_user_by_email(request)
        data = self.get_request_data(request)

        if isinstance(exception_info, UnreadablePostError):
            embed = self._construct_request_data_error_embed(record, request, user)
        else:
            embed = self.construct_default_embed(data, record, request, subject, user)
        embeds = self.create_detail_embeds(message)

        self.execute(embed, embeds, f"Status Code {record.status_code}")

    def _get_short_message(self, exception_info, record, request):
        record_without_exception = copy(record)
        record_without_exception.exc_info = None
        record_without_exception.exc_text = None
        reporter = ExceptionReporter(request, is_email=True, *exception_info)
        request_user = ""
        if hasattr(reporter.request, "user") and isinstance(reporter.request.user, dict):
            for key in sorted(reporter.request.user):
                request_user += f"{key}: {reporter.request.user[key]}\n"
        return f"{self.format(record_without_exception)}\n\n{request_user}"

    @staticmethod
    def get_request_subject(record, request):
        subject = "{} ({} IP): {}".format(
            record.levelname,
            ("internal" if request.META.get("REMOTE_ADDR") in settings.INTERNAL_IPS else "EXTERNAL"),
            record.getMessage(),
        )
        return subject

    @staticmethod
    def get_request_data(request):
        try:
            data = json.dumps(request.body.decode("utf-8")) if request and not request.FILES else "No Data"
        except BaseException:
            data = None
        return data

    @staticmethod
    def get_user_by_email(request) -> str:
        if not hasattr(request, "user") or not isinstance(request.user, dict):
            return ""
        return request.user.get("email")

    @staticmethod
    def create_detail_embeds(message: str):
        split = 3600
        parts = math.ceil(len(message.encode("utf8")) / split)
        embeds = []
        for part in range(parts):
            start = 0 if part == 0 else split * part
            end = split if part == 0 else split * part + split
            detail_text = "\r\n\r\n\r\n\r\n\r\n\r\n\r\n" + message[start:end]
            embeds.append(DiscordEmbed(title=f"Details (Part {part + 1})", description=detail_text))

        return embeds

    @staticmethod
    def construct_default_embed(
        data: str, record: KPLogRecord, request: Request, subject: str, user: str
    ) -> DiscordEmbed:
        traceback_formatted = str(traceback.format_exc(limit=4))
        embed = DiscordEmbed(title=subject, color=RED_COLOR)
        embed.add_embed_field("Level", record.levelname[:EMBED_FIELD_MAX_VALUE], inline=False)
        embed.add_embed_field("Traceback", traceback_formatted[:EMBED_FIELD_MAX_VALUE], inline=False)
        embed.add_embed_field("User", user[:EMBED_FIELD_MAX_VALUE] if user else None, inline=False)
        embed.add_embed_field("Status Code", record.status_code, inline=False)
        embed.add_embed_field("POST Data", data[:1024] if data else "None", inline=False)
        if request:
            embed.add_embed_field("GET Params", str(json.dumps(request.GET))[:1024], inline=False)
            embed.add_embed_field("Method", request.method[:EMBED_FIELD_MAX_VALUE], inline=False)
            embed.add_embed_field("Path", request.path[:EMBED_FIELD_MAX_VALUE], inline=False)
            embed.add_embed_field(
                "UA", request.META.get("HTTP_USER_AGENT", "")[:1024] if request.META else None, inline=False
            )
        return embed

    @staticmethod
    def _construct_request_data_error_embed(record, request, user):
        embed = DiscordEmbed(title="Unreadable Post Error: request data read error", color=WARNING_COLOR)
        embed.add_embed_field("Level", record.levelname, inline=False)
        embed.add_embed_field("User", user, inline=False)
        embed.add_embed_field("Status Code", record.status_code, inline=False)
        if request:
            embed.add_embed_field("Method", request.method, inline=False)
            embed.add_embed_field("Path", request.path, inline=False)
            embed.add_embed_field("UA", request.META.get("HTTP_USER_AGENT") if request.META else None, inline=False)
        return embed

    def execute(
        self, embed: DiscordEmbed, detail_embeds: Optional[List[DiscordEmbed]] = None, footer_text: Optional[str] = None
    ):
        embed.set_footer(text=footer_text)
        embed.set_timestamp()
        embed.add_embed_field(name="RAM memory used", value=f"{psutil.virtual_memory()[2]}%", inline=False)
        webhook = DiscordWebhook(url=self.url)
        webhook.add_embed(embed)
        if detail_embeds:
            detail_embed = detail_embeds[0]
            detail_embed.set_color(embed.color)
            webhook.add_embed(detail_embed)
        try:
            webhook.execute()
        except Timeout as error:
            raise KeepsRuntimeError(f"Connection to Discord timed out: {error}") from error
