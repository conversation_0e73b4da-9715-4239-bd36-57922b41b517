import pika
from django.test import TestCase
from mock import mock
from mock.mock import MagicMock

from custom.message_connection_service import MessageConnectionService


class TestMessageConnectionService(TestCase):
    def setUp(self):
        self.message_broker_url = "amqp://guest:guest@localhost/"
        self.queue = "test_queue"
        self.exchange = "test_exchange"
        self.exchange_type = "direct"
        self.service = MessageConnectionService(
            message_broker_url=self.message_broker_url,
            default_routing_key=self.queue,
            exchange=self.exchange,
            exchange_type=self.exchange_type,
        )

    @mock.patch.object(pika, "URLParameters")
    @mock.patch.object(pika, "BlockingConnection")
    def test_send_with_new_connection(self, blocking_connection: MagicMock, url_parameters: MagicMock):
        payload = {"data": "test"}
        task_name = "test_task"
        channel_mock = MagicMock()
        blocking_connection.return_value.channel.return_value = channel_mock

        self.service.send(payload, task_name)

        blocking_connection.assert_called_with(pika.URLParameters(self.message_broker_url))
        channel_mock.basic_publish.assert_called()
        channel_mock.queue_declare.assert_not_called()
