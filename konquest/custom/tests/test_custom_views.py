from django.test import TestCase
from rest_framework.test import APIClient

from custom.keeps_exception_handler import PAGE_NOT_FOUND


class TestCustomViews(TestCase):
    def setUp(self) -> None:
        self.client = APIClient()
        pass

    def test_page_not_found_error(self):
        response = self.client.get("/unknown-path")

        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json().get("i18n"), PAGE_NOT_FOUND)
