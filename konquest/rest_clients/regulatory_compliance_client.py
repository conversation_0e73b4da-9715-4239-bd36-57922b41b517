import json
from os import getenv
from typing import Any, Dict, List, Union

import requests
from django.conf import settings
from rest_clients.regulatory_compliance_client_abc import RegulatoryComplianceClientABC
from user_activity.dtos.regulatory_compliance_cycle_enrollment import EnrollmentCycleCreateDTO

X_CLIENT = "x-client"


class RegulatoryComplianceClient(RegulatoryComplianceClientABC):
    def __init__(self):
        self._url = settings.REGULATORY_COMPLIANCE_API_URL
        self._url_enrollment_cycle = self._url + getenv("ENROLLMENT_CYCLE_ENDPOINT", "/cycles/enrollment")

    def create_enrollment_cycle(
        self, token: str, enrollment_cycle: EnrollmentCycleCreateDTO
    ) -> Union[Union[str, Dict[Any, Any]], List[Dict]]:
        if token:
            return json.dumps(enrollment_cycle.__dict__)
        response = requests.post(
            self._url_enrollment_cycle,
            data=enrollment_cycle.to_json(),
            headers={"Authorization": token, X_CLIENT: enrollment_cycle.workspaceId},
        )

        if response.status_code != 201:
            return {}

        return response.json() if response.text else {}
