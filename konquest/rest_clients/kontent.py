from typing import List

import requests
from config.settings import APPLICATION_NAME, KEEPS_SECRET_TOKEN_INTEGRATION
from django.conf import settings
from rest_clients.kontent_client_abc import KontentClientAbc
from rest_clients.myaccount import AUTHORIZATION
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_204_NO_CONTENT


class KontentClient(KontentClientAbc):
    def __init__(self):
        self._url = settings.KONTENT_API_URL
        self._url_learn_content = self._url + "/learn-content"
        self._url_learn_content_patch = self._url + "/learn-content/{}"
        self._url_learn_content_copy = self._url + "/learn-content/{}/copy"
        self._url_content_point_rule = self._url + "/learn-content/point-rules"
        self._url_check_learn_contents_analyzed = f"{self._url}/learn-content/check-contents-analyzed"

    def get_docs(self, token, ids):
        response = requests.get(
            self._url_learn_content, params={"pk": ",".join(map(str, ids))}, headers={"Authorization": token}
        )

        if response.status_code != 200:
            return []

        return response.json()

    def get_learn_content(self, token, content_id):
        response = requests.get(
            self._url_learn_content + "/" + str(content_id) + "?request_cache=false",
            headers={"Authorization": str(token)},
        )

        if response.status_code != 200:
            return []

        return response.json() if response.text else {}

    def update_learn_content_duration(self, token, content_id, duration):
        requests.patch(
            self._url_learn_content_patch.format(content_id),
            data={"duration": duration},
            headers={"Authorization": str(token)},
        )

    def copy_learn_content(self, token, content_id):
        response = requests.post(self._url_learn_content_copy.format(content_id), headers={AUTHORIZATION: str(token)})
        if response.status_code != HTTP_201_CREATED:
            return {}
        return response.json()

    def get_content_point_rule(self, token, content_type_name=""):
        url = f"{self._url_content_point_rule}?content_type__name={content_type_name}"
        response = requests.get(url, headers={"Authorization": token})

        if response.status_code != 200:
            return []

        return response.json()

    def create_learn_contents_analyzed_check(self, learn_content_ids: List[str], callback_id: str):
        payload = {
            "learn_content_ids": learn_content_ids,
            "application_name": APPLICATION_NAME,
            "callback_id": str(callback_id),
        }
        response = requests.post(
            self._url_check_learn_contents_analyzed,
            json=payload,
            headers={"Authorization": KEEPS_SECRET_TOKEN_INTEGRATION},
        )
        assert response.status_code in [
            HTTP_204_NO_CONTENT,
            HTTP_201_CREATED,
            HTTP_200_OK,
        ], f"invalid status code: {response.status_code}"
