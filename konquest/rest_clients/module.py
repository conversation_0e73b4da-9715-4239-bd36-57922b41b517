from config import settings
from custom.discord_webhook import DiscordWebhookLogger
from injector import Modu<PERSON>, provider, singleton
from rest_clients.kontent import KontentClient
from rest_clients.kontent_client_abc import KontentClientAbc
from utils.aws import ReportLambdaClient, S3Client


class RestClientModule(Module):
    @singleton
    @provider
    def kontent_client(self) -> KontentClientAbc:
        return KontentClient()

    @singleton
    @provider
    def s3_client(self, discord_webhook_logger: DiscordWebhookLogger) -> S3Client:
        return S3Client(
            aws_access_key=settings.AWS_ACCESS_KEY_ID,
            aws_secret_key=settings.AWS_SECRET_ACCESS_KEY,
            aws_region=settings.AWS_REGION_NAME,
            aws_default_url=settings.AWS_CDN_BASE_URL,
            bucket_name=settings.AWS_BUCKET_NAME,
            bucket_path=settings.AWS_BUCKET_PATH,
            webhook_logger=discord_webhook_logger,
        )

    @singleton
    @provider
    def report_lambda_client(self) -> ReportLambdaClient:
        return ReportLambdaClient(
            access_key=settings.AWS_LAMBDA_ACCESS_KEY_ID,
            secret_key=settings.AWS_LAMBDA_SECRET_ACCESS_KEY,
            region_name=settings.AWS_LAMBDA_REGION_NAME,
            temp_dir=f"{settings.BASE_DIR}/temp",
        )
