import requests
from config.settings import (
    KONQUEST_CONTENT_ROLE,
    KONQUEST_CURATOR_ROLE,
    KONQUEST_INSTRUCTOR_ROLE,
    KONQUEST_MYACC_ADMIN_ROLE,
    KONQUEST_SUPER_ADMIN_ROLE,
)
from custom import KeepsError
from django.conf import settings

ROLES_ABLE_TO_INSTRUCTOR = [
    str(KONQUEST_MYACC_ADMIN_ROLE),
    str(KONQUEST_CONTENT_ROLE),
    str(KONQUEST_CURATOR_ROLE),
    str(KONQUEST_INSTRUCTOR_ROLE),
    str(KONQUEST_SUPER_ADMIN_ROLE),
]
ROLES_ABLE_TO_MANAGE = [
    str(KONQUEST_MYACC_ADMIN_ROLE),
    str(KONQUEST_CONTENT_ROLE),
    str(KONQUEST_CURATOR_ROLE),
    str(KONQUEST_SUPER_ADMIN_ROLE),
    str(KONQUEST_INSTRUCTOR_ROLE),
]
ROLES_ADMIN = [str(KONQUEST_MYACC_ADMIN_ROLE), str(KONQUEST_SUPER_ADMIN_ROLE)]
AUTHORIZATION = "Authorization"
X_CLIENT = "x-client"


class MyAccountClientV2:
    def __init__(self):
        self.base_url = settings.MYACCOUNT_V2_API_URL
        self.endpoints = {
            "user_info": "/api/users/info",
        }

    def get_user_info(self, token: str, workspace_id: str) -> dict:
        response = requests.get(
            self.base_url + self.endpoints["user_info"],
            headers={AUTHORIZATION: f"Bearer {token}", X_CLIENT: workspace_id}
        )
        if str(response.status_code)[0] == "5":
            raise ConnectionError("unable_to_retrieve_user_information_on_my_account")
        if response.status_code == 401:
            raise KeepsError("Unauthorized", "unauthorized", status_code=response.status_code)
        if response.status_code == 400:
            raise KeepsError(response.message, response.message, status_code=response.status_code)

        return response.json()
