from typing import List, Type

from account.models import User
from django.db.models import Model
from transfers.abstracts.abstract_transfer import AbstractTransfer
from transfers.services.helpers import find_models_with_back_references, get_related_field_name


class GenericTransfer(AbstractTransfer):
    """
    A generic transfer service that works for any given model.
    """

    def __init__(self, model: Type[Model]):
        super().__init__(model)

    def transfer(self, source_user_id: str, new_user_id: str, workspace_id: str):
        objects = self.model.objects.filter(user_id=source_user_id, workspace_id=workspace_id)
        object_ids = objects.values_list("id", flat=True)

        for related_model in self.find_related_models():
            self._update_back_references(self.model, related_model, object_ids, new_user_id)

        for model in objects:
            model.user_id = new_user_id
        self.model.objects.bulk_update(objects, fields=["user_id"])

    def find_related_models(self) -> List[Type[Model]]:
        # Implementation to find models with back references
        return find_models_with_back_references(self.model)

    @staticmethod
    def _update_back_references(base_model, model: Type[Model], source_model_ids: List[str], new_user_id: str):
        model_field_name = get_related_field_name(model, base_model)
        user_field_name = get_related_field_name(model, User)
        if not (user_field_name and model_field_name):
            return

        entities = model.objects.filter(**{f"{model_field_name}_id__in": source_model_ids})
        user_id_field_name = f"{user_field_name}_id"
        for entity in entities:
            entity.__setattr__(user_id_field_name, new_user_id)

        model.objects.bulk_update(entities, fields=[user_id_field_name])
