from django.db.models import Q
from gamification.models.gamification_history import GamificationHistory
from transfers.abstracts.abstract_transfer import AbstractTransfer


class GamificationTransfer(AbstractTransfer):
    def __init__(self):
        super().__init__(GamificationHistory)

    def transfer(self, source_user_id: str, new_user_id: str, workspace_id: str):
        """
        Transfers gamification history while avoiding duplicate entries.
        """
        histories_to_transfer = GamificationHistory.objects.filter(user_id=source_user_id, workspace_id=workspace_id)

        mission_ids = histories_to_transfer.filter(mission_id__isnull=False).values_list("mission_id", flat=True)
        pulse_ids = histories_to_transfer.filter(pulse_id__isnull=False).values_list("pulse_id", flat=True)

        GamificationHistory.objects.filter(
            Q(user_id=new_user_id, workspace_id=workspace_id), Q(mission_id__in=mission_ids) | Q(pulse_id__in=pulse_ids)
        ).delete()

        histories_to_transfer.update(user_id=new_user_id)
