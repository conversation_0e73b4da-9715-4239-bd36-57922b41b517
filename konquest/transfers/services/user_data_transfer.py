from typing import List

from django.db import transaction
from learn_content.models.consumption_transfer_log import ConsumptionTransferLog
from transfers.abstracts.abstract_transfer import AbstractTransfer


class UserDataTransferService:
    def __init__(self, transfer_services: List[AbstractTransfer]):
        self.transfer_services: List[AbstractTransfer] = transfer_services

    @transaction.atomic(savepoint=True)
    def transfer(self, source_user_id: str, new_user_id: str, workspace_id: str):
        for service in self.transfer_services:
            service.transfer(source_user_id, new_user_id, workspace_id)
        self.consumption_transfer_log(source_user_id, new_user_id, workspace_id)

    def consumption_transfer_log(self, source_user_id: str, new_user_id: str, workspace_id: str):
        ConsumptionTransferLog.objects.create(
            origin_user_id=source_user_id, destination_user_id=new_user_id, workspace_id=workspace_id
        )
