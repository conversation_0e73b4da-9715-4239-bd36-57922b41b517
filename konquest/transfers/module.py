from injector import Module, inject, provider, singleton
from transfers.services.gamification_transfer import GamificationTransfer
from transfers.services.generic_transfer import GenericTransfer
from transfers.services.user_data_transfer import UserDataTransferService
from user_activity.models import LearnContentActivity, LearningTrailEnrollment, MissionEnrollment


class TransfersModule(Module):
    @inject
    @singleton
    @provider
    def user_data_transfer(self, gamification_transfer: GamificationTransfer) -> UserDataTransferService:
        transfers = [
            GenericTransfer(MissionEnrollment),
            GenericTransfer(LearningTrailEnrollment),
            GenericTransfer(MissionEnrollment),
            GenericTransfer(LearnContentActivity),
            gamification_transfer,
        ]
        return UserDataTransferService(transfers)

    @singleton
    @provider
    def gamification_transfer(self) -> GamificationTransfer:
        return GamificationTransfer()
