from injector import Provider, inject
from rest_framework import status, viewsets
from rest_framework.response import Response
from transfers.serializers.transfer_serializer import TransferSerializer
from transfers.services.user_data_transfer import UserDataTransferService


class TransferViewSet(viewsets.ModelViewSet):
    serializer_class = TransferSerializer

    @inject
    def __init__(self, service: UserDataTransferService = Provider[UserDataTransferService], **kwargs):
        super().__init__(**kwargs)
        self._service = service

    def create(self, request, *args, **kwargs):
        """
        This API facilitates the transfer of all enrollment data and associated activities from one user to another:

        Source User ID: The user whose enrollment and related activity data will be transferred.
        Post-transfer, this user's associated data will be completely removed.
        Target User ID: The recipient of the enrollment and related activity data from the source user.

        Important Note: Exercise caution when using this function, as it is a one-way process.
        Once executed, this transaction is irreversible and cannot be undone by any other API. Therefore,
        it is crucial to verify and confirm the accuracy of the payload before initiating the request.
        """
        workspace_id = self.request.user.get("client_id")
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data

        self._service.transfer(data["source_user_id"], data["target_user_id"], workspace_id)

        return Response(data=None, status=status.HTTP_204_NO_CONTENT)
