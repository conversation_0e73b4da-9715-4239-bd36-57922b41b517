import pytest
from account.models import User, Workspace
from django.core.exceptions import ObjectDoesNotExist
from django.test import TestCase
from gamification.models.gamification_history import GamificationHistory
from mission.models import Mission
from model_mommy import mommy
from transfers.services.gamification_transfer import GamificationTransfer


class TestGamificationTransfer(TestCase):
    def setUp(self) -> None:
        self.user = mommy.make(User)
        self.workspace = mommy.make(Workspace)
        self.gamification_transfer = GamificationTransfer()

    def test_transfer(self) -> None:
        new_user = mommy.make(User)
        gamification_history = mommy.make(GamificationHistory, user=self.user, workspace=self.workspace)

        self.gamification_transfer.transfer(self.user.id, new_user.id, self.workspace)

        gamification_history.refresh_from_db()
        self.assertEqual(gamification_history.user_id, new_user.id)

    def test_transfer_without_duplicated_points(self) -> None:
        """
        The duplicated Gamification History should be deleted
        """
        new_user = mommy.make(User)
        mission = mommy.make(Mission)

        gamification_history = mommy.make(
            GamificationHistory, user=self.user, workspace=self.workspace, mission=mission
        )
        duplicated_gamification_history = mommy.make(
            GamificationHistory, user=new_user, workspace=self.workspace, mission=mission
        )

        self.gamification_transfer.transfer(self.user.id, new_user.id, self.workspace)

        gamification_history.refresh_from_db()
        with pytest.raises(ObjectDoesNotExist):
            duplicated_gamification_history.refresh_from_db()
        self.assertEqual(gamification_history.user_id, new_user.id)
