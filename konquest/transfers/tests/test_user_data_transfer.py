from account.models import User, Workspace
from django.test import TestCase
from learn_content.models.consumption_transfer_log import ConsumptionTransferLog
from model_mommy import mommy
from transfers.services.generic_transfer import GenericTransfer
from transfers.services.user_data_transfer import UserDataTransferService
from user_activity.models import LearnContentActivity, MissionEnrollment


class TestUserDataTransfer(TestCase):
    def setUp(self) -> None:
        self.user = mommy.make(User)
        self.workspace = mommy.make(Workspace)
        self.enrollment = mommy.make(MissionEnrollment, user=self.user, workspace=self.workspace)
        self.learn_content_activities_transfer = GenericTransfer(LearnContentActivity)
        self.transfer = GenericTransfer(MissionEnrollment)
        self.user_data_transfer = UserDataTransferService([self.transfer, self.learn_content_activities_transfer])

    def test_user_data_transfer(self):
        self.activity = mommy.make(LearnContentActivity, user=self.user, mission_enrollment=self.enrollment)
        self.new_user = mommy.make(User)

        self.assertEqual(ConsumptionTransferLog.objects.count(), 0)

        self.user_data_transfer.transfer(self.user.id, self.new_user.id, self.workspace.id)

        self.enrollment.refresh_from_db()
        self.activity.refresh_from_db()

        self.assertEqual(self.enrollment.user_id, self.new_user.id)
        self.assertEqual(self.activity.user_id, self.new_user.id)

        self.assertEqual(ConsumptionTransferLog.objects.count(), 1)
        transfer_log = ConsumptionTransferLog.objects.first()
        self.assertEqual(str(transfer_log.origin_user_id), str(self.user.id))
        self.assertEqual(str(transfer_log.destination_user_id), str(self.new_user.id))
        self.assertEqual(str(transfer_log.workspace_id), str(self.workspace.id))
