import uuid
from contextlib import contextmanager
from datetime import datetime
from typing import Type, TypeVar

import django
from config import settings
from custom.discord_webhook import DiscordWebhookLogger
from django.utils.module_loading import import_string
from django_injector.apps import DjangoModule
from injector import Injector, Module

# pylint: disable=import-outside-toplevel
T = TypeVar("T")


@contextmanager
def task_transaction(method_name: str, service: Type[T] = None):
    injector = _get_injector()
    task_uuid = uuid.uuid4()

    try:
        print(f"{datetime.now()} | START:{task_uuid}-{method_name}")
        service_instance: T = None
        if service:
            service_instance = injector.get(service)
            if not service_instance:
                raise NotImplementedError(f"Service {service} not found")
        yield service_instance
    except Exception as error:
        if settings.ENVIRONMENT_TEST:
            raise error
        logger = injector.get(DiscordWebhookLogger)
        logger.emit_short_message(f"{method_name.replace('_', ' ')} - Worker", error)
    finally:
        print(f"{datetime.now()} | FINISH:{task_uuid}-{method_name}")
        if not settings.ENVIRONMENT_TEST:
            django.db.connections.close_all()


def _get_injector():
    django_module = DjangoModule()
    injector = Injector([django_module])
    for mod_str in getattr(settings, "INJECTOR_MODULES", []):
        module = import_string(mod_str)
        if isinstance(module, type) and issubclass(module, Module):
            module = module()
        injector.binder.install(module)
    return injector
