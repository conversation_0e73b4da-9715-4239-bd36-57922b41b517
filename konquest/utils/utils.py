import datetime
import json
import math
import tempfile
import uuid
from functools import wraps
from typing import List, Tuple

import pandas as pd
import pytz
from account.models import User, Workspace
from config.settings import TEMP_UPLOAD_FOLDER
from custom import KeepsBadRequestError
from django.core.files.storage import default_storage
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db.models import Case, IntegerField, QuerySet, When
from django.utils.translation import gettext
from jose import JWSError, jws
from PIL import Image
from rest_framework.request import Request


class Utils:
    @staticmethod
    def get_user_language(token: str) -> str:
        locale = "pt-BR"
        if not token:
            return locale
        try:
            # SonarQube: disable=S5659
            return (
                json.loads(jws.get_unverified_claims(token).decode("utf-8")).get("locale", "pt-BR") if token else locale
            )
        except JWSError:  # invalid token
            return locale

    @staticmethod
    def temp_file(file: InMemoryUploadedFile) -> str:
        if not file:
            raise KeepsBadRequestError(detail="File not found", i18n="file_not_found")

        extension = file.name.split(".")[-1]
        filename = f"{str(uuid.uuid4())}.{extension}"

        with open(default_storage.path(filename), "wb+") as destination:
            for chunk in file.chunks():
                destination.write(chunk)

        return f"{TEMP_UPLOAD_FOLDER}/{filename}"

    @staticmethod
    def common_member(list_a: list, list_b: list):
        a_set = set(list_a)
        b_set = set(list_b)
        return a_set & b_set

    @staticmethod
    def formatted_string_to_url(string_to_format: str) -> str:
        """
        Function to remove some especial chars of a string to be a normalized url.
        Especial chars removed: ! * ' ( ) ; : @ & = + $ , / ? # [ ]
        :param string_to_format: str
        """
        especial_chars = ["!", "*", "'", "()", ";", ":", "@", "&", "=", "+", "$", "/", "?", "#", "[", "]", " "]

        url = string_to_format

        for char in especial_chars:
            url = url.replace(char, "")

        return url


def action_permission(permission_classes: tuple):
    """
    Decorator to use different permission classes in the viewset methods
    :param permission_classes: tuple
    """

    def decorator(func):
        def decorated_func(self, *args, **kwargs):
            self.permission_classes = permission_classes
            # this call is needed for request permissions
            self.check_permissions(self.request)
            return func(self, *args, **kwargs)

        return decorated_func

    return decorator


def get_user_language(token: str) -> str:
    locale = "en"
    if not token:
        return locale
    try:
        # SonarQube: disable=S5659
        return json.loads(jws.get_unverified_claims(token).decode("utf-8")).get("locale", "pt-BR") if token else locale
    except JWSError:  # invalid token
        return locale


def dict_fetchall(cursor):
    columns = [col[0] for col in cursor.description]
    return [dict(zip(columns, row)) for row in cursor.fetchall()]


def create_empty_image(extension: str):
    image = Image.new("RGB", (100, 100))
    file = tempfile.NamedTemporaryFile(suffix=f"{extension}")
    image.save(file)
    file.seek(0)

    return file


def parser_excel(file: InMemoryUploadedFile) -> dict:
    dataframe = pd.read_excel(file.file)
    formatted_colum_names = []
    for column in dataframe.columns:
        formatted_colum_names.append(column.lower().replace(" ", ""))
    dataframe.columns = formatted_colum_names
    return dataframe.to_dict()


def storage_file(file: InMemoryUploadedFile) -> str:
    extension = file.name.split(".")[1]
    filename = f"{str(uuid.uuid4())}.{extension}"

    with open(default_storage.path(filename), "wb+") as destination:
        for chunk in file.chunks():
            destination.write(chunk)

    return f"{TEMP_UPLOAD_FOLDER}/{filename}"


def order_query_by_language(query: QuerySet, language: str):
    query.query.order_by = ("-lang",) + query.query.order_by
    query = query.annotate(lang=Case(When(language=language, then=1), default=0, output_field=IntegerField()))
    return query


def load_request_user(request: Request) -> User:
    user = User.objects.get(id=request.user.get("sub"))
    user.role = request.user.get("role")
    user.workspace_id = request.user.get("client_id")
    return user


def format_list_to_query_value(sequence: List) -> str:
    return ",".join(f"'{item}'" for item in sequence)


def load_token(token: str) -> dict:
    # SonarQube: disable=S5659
    return json.loads(jws.get_unverified_claims(token).decode("utf-8"))


def get_weekday_by_index(weekday_number: int) -> dict:
    weekdays = {
        0: gettext("monday"),
        1: gettext("tuesday"),
        2: gettext("wednesday"),
        3: gettext("thursday"),
        4: gettext("friday"),
        5: gettext("saturday"),
        6: gettext("sunday"),
    }
    return weekdays[weekday_number]


def get_today_date_formatted(timezone: str = None, fommart: str = "%d/%m/%Y") -> str:
    today = datetime.datetime.today()
    if timezone:
        today = today.astimezone(pytz.timezone(timezone))
    return today.date().strftime(fommart)


def change_timezone(value: datetime.datetime, timezone: str) -> datetime.datetime:
    return value.astimezone(pytz.timezone(timezone))


def convert_seconds_to_hours_and_minutes(seconds: int) -> Tuple[int, int]:
    if not seconds:
        return 0, 0
    hours, remainder = divmod(seconds, 3600)
    minutes, _ = divmod(remainder, 60)
    return int(hours), int(minutes)


def workspace_hash(workspace_id):
    hash_workspace = Workspace.objects.filter(id=workspace_id)
    return hash_workspace.first().hash_id


def truncate_decimal_value(value: float):
    return math.floor(value * 100) / 100


def swagger_safe_queryset(model):
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            if getattr(self, "swagger_fake_view", False):
                return model.objects.none()
            return func(self, *args, **kwargs)

        return wrapper

    return decorator


def format_time_duration(seconds, string_format="hm"):
    """
    format seconds to a String formatted
    :param seconds: seconds
    :param string_format: the format to return: hm = 1h20m; h = 1h, m = 20m
    """
    seconds = seconds if seconds else 0
    minutes, seconds = divmod(seconds, 60)
    hours, minutes = divmod(minutes, 60)

    hours = f"{int(hours):02d}"
    minutes = f"{int(minutes):02d}"

    if string_format == "hm":
        return f"{int(hours)}h{minutes}m"
    if string_format == "h":
        return f"{int(hours)}h"
    if string_format == "m":
        return f"{minutes}m"

    raise ValueError("string_format_not_valid")
