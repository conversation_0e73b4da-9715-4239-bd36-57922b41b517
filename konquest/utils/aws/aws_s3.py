from typing import Optional

import boto3
from boto3.s3.transfer import TransferConfig
from custom import KeepsError
from custom.discord_webhook import DiscordWebhookLogger
from unidecode import unidecode

FILE_TYPES = {
    "image": ["png", "jpg", "jpeg"],
}


class S3Client:
    def __init__(
        self,
        aws_access_key: str,
        aws_secret_key: str,
        aws_region: str,
        aws_default_url: str,
        bucket_name: str,
        webhook_logger: DiscordWebhookLogger,
        bucket_path: Optional[str] = None,
    ):
        self.base_url = aws_default_url
        self.bucket_name = bucket_name
        self.bucket_path = bucket_path
        self.webhook_logger = webhook_logger

        self.config = TransferConfig(
            multipart_threshold=64 * 1024 * 1024,
            max_concurrency=10,
            num_download_attempts=10,
            multipart_chunksize=16 * 1024 * 1024,
            max_io_queue=10000,
        )

        self.s3_client = boto3.client(
            "s3", aws_access_key_id=aws_access_key, aws_secret_access_key=aws_secret_key, region_name=aws_region
        )

    def send_file_obj(self, file: bytes, destiny_name, content_type="jpg"):
        """
        Upload file to AWS S3

        :param file: binary file
        :param destiny_name: where file uploaded
        :param content_type: file content type

        :return: {name: string, url: string}

        """
        content_type_map = {"png": "image/png", "jpg": "image/jpg"}
        content_type = content_type_map.get(content_type)
        if not content_type:
            raise ValueError("invalid content type")

        filename = self.bucket_path + "/" + unidecode(destiny_name).replace(" ", "_")

        self.s3_client.upload_fileobj(
            file,
            self.bucket_name,
            filename,
            Config=self.config,
            ExtraArgs={"ACL": "public-read", "ContentType": content_type},
        )

        response = {"name": filename, "url": f"{self.base_url}/{filename}"}

        return response

    def send_file_path(self, file_path: str, destiny_name: str, content_type: str):
        """
        Upload file to AWS S3
        """
        filename = f"{self.bucket_path}/{destiny_name}"

        try:
            self.s3_client.upload_file(
                file_path,
                self.bucket_name,
                filename,
                Config=self.config,
                ExtraArgs={"ACL": "public-read", "ContentType": content_type},
            )
        except Exception as exception:
            raise KeepsError(
                detail="The file could not be uploaded. Try again later",
                i18n="file_could_not_be_uploaded",
                status_code=500,
            ) from exception

        response = {"name": filename, "url": f"{self.base_url}/{filename}"}

        return response

    def delete_file(self, file_url: str):
        """
        Delete file from AWS S3
        """
        file_name = file_url.replace(f"{self.base_url}/", "").strip()
        self.s3_client.delete_object(Bucket=self.bucket_name, Key=file_name)
