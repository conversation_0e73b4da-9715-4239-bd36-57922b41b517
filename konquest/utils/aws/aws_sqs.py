import base64
import json

import boto3
from config import settings


class AWSSQS:
    def __init__(self):
        self.bucket_name = settings.AWS_BUCKET_NAME
        self.base_url = settings.AWS_BASE_S3_URL

    @staticmethod
    def _get_boto3_instance():
        sqs = boto3.client(
            "sqs",
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name="sa-east-1",
        )
        return sqs

    def send_message(self):
        sqs = self._get_boto3_instance()

        queue_url = "https://sqs.sa-east-1.amazonaws.com/503825601340/konquest-mission-check"

        message = {"test": "test"}
        message_string = json.dumps(message)
        byte_message = base64.b64encode(message_string.encode("utf-8"))
        base64_json_string = byte_message.decode()

        response = sqs.send_message(
            QueueUrl=queue_url,
            DelaySeconds=10,
            MessageAttributes={"mission_id": {"DataType": "String", "StringValue": "123"}},
            MessageBody=(base64_json_string),
        )

        print(response["MessageId"])

    def receive_message(self):
        sqs = self._get_boto3_instance()

        queue_url = "https://sqs.sa-east-1.amazonaws.com/503825601340/konquest-mission-check"
        response = sqs.receive_message(
            QueueUrl=queue_url,
            AttributeNames=["SentTimestamp"],
            MaxNumberOfMessages=1,
            MessageAttributeNames=["All"],
            VisibilityTimeout=0,
            WaitTimeSeconds=0,
        )

        message = response["Messages"][0]
        receipt_handle = message["ReceiptHandle"]

        # Delete received message from queue
        sqs.delete_message(QueueUrl=queue_url, ReceiptHandle=receipt_handle)
        print("Received and deleted message: %s" % message)
