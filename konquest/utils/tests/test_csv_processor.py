import unittest
from io import BytesIO

from django.core.files.uploadedfile import InMemoryUploadedFile
from user_activity.dtos.person import Person
from utils.csv_processor import CSVProcessor


class TestCSVProcessor(unittest.TestCase):
    def setUp(self):
        self.processor = CSVProcessor()

    def test_detect_encoding_native_utf8(self):
        file = InMemoryUploadedFile(BytesIO(b"\xef"), None, "teste.txt", "text/plain", len(b"\xef"), None)
        result = self.processor.detect_encoding_native(file)
        self.assertEqual(result, "utf-8")

    def test_detect_encoding_native_utf16(self):
        file = InMemoryUploadedFile(BytesIO(b"\xff"), None, "teste.txt", "text/plain", len(b"\xff"), None)
        result = self.processor.detect_encoding_native(file)
        self.assertEqual(result, "utf-16")

    def test_search_person_name_from_list(self):
        result = self.processor.search_person_name_from_words_list(["151484", "jo@a.x", "<PERSON>"])
        self.assertEqual(result, "<PERSON>")

    def test_extract_emails_and_names_from_csv(self):
        # Simula um arquivo CSV com emails e nomes
        csv_content = "nome,email\nJoão Silva,<EMAIL>\nMaria Oliveira,<EMAIL>"
        file = InMemoryUploadedFile(
            BytesIO(csv_content.encode()), None, "dados.csv", "text/csv", len(csv_content), None
        )
        result = self.processor.extract_emails_and_names_from_csv(file)
        expected_result = [
            Person(email="<EMAIL>", name="Maria Oliveira"),
            Person(email="<EMAIL>", name="João Silva"),
        ]
        for person in expected_result:
            self.assertIn(person, result)
        self.assertCountEqual(expected_result, result)
