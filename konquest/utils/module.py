from config import settings
from injector import Module, provider, singleton
from utils.aws import ComprehendClient


class UtilsModule(Module):
    @provider
    @singleton
    def comprehend_client(self) -> ComprehendClient:
        return ComprehendClient(
            aws_access_key=settings.AWS_COMPREHEND_ACCESS_KEY_ID,
            aws_secret_key=settings.AWS_COMPREHEND_SECRET_ACCESS_KEY,
            aws_region=settings.AWS_COMPREHEND_REGION_NAME,
        )
