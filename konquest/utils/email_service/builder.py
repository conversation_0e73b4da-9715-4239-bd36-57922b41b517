import gettext

import htmlmin
from attr import dataclass
from config import settings
from jinja2 import Environment, FileSystemLoader

INVALID_LANGUAGE = 'invalid language, pass a language already mapped in the "locale" directory'


@dataclass
class Email:
    data: dict
    language: str
    subject: str
    template_name: str
    header_name: str
    footer_name: str
    receivers: list
    workspace_name: str


# pylint: disable=R0903
class Builder:
    def __init__(self, template_dir: str, locale_dir: str = settings.EMAIL_LOCALE_PATH):
        self.__locale_dir = locale_dir
        self.__template_dir = template_dir

    def build_message(self, email: Email) -> tuple:
        locale = self._build_locale(email.language)
        environment = self._build_environment(locale)

        subject = f"{locale.gettext(email.subject)}"
        subject = f"{subject} - {email.workspace_name}" if email.workspace_name else subject
        email.data["workspace_name"] = email.workspace_name
        html = environment.get_template(email.template_name).render(
            email.data,
            kwargs={
                "header_name": email.header_name,
                "footer_name": email.footer_name,
                "date_format": locale.gettext("date_format"),
            },
        )
        return htmlmin.minify(html), subject

    def _build_environment(self, locale) -> Environment:
        environment = Environment(loader=FileSystemLoader(self.__template_dir), extensions=["jinja2.ext.i18n"])
        environment.install_gettext_translations(locale, newstyle=True)

        return environment

    def _build_locale(self, language):
        if not language:
            raise ValueError(INVALID_LANGUAGE)
        locale = gettext.translation("notification", localedir=self.__locale_dir, languages=[language])
        locale.install()
        locale.info()

        return locale
