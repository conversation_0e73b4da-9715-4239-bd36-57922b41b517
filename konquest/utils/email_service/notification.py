from typing import Sequence

from account.models import Workspace
from celery import shared_task
from config import settings
from config.languages_map import languages
from config.settings import DEFAULT_MISSION_VERTICAL_IMAGE
from constants import MISSION_VERTICAL_COVER_IMAGE, PT_BR
from rest_clients.notification_client import MessagePayload, NotificationClient
from utils.email_service.builder import Email
from utils.email_service.di import Container


def get_container():
    return Container()


def _add_default_vertical_image(email_data: dict) -> dict:
    if not email_data.get(MISSION_VERTICAL_COVER_IMAGE):
        email_data[MISSION_VERTICAL_COVER_IMAGE] = DEFAULT_MISSION_VERTICAL_IMAGE
    return email_data


@shared_task(ignore_result=True, acks_late=False)
def notify_users(
    email_data,
    message_key: str,
    workspace_id: str,
    users_receivers: Sequence[dict],
    application_id: str = settings.APPLICATION_ID,
):
    workspace = Workspace.objects.get(id=workspace_id)
    data = email_data.copy()
    data.update(
        {
            "konquest_certificate_web_url": settings.KONQUEST_CERTIFICATES_WEB_URL_WITH_WORKSPACE.format(
                workspace.hash_id
            ),
            "konquest_web_url": settings.KONQUEST_WEB_URL_WITH_WORKSPACE.format(workspace.hash_id),
            "konquest_web_mission_url": settings.KONQUEST_WEB_MISSION_URL_WITH_WORKSPACE.format(workspace.hash_id),
            "workspace_icon": workspace.icon_url if workspace.icon_url else settings.DEFAULT_WORKSPACE_ICON_URL,
            "workspace_name": workspace.name,
        }
    )
    data = _add_default_vertical_image(data)
    message_mappers = {str(settings.APPLICATION_ID): konquest_msg_send}
    messages = []
    print(f"start task to notify users by e-mail: {users_receivers}")
    for user in users_receivers:
        language = _get_user_language(user)
        message = message_mappers[application_id](
            data=data,
            language=language,
            message_key=message_key,
            receivers=[user.get("email")],
            workspace_name=workspace.name,
            workspace_id=workspace_id,
        )
        messages.append(message)
    return {"email_data": email_data, "messages": messages}


def _get_user_language(user: dict) -> str:
    language_account = languages.get(str(user.get("language_id")), PT_BR)
    language_country = language_account.split("-")[1].upper() if len(language_account.split("-")) > 1 else None
    language = (
        f"{language_account.split('-')[0]}-{language_country}" if language_country is not None else language_account
    )
    return language


def konquest_msg_send(
    data: dict, language: str, message_key: str, receivers: list, workspace_name: str, workspace_id: str
):
    subject = f"konquest_{message_key}_subject"
    template = f"konquest_{message_key}.html"
    email = Email(
        data=data,
        subject=subject,
        language=language,
        receivers=receivers,
        template_name=template,
        header_name="email_header_v1.html",
        footer_name="email_footer_v1.html",
        workspace_name=workspace_name,
    )
    container = get_container()
    message, subject = container.builder().build_message(email)

    for receiver in receivers:
        NotificationClient().send_email(
            MessagePayload(
                template=f"konquest_{message_key}",
                data=data,
                receiver_mail=receiver,
                subject=subject,
                workspace_id=workspace_id,
                language=language,
            )
        )

    return message
