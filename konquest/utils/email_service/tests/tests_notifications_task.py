import uuid
from datetime import date, datetime, time
from unittest import mock

from account.models import Workspace
from config import settings
from config.settings import DEFAULT_MISSION_VERTICAL_IMAGE
from django.test import TestCase
from model_mommy import mommy
from utils.email_service import notification
from utils.utils import get_today_date_formatted, get_weekday_by_index

DEFAULT_DATE_FORMAT = "%d/%m/%Y"
DEFAULT_TIME_FORMAT = "%H:%M"


@mock.patch("rest_clients.notification_client.NotificationClient.send_email", return_value={})
class EmailServiceTaskTestCase(TestCase):
    def setUp(self) -> None:
        self.workspace = mommy.make(Workspace)

    # todo: add html strip to extract translation keys to the translation test
    def test_email_konquest_monthly_user_summary(self, mock_send):
        mock_send.return_value = {}
        email_data = {
            "user_name": "User",
            "mission_enrollment": {
                "enrolled": 1,
                "started": 2,
                "completed": 3,
                "give_up": 4,
            },
            "trail_enrollment": {
                "enrolled": 1,
                "started": 2,
                "completed": 3,
                "give_up": 4,
            },
        }
        receivers = [{"email": "<EMAIL>", "language": "en", "email_verified": True}]

        response = notification.notify_users(
            email_data, "monthly_user_summary", self.workspace.id, receivers, settings.APPLICATION_ID
        )

        message = response.get("messages")[0]
        self.check_email_data(email_data, message)

    def test_email_konquest_enrolled_in_group_missions(self, mock_send):
        mock_send.return_value = {}
        email_data = {
            "user_name": "User",
            "missions": [
                {"name": "This is the mission 1", "enrollment_goal_date": datetime.today().date()},
                {"name": "This is the mission 2", "enrollment_goal_date": datetime.today().date()},
                {"name": "This is the mission 3", "enrollment_goal_date": None},
            ],
        }

        receivers = [{"email": "<EMAIL>", "email_verified": True}]

        response = notification.notify_users(
            email_data, "enrolled_in_group_missions", self.workspace.id, receivers, settings.APPLICATION_ID
        )

        # check if variables are inside generated message
        message = response.get("messages")[0]
        self.check_email_data(email_data, message, "%d/%m/%Y")

    def test_email_konquest_new_pulses_and_channels(self, mock_send):
        mock_send.return_value = {}
        generic_image = "https://avatars.discourse-cdn.com/v4/letter/a/e56c9b/90.png"
        email_data = {
            "user_name": "User",
            "pulses": [
                {
                    "name": "O melhor pulse de todos",
                    "content_type_image": generic_image,
                    "link": settings.KONQUEST_WEB_PULSE_DETAIL_URL.format("workspace-hash", uuid.uuid4()),
                },
                {
                    "name": "O melhor pulse de todos 2",
                    "content_type_image": generic_image,
                    "link": settings.KONQUEST_WEB_PULSE_DETAIL_URL.format("workspace-hash", uuid.uuid4()),
                },
                {
                    "name": "O melhor pulse de todos 3",
                    "content_type_image": generic_image,
                    "link": settings.KONQUEST_WEB_PULSE_DETAIL_URL.format("workspace-hash", uuid.uuid4()),
                },
            ],
            "channels": [
                {
                    "name": "O melhor channel",
                    "link": settings.KONQUEST_WEB_CHANNEL_DETAIL_URL.format("workspace-hash", uuid.uuid4()),
                }
            ],
        }

        receivers = [{"email": "<EMAIL>", "language": "en", "email_verified": True}]

        response = notification.notify_users(
            email_data,
            "new_pulses_and_channels",
            self.workspace.id,
            receivers,
            settings.APPLICATION_ID,
        )

        # check if variables are inside generated message
        message = response.get("messages")[0]
        self.check_email_data(email_data, message)

    def test_email_konquest_new_courses(self, mock_send):
        mock_send.return_value = {}
        email_data = {
            "user_name": "Usuario Com Nome Muito Grande",
            "mission_1_name": "Missão Teste 1",
            "mission_1_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
            "mission_2_name": "Missão Teste 2",
            "mission_2_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
            "mission_3_name": "Missão Teste 3",
            "mission_3_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
        }

        receivers = [{"email": "<EMAIL>", "language": "en", "email_verified": True}]

        response = notification.notify_users(
            email_data, "new_missions", self.workspace.id, receivers, settings.APPLICATION_ID
        )

        # check if variables are inside generated message
        message = response.get("messages")[0]
        self.check_email_data(email_data, message)

    def test_email_konquest_mission_enrollment_expired(self, mock_send):
        mock_send.return_value = {}
        email_data = {
            "user_name": "Usuario",
            "mission_name": "Missão Teste 1",
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
            "mission_vertical_cover_image": "https://url_2d.com",
            "expired_days": 2,
            "enrollment_goal_date": datetime.today().date(),
            "enrollment_created_date": datetime.today().date(),
        }
        receivers = [{"email": "<EMAIL>", "email_verified": True}]

        response = notification.notify_users(
            email_data, "mission_enrollment_expired", self.workspace.id, receivers, settings.APPLICATION_ID
        )
        message = response.get("messages")[0]
        self.check_email_data(email_data, message, "%d/%m/%Y")

    def test_email_konquest_mission_enrollment_expiring(self, mock_send):
        mock_send.return_value = {}
        email_data = {
            "user_name": "Usuario",
            "mission_name": "Missão Teste 1",
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
            "mission_vertical_cover_image": "https://url.com",
            "days_to_expire": 0,
            "enrollment_goal_date": datetime.today().date(),
            "enrollment_created_date": datetime.today().date(),
        }

        receivers = [{"email": "<EMAIL>", "language": "pt-BR", "email_verified": True}]

        response = notification.notify_users(
            email_data, "mission_enrollment_expiring", self.workspace.id, receivers, settings.APPLICATION_ID
        )
        message = response.get("messages")[0]
        self.check_email_data(email_data, message, "%d/%m/%Y")

    def test_email_konquest_live_enrollment_accepted(self, mock_send):
        mock_send.return_value = {}
        email_data = {
            "mission_name": "Missão Teste",
            "mission_start_date": date.today(),
            "mission_start_time": datetime.today().time(),
            "mission_seats": 100,
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
            "user_name": "Usuário",
            "mission_vertical_cover_image": None,
        }

        receivers = [{"email": "<EMAIL>", "language": "pt-BR", "email_verified": True}]

        response = notification.notify_users(
            email_data, "live_enrollment_accepted", self.workspace.id, receivers, settings.APPLICATION_ID
        )
        message = response.get("messages")[0]
        self.check_email_data(email_data, message)

    def test_default_mission_vertical_cover_image_must_be_in_email(self, mock_send):
        mock_send.return_value = {}
        email_data = {
            "mission_name": "Missão Teste",
            "mission_start_date": date.today(),
            "mission_start_time": datetime.today().time(),
            "mission_seats": 100,
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
            "user_name": "Usuário",
            "mission_vertical_cover_image": None,
        }

        receivers = [{"email": "<EMAIL>", "language": "pt-BR", "email_verified": True}]

        response = notification.notify_users(
            email_data, "live_enrollment_accepted", self.workspace.id, receivers, settings.APPLICATION_ID
        )
        message = response.get("messages")[0]
        self.check_email_data(email_data, message)
        self.assertIn(DEFAULT_MISSION_VERTICAL_IMAGE, message)

    def test_email_konquest_presential_enrollment_accepted(self, mock_send):
        mock_send.return_value = {}
        email_data = {
            "mission_address": "Rua Moderador",
            "mission_name": "Missão Teste",
            "mission_start_date": date.today(),
            "mission_start_time": datetime.today().time(),
            "mission_seats": 100,
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
            "user_name": "Usuário",
            "mission_vertical_cover_image": "https://url_2d.com",
        }

        receivers = [{"email": "<EMAIL>", "language": "pt-BR", "email_verified": True}]

        response = notification.notify_users(
            email_data, "presential_enrollment_accepted", self.workspace.id, receivers, settings.APPLICATION_ID
        )
        message = response.get("messages")[0]
        self.check_email_data(email_data, message)

    def test_email_konquest_user_enrolled_in_a_presential_mission(self, mock_send):
        mock_send.return_value = {}
        email_data = {
            "mission_address": "Rua Moderador",
            "mission_name": "Missão Teste",
            "mission_start_date": date.today(),
            "mission_start_time": datetime.today().time(),
            "mission_seats": 100,
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
            "user_name": "Usuário",
            "mission_vertical_cover_image": "https://url_2d.com",
        }

        receivers = [{"email": "<EMAIL>", "language": "pt-BR", "email_verified": True}]

        response = notification.notify_users(
            email_data, "user_enrolled_in_a_presential_mission", self.workspace.id, receivers, settings.APPLICATION_ID
        )
        message = response.get("messages")[0]
        self.check_email_data(email_data, message)

    def test_email_new_presential_mission(self, mock_send):
        mock_send.return_value = {}
        email_data = {
            "mission_address": "Rua Moderador",
            "mission_name": "Missão Teste",
            "mission_start_date": date.today(),
            "mission_start_time": datetime.today().time(),
            "mission_seats": 100,
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
            "user_name": "Usuário",
            "mission_vertical_cover_image": "https://url_2d.com",
        }

        receivers = [{"email": "<EMAIL>", "language": "pt-BR", "email_verified": True}]

        response = notification.notify_users(
            email_data, "new_presential_mission", self.workspace.id, receivers, settings.APPLICATION_ID
        )
        message = response.get("messages")[0]
        self.check_email_data(email_data, message)

    def test_email_new_live_mission(self, mock_send):
        mock_send.return_value = {}
        email_data = {
            "mission_name": "Missão Teste",
            "mission_start_date": date.today(),
            "mission_start_time": datetime.today().time(),
            "mission_seats": 100,
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
            "user_name": "Usuário",
            "mission_vertical_cover_image": "https://url_2d.com",
        }

        receivers = [{"email": "<EMAIL>", "language": "pt-BR", "email_verified": True}]

        response = notification.notify_users(
            email_data, "new_live_mission", self.workspace.id, receivers, settings.APPLICATION_ID
        )
        message = response.get("messages")[0]
        self.check_email_data(email_data, message)

    def test_notification_konquest_live_mission_starts_today(self, mock_send):
        mock_send.return_value = {}
        email_data = {
            "mission_url": "https://app.clickup.com/t/3004080/DEV-2040",
            "mission_name": "Missão Teste",
            "mission_start_date": date.today(),
            "mission_start_time": datetime.today().time(),
            "mission_seats": 100,
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
            "user_name": "Usuário",
            "days_remaining": 0,
            "mission_vertical_cover_image": "https://url_2d.com",
        }

        users = [{"email": "emailemail.com", "language": "pt-BR", "email_verified": True}]

        response = notification.notify_users(
            email_data, "live_mission_starts_soon", self.workspace.id, users, settings.APPLICATION_ID
        )
        message = response.get("messages")[0]
        self.check_email_data(email_data, message)

    def test_notification_konquest_live_mission_starts_in_two_days(self, mock_send):
        mock_send.return_value = {}
        email_data = {
            "mission_name": "Missão Teste",
            "mission_start_date": date.today(),
            "mission_start_time": datetime.today().time(),
            "mission_seats": 100,
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
            "user_name": "Usuário",
            "days_remaining": 2,
            "mission_vertical_cover_image": "https://url_2d.com",
        }

        users = [{"email": "emailemail.com", "language": "pt-BR", "email_verified": True}]

        response = notification.notify_users(
            email_data, "live_mission_starts_soon", self.workspace.id, users, settings.APPLICATION_ID
        )
        message = response.get("messages")[0]
        self.check_email_data(email_data, message)

    def test_notification_konquest_presential_mission_starts_today(self, mock_send):
        mock_send.return_value = {}
        email_data = {
            "mission_address": "Rua Moderador",
            "mission_name": "Missão Teste",
            "mission_start_date": date.today(),
            "mission_start_time": datetime.today().time(),
            "mission_seats": 100,
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
            "user_name": "Usuário",
            "days_remaining": 0,
            "mission_vertical_cover_image": "https://url_2d.com",
        }

        users = [{"email": "emailemail.com", "language": "pt-BR", "email_verified": True}]

        response = notification.notify_users(
            email_data, "presential_mission_starts_soon", self.workspace.id, users, settings.APPLICATION_ID
        )
        message = response.get("messages")[0]
        self.check_email_data(email_data, message)

    def test_notification_konquest_presential_mission_starts_in_two_days(self, mock_send):
        mock_send.return_value = {}
        email_data = {
            "mission_address": "Rua Moderador",
            "mission_name": "Missão Teste",
            "mission_start_date": date.today(),
            "mission_start_time": datetime.today().time(),
            "mission_seats": 100,
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
            "user_name": "Usuário",
            "days_remaining": 2,
            "mission_vertical_cover_image": "https://url_2d.com",
        }

        users = [{"email": "emailemail.com", "language": "pt-BR", "email_verified": True}]

        response = notification.notify_users(
            email_data, "presential_mission_starts_soon", self.workspace.id, users, settings.APPLICATION_ID
        )
        message = response.get("messages")[0]
        self.check_email_data(email_data, message)

    def check_email_data(
        self, email_data, message, date_format=DEFAULT_DATE_FORMAT, time_format=DEFAULT_TIME_FORMAT
    ) -> None:
        # check if the values are inside generated message
        for data_key in email_data:
            data = email_data.get(data_key)
            if not data:
                continue
            if isinstance(data, date):
                data = data.strftime(date_format)
                self.assertIn(str(data), message)
            if isinstance(data, time):
                data = data.strftime(time_format)
                self.assertIn(str(data), message)
            elif isinstance(data, dict):
                self.check_email_data(data, message, date_format)
            elif isinstance(data, list):
                for sub_data in data:
                    self.check_email_data(sub_data, message, date_format)
            else:
                self.assertIn(str(data), message)
        self.assertNotIn("%(", message)

    def test_notification_konquest_new_enrollment_to_review(self, mock_send):
        """ """
        mock_send.return_value = {}
        email_data = {
            "user_email": "<EMAIL>",
            "mission": "Missão Teste",
            "company": "Keeps Learning",
        }

        users = [
            {"email": "<EMAIL>", "language": "en", "email_verified": True},
            {"email": "<EMAIL>", "language": "pt-BR", "email_verified": True},
        ]

        response = notification.notify_users(
            email_data, "new_enrollment_to_review", self.workspace.id, users, settings.APPLICATION_ID
        )

        self.assertEqual(len(response.get("messages")), 2)

        # check if variables are inside generated message
        message = response.get("messages")[0]
        self.check_email_data(email_data, message)

    def test_notification_konquest_new_mission_to_manage(self, mock_send):
        """ """
        mock_send.return_value = {}
        email_data = {
            "user_name": "New User Creator",
            "mission_name": "The NEW MISSION",
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
        }

        users = [
            {"email": "<EMAIL>", "language_id": "8b58683a-0e0b-4c5c-8fbc-5f07d043a365", "email_verified": True}
        ]
        response = notification.notify_users(
            email_data, "new_mission_to_manage", self.workspace.id, users, settings.APPLICATION_ID
        )

        message = response.get("messages")[0]
        self.check_email_data(email_data, message)

    def test_notification_konquest_new_channel_to_manage(self, mock_send):
        """ """
        mock_send.return_value = {}
        email_data = {
            "user_name": "New User Creator",
            "channel_name": "The NEW MISSION",
            "channel_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
        }

        users = [
            {"email": "<EMAIL>", "language_id": "8b58683a-0e0b-4c5c-8fbc-5f07d043a365", "email_verified": True}
        ]

        response = notification.notify_users(
            email_data, "new_channel_to_manage", self.workspace.id, users, settings.APPLICATION_ID
        )
        message = response.get("messages")[0]
        self.check_email_data(email_data, message)

    def test_notification_konquest_mission_enrollment_restarted(self, mock_send):
        """ """
        mock_send.return_value = {}
        email_data = {
            "user_name": "User Name",
            "mission_name": "nome da missão",
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
            "minimum_performance": 0,
        }

        users = [
            {"email": "<EMAIL>", "language_id": "0319c298-c4cf-4c48-82b2-0fd4808a3d07", "email_verified": True}
        ]

        response = notification.notify_users(
            email_data, "mission_enrollment_restarted", self.workspace.id, users, settings.APPLICATION_ID
        )
        message = response.get("messages")[0]
        self.check_email_data(email_data, message)

    def test_notification_mission_minimum_performance_updated(self, mock_send):
        """ """
        mock_send.return_value = {}
        email_data = {
            "user_name": "User Name",
            "mission_name": "nome da missão",
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
            "minimum_performance": 0.7,
        }

        users = [
            {"email": "<EMAIL>", "language_id": "8b58683a-0e0b-4c5c-8fbc-5f07d043a365", "email_verified": True}
        ]

        response = notification.notify_users(
            email_data, "mission_minimum_performance_updated", self.workspace.id, users, settings.APPLICATION_ID
        )
        message = response.get("messages")[0]
        self.check_email_data(email_data, message)

    def test_notification_learning_trail_enrollment_expiring(self, mock_send):
        mock_send.return_value = {}
        email_data = {
            "user_name": "Usuario",
            "learning_trail_name": "Trilha Teste 1",
            "learning_trail_link": settings.KONQUEST_WEB_LEARNING_TRAIL_DETAIL_URL.format(
                self.workspace.hash_id, uuid.uuid4()
            ),
            "learning_trail_vertical_cover_image": "https://url.com",
            "days_to_expire": 0,
            "enrollment_goal_date": datetime.today().date(),
            "enrollment_created_date": datetime.today().date(),
        }

        receivers = [
            {"email": "<EMAIL>", "language_id": "8b58683a-0e0b-4c5c-8fbc-5f07d043a365", "email_verified": True}
        ]

        response = notification.notify_users(
            email_data, "learning_trail_enrollment_expiring", self.workspace.id, receivers, settings.APPLICATION_ID
        )
        message = response.get("messages")[0]
        self.check_email_data(email_data, message, "%d/%m/%Y")

    def test_user_enrolled_in_a_trail_notification(self, mock_send):
        mock_send.return_value = {}
        email_data = {
            "user_name": "User Name",
            "learning_trail_name": "nome da trilha",
            "learning_trail_link": settings.KONQUEST_WEB_LEARNING_TRAIL_DETAIL_URL.format(
                self.workspace.hash_id, uuid.uuid4()
            ),
        }

        users = [
            {"email": "<EMAIL>", "language_id": "8b58683a-0e0b-4c5c-8fbc-5f07d043a365", "email_verified": True}
        ]

        response = notification.notify_users(
            email_data, "user_enrolled_in_a_trail", self.workspace.id, users, settings.APPLICATION_ID
        )
        message = response.get("messages")[0]
        self.check_email_data(email_data, message)

    def test_mission_trail_disabled_notification(self, mock_send):
        mock_send.return_value = {}
        today_date = datetime.today().date()
        email_data = {
            "trail_name": "nome da trilha",
            "mission_name": "Nome da missão",
            "now_date": get_today_date_formatted("america/sao_paulo"),
            "now_week_day": get_weekday_by_index(today_date.weekday()),
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
        }

        users = [
            {"email": "<EMAIL>", "language_id": "ea636f50-fdc4-49b0-b2de-9e5905de456b", "email_verified": True}
        ]

        response = notification.notify_users(
            email_data, "mission_trail_disabled", self.workspace.id, users, settings.APPLICATION_ID
        )
        message = response.get("messages")[0]
        self.check_email_data(email_data, message)
