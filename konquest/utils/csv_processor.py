import re
from typing import List

import spacy
from django.core.files.uploadedfile import InMemoryUploadedFile
from user_activity.dtos.person import Person


class CSVProcessor:
    def __init__(self):
        self.header_names = {"name": ["nome", "name"], "email": ["email", "e-mail"]}

    def detect_encoding_native(self, file: InMemoryUploadedFile) -> str:
        first_byte = file.read(1)
        if first_byte == b"\xef":
            return "utf-8"
        elif first_byte == b"\xff" or first_byte == b"\xfe":
            return "utf-16"
        return "utf-8"

    def search_person_name_from_words_list(self, list: List[str]) -> str:
        nlp = spacy.load("pt_core_news_sm")
        for word in list:
            doc = nlp(word)
            for ent in doc.ents:
                if ent.label_ == "PER":
                    return word

    def check_header_get_index(self, row: List[str]) -> dict:
        header_index = {}
        row_list = [text.lower().strip() for text in row.split(",")]
        for key, values in self.header_names.items():
            for value in values:
                if key in header_index:
                    continue
                if value in row_list:
                    header_index[key] = row_list.index(value)
        return header_index

    def extract_emails_and_names_from_csv(self, file: InMemoryUploadedFile) -> List[Person]:
        encoding = self.detect_encoding_native(file)
        file.seek(0)
        csv_read = file.read().decode(encoding)
        accounts = set()
        email_regex = re.compile(r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b")
        index_name = None
        header_map = {}
        for row in csv_read.splitlines():
            if not header_map:
                header_map = self.check_header_get_index(row)
            if re.search(email_regex, row):
                match = email_regex.search(row)
                email = match.group().lower()
                row_split = row.split(",")
                if not header_map:
                    name = self.search_person_name_from_words_list(row_split)
                else:
                    name = row_split[header_map.get("name")]
                if email in str(accounts):
                    continue
                if name:
                    name, email = (name, email)
                    index_name = row_split.index(name)
                elif index_name:
                    name, email = (row_split[index_name], email)
                else:
                    name, email = (email, email)
                accounts.add((name, email))
        return [Person(email=email, name=name) for name, email in accounts]
