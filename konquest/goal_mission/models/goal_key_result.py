import uuid

from django.db import models

from goal_mission.models.goal_key_result_type import GoalKeyResultType
from goal_mission.models.mission_goal import MissionGoal
from utils.models import BaseModel


class GoalKeyResult(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(verbose_name="Name", max_length=200)
    description = models.TextField(verbose_name="Description", null=True, blank=True)

    mission_goal = models.ForeignKey(MissionGoal, verbose_name="Mission Goal", on_delete=models.PROTECT)
    goal_key_result_type = models.ForeignKey(
        GoalKeyResultType, verbose_name="Goal Key Result Type", on_delete=models.PROTECT
    )
    initial_value = models.FloatField(verbose_name="Initial Value")
    goal_value = models.FloatField(verbose_name="Goal Value")
    goal_hit = models.CharField(verbose_name="Goal Hit", max_length=100)

    class Meta:
        verbose_name_plural = "Goal Key Result"
        db_table = "goal_key_result"
