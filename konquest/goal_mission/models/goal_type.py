import uuid

from django.db import models

from utils.models import BaseModel


class GoalType(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(verbose_name="Name", max_length=200)
    description = models.TextField(verbose_name="Description", null=True, blank=True)

    class Meta:
        verbose_name_plural = "Goal Types"
        db_table = "goal_type"
