# -*- coding: utf-8 -*-

from django_filters.rest_framework import DjangoFilterB<PERSON><PERSON>
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter

from authentication.keeps_permissions import IsAuthenticatedWithoutXClient
from goal_mission.models.goal_key_result import GoalKeyResult
from goal_mission.serializers.goal_key_result_serializer import GoalKeyResultSerializer


class GoalKeyResultViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    permission_classes = (IsAuthenticatedWithoutXClient,)

    def get_queryset(self):
        return GoalKeyResult.objects.all()

    def get_serializer_class(self):
        return GoalKeyResultSerializer
