# -*- coding: utf-8 -*-

from django_filters.rest_framework import DjangoFilter<PERSON><PERSON><PERSON>
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter

from authentication.keeps_permissions import IsAuthenticatedWithoutXClient
from goal_mission.models.goal_type import GoalType
from goal_mission.serializers.goal_type_serializer import GoalTypeSerializer


class GoalTypeViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)

    permission_classes = (IsAuthenticatedWithoutXClient,)

    def get_queryset(self):
        return GoalType.objects.all()

    def get_serializer_class(self):
        return GoalTypeSerializer
