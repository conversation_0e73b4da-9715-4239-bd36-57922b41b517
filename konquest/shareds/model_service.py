from abc import ABC

from django.db import transaction
from django.db.models import Model
from django.utils.timezone import now
from utils.models import BaseModel


class ModelService(ABC):
    def __init__(self, model: Model):
        self.model = model

    def delete_by_id(self, instance_id: str, **kwargs) -> None:
        instance = self.model.objects.get(id=instance_id)
        self.delete_instance(instance)

    @staticmethod
    @transaction.atomic()
    def delete_instance(instance: BaseModel) -> None:
        instance.deleted = True
        instance.delete_date = now()
        instance.save()
