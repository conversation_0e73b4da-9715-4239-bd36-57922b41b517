import uuid

import mock
from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from account.models import User
from achievement.models import Achievement, AchievementClass, AchievementType


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class AchievementViewsetTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = mommy.make(User, id=uuid.uuid4())
        self.user_2 = mommy.make(User, id=uuid.uuid4())
        self.client.force_authenticate(user={"sub": self.user.id})

        self.achievement_type = mommy.make(AchievementType, id=uuid.uuid4(), name="Type")
        self.achievement_type_2 = mommy.make(AchievementType, id=uuid.uuid4())

        self.achievement_class = mommy.make(AchievementClass, id=uuid.uuid4(), name="Class")
        self.achievement_class2 = mommy.make(AchievementClass, id=uuid.uuid4())

        self.achievement = mommy.make(
            Achievement,
            id=uuid.uuid4(),
            achievement_type=self.achievement_type,
            achievement_class=self.achievement_class,
            user=self.user,
        )
        self.achievement_2 = mommy.make(
            Achievement,
            id=uuid.uuid4(),
            achievement_type=self.achievement_type_2,
            achievement_class=self.achievement_class2,
            user=self.user_2,
        )

        self.headers = {}
        self.url = reverse("achievements-list")

    def test_achievement_list(self, mock_return, mock_role):
        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

    def test_achievement_list_filter_by_type(self, mock_return, mock_role):
        response = self.client.get(
            self.url + "?achievement_type=" + str(self.achievement_type.id), **self.headers, format="json"
        ).json()
        self.assertEqual(len(response["results"]), 1)

    def test_achievement_list_filter_by_user(self, mock_return, mock_role):
        response = self.client.get(self.url + "?user=" + str(self.user.id), **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

    def test_achievement_get_success(self, mock_return, mock_role):
        response = self.client.get(
            reverse("achievements-detail", args=[str(self.achievement.id)]), **self.headers, format="json"
        )
        response_json = response.json()
        self.assertEqual(response_json["id"], str(self.achievement.id))
        self.assertEqual(response.status_code, 200)

    def test_achievement_get_not_found(self, mock_return, mock_role):
        response = self.client.get(
            reverse("achievements-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "No Achievement matches the given query.")
        self.assertEqual(response.status_code, 404)

    def test_achievement_post_success(self, mock_return, mock_role):
        data = {
            "user": str(self.user.id),
            "achievement_type": str(self.achievement_type_2.id),
            "achievement_class": str(self.achievement_class.id),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)

    def test_achievement_post_required_field(self, mock_return, mock_role):
        data = {"user": str(self.user.id)}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response_json["achievement_type"], ["This field is required."])
        self.assertEqual(response.status_code, 400)

    def test_achievement_post_invalid_foreign_key(self, mock_return, mock_role):
        data = {"user": str(self.user.id), "achievement_type": str(uuid.uuid4())}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertIsNotNone(response_json.get("achievement_type"))
        self.assertEqual(response.status_code, 400)

    def test_achievement_patch_success(self, mock_return, mock_role):
        data = {"achievement_type": str(self.achievement_type.id)}

        response = self.client.patch(
            reverse("achievements-detail", args=[str(self.achievement.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["achievement_type"], data["achievement_type"])
        self.assertEqual(response.status_code, 200)

    def test_achievement_put_success(self, mock_return, mock_role):
        data = {
            "user": str(self.user.id),
            "achievement_type": str(self.achievement_type.id),
            "achievement_class": str(self.achievement_class.id),
        }

        response = self.client.put(
            reverse("achievements-detail", args=[str(self.achievement.id)]), **self.headers, data=data, format="json"
        )

        response_json = response.json()
        self.assertEqual(response_json["achievement_type"], data["achievement_type"])
        self.assertEqual(response.status_code, 200)

    def test_achievement_delete_success(self, mock_return, mock_role):
        response = self.client.delete(
            reverse("achievements-detail", args=[str(self.achievement.id)]), **self.headers, format="json"
        )
        self.assertEqual(response.status_code, 204)

    def test_achievement_delete_not_found(self, mock_return, mock_role):
        response = self.client.delete(
            reverse("achievements-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "No Achievement matches the given query.")
        self.assertEqual(response.status_code, 404)
