import uuid

import mock
from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from account.models import User
from achievement.models import AchievementClass


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class AchievementClassViewsetTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = mommy.make(User, id=uuid.uuid4())

        self.achievement_type = mommy.make(AchievementClass, id=uuid.uuid4(), name="Type")
        self.achievement_type_2 = mommy.make(AchievementClass, id=uuid.uuid4())

        self.headers = {}
        self.url = reverse("achievements-classes-list")

    def test_achievement_class_list(self, mock_return, mock_role):
        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 2)

    def test_achievement_class_list_search_by_name(self, mock_return, mock_role):
        response = self.client.get(self.url + "?search=Type", **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

    def test_achievement_class_get_success(self, mock_return, mock_role):
        response = self.client.get(
            reverse("achievement-classes-detail", args=[str(self.achievement_type.id)]), **self.headers, format="json"
        )
        response_json = response.json()
        self.assertEqual(response_json["id"], str(self.achievement_type.id))
        self.assertEqual(response.status_code, 200)

    def test_achievement_class_get_not_found(self, mock_return, mock_role):
        response = self.client.get(
            reverse("achievement-classes-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()
        self.assertEqual(response_json["detail"], "No AchievementClass matches the given query.")
        self.assertEqual(response.status_code, 404)
