import uuid

from django.db import models

from utils.models import BaseModel


class AchievementClass(BaseModel):
    """
    Setting achievement class.

    Samples:
    - finish mission
    - create mission
    - hours in mission
    - days in mission
    - answer question
    - goal hit
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(verbose_name="Name", max_length=200)
    description = models.TextField(verbose_name="Description", null=True, blank=True)

    class Meta:
        verbose_name_plural = "Achievement Class"
        db_table = "achievement_class"
