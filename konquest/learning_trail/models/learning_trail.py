import uuid

from django.db import models

from account.models import User
from account.models.language_enum import LanguageEnum
from learning_trail.models.learning_trail_type import LearningTrailType
from utils.models import BaseModel


class LearningTrail(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    name = models.CharField(verbose_name="Name", max_length=200)
    description = models.TextField(verbose_name="Description", null=True, blank=True)
    holder_image = models.URLField(verbose_name="Holder Image", null=True, blank=True)
    thumb_image = models.URLField(verbose_name="Thumb Image", null=True, blank=True)

    duration_time = models.FloatField(verbose_name="Duration Time", default=0, null=True)
    points = models.PositiveIntegerField(verbose_name="Points", default=0, null=True)
    is_active = models.BooleanField(verbose_name="Is Active?", default=True)

    learning_trail_type = models.ForeignKey(
        LearningTrailType, verbose_name="Learning Trail Type", on_delete=models.PROTECT
    )
    language = models.CharField(
        verbose_name="Learning Trail", max_length=20, choices=LanguageEnum.choices(), default="pt-BR"
    )

    user_creator = models.ForeignKey(User, verbose_name="User Creator", on_delete=models.PROTECT)

    expiration_date = models.DateField(verbose_name="Expiration Date", null=True)

    class Meta:
        verbose_name_plural = "Learning Trails"
        db_table = "learning_trail"
