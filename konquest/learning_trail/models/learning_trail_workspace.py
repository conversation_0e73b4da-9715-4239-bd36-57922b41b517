import uuid

from django.db import models

from account.models import Workspace
from learning_trail.models import LearningTrail
from utils.models import BaseModel

RELATIONSHIP_TYPE = (("OWNER", "OWNER"), ("SHARED", "SHARED"))


class LearningTrailWorkspace(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    learning_trail = models.ForeignKey(LearningTrail, verbose_name="Learning Trail", on_delete=models.CASCADE)
    workspace = models.ForeignKey(Workspace, verbose_name="Workspace", on_delete=models.PROTECT)
    relationship_type = models.Char<PERSON><PERSON>(
        verbose_name="Development Status", choices=RELATIONSHIP_TYPE, default="OWNER", max_length=8
    )

    class Meta:
        verbose_name_plural = "Learning Trails Workspaces"
        unique_together = ("learning_trail", "workspace")
        db_table = "learning_trail_workspace"
