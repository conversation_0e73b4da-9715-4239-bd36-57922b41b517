from datetime import datetime

import pytz
from django.forms import model_to_dict
from django.utils.translation import gettext_noop

from config import settings
from learning_trail.models import LearningTrail
from learning_trail.services.abstracts.mission_trail_updated_status_notification_service import (
    MissionTrailUpdatedStatusNotificationService,
)
from mission.models import Mission
from notification.dtos.message_v2 import MessageV2
from notification.services.notification_service_v2 import NotificationServiceV2
from utils.email_service.notification import notify_users
from utils.email_service.templates_keys import MISSION_TRAIL_DISABLED
from utils.utils import get_today_date_formatted, get_weekday_by_index, workspace_hash

MISSION_DISABLED = gettext_noop("mission_disabled")
THE_MISSION_X_LINKED_IN_TRAIL_Y_WAS_DISABLED_BY_Z = gettext_noop("the_mission_x_linked_in_trail_y_was_disabled_by_z")
THE_MISSION_X_LINKED_IN_TRAIL_Y_WAS_DISABLED = gettext_noop("the_mission_x_linked_in_trail_y_was_disabled")


class MissionTrailDisabledNotificationService(MissionTrailUpdatedStatusNotificationService):
    def __init__(self, notification_service: NotificationServiceV2):
        super().__init__(notification_service, "MISSION_TRAIL_DISABLED")

    @staticmethod
    def _get_message(mission_name: str, trail_name: str, modifier_user_name: str) -> MessageV2:
        if modifier_user_name:
            return MessageV2(
                title=MISSION_DISABLED,
                description=THE_MISSION_X_LINKED_IN_TRAIL_Y_WAS_DISABLED_BY_Z,
                description_values={
                    "mission_name": mission_name,
                    "trail_name": trail_name,
                    "user_name": modifier_user_name,
                },
            )
        return MessageV2(
            title=MISSION_DISABLED,
            description=THE_MISSION_X_LINKED_IN_TRAIL_Y_WAS_DISABLED,
            description_values={"mission_name": mission_name, "trail_name": trail_name},
        )

    @staticmethod
    def _send_email(trail: LearningTrail, mission: Mission, workspace_id: str):
        receiver = trail.user_creator
        today = datetime.today().astimezone(tz=pytz.timezone(receiver.time_zone))
        email_data = {
            "trail_name": trail.name,
            "mission_name": mission.name,
            "user_name": receiver.name,
            "now_date": get_today_date_formatted(receiver.time_zone),
            "now_week_day": get_weekday_by_index(today.date().weekday()),
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
                workspace_hash(str(workspace_id)), mission.id
            ),
        }

        notify_users.delay(
            email_data=email_data,
            message_key=MISSION_TRAIL_DISABLED,
            workspace_id=workspace_id,
            users_receivers=[model_to_dict(receiver)],
        )
