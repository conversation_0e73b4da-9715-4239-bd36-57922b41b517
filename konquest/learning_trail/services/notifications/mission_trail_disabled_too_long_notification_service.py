from django.db.models import Q
from django.utils.timezone import now
from django.utils.translation import gettext_noop

from constants import ACTION_LIST
from learning_trail.models import LearningTrail, LearningTrailStep
from mission.models import Mission
from mission.models.mission_workspace import OWNER
from notification.dtos.message_v2 import MessageV2
from notification.services.notification_service_v2 import NotificationServiceV2

MISSION_DISABLED = gettext_noop("mission_disabled")
THE_MISSION_X_LINKED_IN_TRAIL_Y_IS_DISABLED_FOR_Z_DAYS = gettext_noop(
    "the_mission_x_linked_in_trail_y_is_disabled_for_z_days"
)


class MissionTrailDisabledTooLongNotificationService:
    def __init__(self, notification_service: NotificationServiceV2):
        self._notification_service = notification_service

    def send(self, mission: Mission):
        disabled_days = abs(mission.updated_date.date() - now().date()).days
        trail_ids = LearningTrailStep.objects.filter(
            Q(mission_id=mission.id), ~Q(learning_trail__user_creator_id=mission.user_creator_id)
        ).values_list("learning_trail_id", flat=True)
        trails = LearningTrail.objects.filter(id__in=trail_ids)
        users_notified = []
        for trail in trails:
            if trail.user_creator_id in users_notified:
                continue
            self._send_by_trail(trail, mission, disabled_days)
            users_notified.append(trail.user_creator_id)

    def _send_by_trail(self, trail: LearningTrail, mission: Mission, days: int):
        trail_workspace = trail.learningtrailworkspace_set.filter(relationship_type=OWNER).first()
        if not trail_workspace:
            return
        self._send_notification(trail, mission, days, trail_workspace.workspace_id)

    @staticmethod
    def _get_message(mission_name: str, trail_name: str, days: int) -> MessageV2:
        return MessageV2(
            title=MISSION_DISABLED,
            description=THE_MISSION_X_LINKED_IN_TRAIL_Y_IS_DISABLED_FOR_Z_DAYS,
            description_values={
                "mission_name": mission_name,
                "trail_name": trail_name,
                "days": days,
            },
        )

    def _send_notification(self, trail: LearningTrail, mission: Mission, days: int, workspace_id: str):
        message = self._get_message(mission.name, trail.name, days)
        self._notification_service.create_notification(
            user_ids=[str(trail.user_creator_id)],
            workspace_id=workspace_id,
            type_key="MISSION_TRAIL_DISABLED_FOR_TOO_LONG",
            action=ACTION_LIST,
            message=message,
            object=trail.id,
        )
