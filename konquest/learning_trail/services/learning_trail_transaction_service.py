from injector import inject

from account.models.user import User
from custom.keeps_exception_handler import (
    KeepsNoPermissionToTransferOwnerLearningTrail,
    KeepsUserCreatorIsInactive,
)
from learning_trail.dtos.learning_trail_transaction import LearningTrailTransaction
from learning_trail.models import LearningTrail
from learning_trail.tasks.notifications import notify_transfer_learning_trail_user_owner
from myaccount.application.services.myaccount_service import MyAccountService
from myaccount.domain.repositories.myaccount_respository import UserHasPermissionDTO


class LearningTrailTransactionService:
    @inject
    def __init__(self, myaccount_service: MyAccountService) -> None:
        self.myaccount_service = myaccount_service

    def _has_manager_permission(self, transaction: LearningTrailTransaction) -> bool:
        return self.myaccount_service.has_manager_permission(
            UserHasPermissionDTO(
                workspace_id=transaction.target_workspace_id,
                user_id=transaction.user_id,
            )
        )

    def update_user_creator(
        self,
        learning_trail: LearningTrail,
        transaction: LearningTrailTransaction,
    ) -> LearningTrail:
        transaction.new_user_creator_id = (
            transaction.new_user_creator_id if transaction.new_user_creator_id else transaction.user_id
        )

        if str(learning_trail.user_creator_id) != str(transaction.new_user_creator_id):
            user = User.objects.get(id=transaction.new_user_creator_id)
            if not user.status:
                raise KeepsUserCreatorIsInactive
            learning_trail.user_creator_id = transaction.new_user_creator_id
            learning_trail.save()
            return learning_trail
        return None

    def transfer(self, transaction: LearningTrailTransaction) -> None:
        learning_trail = LearningTrail.objects.get(id=transaction.learning_trail_id)

        if not self._has_manager_permission(transaction):
            raise KeepsNoPermissionToTransferOwnerLearningTrail

        if transaction.new_user_creator_id != transaction.user_id:
            self.update_user_creator(learning_trail, transaction)
            notify_transfer_learning_trail_user_owner.delay(learning_trail.id)
