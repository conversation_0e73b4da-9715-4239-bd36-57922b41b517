from django.db.models import Sum
from rest_framework import serializers

from account.models.user import User
from account.models.workspace import Workspace
from account.serializers.user_serializer import UserSerializer
from custom.keeps_serializer import KPSerializer
from learning_trail.models import LearningTrail, LearningTrailStep, LearningTrailType, LearningTrailWorkspace
from mission.serializers.mission_serializer import MissionListSerializer
from pulse.models import Pulse
from user_activity.models import LearningTrailEnrollment


class LearningTrailTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = LearningTrailType
        fields = "__all__"


class LearningTrailEnrollmentShortSerializer(serializers.ModelSerializer):
    class Meta:
        model = LearningTrailEnrollment
        fields = (
            "id",
            "points",
            "start_date",
            "end_date",
            "goal_date",
            "give_up",
            "give_up_comment",
            "status",
            "performance",
            "progress",
            "required",
            "certificate_url",
        )


class LearningTrailShortSerializer(serializers.ModelSerializer):
    user_creator = UserSerializer()
    learning_trail_type = LearningTrailTypeSerializer()
    count_missions = serializers.SerializerMethodField()
    count_pulses = serializers.SerializerMethodField()
    users_enrolled = serializers.SerializerMethodField()
    users_finished = serializers.SerializerMethodField()
    is_owner = serializers.SerializerMethodField()

    @staticmethod
    def get_users_finished(obj):
        """
        Count users are completed the Learning Trail
        """
        return obj.learningtrailenrollment_set.filter(status="COMPLETED").count()

    @staticmethod
    def get_users_enrolled(obj):
        """
        Count users are enrolled in the Learning Trail
        """
        return obj.learningtrailenrollment_set.count()

    @staticmethod
    def get_count_missions(obj):
        """
        Count mission in Learning Trail
        """
        return obj.learningtrailstep_set.filter(mission_id__isnull=False).count()

    @staticmethod
    def get_count_pulses(obj):
        """
        Count pulses in Learning Trail
        """
        return obj.learningtrailstep_set.filter(pulse_id__isnull=False).count()

    def get_is_owner(self, obj):
        """
        Check if user logged is owner
        """
        user = self.context["request"].user

        return str(obj.user_creator.id) == user["sub"]

    class Meta:
        model = LearningTrail
        fields = "__all__"


class LearningTrailShortOptimizedSerializer(serializers.ModelSerializer):
    # Joins
    user_creator = UserSerializer()
    learning_trail_type = LearningTrailTypeSerializer()
    is_owner = serializers.SerializerMethodField()

    # Extras
    count_missions = serializers.ReadOnlyField()
    count_pulses = serializers.ReadOnlyField()
    users_enrolled = serializers.ReadOnlyField()
    users_finished = serializers.ReadOnlyField()

    def get_is_owner(self, obj):
        """
        Check if user logged is owner
        """
        user = self.context["request"].user

        return str(obj.user_creator.id) == user["sub"]

    class Meta:
        model = LearningTrail
        fields = "__all__"


class LearningTrailListOptimizedSerializer(LearningTrailShortOptimizedSerializer):
    class Meta:
        model = LearningTrail
        fields = "__all__"


class LearningTrailListSerializer(LearningTrailShortSerializer):
    enrolled = serializers.SerializerMethodField()
    enrollment = serializers.SerializerMethodField()

    def get_enrollment(self, obj):
        """
        Get enrollment info if user logged is enrolled in this mission
        """
        user = self.context["request"].user
        lts_enrollments = obj.learningtrailenrollment_set.filter(user_id=user["sub"]).first()
        return LearningTrailEnrollmentShortSerializer(lts_enrollments).data

    def get_enrolled(self, obj):
        """
        Check if user logged is enrolled in this Learning Trail
        """
        user = self.context["request"].user
        return obj.learningtrailenrollment_set.filter(user_id=user["sub"]).exists()

    class Meta:
        model = LearningTrail
        fields = "__all__"


class StepLearningTrailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Pulse
        fields = (
            "id",
            "name",
        )


class StepPulseSerializer(serializers.ModelSerializer):
    consumed = serializers.SerializerMethodField()
    consume_time_in = serializers.SerializerMethodField()
    is_active = serializers.SerializerMethodField()

    def get_consume_time_in(self, obj):
        user_id = self.context["request"].user["sub"]
        sum_consume = obj.learncontentactivity_set.filter(user_id=user_id).aggregate(Sum("time_in"))["time_in__sum"]

        return sum_consume

    def get_consumed(self, obj):
        user_id = self.context["request"].user["sub"]
        consumed = obj.learncontentactivity_set.filter(user_id=user_id).exists()

        return consumed

    @staticmethod
    def get_is_active(obj) -> bool:
        pulse_channel = obj.pulsechannel_set.first()
        return pulse_channel.channel.is_active if pulse_channel else False

    class Meta:
        model = Pulse
        fields = "__all__"


class LearningTrailStepListSerializer(serializers.ModelSerializer):
    learning_trail = StepLearningTrailSerializer()
    mission = MissionListSerializer(many=False)
    pulse = StepPulseSerializer()

    class Meta:
        model = LearningTrailStep
        fields = "__all__"


class LearningTrailStepSerializer(serializers.ModelSerializer):
    class Meta:
        model = LearningTrailStep
        fields = "__all__"


class LearningTrailDetailSerializer(LearningTrailListSerializer):
    """
    Serializer to retrieve Learning Trail (get one)
    """

    steps = serializers.SerializerMethodField()

    def get_steps(self, obj):
        return LearningTrailStepListSerializer(
            obj.learningtrailstep_set.all().order_by("order"), many=True, context=self.context
        ).data

    class Meta:
        model = LearningTrail
        fields = "__all__"
        depth = 1


class LearningTrailSerializer(serializers.ModelSerializer):
    class Meta:
        model = LearningTrail
        fields = "__all__"


class LearningTrailWorkspaceSerializer(serializers.ModelSerializer):
    class Meta:
        model = LearningTrailWorkspace
        fields = "__all__"


class LearningTrailTransferInputSerializer(KPSerializer):
    target_workspace = serializers.PrimaryKeyRelatedField(
        many=False, queryset=Workspace.objects.filter(), required=False, allow_null=True
    )
    user_creator = serializers.PrimaryKeyRelatedField(many=False, required=False, queryset=User.objects.filter())
