from django.db import transaction
from django.db.models.signals import post_delete, post_save

from config import settings
from learning_trail.models import LearningTrail, LearningTrailStep, LearningTrailWorkspace
from learning_trail.tasks.learning_trail import update_learning_trail_consume_data, update_learning_trails_consume_data
from learning_trail.tasks.notifications import notify_new_trail_to_workspace_users
from mission.models import Mission
from utils.signals import receiver


@receiver([post_save, post_delete], sender=Mission)
def update_mission_observer(sender, instance, **kwargs):
    if instance.development_status != "DONE":
        return
    steps = LearningTrailStep.objects.filter(mission=instance)
    trails = LearningTrail.objects.filter(id__in=steps.values_list("learning_trail_id", flat=True))
    transaction.on_commit(lambda: update_learning_trails_consume_data(trails))


@receiver(post_save, sender=LearningTrailStep)
def update_learning_trail_step_observer(sender, instance, created, **kwargs):
    if created:
        transaction.on_commit(
            lambda: update_learning_trail_consume_data.delay(learning_trail_id=instance.learning_trail.id)
        )


@receiver(post_delete, sender=LearningTrailStep)
def delete_learning_trail_step_observer(sender, instance, **kwargs):
    transaction.on_commit(
        lambda: update_learning_trail_consume_data.delay(learning_trail_id=instance.learning_trail_id)
    )


@receiver(post_save, sender=LearningTrailWorkspace)
def create_new_trail_notification_to_company_users(sender, instance: LearningTrailWorkspace, created, **kwargs):
    is_public_trail = instance.learning_trail.learning_trail_type.name == settings.LEARNING_TRAIL_OPEN_FOR_COMPANY
    if created and is_public_trail:
        transaction.on_commit(lambda: notify_new_trail_to_workspace_users.delay(instance.id))
