import uuid

import mock
from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from account.models import User, Workspace
from config import settings
from learning_trail.models import LearningTrail, LearningTrailType, LearningTrailWorkspace


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info")
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class LearningTrailRecommendationViewsetTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()

        self.user_creator = mommy.make(User, id=uuid.uuid4(), email="<EMAIL>")
        self.user_consumer = mommy.make(User, id=uuid.uuid4())

        self.workspace_keeps = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps")
        self.learning_trail_type_public = mommy.make(LearningTrailType, name=settings.LEARNING_TRAIL_OPEN_FOR_COMPANY)

        self.learning_trail_1 = mommy.make(
            LearningTrail,
            id=uuid.uuid4(),
            name="Learning Trail 1",
            learning_trail_type=self.learning_trail_type_public,
            user_creator=self.user_creator,
            is_active=True,
        )
        self.learning_trail_2 = mommy.make(
            LearningTrail,
            id=uuid.uuid4(),
            name="Learnig Trail 2",
            learning_trail_type=self.learning_trail_type_public,
            user_creator=self.user_creator,
            is_active=True,
        )
        self.learning_trail_3 = mommy.make(
            LearningTrail,
            id=uuid.uuid4(),
            name="Learnig Trail 3",
            learning_trail_type=self.learning_trail_type_public,
            user_creator=self.user_creator,
            is_active=True,
        )

        LearningTrailWorkspace(learning_trail=self.learning_trail_1, workspace=self.workspace_keeps).save()
        LearningTrailWorkspace(learning_trail=self.learning_trail_2, workspace=self.workspace_keeps).save()
        LearningTrailWorkspace(learning_trail=self.learning_trail_3, workspace=self.workspace_keeps).save()

    def test_learning_trail_recommendation_list(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id)}
        )

        url = reverse("learning-trail-recommendation-list")
        response = self.client.get(url, **self.headers, format="json")

        results = response.data.get("results")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(results), 3)
