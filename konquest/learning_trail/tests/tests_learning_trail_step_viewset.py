import uuid
from unittest.mock import patch

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from account.models import User, Workspace
from authentication.keeps_permissions import ADMIN
from config import settings
from group.models import Group
from learning_trail.models import LearningTrail, LearningTrailStep, LearningTrailType, LearningTrailWorkspace
from mission.models import Mission, MissionCategory, MissionType, MissionTypeEnum, MissionWorkspace
from pulse.models import Channel, ChannelType, ChannelTypeEnum, Pulse, PulseChannel

UPDATE_LT_CONSUME_DATA = "learning_trail.tasks.learning_trail.update_learning_trail_consume_data.delay"
ENROLL_USERS_IN_NEW_STEP_MISSION = (
    "user_activity.tasks.learning_trail_enrollment_task.enroll_users_in_new_step_mission.delay"
)
DELETE_MISSION_ENROLLS = "user_activity.tasks.learning_trail_enrollment_task.delete_step_mission_enrollments.delay"


@patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
@patch(ENROLL_USERS_IN_NEW_STEP_MISSION, return_value=None)
@patch(UPDATE_LT_CONSUME_DATA, return_value=None)
class LearningTrailStepViewsetTestCase(TestCase):
    @patch(UPDATE_LT_CONSUME_DATA, return_value={})
    def setUp(self, mock_update_lt_consume_data):
        self.client = APIClient()

        self.user = mommy.make(User, id=uuid.uuid4(), email="<EMAIL>")
        self.admin = mommy.make(User, email="<EMAIL>")

        self.workspace = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps")
        self.workspace_new = mommy.make(Workspace, id=uuid.uuid4(), name="New Workspace")

        self.learning_trail_type_open_for_workspace = mommy.make(
            LearningTrailType, name=settings.LEARNING_TRAIL_OPEN_FOR_COMPANY
        )
        self.learning_trail_type_close_for_workspace = mommy.make(
            LearningTrailType, name=settings.LEARNING_TRAIL_CLOSE_FOR_COMPANY
        )

        self.channel_type_open_for_workspace = mommy.make(ChannelType, id=ChannelTypeEnum.OPEN_FOR_COMPANY.value)

        self.mission_type_open_for_workspace = mommy.make(MissionType, id=MissionTypeEnum.OPEN_FOR_COMPANY.value)
        self.mission_type_close_for_workspace = mommy.make(MissionType, id=MissionTypeEnum.CLOSE_FOR_COMPANY.value)

        self.mission_category = mommy.make(MissionCategory, id=uuid.uuid4())
        self.mission = self.create_mission()

        self.group = mommy.make(Group, id=uuid.uuid4(), name="my group", workspace=self.workspace)

        self.pulse = self.create_pulses()

        self.url = reverse("learning-trail-steps-list")
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})

    def test_learning_step_list(self, mock_update_lt_consume, mock_enroll_users, mock_check_role):
        learning_trail = self.create_learning_trail_steps()

        response = self.client.get(self.url, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["results"][0]["learning_trail"]["id"], str(learning_trail.id))

    def test_admin_can_list_all_steps(self, mock_update_lt_consume, mock_enroll_users, mock_check_role):
        self.client.force_authenticate(
            user={"sub": str(self.admin.id), "client_id": str(self.workspace.id), "role": ADMIN}
        )
        closed_learning_trail = self.create_learning_trail_steps(self.learning_trail_type_close_for_workspace)

        response = self.client.get(self.url, **self.headers, format="json")

        self.assertEqual(response.status_code, 200)
        assert len(response.data["results"]) != 0
        self.assertEqual(response.data["results"][0]["learning_trail"]["id"], str(closed_learning_trail.id))

    def test_learning_trail_step_create(self, mock_update_lt_consume, mock_enroll_users, mock_check_role):
        learning_trail = self.create_learning_trail_steps()

        data = {"learning_trail": learning_trail.id, "mission": self.mission.id, "order": 0}

        response = self.client.post(self.url, data=data, **self.headers)
        self.assertEqual(response.status_code, 201)

    def test_learning_trail_step_create_error_mission_already_link(
        self, mock_update_lt_consume, mock_enroll_users, mock_check_role
    ):
        learning_trail = self.create_learning_trail_steps()
        mommy.make(LearningTrailStep, learning_trail=learning_trail, mission=self.mission)

        data = {"learning_trail": learning_trail.id, "mission": self.mission.id, "order": 1}

        response = self.client.post(self.url, data=data, **self.headers)
        self.assertEqual(response.data.get("i18n"), "mission_already_linked_in_learning_trail")
        self.assertEqual(response.status_code, 400)

    def test_learning_trail_step_create_error_mission_not_found(
        self, mock_update_lt_consume, mock_enroll_users, mock_check_role
    ):
        mission_without_permission = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="New Workspace Mission",
            user_creator=self.user,
            development_status="DONE",
            is_active=True,
            mission_type=self.mission_type_open_for_workspace,
        )
        MissionWorkspace(mission=mission_without_permission, workspace=self.workspace_new).save()

        learning_trail = self.create_learning_trail_steps()

        data = {"learning_trail": learning_trail.id, "mission": mission_without_permission.id, "order": 1}

        response = self.client.post(self.url, data=data, **self.headers)
        self.assertEqual(response.data.get("i18n"), "mission_not_found")
        self.assertEqual(response.status_code, 400)

    def test_learning_trail_step_pulse_create(self, mock_update_lt_consume, mock_enroll_users, mock_check_role):
        learning_trail = self.create_learning_trail_steps()

        data = {"learning_trail": learning_trail.id, "pulse": self.pulse.id, "order": 0}

        response = self.client.post(self.url, data=data, **self.headers)
        self.assertEqual(response.status_code, 201)

    def test_learning_trail_step_pulse_create_error_pulse_and_mission(
        self, mock_update_lt_consume, mock_enroll_users, mock_check_role
    ):
        learning_trail = self.create_learning_trail_steps()

        data = {"learning_trail": learning_trail.id, "pulse": self.pulse.id, "mission": self.mission.id, "order": 0}

        response = self.client.post(self.url, data=data, **self.headers)
        self.assertEqual(response.data.get("i18n"), "learning_trail_step_cannot_have_a_mission_and_a_pulse")
        self.assertEqual(response.status_code, 400)

    def test_learning_trail_step_create_error_pulse_already_link(
        self, mock_update_lt_consume, mock_enroll_users, mock_check_role
    ):
        learning_trail = self.create_learning_trail_steps()
        mommy.make(LearningTrailStep, learning_trail=learning_trail, pulse=self.pulse)

        data = {"learning_trail": learning_trail.id, "pulse": self.pulse.id, "order": 1}

        response = self.client.post(self.url, data=data, **self.headers)
        self.assertEqual(response.data.get("i18n"), "pulse_already_linked_in_learning_trail")
        self.assertEqual(response.status_code, 400)

    def test_learning_trail_step_create_error_pulse_not_found(
        self, mock_update_lt_consume, mock_enroll_users, mock_check_role
    ):
        channel_new = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="My Channel",
            is_active=True,
            workspace=self.workspace_new,
            channel_type=self.channel_type_open_for_workspace,
        )
        pulse_new = mommy.make(Pulse, id=uuid.uuid4(), name="My Pulse", status="DONE")
        PulseChannel(pulse=pulse_new, channel=channel_new).save()

        learning_trail = self.create_learning_trail_steps()

        data = {"learning_trail": learning_trail.id, "pulse": pulse_new.id, "order": 1}

        response = self.client.post(self.url, data=data, **self.headers)
        self.assertEqual(response.data.get("i18n"), "pulse_not_found")
        self.assertEqual(response.status_code, 400)

    @patch(DELETE_MISSION_ENROLLS, return_value=None)
    def test_learning_trail_step_delete(
        self, mock_delete_mission_enrolls, mock_update_lt_consume, mock_enroll_users, mock_check_role
    ):
        learning_trail = self.create_learning_trail_steps()
        learning_trail_step = LearningTrailStep.objects.filter(learning_trail=learning_trail).first()

        url = reverse("learning-trail-steps-detail", args=[str(learning_trail_step.id)])
        response = self.client.delete(url, **self.headers)
        self.assertEqual(response.status_code, 204)

    @patch(DELETE_MISSION_ENROLLS, return_value=None)
    def test_learning_trail_step_reorder(
        self, mock_delete_mission_enrolls, mock_update_lt_consume, mock_enroll_users, mock_check_role
    ):
        learning_trail = self.create_learning_trail_steps()
        pulse = self.create_pulses()
        mommy.make(LearningTrailStep, learning_trail=learning_trail, pulse=pulse)

        steps = LearningTrailStep.objects.filter(learning_trail=learning_trail)
        steps_order = []
        for step in steps:
            steps_order.append({"step_id": str(step.id), "order": int(round(0, 5))})

        url = reverse("learning-trail-steps-reorder", args=[str(learning_trail.id)])
        response = self.client.patch(url, steps_order, **self.headers, format="json")
        print(response.json())
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, steps_order)

    # LOAD DATA
    def create_learning_trail_steps(self, learning_trail_type=None):
        if not learning_trail_type:
            learning_trail_type = self.learning_trail_type_open_for_workspace
        learning_trail = mommy.make(
            LearningTrail,
            id=uuid.uuid4(),
            name="Keeps Open For Workspace Learning Trail",
            learning_trail_type=learning_trail_type,
            user_creator=self.user,
            is_active=True,
        )
        LearningTrailWorkspace(learning_trail=learning_trail, workspace=self.workspace).save()
        mission = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Mission Name",
            user_creator=self.user,
            development_status="DONE",
            is_active=True,
        )
        LearningTrailStep(learning_trail=learning_trail, mission=mission, order=1).save()
        return learning_trail

    def create_mission(self):
        mission_template = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Keeps public mission",
            user_creator=self.user,
            development_status="DONE",
            is_active=True,
        )
        MissionWorkspace(mission=mission_template, workspace=self.workspace).save()

        return mission_template

    def create_pulses(self):
        my_channel = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="My Channel",
            is_active=True,
            workspace=self.workspace,
            channel_type=self.channel_type_open_for_workspace,
        )
        my_pulse = mommy.make(Pulse, id=uuid.uuid4(), name="My Pulse", status="DONE")
        PulseChannel(pulse=my_pulse, channel=my_channel).save()

        return my_pulse
