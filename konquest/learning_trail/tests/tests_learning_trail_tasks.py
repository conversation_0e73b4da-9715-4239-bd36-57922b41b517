import uuid

from django.test import TestCase
from model_mommy import mommy

from account.models import User, Workspace
from config import settings
from learning_trail.models import LearningTrail, LearningTrailStep, LearningTrailType, LearningTrailWorkspace
from learning_trail.tasks.learning_trail import update_learning_trail_consume_data
from mission.models import Mission
from pulse.models import Pulse

DELETE_MISSION_ENROLLMENTS = "user_activity.tasks.learning_trail_enrollment_task.delete_step_mission_enrollments.delay"


class LearningTrailTasksTestCase(TestCase):
    fixtures = [
        "workspace",
        "user",
        "group",
        "learning_trail_type",
        "learning_trail",
        "mission_type",
        "mission_category",
        "channel_type",
        "channel_category",
        "pulse_type",
        "learning_trail_with_closed_steps",
    ]

    def setUp(self):
        self.user = mommy.make(User, id=uuid.uuid4(), email="<EMAIL>")
        self.learning_trail_type_open_for_workspace = mommy.make(
            LearningTrailType, name=settings.LEARNING_TRAIL_OPEN_FOR_COMPANY
        )
        self.workspace = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps")
        self.mission_1_duration = 1000
        self.mission_1_points = 100
        self.pulse_1_duration = 600
        self.pulse_1_points = 1000
        self.closed_trail_id = "3c1844c5-8359-4452-89e0-88c5cfc2f99b"
        self.learning_trail = self.create_learning_trails()

    def test_update_learning_trail_consume_data(self):
        mission = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Mission Name",
            user_creator=self.user,
            development_status="DONE",
            duration_time=self.mission_1_duration,
            points=self.mission_1_points,
            is_active=True,
        )
        LearningTrailStep(learning_trail=self.learning_trail, mission=mission, order=1).save()
        my_pulse = mommy.make(
            Pulse,
            id=uuid.uuid4(),
            name="My Pulse",
            status="DONE",
            duration_time=self.pulse_1_duration,
            points=self.pulse_1_points,
        )
        LearningTrailStep(learning_trail=self.learning_trail, pulse=my_pulse, order=0).save()

        update_learning_trail_consume_data(self.learning_trail.id)
        learning_trail_updated = LearningTrail.objects.get(id=self.learning_trail.id)

        self.assertEqual((self.mission_1_duration + self.pulse_1_duration), learning_trail_updated.duration_time)
        self.assertEqual((self.mission_1_points + self.pulse_1_points), learning_trail_updated.points)

    def test_update_learning_trail_consume_data_update_mission(self):
        mission = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Mission Name",
            user_creator=self.user,
            development_status="DONE",
            duration_time=self.mission_1_duration,
            points=self.mission_1_points,
            is_active=True,
        )
        LearningTrailStep(learning_trail=self.learning_trail, mission=mission, order=1).save()
        my_pulse = mommy.make(
            Pulse,
            id=uuid.uuid4(),
            name="My Pulse",
            status="DONE",
            duration_time=self.pulse_1_duration,
            points=self.pulse_1_points,
        )
        LearningTrailStep(learning_trail=self.learning_trail, pulse=my_pulse, order=0).save()

        new_mission_duration_time = 5000
        mission.duration_time = 5000
        mission.save()

        update_learning_trail_consume_data(self.learning_trail.id)
        learning_trail_updated = LearningTrail.objects.get(id=self.learning_trail.id)

        self.assertEqual((new_mission_duration_time + self.pulse_1_duration), learning_trail_updated.duration_time)
        self.assertEqual((self.mission_1_points + self.pulse_1_points), learning_trail_updated.points)

    def create_learning_trails(self):
        learning_trail = mommy.make(
            LearningTrail,
            id=uuid.uuid4(),
            name="Keeps Open For Workspace Learning Trail",
            learning_trail_type=self.learning_trail_type_open_for_workspace,
            user_creator=self.user,
            is_active=True,
        )
        LearningTrailWorkspace(learning_trail=learning_trail, workspace=self.workspace).save()
        return learning_trail
