import uuid

import mock
from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from account.models import User, Workspace
from config import settings
from learning_trail.models import LearningTrail, LearningTrailStep, LearningTrailType, LearningTrailWorkspace
from mission.models import Mission<PERSON>ategory, MissionType, MissionTypeEnum
from pulse.models import Channel, Pulse, PulseChannel

MOCK_GET_TOKEN_INFO = "authentication.keeps_authentication.KeepsAuthentication._get_token_info"
MOCK_CHECK_ROLE = "authentication.keeps_permissions.KeepsBasePermission._check_role"
MOCK_GET_ROLES = "authentication.keeps_permissions.KeepsBasePermission.get_user_roles"


@mock.patch(MOCK_GET_TOKEN_INFO)
@mock.patch(MOCK_CHECK_ROLE, return_value=True)
class LearningTrailViewsetTestCase(TestCase):
    def setUp(self):
        """
        Workspace: Keeps Test

        Workspace: Bayer Test
        """
        self.client = APIClient()

        self.user_creator = mommy.make(User, id=uuid.uuid4(), email="<EMAIL>")
        self.user_consumer = mommy.make(User, id=uuid.uuid4(), email="<EMAIL>")

        self.workspace_keeps = mommy.make(
            Workspace,
            id=uuid.uuid4(),
            name="Keeps",
            allow_list_public_mission=True,
            allow_list_paid_mission=True,
            allow_create_public_mission=True,
            allow_create_paid_mission=True,
        )

        self.workspace_bayer = mommy.make(
            Workspace,
            id=uuid.uuid4(),
            name="Bayer",
            allow_list_public_mission=True,
            allow_list_paid_mission=True,
            allow_create_public_mission=True,
            allow_create_paid_mission=True,
        )

        self.learning_trail_type_open_for_workspace = mommy.make(
            LearningTrailType, name=settings.LEARNING_TRAIL_OPEN_FOR_COMPANY
        )
        self.learning_trail_type_close_for_workspace = mommy.make(
            LearningTrailType, name=settings.LEARNING_TRAIL_CLOSE_FOR_COMPANY
        )

        self.mission_type_public = mommy.make(MissionType, id=MissionTypeEnum.PUBLIC.value)
        self.mission_type_open_for_workspace = mommy.make(MissionType, id=MissionTypeEnum.OPEN_FOR_COMPANY.value)
        self.mission_type_close_for_workspace = mommy.make(MissionType, id=MissionTypeEnum.CLOSE_FOR_COMPANY.value)
        self.mission_type_paid = mommy.make(MissionType, id=MissionTypeEnum.PAID.value)

        self.mission_category = mommy.make(MissionCategory, id=uuid.uuid4())
        self.mission_category_2 = mommy.make(MissionCategory, id=uuid.uuid4())

        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )
        self.url = reverse("learning-trail-list")

    def test_learning_trail_workspace_keeps_list(self, mock_check_role, mock_token_info):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id)}
        )

        self.create_learning_trails()

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

        _ids = [x["learning_trail_type"].get("id") for x in response["results"]]
        _names = [x["name"] for x in response["results"]]

        self.assertIn(str(self.learning_trail_type_open_for_workspace.id), _ids)

        self.assertIn("Keeps Open For Workspace Learning Trail", _names)

    @mock.patch(MOCK_GET_ROLES)
    def test_learning_trail_workspace_admin_list(self, mock_get_roles, mock_check_role, mock_token_info):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id)}
        )
        mock_get_roles.return_value = ["admin"]
        self.create_learning_trails()

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

        _ids = [x["learning_trail_type"].get("id") for x in response["results"]]
        _names = [x["name"] for x in response["results"]]

        self.assertIn(str(self.learning_trail_type_open_for_workspace.id), _ids)

        self.assertIn("Keeps Open For Workspace Learning Trail", _names)

    def test_learning_trail_post(self, mock_check_role, mock_token_info):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id)}
        )

        data = {
            "name": "Trails",
            "description": "Description",
            "is_active": True,
            "expiration_date": None,
            "learning_trail_type": str(self.learning_trail_type_open_for_workspace.id),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)

    def test_learning_trail_delete(self, mock_check_role, mock_token_info):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )

        learning_trail = self.create_learning_trails()

        url = reverse("learning-trail-detail", args=[str(learning_trail.id)])

        response = self.client.delete(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 204)

    def test_learning_trail_pulse_is_active_for_detail(self, mock_check_role, mock_token_info):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )
        learning_trail = self.create_learning_trails()
        self._create_pulse_step(learning_trail)

        url = reverse("learning-trail-detail", args=[str(learning_trail.id)])

        response = self.client.get(url, **self.headers, format="json")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["steps"][0]["pulse"]["is_active"], True)

    @staticmethod
    def _create_pulse_step(learning_trail):
        channel = mommy.make(Channel)
        pulse = mommy.make(Pulse)
        mommy.make(PulseChannel, pulse=pulse, channel=channel)
        mommy.make(LearningTrailStep, learning_trail=learning_trail, pulse=pulse)

    def test_learning_trail_delete_error_not_found(self, mock_check_role, mock_token_info):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )
        url = reverse("learning-trail-detail", args=[str(uuid.uuid4())])

        response = self.client.delete(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 404)

    def test_learning_trail_path(self, mock_check_role, mock_token_info):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )

        learning_trail = self.create_learning_trails()

        url = reverse("learning-trail-detail", args=[str(learning_trail.id)])

        data = {"name": "learning_trail_updated"}

        response = self.client.patch(url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 200)

    def test_update_trail_user_creator(self, mock_check_role, mock_token_info):
        new_user = mommy.make(User)
        learning_trail = self.create_learning_trails()
        url = reverse("learning-trail-detail", args=[str(learning_trail.id)])
        data = {"user_creator": str(new_user.id)}

        response = self.client.patch(url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["user_creator"], new_user.id)

    def test_learning_trail_path_error_403(self, mock_check_role, mock_token_info):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_consumer.id), "client_id": str(self.workspace_keeps.id)}
        )

        learning_trail = self.create_learning_trails()

        url = reverse("learning-trail-detail", args=[str(learning_trail.id)])

        data = {"name": "learning_trail_updated"}

        response = self.client.patch(url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 403)

    def create_learning_trails(self):
        learning_trail_keeps_open = mommy.make(
            LearningTrail,
            id=uuid.uuid4(),
            name="Keeps Open For Workspace Learning Trail",
            learning_trail_type=self.learning_trail_type_open_for_workspace,
            user_creator=self.user_creator,
            is_active=True,
        )
        LearningTrailWorkspace(learning_trail=learning_trail_keeps_open, workspace=self.workspace_keeps).save()

        learning_trail_keeps_close = mommy.make(
            LearningTrail,
            id=uuid.uuid4(),
            name="Keeps Close For Workspace Learning Trail",
            learning_trail_type=self.learning_trail_type_close_for_workspace,
            user_creator=self.user_creator,
            is_active=True,
        )
        LearningTrailWorkspace(learning_trail=learning_trail_keeps_close, workspace=self.workspace_keeps).save()

        learning_trail_bayer_close = mommy.make(
            LearningTrail,
            id=uuid.uuid4(),
            name="Bayer Close For Workspace Learning Trail",
            learning_trail_type=self.learning_trail_type_open_for_workspace,
            user_creator=self.user_creator,
            is_active=True,
        )
        LearningTrailWorkspace(learning_trail=learning_trail_bayer_close, workspace=self.workspace_bayer).save()

        learning_trail_bayer_open = mommy.make(
            LearningTrail,
            id=uuid.uuid4(),
            name="Bayer Open For Workspace Learning Trail",
            learning_trail_type=self.learning_trail_type_close_for_workspace,
            user_creator=self.user_creator,
            is_active=True,
        )
        LearningTrailWorkspace(learning_trail=learning_trail_bayer_open, workspace=self.workspace_bayer).save()

        return learning_trail_keeps_open

    def test_learning_trail_detail_enrollment_has_required_field(self, mock_check_role, mock_token_info):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace_keeps.id)}
        self.client.force_authenticate(
            user={"sub": str(self.user_creator.id), "client_id": str(self.workspace_keeps.id)}
        )
        learning_trail = self.create_learning_trails()
        self._create_pulse_step(learning_trail)

        url = reverse("learning-trail-detail", args=[str(learning_trail.id)])

        response = self.client.get(url, **self.headers, format="json")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["enrollment"]["required"], False)
