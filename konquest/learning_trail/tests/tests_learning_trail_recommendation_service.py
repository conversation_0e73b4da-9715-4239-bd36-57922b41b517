from unittest import mock

from django.test import TestCase
from model_mommy import mommy

from account.models import User, Workspace
from learning_trail.models import LearningTrail, LearningTrailStep
from learning_trail.services import LearningTrailRecommendationService
from mission.models import Mission


class LearningTrailRecommendationTestCase(TestCase):
    def setUp(self) -> None:
        # Create mocks for the dependent services
        self.mock_mission_recommendation_service = mock.Mock()
        self.mock_learning_trail_service = mock.Mock()

        # Inject mocks into the service manually
        self.__trail_recommendation = LearningTrailRecommendationService(
            mission_recommendation_service=self.mock_mission_recommendation_service,
            learning_trail_service=self.mock_learning_trail_service,
        )

        # Setup test data
        self.workspace = mommy.make(Workspace)
        self.user = mommy.make(User, email="<EMAIL>")

        self.mission_1 = mommy.make(Mission, name="main_mission")
        self.mission_2 = mommy.make(Mission, name="mission_about_ti")

        self.learning_trail_1 = mommy.make(LearningTrail)
        self.learning_trail_2 = mommy.make(LearningTrail)

    def test_learning_trail_recommendation(self):
        # Mock services behavior
        self.mock_mission_recommendation_service.get_recommendations.return_value = Mission.objects.all()
        self.mock_learning_trail_service.get_allowed_learning_trails.return_value = LearningTrail.objects.all()

        mommy.make(LearningTrailStep, mission=self.mission_1, learning_trail=self.learning_trail_1)

        recommendation = self.__trail_recommendation.get_recommendations(
            user_id=self.user.id, workspace_id=self.workspace.id, user_language="pt-BR"
        )

        self.assertEqual(self.learning_trail_1, recommendation[0])
        self.assertIn(self.learning_trail_2, recommendation[1:])

    def test_learning_trail_recommendation_without_mission_recommendation(self):
        self.mock_mission_recommendation_service.get_recommendations.return_value = []
        self.mock_learning_trail_service.get_allowed_learning_trails.return_value = LearningTrail.objects.all()

        mommy.make(LearningTrailStep, mission=self.mission_1, learning_trail=self.learning_trail_1)

        recommendation = self.__trail_recommendation.get_recommendations(
            user_id=self.user.id, workspace_id=self.workspace.id, user_language="pt-BR"
        )

        self.assertIn(self.learning_trail_1, recommendation)
