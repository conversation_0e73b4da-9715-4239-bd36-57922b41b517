import uuid
from unittest import mock

from django.test import TestCase
from model_mommy import mommy

from account.models import User, Workspace
from authentication.keeps_permissions import SUPER_ADMIN
from config.settings import LEARNING_TRAIL_CLOSE_FOR_COMPANY
from learning_trail.models import LearningTrail, LearningTrailStep, LearningTrailType, LearningTrailWorkspace
from learning_trail.services import LearningTrailService
from mission.models import Mission, MissionWorkspace


class LearningTrailServiceTestCase(TestCase):
    def setUp(self):
        self.user = mommy.make(User, id=uuid.uuid4(), email="<EMAIL>")
        self.workspace = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps")
        self.closed_trail_type = mommy.make(LearningTrailType, name=LEARNING_TRAIL_CLOSE_FOR_COMPANY)

        self.mock_group_filter = mock.Mock()
        self.mock_mission_service = mock.Mock()
        self.mock_channel_service = mock.Mock()

        self.lt_service = LearningTrailService(
            group_filter=self.mock_group_filter,
            mission_service=self.mock_mission_service,
            channel_service=self.mock_channel_service,
        )

    def test_delete_trail(self):
        trail = self.create_learning_trails()
        self.lt_service.delete_instance(trail)
        trail.refresh_from_db()
        self.assertTrue(trail.deleted)
        self.assertFalse(trail.learningtrailworkspace_set.filter().exists())
        self.assertFalse(trail.learningtrailenrollment_set.filter().exists())
        self.assertFalse(trail.grouplearningtrail_set.filter().exists())
        self.assertFalse(trail.learningtrailstep_set.filter().exists())

    def test_admin_can_create_new_step_content_in_a_closed_trail(self):
        learning_trail = mommy.make(LearningTrail, learning_trail_type=self.closed_trail_type)
        mommy.make(LearningTrailWorkspace, learning_trail=learning_trail, workspace=self.workspace)
        mission = mommy.make(Mission)
        mommy.make(MissionWorkspace, mission=mission, workspace=self.workspace)

        self.mock_mission_service.get_workspace_missions.return_value = Mission.objects.filter(id=mission.id)

        self.lt_service.check_step_request_data_before_create(
            self.workspace.id,
            self.user.id,
            {"learning_trail": str(learning_trail.id), "mission": str(mission.id)},
            SUPER_ADMIN,
        )

    def test_soft_deleted_cascade_with_relationship_from_mission(self):
        trail = self.create_learning_trails()
        step = trail.learningtrailstep_set.first()
        mission = step.mission

        self.assertIsNotNone(mission)
        self.assertTrue(mission.learning_trail_step.exists())
        self.assertEqual(mission.learning_trail_step.first(), step)

        self.lt_service.delete_instance(trail)
        trail.refresh_from_db()
        step.refresh_from_db()
        mission.refresh_from_db()

        self.assertTrue(trail.deleted)
        self.assertTrue(step.deleted)
        self.assertFalse(mission.learning_trail_step.exists())

    def create_learning_trails(self):
        learning_trail = mommy.make(
            LearningTrail,
            id=uuid.uuid4(),
            name="Keeps Open For Workspace Learning Trail",
            user_creator=self.user,
            is_active=True,
        )
        LearningTrailWorkspace(learning_trail=learning_trail, workspace=self.workspace).save()
        mission = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Mission Name",
            user_creator=self.user,
            development_status="DONE",
            is_active=True,
        )
        LearningTrailStep(learning_trail=learning_trail, mission=mission, order=1).save()
        return learning_trail

    def create_mission(self):
        mission_template = mommy.make(
            Mission,
            id=uuid.uuid4(),
            name="Keeps public mission",
            user_creator=self.user,
            development_status="DONE",
            is_active=True,
        )
        MissionWorkspace(mission=mission_template, workspace=self.workspace).save()
        return mission_template
