from celery import shared_task
from django.utils.translation import gettext_noop as _

from account.models import User
from constants import ACTION_LIST
from custom.discord_webhook import DiscordWebhookLogger
from learning_trail.models import LearningTrailWorkspace
from learning_trail.services.notifications.mission_trail_disabled_notification_service import (
    MissionTrailDisabledNotificationService,
)
from learning_trail.services.notifications.mission_trail_enabled_notification_service import (
    MissionTrailEnabledNotificationService,
)
from mission.models import Mission
from myaccount.application.services.myaccount_service import MyAccountService
from notification.services.notification_service import Message
from notification.services.notification_service_v2 import NotificationServiceV2
from utils.task_transaction import task_transaction

NEW_TRAIL = _("new_trail")
YOUR_LEARNING_TRAIL_HAS_EXPIRED = _("your_learning_trail_has_expired")
LEARNING_TRAIL_TRANSFERRED_TO_YOU = _("learning_trail_transferred_to_you")


@shared_task(ignore_result=True)
def notify_new_trail_to_workspace_users(trail_workspace_id) -> None:
    with task_transaction(notify_new_trail_to_workspace_users.__name__, MyAccountService) as myaccount_service:
        trail_workspace = LearningTrailWorkspace.objects.get(id=trail_workspace_id)
        myaccount_service: MyAccountService
        workspace_users = myaccount_service.list_all_consumer_users(trail_workspace.workspace_id)

    with task_transaction(notify_new_trail_to_workspace_users.__name__, NotificationServiceV2) as notification_service:
        logger = DiscordWebhookLogger()
        trail = trail_workspace.learning_trail
        message = Message(title=NEW_TRAIL, description=trail.name)
        for user in workspace_users:
            try:
                if user.id == str(trail.user_creator_id):
                    continue
                notification_service.create_notification(
                    user_ids=[user.id],
                    type_key="NEW_TRAIL_PUBLISHED",
                    action="CREATE",
                    object=trail.id,
                    workspace_id=trail_workspace.workspace_id,
                    message=message,
                )
            except Exception as exception:
                logger.emit_short_message(notify_new_trail_to_workspace_users.__name__, exception)


@shared_task(ignore_result=True)
def notify_learning_trail_has_expired(trail_id) -> None:
    with task_transaction("notify_learning_trail_has_expired", NotificationServiceV2) as notification_service:
        trail_workspace = LearningTrailWorkspace.objects.get(learning_trail_id=trail_id, relationship_type="OWNER")
        trail = trail_workspace.learning_trail
        notification_service.create_notification(
            user_ids=[trail.user_creator_id],
            type_key="LEARNING_TRAIL_HAS_EXPIRED",
            action="REVIEW",
            object=trail.id,
            workspace_id=trail_workspace.workspace_id,
            message=Message(title=YOUR_LEARNING_TRAIL_HAS_EXPIRED, description=trail.name),
        )


@shared_task(ignore_result=True)
def notify_mission_trail_status_updated(mission_id: str, user_actor_id: str, enabled: bool) -> None:
    service_type = MissionTrailEnabledNotificationService if enabled else MissionTrailDisabledNotificationService
    with task_transaction(notify_mission_trail_status_updated.__name__, service_type) as service:
        service: service_type
        mission = Mission.objects.get(id=mission_id)
        if not user_actor_id:
            service.send(mission, None)
            return
        user = User.objects.get(id=user_actor_id)
        service.send(mission, user)


@shared_task(ignore_result=True)
def notify_transfer_learning_trail_user_owner(trail_id: str) -> None:
    with task_transaction(
        notify_transfer_learning_trail_user_owner.__name__, NotificationServiceV2
    ) as notification_service:
        trail_workspace = LearningTrailWorkspace.objects.get(learning_trail_id=trail_id, relationship_type="OWNER")
        trail = trail_workspace.learning_trail
        notification_service.create_notification(
            user_ids=[trail.user_creator_id],
            type_key="LEARNING_TRAIL_HAS_BEEN_TRANSFERRED_TO_YOU",
            action=ACTION_LIST,
            object=trail.id,
            workspace_id=trail_workspace.workspace_id,
            message=Message(title=LEARNING_TRAIL_TRANSFERRED_TO_YOU, description=trail.name),
        )
