from datetime import datetime, timedelta

from celery import shared_task
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, ExpressionWrapper, F, QuerySet
from django.utils.timezone import now

from learning_trail.services.notifications.mission_trail_disabled_too_long_notification_service import (
    MissionTrailDisabledTooLongNotificationService,
)
from mission.models import Mission
from mission.models.mission_development_status_enum import DISABLED_DEVELOPMENT_STATUS
from utils.task_transaction import task_transaction


class MissionTrailDisabledTooLongNotificationTask:
    def __init__(self, service: MissionTrailDisabledTooLongNotificationService):
        self._service = service

    def notify_trail_creators(self, disabled_days: int):
        missions = self._get_mission_disabled_too_long(disabled_days)
        for mission in missions:
            self._service.send(mission)

    @staticmethod
    def _get_mission_disabled_too_long(remaining_days: int) -> QuerySet:
        today_date = now()
        min_date = today_date - timedelta(days=remaining_days)
        min_datetime = datetime.combine(min_date, datetime.max.time())

        missions = Mission.objects.annotate(
            disabled_days=ExpressionWrapper(F("updated_date") - today_date, output_field=DurationField())
        ).filter(
            updated_date__lte=min_datetime,
            development_status__in=DISABLED_DEVELOPMENT_STATUS,
            disabled_days__lte=timedelta(days=-remaining_days),
            disabled_days__gte=timedelta(days=-remaining_days - 1),
        )
        return missions


@shared_task(ignore_result=True)
def notify_mission_trail_disabled_too_long(disabled_days: int):
    with task_transaction(
        notify_mission_trail_disabled_too_long.__name__, MissionTrailDisabledTooLongNotificationTask
    ) as task:
        task.notify_trail_creators(disabled_days)
