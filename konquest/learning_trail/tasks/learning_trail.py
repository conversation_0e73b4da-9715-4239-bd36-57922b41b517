from uuid import UUID

from celery import shared_task
from django.conf import settings
from django.db.models import QuerySet, Sum

from learning_trail.models import LearningTrail, LearningTrailStep
from mission.models import Mission
from pulse.models import Pulse
from utils.task_transaction import task_transaction


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def update_learning_trail_consume_data(learning_trail_id: UUID) -> None:
    with task_transaction("update_learning_trail_consume_data"):
        trail = LearningTrail.objects.filter(id=learning_trail_id).first()
        if not trail:
            return
        steps = LearningTrailStep.objects.filter(learning_trail_id=learning_trail_id)

        missions = Mission.objects.filter(id__in=steps.values_list("mission_id", flat=True))
        pulses = Pulse.objects.filter(id__in=steps.values_list("pulse_id", flat=True))

        sum_missions_duration = missions.aggregate(sum_d=Sum("duration_time")).get("sum_d")
        sum_missions_points = missions.aggregate(sum_p=Sum("points")).get("sum_p")

        sum_pulses_duration = pulses.aggregate(sum_d=Sum("duration_time")).get("sum_d")
        sum_pulses_points = pulses.aggregate(sum_p=Sum("points")).get("sum_p")

        missions_duration = sum_missions_duration if sum_missions_duration else 0
        missions_points = sum_missions_points if sum_missions_points else 0

        pulses_duration = sum_pulses_duration if sum_pulses_duration else 0
        pulses_points = sum_pulses_points if sum_pulses_points else 0

        trail.duration_time = missions_duration + pulses_duration
        trail.points = missions_points + pulses_points

        trail.save()


def update_learning_trails_consume_data(trails: QuerySet) -> None:
    for trail in trails:
        update_learning_trail_consume_data.delay(trail.id)
