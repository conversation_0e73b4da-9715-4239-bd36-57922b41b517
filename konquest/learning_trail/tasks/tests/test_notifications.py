from unittest.mock import Magic<PERSON>ock

import mock
from django.test import TestCase
from injector import Injector

from account.models import User
from learning_trail.services.notifications.mission_trail_disabled_notification_service import (
    MissionTrailDisabledNotificationService,
)
from learning_trail.tasks.notifications import notify_mission_trail_status_updated
from mission.models import Mission


class TestNotifications(TestCase):
    @mock.patch.object(Mission.objects, "get")
    @mock.patch.object(User.objects, "get")
    @mock.patch.object(Injector, "get")
    def test_notify_mission_trail_disabled(
        self, injector_get: mock.MagicMock, user_get: mock.MagicMock, mission_get: mock.MagicMock
    ):
        mission_id = "1234"
        user_id = "2314"
        mission = MagicMock(Mission)
        user = MagicMock(User)
        mission_get.return_value = mission
        user_get.return_value = user
        injector_get.return_value = MagicMock(MissionTrailDisabledNotificationService)

        notify_mission_trail_status_updated(mission_id, user_id, False)

        mission_get.assert_called_with(id=mission_id)
        user_get.assert_called_with(id=user_id)
        injector_get.assert_called_with(MissionTrailDisabledNotificationService)
        injector_get.return_value.send.assert_called_with(mission, user)

    @mock.patch.object(Mission.objects, "get")
    @mock.patch.object(User.objects, "get")
    @mock.patch.object(Injector, "get")
    def test_notify_mission_trail_disabled_with_user_none(
        self, injector_get: mock.MagicMock, user_get: mock.MagicMock, mission_get: mock.MagicMock
    ):
        mission_id = None
        user_id = "2314"
        mission = MagicMock(Mission)
        user = MagicMock(User)
        mission_get.return_value = mission
        user_get.return_value = user
        injector_get.return_value = MagicMock(MissionTrailDisabledNotificationService)

        notify_mission_trail_status_updated(mission_id, user_id, False)

        mission_get.assert_called_with(id=mission_id)
        user_get.assert_called_with(id=user_id)
        injector_get.assert_called_with(MissionTrailDisabledNotificationService)
        injector_get.return_value.send.assert_called_with(mission, user)
