from datetime import datetime
from unittest.mock import MagicMock, patch

import mock
from django.test import TestCase

from learning_trail.tasks.mission_trail_disabled_too_long_notification_task import (
    MissionTrailDisabledTooLongNotificationTask,
)
from mission.models import Mission


class TestMisionTrailDisabledTooLongNotificationTask(TestCase):
    def setUp(self):
        self._notification_service = MagicMock()
        self._task = MissionTrailDisabledTooLongNotificationTask(self._notification_service)

    @patch("mission.models.Mission.objects.annotate")
    @patch("django.utils.timezone.now")
    def test_should_notify_all_trail_creators(self, now: mock.MagicMock, annotate: mock.MagicMock):
        mission = MagicMock(Mission)
        today_date = datetime(2023, 1, 4)
        now.return_value = today_date
        annotate.return_value.filter.return_value = [mission]

        self._task.notify_trail_creators(3)

        self._notification_service.send.assert_called_with(mission)
