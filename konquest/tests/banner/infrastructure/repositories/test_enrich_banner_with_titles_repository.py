from unittest.mock import Mock
from uuid import uuid4

from banner.domain.entities.banner import Banner, LearningResource
from banner.infrastructure.models.banner_learning_resource import ResourceType
from banner.infrastructure.repositories.enrich_banner_with_titles_repository import EnrichBannerWithTitlesRepository
from django.core.exceptions import ObjectDoesNotExist
from django.test import TestCase


class EnrichBannerWithTitlesRepositoryTestCase(TestCase):
    def setUp(self):
        self.workspace_id = str(uuid4())
        self.resource_id = uuid4()

        self.learning_resource = LearningResource(
            resource_type=ResourceType.COURSE.value,
            resource_id=self.resource_id,
            external_resource_title="Fallback Title",
            external_resource_url="https://example.com",
            external_resource_image="https://example.com/image.png",
            order=1,
            title=None
        )

        self.banner = Banner(
            id=uuid4(),
            start_date=None,
            end_date=None,
            workspace=self.workspace_id,
            learning_resources=[self.learning_resource]
        )

        self.course_repository = Mock()
        self.trail_repository = Mock()

        self.repository = EnrichBannerWithTitlesRepository(
            course_repository=self.course_repository,
            trail_repository=self.trail_repository
        )

    def test_should_enrich_title_from_course(self):
        mock_course = Mock()
        mock_course.name = "Course Title"
        self.course_repository.get_workspace_missions.return_value.get.return_value = mock_course

        result = self.repository.enrich(self.banner)

        self.course_repository.get_workspace_missions.assert_called_once_with(self.workspace_id)
        self.assertEqual(result.learning_resources[0].title, "Course Title")

    def test_should_enrich_title_from_trail(self):
        self.learning_resource.resource_type = ResourceType.LEARNING_TRAIL.value

        mock_trail = Mock()
        mock_trail.name = "Trail Title"
        self.trail_repository.get_trails_workspace.return_value.get.return_value = mock_trail

        result = self.repository.enrich(self.banner)

        self.trail_repository.get_trails_workspace.assert_called_once_with(self.workspace_id)
        self.assertEqual(result.learning_resources[0].title, "Trail Title")

    def test_should_return_empty_title_when_resource_not_found(self):
        self.course_repository.get_workspace_missions.return_value.get.side_effect = ObjectDoesNotExist()

        result = self.repository.enrich(self.banner)

        self.assertEqual(result.learning_resources[0].title, "")
