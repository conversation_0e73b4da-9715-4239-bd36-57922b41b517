import unittest
from unittest.mock import <PERSON>Mock
from uuid import uuid4

from banner.application.use_cases.get_recommendations_use_case import GetRecommendationsUseCase
from banner.domain.entities.recommendation import Recommendation


class TestGetRecommendationsUseCase(unittest.TestCase):
    def setUp(self):
        self.mock_workspace_service = MagicMock()
        self.mock_banner_strategy = MagicMock()
        self.mock_mission_strategy = MagicMock()

        self.use_case = GetRecommendationsUseCase(
            workspace_repository=self.mock_workspace_service,
            banner_strategy=self.mock_banner_strategy,
            mission_strategy=self.mock_mission_strategy
        )

        self.user_id = str(uuid4())
        self.workspace_id = str(uuid4())
        self.language = "en"

        self.recommendation = Recommendation(
            id=uuid4(),
            external_resource_url="https://example.com",
            title="Sample Recommendation",
            holder_image=None,
            resource_type="COURSE"
        )

    def test_returns_mission_when_banner_mode_is_not_default(self):
        mock_workspace = MagicMock()
        mock_workspace.banner_mode = "MANUAL"
        self.mock_workspace_service.get_workspace.return_value = mock_workspace
        self.mock_mission_strategy.get_recommendations.return_value = [self.recommendation]

        result = self.use_case.execute(self.user_id, self.workspace_id, self.language)

        self.mock_mission_strategy.get_recommendations.assert_called_once()
        self.mock_banner_strategy.get_recommendations.assert_not_called()
        self.assertEqual(result, [self.recommendation])

    def test_returns_banner_when_mode_is_default_and_has_recommendations(self):
        mock_workspace = MagicMock()
        mock_workspace.banner_mode = "RECOMMENDATION"
        self.mock_workspace_service.get_workspace.return_value = mock_workspace
        self.mock_banner_strategy.get_recommendations.return_value = [self.recommendation]

        result = self.use_case.execute(self.user_id, self.workspace_id, self.language)

        self.mock_banner_strategy.get_recommendations.assert_called_once()
        self.mock_mission_strategy.get_recommendations.assert_not_called()
        self.assertEqual(result, [self.recommendation])

    def test_fallback_to_mission_when_default_mode_and_banner_returns_empty(self):
        mock_workspace = MagicMock()
        mock_workspace.banner_mode = "RECOMMENDATION"
        self.mock_workspace_service.get_workspace.return_value = mock_workspace
        self.mock_banner_strategy.get_recommendations.return_value = []
        self.mock_mission_strategy.get_recommendations.return_value = [self.recommendation]

        result = self.use_case.execute(self.user_id, self.workspace_id, self.language)

        self.mock_banner_strategy.get_recommendations.assert_called_once()
        self.mock_mission_strategy.get_recommendations.assert_called_once()
        self.assertEqual(result, [self.recommendation])

