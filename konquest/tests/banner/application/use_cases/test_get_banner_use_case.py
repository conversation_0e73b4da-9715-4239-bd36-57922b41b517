from unittest.mock import Mock
from uuid import uuid4

from banner.application.use_cases.get_banner_use_case import GetBannerUseCase
from banner.domain.entities.banner import Banner, LearningResource
from custom.exceptions.banner_exceptions import BannerNotFoundError
from django.test import TestCase


class GetBannerUseCaseTestCase(TestCase):
    def setUp(self):
        self.workspace_id = uuid4()

        self.banner = Banner(
            id=uuid4(),
            start_date=None,
            end_date=None,
            workspace=self.workspace_id,
            learning_resources=[
                LearningResource(
                    resource_type="COURSE",
                    resource_id=uuid4(),
                    external_resource_title="External Course",
                    external_resource_url="http://example.com",
                    external_resource_image=None,
                    order=1,
                    title=None
                )
            ]
        )

        self.banners_repository = Mock()
        self.enrichment_repository = Mock()

        self.use_case = GetBannerUseCase(
            banners_repository=self.banners_repository,
            enrich_banner_with_titles_repository=self.enrichment_repository
        )

    def test_should_return_enriched_banner(self):
        self.banners_repository.get_by_workspace.return_value = self.banner
        self.enrichment_repository.enrich.return_value = self.banner

        result = self.use_case.execute(self.workspace_id)

        self.banners_repository.get_by_workspace.assert_called_once_with(self.workspace_id)
        self.enrichment_repository.enrich.assert_called_once_with(self.banner)
        self.assertEqual(result, self.banner)

    def test_should_raise_banner_not_found_error(self):
        self.banners_repository.get_by_workspace.return_value = None

        with self.assertRaises(BannerNotFoundError) as context:
            self.use_case.execute(self.workspace_id)

        self.banners_repository.get_by_workspace.assert_called_once_with(self.workspace_id)
        self.enrichment_repository.enrich.assert_not_called()
        self.assertEqual(str(context.exception.description), "Not found any banner for the workspace")
