from django.urls import path

from account.views.user_instructor_viewset import UserInstructorViewSet
from account.views.user_parser_viewset import UserParserViewSet
from account.views.workspace_certificate_viewset import WorkspaceCertificateViewSet

_LIST = {"get": "list", "post": "create"}

_SAVE_ONLY = {"post": "create"}

_DETAIL = {"get": "retrieve", "put": "update", "patch": "partial_update", "delete": "destroy"}


urlpatterns = [
    path("/workspaces-certificates", WorkspaceCertificateViewSet.as_view(_LIST), name="workspaces-certificate-list"),
    path(
        "/workspaces-certificates/<uuid:pk>",
        WorkspaceCertificateViewSet.as_view(_DETAIL),
        name="workspaces-certificate-detail",
    ),
    path("/users/parser", UserParserViewSet.as_view(_SAVE_ONLY), name="users-parser-list"),
    path("/users/instructors", UserInstructorViewSet.as_view(_LIST), name="instructor-users-list"),
]
