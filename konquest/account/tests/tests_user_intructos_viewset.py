import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient

from myaccount.infrastructure.repositories.myaccount_repository import MyAccountRepository
from utils.utils import create_empty_image


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class UserViewsetTestCase(TestCase):
    fixtures = ["workspace"]

    def setUp(self):
        self.client = APIClient()
        self.company_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        self.headers = {"client_id": self.company_id}
        self.url = reverse("instructor-users-list")
        self.client.force_authenticate(user={})

    @mock.patch.object(MyAccountRepository, "list_users")
    def test_user_list(self, mock_get_able_users, mock_return, mock_rule):
        mock_get_able_users.return_value = {"data": [{"email": "<EMAIL>"}, {"email": "<EMAIL>"}]}
        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["data"]), 2)

    @mock.patch.object(MyAccountRepository, "list_users")
    def test_user_list_with_pagination(self, mock_get_able_users, mock_return, mock_rule):
        next_query = "per_page=1&page=2"
        previous_query = "per_page=1&page=1"
        previous_api = f"/users?{previous_query}"
        next_api = f"/users?{next_query}"
        mock_get_able_users.return_value = {
            "next": next_api,
            "previous": previous_api,
            "data": [{"email": "<EMAIL>"}, {"email": "<EMAIL>"}],
        }
        response = self.client.get(f"{self.url}?per_page=1", **self.headers, format="json").json()
        self.assertEqual(response["next"], f"/accounts/users/instructors?{next_query}")
        self.assertEqual(response["previous"], f"/accounts/users/instructors?{previous_query}")

    @mock.patch.object(MyAccountRepository, "create_user")
    def test_create_new_instructor(self, create_instructor, mock_return, mock_rule):
        avatar = create_empty_image(".jpg")
        payload = {"name": "new instructor", "email": "<EMAIL>", "avatar": avatar}
        create_user_response = payload.copy()
        create_user_response["id"] = uuid.uuid4()
        create_instructor.return_value = create_user_response

        response = self.client.post(self.url, data=payload, **self.headers, format="multipart")

        self.assertEqual(response.status_code, 201)
        self.assertEqual(
            response.json(), {"id": str(create_user_response["id"]), "name": payload["name"], "email": payload["email"]}
        )


class MockResponse:
    def __init__(self, data, status_code=200):
        self._data = data
        self.status_code = status_code

    def json(self):
        return self._data
