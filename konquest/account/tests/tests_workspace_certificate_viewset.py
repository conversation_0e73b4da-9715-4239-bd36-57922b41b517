import uuid

from django.test import TestCase
from django.urls import reverse
from mock import patch
from model_mommy import mommy
from rest_framework.test import APIClient

from account.models import User, Workspace, WorkspaceCertificate


@patch("utils.utils.Utils.temp_file", return_value={})
@patch("account.services.workspace_service.WorkspaceService.upload_certificate_template", return_value={})
@patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class WorkspaceViewsetTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.workspace = mommy.make(Workspace, id=uuid.uuid4(), name="Workspace 1")
        self.user = mommy.make(User, id=uuid.uuid4(), email="<EMAIL>")
        self.headers = {}
        self.client.force_authenticate(user={"client_id": self.workspace.id})

    def test_post_workspace_certificate(self, mock_temp_file, mock_upload_certificate_template, mock_check_role):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})
        mock_upload_certificate_template.return_value = {"name": "path/file", "url": "file.jasper"}
        mock_temp_file.return_value = "file.jasper"
        data = {"jasper_file": "file_binary", "language": "pt-BR"}
        response = self.client.post(reverse("workspaces-certificate-list"), **self.headers, data=data, format="json")
        response_json = response.json()
        self.assertEqual(response_json.get("certificate_path"), "path/file")
        self.assertEqual(response.status_code, 201)

    def test_patch_workspace_certificate_no_jasper_file(
        self, mock_temp_file, mock_upload_certificate_template, mock_check_role
    ):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})
        mock_upload_certificate_template.return_value = {"name": "path/file", "url": "file.jasper"}
        mock_temp_file.return_value = "file.jasper"
        certificate = mommy.make(
            WorkspaceCertificate,
            id=uuid.uuid4(),
            workspace_id=self.workspace.id,
            language="pt-BR",
            certificate_type="MISSION",
            certificate_path="path/path.jasper",
        )
        data = {"language": "pt-BR"}
        response = self.client.patch(
            reverse("workspaces-certificate-detail", args=[str(certificate.id)]),
            **self.headers,
            data=data,
            format="json",
        )
        response_json = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json.get("certificate_path"), "path/path.jasper")

    def test_patch_workspace_certificate_with_jasper_file(
        self, mock_temp_file, mock_upload_certificate_template, mock_check_role
    ):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})
        mock_upload_certificate_template.return_value = {"name": "path/other.jasper", "url": "file.jasper"}
        mock_temp_file.return_value = "path/other.jasper"
        certificate = mommy.make(
            WorkspaceCertificate,
            id=uuid.uuid4(),
            workspace_id=self.workspace.id,
            language="pt-BR",
            certificate_type="MISSION",
            certificate_path="path/path.jasper",
        )
        data = {"language": "pt-BR", "jasper_file": "file_binary"}
        response = self.client.patch(
            reverse("workspaces-certificate-detail", args=[str(certificate.id)]),
            **self.headers,
            data=data,
            format="json",
        )
        response_json = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json.get("certificate_path"), "path/other.jasper")
