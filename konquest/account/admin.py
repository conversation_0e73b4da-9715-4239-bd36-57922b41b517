from django.contrib import admin

from account.models.user import User
from account.models.workspace import Workspace


@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = ("name", "email", "phone", "language", "related_user_leader")
    search_fields = (
        "id",
        "name",
        "email",
        "phone",
        "related_user_leader__id",
        "related_user_leader__name",
    )
    list_per_page = 50
    list_max_show_all = 50


@admin.register(Workspace)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ("name",)
    search_fields = (
        "id",
        "name",
    )
    list_per_page = 50
    list_max_show_all = 50
