from rest_framework import serializers

from account.models.user import User
from account.models.user_profile_workspace import UserProfileWorkspace


class UserSerializer(serializers.ModelSerializer):
    job = serializers.SerializerMethodField()

    def get_job(self, obj):
        profile = UserProfileWorkspace.objects.filter(user=obj).first()
        if profile and profile.job_position:
            return profile.job_position.name.strip()
        return None

    class Meta:
        model = User
        fields = "__all__"


class UserParserSerializer(serializers.Serializer):
    founds = UserSerializer(many=True)
    not_founds = serializers.ListSerializer(child=serializers.CharField())

    def update(self, instance, validated_data):
        """
        This method is empty because updating instances is not required for this serializer.
        """
        pass

    def create(self, validated_data):
        """
        This method is empty because creation instances is not required for this serializer.
        """
        pass
