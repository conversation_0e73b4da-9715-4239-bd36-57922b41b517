import uuid

from django.db import models

from account.models.user import User
from account.models.workspace import Workspace


class UserRoleWorkspace(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
    )
    workspace = models.ForeignKey(Workspace, on_delete=models.SET_NULL, null=True)

    class Meta:
        verbose_name_plural = "Users role workspace"
        db_table = "user_role_workspace"
