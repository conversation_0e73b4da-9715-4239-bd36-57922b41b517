import uuid

from django.db import models

from account.models import Workspace
from account.models.language_enum import LanguageEnum

CERTIFICATE_TYPE = (("MISSION", "MISSION"), ("LEARNING_TRAIL", "LEARNING_TRAIL"))


class WorkspaceCertificate(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    workspace = models.ForeignKey(Workspace, verbose_name="Workspace", on_delete=models.PROTECT)
    language = models.CharField(verbose_name="Language", choices=LanguageEnum.choices(), max_length=20)
    certificate_path = models.TextField(verbose_name="Certificate Path")
    certificate_type = models.CharField(
        verbose_name="Certificate Type", choices=CERTIFICATE_TYPE, max_length=20, default="MISSION"
    )

    class Meta:
        verbose_name_plural = "Workspace Certificates"
        unique_together = ("workspace", "language", "certificate_type")
        db_table = "workspace_certificate"
