import uuid

from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models


class Workspace(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    name = models.Char<PERSON>ield(verbose_name="Workspace Name", max_length=200)
    hash_id = models.CharField(max_length=125, unique=True, null=True, blank=True)

    # Configuration
    allow_list_public_categories = models.BooleanField(verbose_name="Allow List Public Categories", default=True)
    allow_list_public_channel = models.BooleanField(verbose_name="Allow List Public Channel", default=False)
    allow_create_public_channel = models.BooleanField(verbose_name="Allow Create Public Channel", default=False)
    allow_list_paid_channel = models.BooleanField(verbose_name="Allow List Paid Channel", default=False)
    allow_create_paid_channel = models.BooleanField(verbose_name="Allow Create Paid Channel", default=False)
    need_approve_channel = models.<PERSON>oleanField(verbose_name="Need Approve Channel", default=True)

    allow_list_public_mission = models.BooleanField(verbose_name="Allow List Public Mission", default=False)
    allow_create_public_mission = models.BooleanField(verbose_name="Allow Create Public Mission", default=False)
    allow_list_paid_mission = models.BooleanField(verbose_name="Allow List Paid Mission", default=False)
    allow_create_paid_mission = models.BooleanField(verbose_name="Allow Create Paid Mission", default=False)
    need_approve_mission = models.BooleanField(verbose_name="Need Approve Mission", default=True)
    enrollment_goal_duration_days = models.IntegerField(verbose_name="Enrollment Goal Duration", default=30, null=True)
    block_reenrollment = models.BooleanField(verbose_name="Block re-enrollment", default=False)

    status = models.BooleanField(verbose_name="Status", default=True)

    icon_url = models.TextField(verbose_name="Address", null=True, blank=True)
    logo_url = models.TextField(verbose_name="Address", null=True, blank=True)
    custom_color = models.CharField(verbose_name="Custom Color", max_length=16, null=True, blank=True)
    notify_slack = models.BooleanField(verbose_name="Notify Slack", default=False, null=True, blank=True)
    notify_teams = models.BooleanField(verbose_name="Notify Teams", default=False, null=True, blank=True)
    alura_integration_active = models.BooleanField(
        verbose_name="Alura Integration Active", default=False, null=True, blank=True
    )

    min_performance_certificate = models.FloatField(
        verbose_name="Minimum Performance Certificate",
        default=0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
    )

    banner_mode = models.CharField(verbose_name="Banner Mode", null=True, blank=True, max_length=100)

    class Meta:
        verbose_name_plural = "Workspaces"
        db_table = "workspace"

    def __str__(self):
        return self.name
