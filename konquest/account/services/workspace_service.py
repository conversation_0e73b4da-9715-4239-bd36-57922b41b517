import uuid

from django.core.files.storage import default_storage

import utils
from account.models import Workspace
from config import settings
from custom.discord_webhook import DiscordWebhookLogger
from custom.keeps_exception_handler import KeepsError


class WorkspaceService:
    def __init__(self):
        self.aws_s3_client = utils.aws_s3.S3Client(
            aws_access_key=settings.AWS_ACCESS_KEY_ID,
            aws_secret_key=settings.AWS_SECRET_ACCESS_KEY,
            aws_region=settings.AWS_REGION_NAME,
            aws_default_url=settings.AWS_REPORTS_S3_BASE_URL,
            bucket_name=settings.AWS_REPORTS_BUCKET_NAME,
            bucket_path=settings.AWS_CERTIFICATE_TEMPLATE_BUCKET_PATH,
            webhook_logger=DiscordWebhookLogger(),
        )

    def upload_certificate_template(self, jasper_file, workspace_id, language, certificate_type):
        content_type = "application/octet-stream"

        try:
            workspace_instance = Workspace.objects.filter(id=workspace_id).first()
            workspace_name = workspace_instance.name.replace(" ", "").replace(".", "_")
            language = language.replace(" ", "").replace(".", "_")

            certificate_template_path = (
                f"{workspace_name}-{workspace_instance.id}_"
                f"{certificate_type}_certificate_template_{language}-{uuid.uuid4()}.jasper"
            )

            response = self.aws_s3_client.send_file_path(jasper_file, certificate_template_path, content_type)

            return response
        except Exception:
            KeepsError(detail="unexpected error happened", i18n="unexpected_error_happened", status_code=500)

    @staticmethod
    def get_workspace(workspace_id):
        return Workspace.objects.get(id=workspace_id)

    @staticmethod
    def temp_file(data):
        file = data.get("file")
        extension = file.name.split(".")[1]
        filename = f"{uuid.uuid4()}.{extension}"

        with open(default_storage.path(filename), "wb+") as destination:
            for chunk in file.chunks():
                destination.write(chunk)

        return f"{settings.TEMP_UPLOAD_FOLDER}/{filename}"
