"""
Konquest URL Configuration
"""

from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path, re_path
from django.views.generic import RedirectView

from config.docs import swagger_spec_view

urlpatterns = [
    re_path("^$", RedirectView.as_view(pattern_name="swagger-spec", permanent=True), name="swagger-spec"),
    path("swagger-spec/", swagger_spec_view, name="swagger-spec"),
    path("goals-mission", include("goal_mission.urls")),
    path("pulses", include("pulse.urls")),
    path("channels", include("pulse.channel_urls")),
    path("achievements", include("achievement.urls")),
    path("exams", include("learn_content.exam_urls")),
    path("accounts", include("account.urls")),
    path("workspaces", include("account.workspace_urls")),
    path("learning-trails", include("learning_trail.urls")),
    path("missions", include("mission.urls")),
    path("mission-enrollments", include("user_activity.mission_enrollment_urls")),
    path("learning-trail-enrollments", include("user_activity.learning_trail_enrollment_urls")),
    path("users", include("user_activity.urls")),
    path("groups", include("group.urls")),
    path("notifications", include("notification.urls")),
    path("categories", include("category.urls")),
    path("learn-contents", include("learn_content.urls")),
    path("gamification", include("gamification.urls")),
    path("transfers", include("transfers.urls")),
    path("banners", include("banner.presentation.urls")),
]

if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

handler404 = "custom.views.page_not_found_view"
