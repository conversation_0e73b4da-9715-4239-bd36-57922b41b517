from rest_framework import serializers

from gamification.models.gamification_history import GamificationHistory


class ExtractUserSerializer(serializers.ModelSerializer):
    id = serializers.UUIDField(read_only=True)
    user = serializers.CharField(source="user__name")
    user_id = serializers.UUIDField(source="user__id")
    avatar = serializers.CharField(source="user__avatar")
    job_position = serializers.CharField()
    leader_name = serializers.CharField(source="leader__name")
    content = serializers.CharField()
    content_type = serializers.CharField()
    acquire_date = serializers.DateTimeField(format="%Y-%m-%d")
    performance = serializers.SerializerMethodField()
    total_points = serializers.IntegerField()

    def get_performance(self, obj):
        return "{}%".format(min(int((obj.get("performance") or 0) * 100), 100))

    class Meta:
        model = GamificationHistory
        fields = [
            "id",
            "user_id",
            "content",
            "content_type",
            "user",
            "avatar",
            "job_position",
            "leader_name",
            "performance",
            "total_points",
            "points_acquired",
            "acquire_date",
            "area_of_activity",
            "director",
            "manager",
        ]


class StatisticsSerializer(serializers.Serializer):
    position = serializers.IntegerField()
    completed_trails = serializers.IntegerField()
    completed_missions = serializers.IntegerField()
    consumed_pulses = serializers.IntegerField()
    learn_hours = serializers.IntegerField()
    performance_avg = serializers.CharField()
    conclusion_rate = serializers.CharField()


class GeneralRankingSerializer(serializers.ModelSerializer):
    user = serializers.CharField(source="user__name")
    avatar = serializers.CharField(source="user__avatar")
    job_position = serializers.CharField()
    leader_name = serializers.CharField(source="user__related_user_leader__name")
    points = serializers.IntegerField()
    position = serializers.IntegerField()

    class Meta:
        model = GamificationHistory
        fields = [
            "user",
            "avatar",
            "job_position",
            "leader_name",
            "position",
            "points",
            "area_of_activity",
            "director",
            "manager",
            "user_id",
        ]


class DirectorRankingSerializer(serializers.ModelSerializer):
    num_users = serializers.IntegerField()
    points_avg = serializers.IntegerField()
    position = serializers.IntegerField()

    class Meta:
        model = GamificationHistory
        fields = ["director", "num_users", "points_avg", "position"]


class ManagerRankingSerializer(serializers.ModelSerializer):
    num_users = serializers.IntegerField()
    points_avg = serializers.IntegerField()
    position = serializers.IntegerField()

    class Meta:
        model = GamificationHistory
        fields = ["user_id", "manager", "num_users", "points_avg", "position"]


class ActivityAreaRankingSerializer(serializers.ModelSerializer):
    num_users = serializers.IntegerField()
    points_avg = serializers.IntegerField()
    position = serializers.IntegerField()

    class Meta:
        model = GamificationHistory
        fields = ["user_id", "area_of_activity", "num_users", "points_avg", "position"]


class LeaderRankingSerializer(serializers.ModelSerializer):
    num_users = serializers.IntegerField()
    points_avg = serializers.IntegerField()
    position = serializers.IntegerField()
    leader = serializers.CharField(source="leader__name")
    avatar = serializers.CharField(source="leader__avatar")
    user_id = serializers.UUIDField(source="leader__id")

    class Meta:
        model = GamificationHistory
        fields = ["leader", "num_users", "points_avg", "position", "user_id", "avatar"]


class MultiplierRankingSerializer(serializers.ModelSerializer):
    num_users = serializers.IntegerField()
    points_avg = serializers.IntegerField()
    position = serializers.IntegerField()
    multiplier = serializers.CharField(source="multiplier__name")
    avatar = serializers.CharField(source="user__avatar")

    class Meta:
        model = GamificationHistory
        fields = ["multiplier", "num_users", "points_avg", "position", "user_id", "avatar"]
