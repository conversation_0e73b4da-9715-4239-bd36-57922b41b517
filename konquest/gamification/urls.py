from django.urls import path

from gamification.views.gamification_viewset import GamificationViewSet

EXTRACT_USER = {"get": "extract_user"}
STATISTICS_USER = {"get": "statistics_user"}
GENERAL_RANKING = {"get": "general_ranking"}
MULTIPLIER_RANKING = {"get": "multiplier_ranking"}
LEADER_RANKING = {"get": "leader_ranking"}
DIRECTOR_RANKING = {"get": "director_ranking"}
MANAGER_RANKING = {"get": "manager_ranking"}
ACTIVITY_AREA_RANKING = {"get": "activity_area_ranking"}


urlpatterns = [
    path("/extract-user", GamificationViewSet.as_view(EXTRACT_USER), name="gamification-extract-user"),
    path("/statistics-user", GamificationViewSet.as_view(STATISTICS_USER), name="gamification-statistics-user"),
    path("/ranking/general", GamificationViewSet.as_view(GENERAL_RANKING), name="gamification-general-ranking"),
    path(
        "/ranking/multipliers", GamificationViewSet.as_view(MULTIPLIER_RANKING), name="gamification-multiplier-ranking"
    ),
    path("/ranking/leaders", GamificationViewSet.as_view(LEADER_RANKING), name="gamification-leader-ranking"),
    path("/ranking/directors", GamificationViewSet.as_view(DIRECTOR_RANKING), name="gamification-director-ranking"),
    path("/ranking/managers", GamificationViewSet.as_view(MANAGER_RANKING), name="gamification-manager-ranking"),
    path(
        "/ranking/activity-areas",
        GamificationViewSet.as_view(ACTIVITY_AREA_RANKING),
        name="gamification-area-activity-ranking",
    ),
]
