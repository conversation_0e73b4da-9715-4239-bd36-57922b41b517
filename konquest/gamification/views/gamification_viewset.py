from datetime import date

from django.db.models import Q
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets
from rest_framework.response import Response

from gamification.serializers.gamification_serializer import (
    ActivityAreaRankingSerializer,
    DirectorRankingSerializer,
    ExtractUserSerializer,
    GeneralRankingSerializer,
    LeaderRankingSerializer,
    ManagerRankingSerializer,
    MultiplierRankingSerializer,
    StatisticsSerializer,
)
from gamification.services.gamification_service import GamificationService
from utils.swagger import authorization_parameters, date_interval_parameters, pagination_parameters, search_parameter


class GamificationViewSet(viewsets.ModelViewSet):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.LOWEST_VALID_DATE = date(1900, 1, 1)
        self.LONGEST_VALID_DATE = date.today()

        self._service = GamificationService()

    @swagger_auto_schema(
        manual_parameters=authorization_parameters
        + pagination_parameters
        + date_interval_parameters
        + search_parameter,
        responses={"200": ExtractUserSerializer(many=True)},
    )
    def extract_user(self, request):
        self._set_params()
        queryset = self._service.get_extract_user(self.user_id, self.workspace_id, self.start_date, self.end_date)
        if self.search:
            queryset = queryset.filter(
                Q(user__name__icontains=self.search)
                | Q(mission__name__icontains=self.search)
                | Q(pulse__name__icontains=self.search)
            )
        serializer = ExtractUserSerializer(queryset, many=True)
        page = self.paginate_queryset(serializer.data)
        return self.get_paginated_response(page)

    @swagger_auto_schema(manual_parameters=authorization_parameters, responses={"200": StatisticsSerializer(many=True)})
    def statistics_user(self, request):
        self._set_params()
        data = self._service.get_statistics_user(self.user_id, self.workspace_id)
        return Response(data)

    @swagger_auto_schema(
        manual_parameters=authorization_parameters
        + pagination_parameters
        + date_interval_parameters
        + search_parameter,
        responses={"200": GeneralRankingSerializer(many=True)},
    )
    def general_ranking(self, request):
        self._set_params()
        queryset = self._service.get_general_ranking_gamification(self.workspace_id, self.start_date, self.end_date)
        if self.search:
            queryset = queryset.filter(
                user__name__icontains=self.search,
            )
        serializer = GeneralRankingSerializer(queryset, many=True)
        page = self.paginate_queryset(serializer.data)
        return self.get_paginated_response(page)

    def multiplier_ranking(self, request):
        self._set_params()
        queryset = self._service.get_multiplier_ranking_gamification(self.workspace_id, self.start_date, self.end_date)
        serializer = MultiplierRankingSerializer(queryset, many=True)
        page = self.paginate_queryset(serializer.data)
        return self.get_paginated_response(page)

    @swagger_auto_schema(
        manual_parameters=authorization_parameters
        + pagination_parameters
        + date_interval_parameters
        + search_parameter,
        responses={"200": LeaderRankingSerializer(many=True)},
    )
    def leader_ranking(self, request):
        self._set_params()
        queryset = self._service.get_leader_ranking_gamification(self.workspace_id, self.start_date, self.end_date)
        if self.search:
            queryset = queryset.filter(
                leader__name__icontains=self.search,
            )
        serializer = LeaderRankingSerializer(queryset, many=True)
        page = self.paginate_queryset(serializer.data)
        return self.get_paginated_response(page)

    @swagger_auto_schema(
        manual_parameters=authorization_parameters
        + pagination_parameters
        + date_interval_parameters
        + search_parameter,
        responses={"200": DirectorRankingSerializer(many=True)},
    )
    def director_ranking(self, request):
        self._set_params()
        queryset = self._service.get_director_ranking_gamification(self.workspace_id, self.start_date, self.end_date)
        if self.search:
            queryset = queryset.filter(
                director__icontains=self.search,
            )
        serializer = DirectorRankingSerializer(queryset, many=True)
        page = self.paginate_queryset(serializer.data)
        return self.get_paginated_response(page)

    @swagger_auto_schema(
        manual_parameters=authorization_parameters
        + pagination_parameters
        + date_interval_parameters
        + search_parameter,
        responses={"200": ManagerRankingSerializer(many=True)},
    )
    def manager_ranking(self, request):
        self._set_params()
        queryset = self._service.get_manager_ranking_gamification(self.workspace_id, self.start_date, self.end_date)
        if self.search:
            queryset = queryset.filter(
                manager__icontains=self.search,
            )
        serializer = ManagerRankingSerializer(queryset, many=True)
        page = self.paginate_queryset(serializer.data)
        return self.get_paginated_response(page)

    @swagger_auto_schema(
        manual_parameters=authorization_parameters
        + pagination_parameters
        + date_interval_parameters
        + search_parameter,
        responses={"200": ActivityAreaRankingSerializer(many=True)},
    )
    def activity_area_ranking(self, request):
        self._set_params()
        queryset = self._service.get_activity_area_ranking_gamification(
            self.workspace_id, self.start_date, self.end_date
        )
        if self.search:
            queryset = queryset.filter(
                area_of_activity__icontains=self.search,
            )
        serializer = ActivityAreaRankingSerializer(queryset, many=True)
        page = self.paginate_queryset(serializer.data)
        return self.get_paginated_response(page)

    def _set_params(self):
        self.workspace_id = self.request.user.get("client_id") if self.request.user else None
        self.user_id = self.request.user.get("sub") if self.request.user else None
        self.start_date = self.request.GET.get("start_date") or self.LOWEST_VALID_DATE
        self.end_date = self.request.GET.get("end_date") or self.LONGEST_VALID_DATE
        self.search = self.request.GET.get("search", "")
