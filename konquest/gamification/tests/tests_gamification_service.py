from datetime import date, datetime, timedelta

from django.test import TestCase
from model_mommy import mommy

from account.models.user import User
from account.models.user_profile_workspace import UserProfileWorkspace
from account.models.workspace import Workspace
from gamification.models.gamification_history import GamificationHistory
from gamification.services.gamification_service import GamificationService
from mission.models.mission import Mission
from pulse.models.pulse import Pulse
from user_activity.models.learn_content_activity import LearnContentActivity
from user_activity.models.mission_enrollment import COMPLETED, MissionEnrollment


class TestGamificationService(TestCase):
    def setUp(self):
        self.start_date = date(1900, 1, 1)
        self.end_date = date(2024, 12, 31)
        self.user = mommy.make(User, related_user_leader=mommy.make(User))
        self.workspace = mommy.make(Workspace)
        mommy.make(
            UserProfileWorkspace,
            user=self.user,
            area_of_activity="TI",
            manager="Manager",
            director="Director",
            workspace=self.workspace,
        )
        self.mission = mommy.make(Mission, points=1000)
        self.pulse = mommy.make(Pulse, points=1, duration_time=60)
        self.pulse2 = mommy.make(Pulse, points=1, duration_time=60)
        self.service = GamificationService()

        self.mission_enrollment_1 = mommy.make(
            MissionEnrollment,
            mission=self.mission,
            user=self.user,
            workspace=self.workspace,
            performance=0.8,
            status=COMPLETED,
            end_date=datetime(2023, 5, 3),
        )
        self.mission_enrollment_2 = mommy.make(
            MissionEnrollment,
            mission=self.mission,
            user=self.user,
            workspace=self.workspace,
            performance=0.9,
            status=COMPLETED,
            end_date=datetime(2024, 1, 15),
        )
        self.pulse_1 = mommy.make(
            LearnContentActivity,
            user=self.user,
            time_stop=datetime(2023, 5, 10),
            workspace=self.workspace,
            pulse=self.pulse,
            time_in=timedelta(seconds=20),
        )
        self.pulse_2 = mommy.make(
            LearnContentActivity,
            user=self.user,
            time_stop=datetime(2024, 2, 10),
            workspace=self.workspace,
            pulse=self.pulse2,
            time_in=timedelta(seconds=55),
        )

    def create_gamification_history(self):
        self.service.create_gamification_history_from_mission(self.mission_enrollment_1)
        self.service.create_gamification_history_from_mission(self.mission_enrollment_2)
        self.service.create_gamification_history_from_pulse(self.pulse_1)
        self.service.create_gamification_history_from_pulse(self.pulse_2)

    def test_gamification_history_mission(self):
        expected_points = 800
        self.service.create_gamification_history_from_mission(self.mission_enrollment_1)

        history = GamificationHistory.objects.filter(user=self.user, mission=self.mission)
        self.assertEqual(history.count(), 1)
        self.assertEqual(history.first().points_acquired, expected_points)

        self.service.create_gamification_history_from_mission(self.mission_enrollment_2)
        history = GamificationHistory.objects.filter(user=self.user, mission=self.mission)
        self.assertEqual(history.count(), 1)
        self.assertEqual(history.first().points_acquired, expected_points)

    def test_gamification_history_pulse(self):
        self.service.create_gamification_history_from_pulse(self.pulse_1)
        history = GamificationHistory.objects.filter(user=self.user, pulse=self.pulse)
        self.assertEqual(history.count(), 0)

        self.service.create_gamification_history_from_pulse(self.pulse_2)
        history = GamificationHistory.objects.filter(user=self.user, pulse=self.pulse2)
        self.assertEqual(history.count(), 1)

        self.service.create_gamification_history_from_pulse(self.pulse_2)
        history = GamificationHistory.objects.filter(user=self.user, pulse=self.pulse2)
        self.assertEqual(history.count(), 1)

    def test_gamification_extract_user(self):
        self.create_gamification_history()
        extract_user = self.service.get_extract_user(self.user.id, self.workspace.id, self.start_date, self.end_date)
        self.assertEqual(extract_user.count(), 2)

    def test_gamification_statistics_user(self):
        self.create_gamification_history()
        statistics = self.service.get_statistics_user(self.user.id, self.workspace.id)
        self.assertEqual(statistics["position"], 1)
        self.assertEqual(statistics["completed_trails"], 0)
        self.assertEqual(statistics["completed_missions"], 1)
        self.assertEqual(statistics["consumed_pulses"], 1)
        self.assertEqual(statistics["learn_hours"], 0)
        self.assertEqual(statistics["performance_avg"], "86%")
        self.assertEqual(statistics["conclusion_rate"], "100%")

    def test_gamification_ranking_general(self):
        self.create_gamification_history()
        general_ranking = self.service.get_general_ranking_gamification(
            self.workspace.id, self.start_date, self.end_date
        )
        expected_points = (
            self.mission_enrollment_1.mission.points * self.mission_enrollment_1.performance
        ) + self.pulse2.points
        self.assertEqual(general_ranking.count(), 1)
        self.assertEqual(general_ranking[0].get("points"), expected_points)

    def test_gamification_ranking_director(self):
        self.create_gamification_history()
        director_ranking = self.service.get_director_ranking_gamification(
            self.workspace.id, self.start_date, self.end_date
        )
        expected_points = (
            self.mission_enrollment_1.mission.points * self.mission_enrollment_1.performance
        ) + self.pulse2.points

        self.assertEqual(director_ranking.count(), 1)
        self.assertEqual(director_ranking[0].get("points_avg"), expected_points)

    def test_gamification_ranking_manager(self):
        self.create_gamification_history()
        manager_ranking = self.service.get_manager_ranking_gamification(
            self.workspace.id, self.start_date, self.end_date
        )
        expected_points = (
            self.mission_enrollment_1.mission.points * self.mission_enrollment_1.performance
        ) + self.pulse2.points
        self.assertEqual(manager_ranking.count(), 1)
        self.assertEqual(manager_ranking[0].get("points_avg"), expected_points)

    def test_gamification_ranking_activity_area(self):
        self.create_gamification_history()
        manager_ranking = self.service.get_activity_area_ranking_gamification(
            self.workspace.id, self.start_date, self.end_date
        )
        expected_points = (
            self.mission_enrollment_1.mission.points * self.mission_enrollment_1.performance
        ) + self.pulse2.points

        self.assertEqual(manager_ranking.count(), 1)
        self.assertEqual(manager_ranking[0].get("points_avg"), expected_points)

    def test_gamification_ranking_leader(self):
        self.create_gamification_history()
        manager_ranking = self.service.get_leader_ranking_gamification(
            self.workspace.id, self.start_date, self.end_date
        )
        expected_points = (
            self.mission_enrollment_1.mission.points * self.mission_enrollment_1.performance
        ) + self.pulse2.points
        self.assertEqual(manager_ranking.count(), 1)
        self.assertEqual(manager_ranking[0].get("points_avg"), expected_points)
