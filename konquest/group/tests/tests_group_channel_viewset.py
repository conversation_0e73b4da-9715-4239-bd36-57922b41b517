import uuid
from unittest import mock

from django.test import TestCase, override_settings
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from pulse.models import Channel


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
@override_settings(SUSPEND_SIGNALS=True)
class GroupChannelViewsetTestCase(TestCase):
    fixtures = ["user", "workspace", "channel_type", "channel_category", "channel", "group", "group_channel"]

    def setUp(self):
        self.client = APIClient()

        self.channel_id = "411fdd8a-f7f4-4354-b08d-a318c9ffd862"
        self.channel_2_id = "6b6c05fb-c292-4667-9da9-dce928e4e31f"

        self.workspace_id = "1ef9e947-5746-4171-b218-0f9bfd6c6a30"
        self.group_id = "63c685e6-7e15-4a15-8e05-7a4b31d483bf"
        self.group_mission_id = "4ad0f5eb-ba0f-434c-b291-80cea683d73e"

        self.workspace_2_id = "1ef9e947-5746-4171-b218-0f9bfd6c6a29"
        self.group_2_id = "7ea6f902-07ed-4abf-8cc1-d91a971c5117"
        self.group_mission_2_id = "55a82f5a-e8fb-4def-9ba2-2e713843ec49"

        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.url = reverse("group-channels", args=[self.group_id])

    def test_group_missions_list(self, mock_return, mock_role):
        response = self.client.get(self.url, **self.headers, format="json").json()

        self.assertEqual(len(response["results"]), 1)

    def test_group_missions_get_not_found(self, mock_return, mock_role):
        url = reverse("group-channels", args=[str(uuid.uuid4())])
        response = self.client.get(url, **self.headers, format="json").json()

        self.assertEqual(len(response["results"]), 0)

    def test_group_missions_post_success(self, mock_return, mock_role):
        channel = mommy.make(Channel, id=uuid.uuid4(), name="New Channel")
        url = reverse("group-channel", args=[self.group_id, str(channel.id)])
        response = self.client.post(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 201)

    def test_group_missions_post_error(self, mock_return, mock_role):
        url = reverse("group-channel", args=[self.group_id, str(uuid.uuid4())])
        response = self.client.post(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 400)

    def test_group_missions_delete_success(self, mock_return, mock_role):
        url = reverse("group-channel", args=[self.group_id, self.channel_id])

        response = self.client.delete(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 204)

    def test_group_missions_delete_error(self, mock_return, mock_role):
        """
        Try delete channel not recorded
        """
        url = reverse("group-channel", args=[self.group_id, str(uuid.uuid4())])

        response = self.client.delete(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 404)

    def test_group_missions_delete_error_workspace(self, mock_return, mock_role):
        """
        Try delete (remove) channel from group of workspace 2 logged with workspace 1
        """
        url = reverse("group-channel", args=[self.group_2_id, self.channel_id])

        response = self.client.delete(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 403)

    def test_group_missions_post_batch_success(self, mock_return, mock_role):
        channel_1 = mommy.make(Channel, id=uuid.uuid4(), name="New Channel 1")
        channel_2 = mommy.make(Channel, id=uuid.uuid4(), name="New Channel 2")

        data = [str(channel_1.id), str(channel_2.id)]

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response_json), 2)
        self.assertEqual(response_json["success"][0]["channel"], data[0])
        self.assertEqual(response_json["success"][1]["channel"], data[1])

    def test_group_missions_post_batch_error(self, mock_return, mock_role):
        channel_1 = mommy.make(Channel, id=uuid.uuid4(), name="New Channel 1")

        data = [str(channel_1.id), str(uuid.uuid4())]

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response_json), 2)
        self.assertEqual(response_json["success"][0]["channel"], data[0])
        self.assertEqual(response_json["errors"][0]["channel"], data[1])
