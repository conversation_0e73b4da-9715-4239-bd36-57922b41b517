import uuid
from datetime import datetime
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from account.models import User
from account.models.job import Job
from account.models.user_profile_workspace import UserProfileWorkspace
from custom.key_errors import USER_NOT_FOUND
from group.models import GroupLearningTrail, GroupMission, GroupUser
from mission.models import MissionWorkspace
from myaccount.application.services.myaccount_service import MyAccountService

ACCOUNT_ADD_USER_ROLE = "rest_clients.myaccount.MyAccountClient.add_user_role"
SEND_TASK = "celery.Celery.send_task"


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class GroupUserViewsetTestCase(TestCase):
    fixtures = ["user", "workspace", "group", "mission_type", "mission_category", "mission"]

    def setUp(self):
        self.client = APIClient()

        self.user_id = "4c41bde6-ebc5-48a3-86d6-75eceebebe89"
        self.user_2_id = "2b7bbd80-7fce-43ae-9469-c119cb506b94"

        self.workspace_id = "1ef9e947-5746-4171-b218-0f9bfd6c6a30"
        self.group_id = "63c685e6-7e15-4a15-8e05-7a4b31d483bf"

        # Precisa importar o fixture
        self.group_user = mommy.make(GroupUser, id=uuid.uuid4(), user_id=self.user_id, group_id=self.group_id)

        self.workspace_2_id = "1ef9e947-5746-4171-b218-0f9bfd6c6a29"
        self.group_2_id = "7ea6f902-07ed-4abf-8cc1-d91a971c5117"

        # Precisa importar o fixture
        self.group_user2 = mommy.make(GroupUser, id=uuid.uuid4(), user_id=self.user_id, group_id=self.group_2_id)

        self.group_without_missions_id = "799a2ca5-4cbb-492e-8409-f8b3acf3338f"

        self.mission_id = "e68ec9ed-30ed-4f96-8e81-e782cd3b0f89"
        self.mission_2_id = "d48eac73-c363-4c1a-9683-ba46a6b97120"
        self.mission_3_id = "6d60a7df-0fb5-4551-915e-9f792b317ee5"
        MissionWorkspace(mission_id=self.mission_id, workspace_id=self.workspace_id).save()
        MissionWorkspace(mission_id=self.mission_2_id, workspace_id=self.workspace_id).save()

        self.group_mission = mommy.make(GroupMission, mission_id=self.mission_id, group_id=self.group_id)
        self.group_mission = mommy.make(GroupMission, mission_id=self.mission_2_id, group_id=self.group_id)
        self.group_trail = mommy.make(GroupLearningTrail, group_id=self.group_id)

        self.headers = {"HTTP_X_CLIENT": self.workspace_id, "HTTP_AUTHORIZATION": "Bearer 123"}
        self.url = reverse("group-users", args=[self.group_id])

    def test_group_users_list(self, mock_return, mock_role):
        response = self.client.get(self.url, **self.headers, format="json").json()

        self.assertEqual(len(response["results"]), 1)

    def test_group_users_list_job(self, mock_return, mock_role):
        job = mommy.make(Job, name="New Job")
        mommy.make(UserProfileWorkspace, user_id=self.user_id, workspace_id=self.workspace_id, job_position=job)
        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(response["results"][0]["user"]["job"], job.name)

    def test_group_user_get_not_found(self, mock_return, mock_role):
        url = reverse("group-users", args=[str(uuid.uuid4())])
        response = self.client.get(url, **self.headers, format="json").json()

        self.assertEqual(len(response["results"]), 0)

    def test_group_user_post_success(self, mock_return, mock_role):
        user = mommy.make(User, id=uuid.uuid4(), name="New User")
        url = reverse("group-user", args=[self.group_id, str(user.id)])
        response = self.client.post(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 201)

    def test_group_user_post_error(self, mock_return, mock_role):
        url = reverse("group-user", args=[self.group_id, str(uuid.uuid4())])
        response = self.client.post(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 400)

    def test_group_user_delete_success(self, mock_return, mock_role):
        url = reverse("group-user", args=[self.group_id, self.user_id])

        response = self.client.delete(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 204)

    def test_group_user_delete_error(self, mock_return, mock_role):
        """
        Try delete user not recorded
        """
        url = reverse("group-user", args=[self.group_id, str(uuid.uuid4())])

        response = self.client.delete(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 404)

    def test_group_user_delete_error_workspace(self, mock_return, mock_role):
        """
        Try delete (remove) user from group of workspace 2 logged with workspace 1
        """
        url = reverse("group-user", args=[self.group_2_id, self.user_id])

        response = self.client.delete(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 403)

    @mock.patch.object(MyAccountService, "list_all_consumer_users")
    def test_group_user_post_batch_success(self, list_all_consumer_users: mock.MagicMock, mock_return, mock_role):
        user_1 = mommy.make(User, id=uuid.uuid4(), name="New User 1")
        user_2 = mommy.make(User, id=uuid.uuid4(), name="New User 2")
        list_all_consumer_users.return_value = User.objects.all()

        data = {"users": [str(user_1.id), str(user_2.id)], "enrollment_goal_date": None}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response_json["group_user_errors"]), 0)

    @mock.patch.object(MyAccountService, "list_all_consumer_users")
    def test_group_user_post_batch_success_user_already_linked_deleted(
        self, list_all_consumer_users: mock.MagicMock, mock_return, mock_role
    ):
        user_1 = mommy.make(User, id=uuid.uuid4(), name="New User 1")
        user_2 = mommy.make(User, id=uuid.uuid4(), name="User already linked deleted")

        mommy.make(GroupUser, id=uuid.uuid4(), user_id=user_2.id, group_id=self.group_id, deleted=True)

        list_all_consumer_users.return_value = User.objects.all()

        data = {"users": [str(user_1.id), str(user_2.id)], "enrollment_goal_date": None}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response_json["group_user_errors"]), 0)

    @mock.patch.object(MyAccountService, "list_all_consumer_users")
    def test_group_user_post_batch_error(self, list_all_consumer_users: mock.MagicMock, mock_return, mock_role):
        user_1 = mommy.make(User, id=uuid.uuid4(), name="New User 1")
        list_all_consumer_users.return_value = User.objects.all()
        user_ids = {"users": [str(uuid.uuid4()), str(user_1.id)]}

        response = self.client.post(self.url, **self.headers, data=user_ids, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        list_all_consumer_users.assert_called()
        self.assertEqual(len(response_json["group_user_errors"]), 1)
        self.assertEqual(response_json["group_user_errors"][0]["error"]["i18n"], USER_NOT_FOUND)

    @mock.patch(SEND_TASK)
    @mock.patch.object(MyAccountService, "list_all_consumer_users")
    def test_group_user_post_with_enrollment_success(
        self, list_all_consumer_users, send_task: mock.MagicMock, mock_return, mock_role
    ):
        user_1 = mommy.make(User, id=uuid.uuid4(), name="New User 1")
        user_2 = mommy.make(User, id=uuid.uuid4(), name="New User 2")
        user_3 = mommy.make(User, id=uuid.uuid4(), name="New User 3")
        list_all_consumer_users.return_value = User.objects.filter(id__in=[user_1.id, user_2.id])

        data = {
            "users": [str(user_1.id), str(user_2.id)],
            "enrollment_goal_date": str(datetime.today()),
            "enrollment_required_mission": True,
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        group_users = GroupUser.objects.filter(group_id=self.group_id)
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        send_task.assert_called()
        self.assertEqual(group_users.filter(user_id__in=[user_1.id, user_2.id]).count(), 2)
        self.assertFalse(group_users.filter(user_id=user_3.id).exists())
        self.assertEqual(0, len(response_json["group_user_errors"]))

    @mock.patch(SEND_TASK)
    @mock.patch.object(MyAccountService, "list_all_consumer_users")
    def test_group_user_post_warning_mission_inactive(
        self, list_all_consumer_users, send_task: mock.MagicMock, mock_return, mock_allowed_companies
    ):
        user_1 = mommy.make(User, id=uuid.uuid4(), name="New User 1")

        self.group_mission.mission.development_status = "INACTIVATED"
        self.group_mission.mission.save()

        list_all_consumer_users.return_value = User.objects.all()

        data = {"users": [str(user_1.id)], "enrollment_goal_date": str(datetime.today())}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response_json["group_user_warnings"][0]["warning_detail"]["i18n"],
            "unable_to_enroll_in_an_in_inactive_mission",
        )

    def test_grou_user_filter_deleted(self, mock_return, mock_role):
        self.group_user.deleted = True
        self.group_user.save()
        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)
        response = self.client.get(self.url, **self.headers, format="json", data={"deleted": "false"}).json()
        self.assertEqual(len(response["results"]), 0)

    def test_grou_user_deleted_unabled_sucess(self, mock_return, mock_role):
        self.group_user_deleted = mommy.make(GroupUser, user_id=self.user_2_id, group_id=self.group_id, deleted=True)
        self.assertEqual(len(GroupUser.objects.get_all_including_deleted()), 3)

        self.assertEqual(len(GroupUser.objects.get_all_including_deleted()), 3)
        response = self.client.delete(
            reverse("delete-unabled-group-user", args=[self.group_id]), **self.headers, format="json"
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(GroupUser.objects.get_all_including_deleted()), 2)

    def test_deleted_unabled_group_not_exists_in_workspace(self, mock_return, mock_role):
        response = self.client.delete(
            reverse("delete-unabled-group-user", args=[self.group_2_id]), **self.headers, format="json"
        )
        self.assertEqual(response.status_code, 403)
