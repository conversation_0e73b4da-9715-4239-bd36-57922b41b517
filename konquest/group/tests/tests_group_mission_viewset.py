import uuid
from datetime import datetime
from unittest import mock

from django.conf import settings
from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from account.models import User
from authentication.keeps_permissions import ADMIN
from group.models import GroupMission, GroupUser
from mission.models import Mission, MissionWorkspace
from myaccount.application.services.myaccount_service import MyAccountService
from user_activity.models import MissionEnrollment


@mock.patch("user_activity.tasks.notifications.notify_enrolled_in_a_new_mission.delay")
@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class GroupMissionViewsetTestCase(TestCase):
    fixtures = [
        "base",
        "group_mission",
        "group_user",
        "user",
        "workspace",
        "group",
        "mission_type",
        "mission_category",
        "mission",
        "mission_workspace",
    ]

    def setUp(self):
        self.client = APIClient()

        self.mission_id = "78484ad1-96e5-4ee0-88a1-3860e3698147"
        self.mission_2_id = "8e371e74-59b8-42c3-9ffa-a0545a01a288"

        self.workspace_id = "1ef9e947-5746-4171-b218-0f9bfd6c6a28"
        self.group_id = "98c9741e-5c9f-485b-be2a-0b3327c2a691"

        self.group_mission = mommy.make(
            GroupMission, id=uuid.uuid4(), mission_id=self.mission_id, group_id=self.group_id
        )
        self.admin = mommy.make(User)

        self.workspace_2_id = "d27b7876-9936-4529-b394-f7df10fe5899"
        self.group_2_id = "4e3e5c92-da7c-41a1-bd53-b788abe1d44a"

        self.group_mission2 = mommy.make(
            GroupMission, id=uuid.uuid4(), mission_id=self.mission_id, group_id=self.group_2_id
        )

        self.headers = {"HTTP_X_CLIENT": str(self.workspace_id)}
        self.url = reverse("group-missions", args=[self.group_id])

    def test_group_missions_list(self, mock_return, mock_role, notify_enrolled):
        response = self.client.get(self.url, **self.headers, format="json").json()

        self.assertEqual(len(response["results"]), 1)

    def test_group_missions_get_not_found(self, mock_return, mock_role, notify_enrolled):
        url = reverse("group-missions", args=[str(uuid.uuid4())])
        response = self.client.get(url, **self.headers, format="json").json()

        self.assertEqual(len(response["results"]), 0)

    def test_group_missions_post_success(self, mock_return, mock_role, notify_enrolled):
        mission = mommy.make(Mission, id=uuid.uuid4(), name="New Mission")
        url = reverse("group-mission", args=[self.group_id, str(mission.id)])
        response = self.client.post(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 201)

    def test_group_missions_post_error_mission_not_found(self, mock_return, mock_role, notify_enrolled):
        url = reverse("group-mission", args=[self.group_id, str(uuid.uuid4())])
        response = self.client.post(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 400)

    def test_group_missions_post_error_already_exists(self, mock_return, mock_role, notify_enrolled):
        url = reverse("group-mission", args=[self.group_id, self.mission_id])
        response = self.client.post(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data.get("i18n"), "group_mission_already_exists")

    def test_group_missions_delete_success(self, mock_return, mock_role, notify_enrolled):
        url = reverse("group-mission", args=[self.group_id, self.mission_id])

        response = self.client.delete(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 204)

    def test_group_missions_delete_error(self, mock_return, mock_role, notify_enrolled):
        """
        Try delete mission not recorded
        """
        url = reverse("group-mission", args=[self.group_id, str(uuid.uuid4())])

        response = self.client.delete(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 404)

    def test_group_missions_delete_error_workspace(self, mock_return, mock_role, notify_enrolled):
        """
        Try delete (remove) mission from group of workspace 2 logged with workspace 1
        """
        url = reverse("group-mission", args=[self.group_2_id, self.mission_id])

        response = self.client.delete(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 403)

    def test_group_missions_post_batch_success(self, mock_return, mock_role, notify_enrolled):
        self.client.force_authenticate(user={"sub": str(self.admin.id), "client_id": self.workspace_id, "role": ADMIN})
        mission_1 = mommy.make(Mission, id=uuid.uuid4())
        mission_2 = mommy.make(Mission, id=uuid.uuid4())
        mommy.make(MissionWorkspace, id=uuid.uuid4(), workspace_id=self.workspace_id, mission=mission_1)
        mommy.make(MissionWorkspace, id=uuid.uuid4(), workspace_id=self.workspace_id, mission=mission_2)

        data = {"missions": [str(mission_1.id), str(mission_2.id)]}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response_json["group_mission_errors"]), 0)

    def test_group_missions_post_batch_error(self, mock_return, mock_role, notify_enrolled):
        self.client.force_authenticate(user={"sub": str(self.admin.id), "client_id": self.workspace_id, "role": ADMIN})
        mission_1 = mommy.make(Mission, id=uuid.uuid4(), name="New Mission 1")
        mommy.make(MissionWorkspace, id=uuid.uuid4(), mission=mission_1, workspace_id=self.workspace_id)

        data = {"missions": [str(mission_1.id), str(uuid.uuid4())]}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response_json["group_mission_errors"]), 1)
        self.assertEqual(response_json["group_mission_errors"][0]["mission"]["id"], data["missions"][1])
        self.assertEqual(response_json["group_mission_errors"][0]["error"]["i18n"], "mission_not_found")

    @mock.patch.object(MyAccountService, "has_access", return_value=True)
    def test_group_missions_post_with_enrollment_success(
        self, mock_return, mock_role, notify_enrolled, mock_user_has_permission
    ):
        self.client.force_authenticate(user={"sub": str(self.admin.id), "client_id": self.workspace_id, "role": ADMIN})
        mission_1 = mommy.make(Mission, id=uuid.uuid4(), name="New Mission 1")
        mission_2 = mommy.make(Mission, id=uuid.uuid4(), name="New Mission 2")
        mommy.make(MissionWorkspace, id=uuid.uuid4(), mission=mission_1, workspace_id=self.workspace_id)
        mommy.make(MissionWorkspace, id=uuid.uuid4(), mission=mission_2, workspace_id=self.workspace_id)

        user_1 = mommy.make(User, id=uuid.uuid4(), name="New user 1")
        user_2 = mommy.make(User, id=uuid.uuid4(), name="New user 2")

        mommy.make(GroupUser, user_id=user_1.id, group_id=self.group_id)
        mommy.make(GroupUser, user_id=user_2.id, group_id=self.group_id)

        data = {
            "missions": [str(mission_1.id), self.mission_2_id],
            "enrollment_goal_date": str(datetime.now()),
            "enrollment_required_mission": True,
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        enrollment_1 = MissionEnrollment.objects.filter(user=user_1, mission=mission_1).first()

        self.assertEqual(200, response.status_code)
        self.assertEqual(0, len(response_json["enrollment_errors"]))
        self.assertIsNotNone(enrollment_1.goal_date)
        self.assertEqual(enrollment_1.required, data.get("enrollment_required_mission"))

    def test_group_missions_post_with_enrollment_error_no_users_in_group(self, mock_return, mock_role, notify_enrolled):
        self.client.force_authenticate(user={"sub": str(self.admin.id), "client_id": self.workspace_id, "role": ADMIN})
        mission_1 = mommy.make(Mission, id=uuid.uuid4(), name="New Mission 1")
        mission_2 = mommy.make(Mission, id=uuid.uuid4(), name="New Mission 2")
        mommy.make(MissionWorkspace, id=uuid.uuid4(), mission=mission_1, workspace_id=self.workspace_id)
        mommy.make(MissionWorkspace, id=uuid.uuid4(), mission=mission_2, workspace_id=self.workspace_id)
        GroupUser.objects.filter(group_id=self.group_id).delete()

        data = {"missions": [str(mission_1.id), self.mission_2_id], "enrollment_goal_date": str(datetime.now())}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        print(response_json)
        self.assertEqual(response_json["enrollment_errors"][0]["error"]["i18n"], "not_found_missions_or_users")

    @mock.patch("authentication.keeps_permissions.KeepsBasePermission.get_user_roles", return_value=["admin"])
    def test_group_missions_retrieve_without_group_access(self, mock_return, mock_role, mock_rules, notify_enrolled):
        """
        User Case: When user was added into group, enrolled in a mission and the manager (admin) remove the user
        from the group where private mission (CLOSE FOR WORKSPACE) is linked and don't exclude user enrolled should
        be possible user still access the mission to complete their mission.
        """
        private_mission_id = "e91c6bff-fb4f-42b4-91d9-8147fd121f6c"
        group_user_id = "1c1df74c-82d9-46a5-9db0-e1bfc4473081"
        group_user = GroupUser.objects.get(id=group_user_id)
        mommy.make(GroupMission, group_id=group_user.group_id, mission_id=private_mission_id)
        user_id = group_user.user_id
        self.client.force_authenticate(user={"sub": str(user_id), "client_id": self.workspace_id})
        mission_stage_retrieve_url = reverse("mission-stages-list", args=[private_mission_id])

        response_before_remove_user = self.client.get(mission_stage_retrieve_url, **self.headers, format="json")
        mommy.make(MissionEnrollment, mission_id=private_mission_id, user_id=user_id, workspace_id=self.workspace_id)
        GroupUser.objects.filter(id=group_user_id).delete()
        response_after_remove_user = self.client.get(mission_stage_retrieve_url, **self.headers, format="json")

        self.assertEqual(200, response_before_remove_user.status_code)
        self.assertEqual(200, response_after_remove_user.status_code)

    def test_group_missions_retrieve_stages_without_group_access(self, mock_return, mock_role, notify_enrolled):
        """
        User Case: When user was added into group, enrolled in a mission and the manager (admin) remove the user
        from the group where private mission (CLOSE FOR COMPANY) is linked and don't exclude user enrolled should
        be possible user still access the mission stage to access classroom and complete their mission.
        """
        private_mission_id = "e91c6bff-fb4f-42b4-91d9-8147fd121f6c"
        group_user_id = "1c1df74c-82d9-46a5-9db0-e1bfc4473081"
        group_user = GroupUser.objects.get(id=group_user_id)
        mommy.make(GroupMission, group_id=group_user.group_id, mission_id=private_mission_id)
        user_id = group_user.user_id
        self.client.force_authenticate(user={"sub": str(user_id), "client_id": self.workspace_id})
        mission_stage_retrieve_url = reverse("mission-stages-list", args=[private_mission_id])

        response_before_remove_user = self.client.get(mission_stage_retrieve_url, **self.headers, format="json")
        mommy.make(MissionEnrollment, mission_id=private_mission_id, user_id=user_id, workspace_id=self.workspace_id)
        GroupUser.objects.filter(id=group_user_id).delete()
        response_after_remove_user = self.client.get(mission_stage_retrieve_url, **self.headers, format="json")

        self.assertEqual(200, response_before_remove_user.status_code)
        self.assertEqual(200, response_after_remove_user.status_code)

    def test_not_permission_remove_this_group_from_mission(self, mock_return, mock_role, notify_enrolled):
        self.group_mission.group.name = settings.GROUP_NAME_ALURA
        self.group_mission.group.save()
        url = reverse("group-mission", args=[self.group_id, self.mission_id])

        response = self.client.delete(url, **self.headers, format="json")
        self.assertEqual(response.json()["i18n"], "not_permission_add_or_remove_this_group_from_mission")

    def test_not_permission_add_this_group_from_mission(self, mock_return, mock_role, notify_enrolled):
        self.group_mission.group.name = settings.GROUP_NAME_ALURA
        self.group_mission.group.save()
        url = reverse("group-mission", args=[self.group_id, self.mission_id])

        response = self.client.post(url, **self.headers, format="json")
        self.assertEqual(response.json()["i18n"], "not_permission_add_or_remove_this_group_from_mission")
