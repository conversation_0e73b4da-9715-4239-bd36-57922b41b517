import uuid
from datetime import datetime
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from account.models import User
from authentication.keeps_permissions import ADMIN
from group.models import GroupLearningTrail, GroupUser
from learning_trail.models import LearningTrail, LearningTrailWorkspace
from observer.event_manager import EventManager

ENROLL_USERS_TASK_PATH = "user_activity.tasks.learning_trail_enrollment_task.enroll_users.delay"


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class GroupLearningTrailViewsetTestCase(TestCase):
    fixtures = ["user", "workspace", "group", "learning_trail_type", "learning_trail"]

    def setUp(self):
        self.client = APIClient()

        self.learning_trail_id = "a668af44-76fd-49c5-ba8e-a4d05098e4a7"
        self.learning_trail_2_id = "a735399b-a851-4310-8d10-db7921a0c700"

        self.workspace_id = "1ef9e947-5746-4171-b218-0f9bfd6c6a28"
        self.group_id = "98c9741e-5c9f-485b-be2a-0b3327c2a691"

        self.group_learning_trail = mommy.make(
            GroupLearningTrail, id=uuid.uuid4(), learning_trail_id=self.learning_trail_id, group_id=self.group_id
        )

        self.workspace_2_id = "d27b7876-9936-4529-b394-f7df10fe5899"
        self.group_2_id = "4e3e5c92-da7c-41a1-bd53-b788abe1d44a"

        self.group_learning_trail2 = mommy.make(
            GroupLearningTrail, id=uuid.uuid4(), learning_trail_id=self.learning_trail_id, group_id=self.group_2_id
        )

        self.headers = {"HTTP_X_CLIENT": self.workspace_id}
        self.url = reverse("group-learning-trails", args=[self.group_id])

    def test_group_learning_trails_list(self, mock_return, mock_role):
        response = self.client.get(self.url, **self.headers, format="json").json()

        self.assertEqual(len(response["results"]), 1)

    def test_group_learning_trails_get_not_found(self, mock_return, mock_role):
        url = reverse("group-learning-trails", args=[str(uuid.uuid4())])
        response = self.client.get(url, **self.headers, format="json").json()

        self.assertEqual(len(response["results"]), 0)

    def test_group_learning_trails_post_success(self, mock_return, mock_role):
        learning_trail = mommy.make(LearningTrail, id=uuid.uuid4(), name="New learning_trail")
        url = reverse("group-learning-trail", args=[self.group_id, str(learning_trail.id)])
        response = self.client.post(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 201)

    def test_group_learning_trails_post_error(self, mock_return, mock_role):
        url = reverse("group-learning-trail", args=[self.group_id, str(uuid.uuid4())])
        response = self.client.post(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 400)

    def test_group_learning_trails_delete_success(self, mock_return, mock_role):
        url = reverse("group-learning-trail", args=[self.group_id, self.learning_trail_id])

        response = self.client.delete(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 204)

    def test_group_learning_trails_delete_error(self, mock_return, mock_role):
        """
        Try delete learning_trail not recorded
        """
        url = reverse("group-learning-trail", args=[self.group_id, str(uuid.uuid4())])

        response = self.client.delete(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 404)

    def test_group_learning_trails_delete_error_workspace(self, mock_return, mock_role):
        """
        Try delete (remove) learning_trail from group of workspace 2 logged with workspace 1
        """
        url = reverse("group-learning-trail", args=[self.group_2_id, self.learning_trail_id])

        response = self.client.delete(url, **self.headers, format="json")

        self.assertEqual(response.status_code, 404)

    def test_group_learning_trails_post_batch_success(self, mock_return, mock_role):
        learning_trail_1 = mommy.make(LearningTrail, id=uuid.uuid4(), name="New learning trail 1")
        learning_trail_2 = mommy.make(LearningTrail, id=uuid.uuid4(), name="New learning trail 2")
        mommy.make(
            LearningTrailWorkspace, id=uuid.uuid4(), workspace_id=self.workspace_id, learning_trail=learning_trail_1
        )
        mommy.make(
            LearningTrailWorkspace, id=uuid.uuid4(), workspace_id=self.workspace_id, learning_trail=learning_trail_2
        )

        data = {"learning_trails": [str(learning_trail_1.id), str(learning_trail_2.id)]}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)

        self.assertEqual(len(response_json["group_learning_trail_errors"]), 0)

    def test_group_learning_trails_post_batch_error(self, mock_return, mock_role):
        learning_trail_1 = mommy.make(LearningTrail, id=uuid.uuid4(), name="New learning_trail 1")
        mommy.make(
            LearningTrailWorkspace, id=uuid.uuid4(), learning_trail=learning_trail_1, workspace_id=self.workspace_id
        )

        data = {"learning_trails": [str(learning_trail_1.id), str(uuid.uuid4())]}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response_json["group_learning_trail_errors"]), 1)
        self.assertEqual(
            response_json["group_learning_trail_errors"][0]["learning_trail"]["id"], data.get("learning_trails")[1]
        )
        self.assertEqual(response_json["group_learning_trail_errors"][0]["error"]["i18n"], "learning_trail_not_found")

    @mock.patch(
        ENROLL_USERS_TASK_PATH,
        return_value={},
    )
    @mock.patch.object(EventManager, "notify")
    def test_group_learning_trails_post_with_enrollment_success(
        self, mock_enroll_users, mock_return, mock_role, notify: mock.MagicMock
    ):
        learning_trail_1 = mommy.make(LearningTrail, id=uuid.uuid4(), name="New learning_trail 1")
        learning_trail_2 = mommy.make(LearningTrail, id=uuid.uuid4(), name="New learning_trail 2")
        mommy.make(
            LearningTrailWorkspace, id=uuid.uuid4(), learning_trail=learning_trail_1, workspace_id=self.workspace_id
        )
        mommy.make(
            LearningTrailWorkspace, id=uuid.uuid4(), learning_trail=learning_trail_2, workspace_id=self.workspace_id
        )

        user_1 = mommy.make(User, id=uuid.uuid4(), name="New user 1")
        user_2 = mommy.make(User, id=uuid.uuid4(), name="New user 2")

        mommy.make(GroupUser, user_id=user_1.id, group_id=self.group_id)
        mommy.make(GroupUser, user_id=user_2.id, group_id=self.group_id)

        data = {
            "learning_trails": [str(learning_trail_1.id), str(learning_trail_2.id)],
            "enrollment_goal_date": str(datetime.now()),
        }

        self.client.force_authenticate(user={"sub": user_1.id, "client_id": self.workspace_id, "role": ADMIN})

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response_json["enrollment_errors"]), 0)

    @mock.patch(
        "group.services.group_learning_trail_service.GroupLearningTrailService.link_learning_trails_in_group",
        return_value={},
    )
    @mock.patch.object(EventManager, "notify")
    def test_group_learning_trails_with_enrollment_with_cycle_post_success(
        self, mock_enroll_users, mock_return, mock_role, notify: mock.MagicMock
    ):
        learning_trail_1 = mommy.make(LearningTrail, id=uuid.uuid4(), name="New learning_trail 1")
        learning_trail_2 = mommy.make(LearningTrail, id=uuid.uuid4(), name="New learning_trail 2")
        mommy.make(
            LearningTrailWorkspace, id=uuid.uuid4(), learning_trail=learning_trail_1, workspace_id=self.workspace_id
        )
        mommy.make(
            LearningTrailWorkspace, id=uuid.uuid4(), learning_trail=learning_trail_2, workspace_id=self.workspace_id
        )

        user_1 = mommy.make(User, id=uuid.uuid4(), name="New user 1")
        user_2 = mommy.make(User, id=uuid.uuid4(), name="New user 2")

        mommy.make(GroupUser, user_id=user_1.id, group_id=self.group_id)
        mommy.make(GroupUser, user_id=user_2.id, group_id=self.group_id)
        cycle_id = uuid.uuid4()
        data = {
            "learning_trails": [str(learning_trail_1.id), str(learning_trail_2.id)],
            "enrollment_goal_date": str(datetime.now()),
            "regulatory_compliance_cycle_id": cycle_id,
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response.json()

        mock_return.assert_called_once_with(
            [learning_trail_1.id, learning_trail_2.id],
            uuid.UUID(self.group_id),
            str(self.workspace_id),
            datetime.now().date(),
            str(cycle_id),
        )
