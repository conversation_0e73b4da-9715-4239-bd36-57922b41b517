from unittest import mock

from django.test import TestCase, override_settings
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from account.models import Workspace
from authentication.keeps_permissions import USER
from group.models import GroupMission, GroupUser
from mission.models import Mission, MissionWorkspace


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info")
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
@override_settings(SUSPEND_SIGNALS=True)
class GroupFilterMissionViewsetTestCase(TestCase):
    """
    Test case to check if Missions must be listed correctly.
    """

    fixtures = ["mission_type", "mission_category", "user", "workspace", "mission", "group"]

    def setUp(self):
        self.client = APIClient()

        self.user_creator_id = "00e70a6e-f2c6-435d-91e5-f9496d6c99c2"

        self.user_keeps_id = "5f1f2d49-e408-4f46-a6d7-cee808c22e22"
        self.user_bayer_id = "6d3beafd-6d1d-4135-a74e-3538a0bcd28d"
        self.user_all_id = "4f70d72b-d5ab-4293-93d0-c657d8b718b3"

        self.workspace_keeps_id = "a6f33710-c07b-4051-9a80-c30ac8f6ebf1"
        self.workspace_bayer_id = "d27b7876-9936-4529-b394-f7df10fe5899"
        self.workspace = Workspace.objects.get(id=self.workspace_bayer_id)
        self.keeps_mission_a_id = "cc0530f7-9e6e-447b-a36c-7d43720eb426"

        self.keeps_mission_b_id = "e95802c9-47e8-430f-b1b1-8af583756d98"

        self.keeps_mission_c_id = "42f7e63e-a1b7-4cd0-909c-ff290650a38c"

        mommy.make(MissionWorkspace, mission_id=self.keeps_mission_a_id, workspace_id=self.workspace_keeps_id)
        mommy.make(MissionWorkspace, mission_id=self.keeps_mission_b_id, workspace_id=self.workspace_keeps_id)
        mommy.make(MissionWorkspace, mission_id=self.keeps_mission_c_id, workspace_id=self.workspace_keeps_id)

        self.bayer_mission_a_id = "e91c6bff-fb4f-42b4-91d9-8147fd121f6c"

        self.bayer_mission_b_id = "e95802c9-47e8-430f-b1b1-8af583756d98"

        mommy.make(MissionWorkspace, mission_id=self.bayer_mission_a_id, workspace_id=self.workspace_bayer_id)
        mommy.make(MissionWorkspace, mission_id=self.bayer_mission_b_id, workspace_id=self.workspace_bayer_id)

        self.keeps_g1_id = "0d4c3ab9-4115-4f60-92d4-e23354f4eaec"
        self.keeps_g2_id = "d6108fe7-9e93-4092-b6f1-dddb7c2e808e"
        self.bayer_g1_id = "bf926b0d-5e06-4c7e-9d60-a63e538ee72d"

        mommy.make(GroupMission, mission_id=self.keeps_mission_a_id, group_id=self.keeps_g1_id)
        mommy.make(GroupMission, mission_id=self.keeps_mission_a_id, group_id=self.keeps_g2_id)
        mommy.make(GroupMission, mission_id=self.keeps_mission_b_id, group_id=self.keeps_g2_id)
        mommy.make(GroupMission, mission_id=self.bayer_mission_a_id, group_id=self.bayer_g1_id)

        mommy.make(GroupUser, user_id=self.user_keeps_id, group_id=self.keeps_g1_id)
        mommy.make(GroupUser, user_id=self.user_all_id, group_id=self.keeps_g2_id)
        mommy.make(GroupUser, user_id=self.user_all_id, group_id=self.bayer_g1_id)

        self.keeps_mission_a = Mission.objects.get(id=self.keeps_mission_a_id)
        self.keeps_mission_c = Mission.objects.get(id=self.keeps_mission_c_id)
        self.bayer_mission_b = Mission.objects.get(id=self.bayer_mission_b_id)

        self.url = reverse("missions-list")

    def test_mission_workspace_keeps_user_keeps(self, mock_return, mock_return_roles):
        """
        user_keeps --> keeps_g1 (group 1)
        keeps_mission_a --> keeps_g1 (group 1)

        user_keeps should get keeps_mission_c (open for workspace) and keeps_mission_a (close).
        """
        self.headers = {"HTTP_X_CLIENT": self.user_keeps_id}
        self.client.force_authenticate(
            user={"sub": self.user_keeps_id, "client_id": self.workspace_keeps_id, "role": USER}
        )

        response = self.client.get(self.url, **self.headers, format="json").json()
        print(response)
        self.assertEqual(len(response["results"]), 3)
        _ids = [x["id"] for x in response["results"]]
        _names = [x["name"] for x in response["results"]]

        self.assertIn(self.keeps_mission_a_id, _ids)
        self.assertIn(self.keeps_mission_c_id, _ids)
        self.assertIn(self.keeps_mission_a.name, _names)
        self.assertIn(self.keeps_mission_c.name, _names)

    def test_mission_workspace_bayer_user_bayer(self, mock_return, mock_return_roles):
        """
        user_bayer no group linked

        user_bayer should get only bayer_mission_b (open for workspace)
        """
        self.headers = {"HTTP_X_CLIENT": self.workspace_bayer_id}
        self.client.force_authenticate(
            user={"sub": self.user_bayer_id, "client_id": self.workspace_bayer_id, "role": USER}
        )

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)

        # mission open
        self.assertEqual(response["results"][0]["id"], self.bayer_mission_b_id)
        self.assertEqual(response["results"][0]["name"], self.bayer_mission_b.name)

    def test_mission_workspace_keeps_user_full(self, mock_return, mock_return_roles):
        """
        user_all ---> keeps_g2 (group 2 keeps)
        user_all ---> bayer_g1 (group 1 bayer)

        keeps_g2 ---> keeps_mission_a
        keeps_g2 ---> keeps_mission_b

        bayer_g1 ---> bayer_mission_a


        user_all logged with workspace_keeps should get keeps_mission_a (close),
        keeps_mission_b (close), keeps_mission_c (open)

        """
        self.headers = {"HTTP_X_CLIENT": self.user_keeps_id}
        self.client.force_authenticate(
            user={"sub": self.user_all_id, "client_id": self.workspace_keeps_id, "role": USER}
        )

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 3)
        _ids = [x["id"] for x in response["results"]]

        self.assertIn(self.keeps_mission_a_id, _ids)
        self.assertIn(self.keeps_mission_b_id, _ids)
        self.assertIn(self.keeps_mission_c_id, _ids)

    def test_mission_workspace_bayer_user_full(self, mock_return, mock_return_roles):
        """
        user_all ---> keeps_g2 (group 2 keeps)
        user_all ---> bayer_g1 (group 1 bayer)

        keeps_g2 ---> keeps_mission_a
        keeps_g2 ---> keeps_mission_b

        bayer_g1 ---> bayer_mission_a


        user_all logged with workspace_bayer should get bayer_mission_a (close) and bayer_mission_b (open)

        """
        self.headers = {"HTTP_X_CLIENT": self.workspace_bayer_id}
        self.client.force_authenticate(
            user={"sub": self.user_all_id, "client_id": self.workspace_bayer_id, "role": USER}
        )

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 3)

        _ids = [x["id"] for x in response["results"]]

        self.assertIn(self.bayer_mission_a_id, _ids)
        self.assertIn(self.bayer_mission_b_id, _ids)
