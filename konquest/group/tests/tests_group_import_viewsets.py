from uuid import uuid4

import mock
import pandas as pd
from django.db.models import QuerySet
from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from account.models import User, Workspace
from config.settings import TEMP_FOLDER
from group.models import Group, GroupUser
from group.views.group_user_viewset import GROUP_COLUMN, USER_COLUMN
from myaccount.application.services.myaccount_service import MyAccountService
from myaccount.domain.repositories.myaccount_respository import UserHasPermissionDTO


@mock.patch.object(MyAccountService, "has_access")
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class GroupImportViewsetsTestCase(TestCase):
    def setUp(self) -> None:
        self.client = APIClient()
        self.import_users_url = reverse("group-users-import")
        self.workspace = mommy.make(Workspace)
        self.user_1 = mommy.make(User, email="<EMAIL>")
        self.user_2 = mommy.make(User, email="<EMAIL>")
        self.group = mommy.make(Group, workspace=self.workspace)
        self.fake_token = "faketoken"

        self.client.force_authenticate(
            user={"sub": self.user_1.id, "token": self.fake_token, "client_id": self.workspace.id}
        )
        self.headers = {"HTTP_X_CLIENT": self.workspace.id}

    def test_import_users(self, check_role, user_has_access: mock.MagicMock):
        user_has_access.return_value = True
        file_path = self.create_users_xlsx(User.objects.filter())

        with open(file_path, "rb") as file:
            response = self.client.post(self.import_users_url, {"file": file}, **self.headers, format="multipart")

        group_user = GroupUser.objects.filter(user_id=self.user_1.id, group_id=self.group.id)
        group_user_2 = GroupUser.objects.filter(user_id=self.user_2.id, group_id=self.group.id)
        user_has_access.has_access(UserHasPermissionDTO(workspace_id=user_has_access, user_id=self.user_1.id))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["group_user_import_errors"], [])
        self.assertTrue(group_user.exists())
        self.assertTrue(group_user_2.exists())

    def test_import_missions(self, check_role, user_has_access):
        user_has_access.return_value = True
        file_path = self.create_users_xlsx(User.objects.filter())

        with open(file_path, "rb") as file:
            response = self.client.post(self.import_users_url, {"file": file}, **self.headers, format="multipart")

        group_user = GroupUser.objects.filter(user_id=self.user_1.id, group_id=self.group.id)
        group_user_2 = GroupUser.objects.filter(user_id=self.user_2.id, group_id=self.group.id)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["group_user_import_errors"], [])
        self.assertTrue(group_user.exists())
        self.assertTrue(group_user_2.exists())

    def create_users_xlsx(self, users: QuerySet) -> str:
        file_path = f"{TEMP_FOLDER}/{uuid4()}.xlsx"
        mocked_data = {USER_COLUMN: [], GROUP_COLUMN: []}
        for user in users:
            mocked_data[USER_COLUMN].append(user.email)
            mocked_data[GROUP_COLUMN].append(self.group.name)
        dataframe = pd.DataFrame(mocked_data)
        dataframe.to_excel(file_path, index=False)
        return file_path
