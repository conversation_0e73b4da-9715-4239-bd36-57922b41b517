from django.db import IntegrityError, transaction

from custom import KeepsBadRequestError, KeepsError
from group.models import Group, GroupLearningTrail, GroupUser
from learning_trail.models import LearningTrail, LearningTrailWorkspace
from user_activity.dtos.batch_trail_enroll_result import BatchTrailEnrollResult
from user_activity.services.learning_trail_batch_enrollment_service_v2 import (
    LearningTrailBatchEnrollmentService,
)
from user_activity.tasks.learning_trail_enrollment_task import enroll_users


class GroupLearningTrailService:
    def __init__(self, learning_trail_batch_enrollment_service: LearningTrailBatchEnrollmentService):
        self._lt_batch_enrollment_service = learning_trail_batch_enrollment_service

    @transaction.atomic()
    def link_learning_trails_in_group(
        self,
        learning_trail_id_list,
        group_id,
        workspace_id,
        enrollment_goal_date=None,
        regulatory_compliance_cycle_id=None,
    ):
        errors = {"group_learning_trail_errors": []}
        lts_linked = []

        group_instance = Group.objects.filter(id=group_id, workspace_id=workspace_id).first()

        for learning_trail_id in learning_trail_id_list:
            workspace_lts = LearningTrailWorkspace.objects.filter(workspace_id=workspace_id).values_list(
                "learning_trail_id", flat=True
            )

            lt_instance = LearningTrail.objects.filter(id=learning_trail_id, id__in=workspace_lts).first()

            try:
                self.__create_group_learning_trail(group_instance, lt_instance)
                lts_linked.append(learning_trail_id)

            except Exception as e:
                learning_trail_id__name = {"id": learning_trail_id}
                group_id__name = {"id": group_id}

                if lt_instance:
                    learning_trail_id__name.update({"name": lt_instance.name})
                if group_instance:
                    group_id__name.update({"name": group_instance.name})

                errors["group_learning_trail_errors"].append(
                    {
                        "learning_trail": learning_trail_id__name,
                        "group": group_id__name,
                        "error": {"i18n": e.i18n, "detail": e.detail},
                    }
                )

        if enrollment_goal_date is not None and lts_linked:
            group_users = GroupUser.objects.filter(group=str(group_id)).all().values_list("user_id", flat=True)
            group_users = [str(group_user) for group_user in group_users]
            lts_linked = [str(learning_trail) for learning_trail in lts_linked]

            enroll_users.delay(
                lts_linked,
                group_users,
                workspace_id,
                str(enrollment_goal_date),
                bool(regulatory_compliance_cycle_id),
                str(regulatory_compliance_cycle_id),
            )

            errors.update(BatchTrailEnrollResult([]).__dict__)

        return errors

    @staticmethod
    def __create_group_learning_trail(group_instance, lt_instance):
        if not lt_instance:
            raise KeepsBadRequestError(detail="learning trail not found", i18n="learning_trail_not_found")

        if not group_instance:
            raise KeepsBadRequestError(detail="group not found", i18n="group_not_found")

        try:
            group_learning_trail = GroupLearningTrail.objects.create(learning_trail=lt_instance, group=group_instance)
            return group_learning_trail

        except IntegrityError:
            raise KeepsBadRequestError(
                detail="this learning trail is already linked to the group", i18n="group_learning_trail_already_exists"
            )
        except Exception as e:
            print(e)
            raise KeepsError(detail="unexpected error happened", i18n="unexpected_error_happened", status_code=500)
