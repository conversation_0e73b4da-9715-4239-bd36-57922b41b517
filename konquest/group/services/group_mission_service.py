from django.db import IntegrityError

from account.models import User
from custom import KeepsBadRequestError, KeepsError
from group.models import Group, GroupMission, GroupUser
from mission.models import Mission, MissionWorkspace
from user_activity.services.mission_batch_enrollment_service import EnrollmentsBatch, MissionBatchEnrollmentService


class GroupMissionService:
    def __init__(self, mission_batch_enrollment_service: MissionBatchEnrollmentService):
        self.__mission_batch_enrollment_service = mission_batch_enrollment_service

    def link_mission_in_group(self, mission_id_list, group_id, workspace_id, action_user: User, **kwargs):
        errors = {"group_mission_errors": []}
        missions_linked = []
        goal_date = kwargs.get("goal_date")
        required = kwargs.get("required")

        group_instance = Group.objects.filter(id=group_id, workspace_id=workspace_id).first()

        for mission_id in mission_id_list:
            workspace_missions = MissionWorkspace.objects.filter(workspace_id=workspace_id).values_list(
                "mission_id", flat=True
            )
            mission_instance = Mission.objects.filter(id=mission_id, id__in=workspace_missions).first()

            try:
                self.__create_group_mission(group_instance, mission_instance)
                missions_linked.append(mission_id)

            except Exception as e:
                mission_id__name = {"id": mission_id}
                group_id__name = {"id": group_id}

                if mission_instance:
                    mission_id__name.update({"name": mission_instance.name})
                if group_instance:
                    group_id__name.update({"name": group_instance.name})

                errors["group_mission_errors"].append(
                    {
                        "mission": mission_id__name,
                        "group": group_id__name,
                        "error": {"i18n": e.i18n, "detail": e.detail},
                    }
                )

        if goal_date is not None and missions_linked:
            group_users = GroupUser.objects.filter(group=str(group_id)).all().values_list("user_id", flat=True)
            regulatory_compliance_cycle_id = kwargs.get("regulatory_compliance_cycle_id")
            enrollments_batch = EnrollmentsBatch(
                mission_ids=missions_linked,
                user_ids=group_users,
                workspace_id=workspace_id,
                action_user=action_user,
                goal_date=goal_date,
                required=required,
                regulatory_compliance_cycle_id=regulatory_compliance_cycle_id,
            )

            enrollment_error = self.__mission_batch_enrollment_service.enroll(enrollments_batch)

            errors.update(enrollment_error)

        return errors

    @staticmethod
    def __create_group_mission(group_instance, mission_instance):
        if not mission_instance:
            raise KeepsBadRequestError(detail="mission not found", i18n="mission_not_found")

        if not group_instance:
            raise KeepsBadRequestError(detail="group not found", i18n="group_not_found")

        try:
            group_mission = GroupMission.objects.create(mission=mission_instance, group=group_instance)
            return group_mission

        except IntegrityError:
            raise KeepsBadRequestError(
                detail="this mission is already linked to the group", i18n="group_mission_already_exists"
            )
        except Exception:
            raise KeepsError(detail="unexpected error happened", i18n="unexpected_error_happened", status_code=500)
