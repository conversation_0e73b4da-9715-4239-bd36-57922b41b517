from typing import List, Sequence, Union
from uuid import UUID

from django.db.models import QuerySet
from django.utils.translation import gettext as _

from account.models import User
from config.celery import app
from constants import ENROLL_TRAIL_USERS_TASK
from custom.key_errors import (
    AN_UNEXPECTED_ERROR_HAS_OCCURRED,
    UNABLE_TO_ENROLL_IN_AN_IN_INACTIVE_MISSION,
    USER_ALREADY_LINKED,
    USER_NOT_FOUND,
)
from group.dataclasses.link_users_in_group_create import LinkUsersInGroupCreate
from group.dataclasses.link_users_in_group_result import (
    ErrorDetail,
    GroupDetail,
    GroupUserErrors,
    LinkUsersInGroupResult,
    UserDetail,
    Warning,
)
from group.models import Group, GroupLearningTrail, GroupMission, GroupUser
from mission.models.mission_development_status_enum import INACTIVATED
from myaccount.application.services.myaccount_service import MyAccountService
from user_activity.task_names import ENROLL_USERS_TASK

RESULTS = "results"


class GroupUserService:
    def __init__(self, myaccount_service: MyAccountService):
        self._myaccount_service = myaccount_service

    def _list_workspace_users(self, ids: List[str], workspace_id: str) -> Union[QuerySet, Sequence[User]]:
        return self._myaccount_service.list_all_consumer_users(workspace_id).filter(id__in=ids)

    @staticmethod
    def _list_users_not_founded(workspace_user_ids: List[str], all_user_ids: List[UUID]) -> List:
        not_founded_ids = []
        for user_id in all_user_ids:
            if user_id not in workspace_user_ids:
                not_founded_ids.append(user_id)
        return not_founded_ids

    @staticmethod
    def _list_users_already_linked(users: QuerySet, group_id: str):
        group_users = GroupUser.objects.filter(user__in=users, group_id=group_id)
        return User.objects.filter(id__in=group_users.values_list("user_id"))

    @staticmethod
    def _list_users_grou_deleted(users: QuerySet, group_id: str):
        group_users = GroupUser.objects.get_all_including_deleted().filter(
            user__in=users, group_id=group_id, deleted=True
        )
        return group_users

    @staticmethod
    def save_group_users(group: Group, users: Union[List[User], QuerySet]) -> QuerySet:
        group_users = [GroupUser(user=user, group=group) for user in users]
        ids = [str(group.id) for group in group_users]
        GroupUser.objects.bulk_create(group_users, ignore_conflicts=True)
        created = GroupUser.objects.filter(id__in=ids)
        return created

    @staticmethod
    def _format_warnings(
        mission_group_inactive: List[GroupMission],
    ) -> List[GroupUserErrors]:
        warnings: List[GroupUserErrors] = []

        for mission_group in mission_group_inactive:
            warnings.append(
                Warning(
                    object=mission_group.mission.name,
                    warning_detail=ErrorDetail(
                        i18n=UNABLE_TO_ENROLL_IN_AN_IN_INACTIVE_MISSION,
                        detail=_(UNABLE_TO_ENROLL_IN_AN_IN_INACTIVE_MISSION),
                    ),
                )
            )
        return warnings

    @staticmethod
    def _format_errors(
        users_already_linked: Union[List[GroupUser], QuerySet],
        users_ids_not_found: List[str],
        users_not_linked: List[User],
        group: Group,
    ) -> List[GroupUserErrors]:
        errors: List[GroupUserErrors] = []
        for user in users_already_linked:
            errors.append(
                GroupUserErrors(
                    user=UserDetail(id=user.id, email=user.email),
                    group=GroupDetail(id=group.id, name=group.name),
                    error=ErrorDetail(i18n=USER_ALREADY_LINKED, detail=_(USER_ALREADY_LINKED)),
                )
            )
        for user_id in users_ids_not_found:
            errors.append(
                GroupUserErrors(
                    user=UserDetail(id=user_id),
                    group=GroupDetail(id=group.id, name=group.name),
                    error=ErrorDetail(i18n=USER_NOT_FOUND, detail=_(USER_NOT_FOUND)),
                )
            )
        for user in users_not_linked:
            errors.append(
                GroupUserErrors(
                    user=UserDetail(id=user.id, email=user.email),
                    group=GroupDetail(id=group.id, name=group.name),
                    error=ErrorDetail(
                        i18n=AN_UNEXPECTED_ERROR_HAS_OCCURRED, detail=_(AN_UNEXPECTED_ERROR_HAS_OCCURRED)
                    ),
                )
            )
        return errors

    @staticmethod
    def enroll_users(users_group: LinkUsersInGroupCreate, users_linked_ids: List[str]):
        if not users_group.goal_date or not users_linked_ids:
            return
        mission_ids = list(
            GroupMission.objects.filter(group_id=users_group.group_id).values_list("mission_id", flat=True)
        )
        group_trail_ids = list(
            GroupLearningTrail.objects.filter(group_id=users_group.group_id).values_list("learning_trail_id", flat=True)
        )

        if mission_ids:
            app.send_task(
                ENROLL_USERS_TASK,
                args=(
                    mission_ids,
                    users_linked_ids,
                    users_group.workspace_id,
                    str(users_group.goal_date),
                    users_group.required,
                    users_group.regulatory_compliance_cycle_id,
                ),
            )
        if group_trail_ids:
            app.send_task(
                ENROLL_TRAIL_USERS_TASK,
                args=(
                    group_trail_ids,
                    users_linked_ids,
                    users_group.workspace_id,
                    str(users_group.goal_date),
                    users_group.required,
                    users_group.regulatory_compliance_cycle_id,
                ),
            )

    def _get_missions_group_inactive(self, group):
        return GroupMission.objects.filter(group=group, mission__development_status=INACTIVATED)

    def link_user_in_group(self, users_group: LinkUsersInGroupCreate) -> LinkUsersInGroupResult:
        workspace_users = self._list_workspace_users(users_group.user_ids, users_group.workspace_id)
        users_already_linked = list(self._list_users_already_linked(workspace_users, users_group.group_id))
        users_not_found_ids = self._list_users_not_founded(
            workspace_users.values_list("id", flat=True), users_group.user_ids
        )
        group = Group.objects.get(id=users_group.group_id, workspace_id=users_group.workspace_id)
        users_able_to_link = workspace_users.exclude(id__in=[user.id for user in users_already_linked])

        users_grou_deleted = self._list_users_grou_deleted(workspace_users, users_group.group_id)
        users_grou_deleted_ids = list(users_grou_deleted.values_list("user_id", flat=True))
        users_grou_deleted.update(deleted=False, deleted_date=None)

        users_linked_ids = list(self.save_group_users(group, users_able_to_link).values_list("user_id", flat=True))
        users_linked_ids += users_grou_deleted_ids
        users_not_linked = list(filter(lambda user: user.id not in users_linked_ids, users_able_to_link))

        mission_group_inactive = self._get_missions_group_inactive(group)

        errors = self._format_errors(users_already_linked, users_not_found_ids, users_not_linked, group)
        warnings = self._format_warnings(mission_group_inactive)
        self.enroll_users(users_group, users_linked_ids)

        return LinkUsersInGroupResult(errors, warnings)
