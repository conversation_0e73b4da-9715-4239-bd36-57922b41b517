import uuid

from celery import shared_task
from django.utils.translation import gettext_noop as _

from group.models import GroupChannel, GroupLearningTrail, GroupMission, GroupUser
from notification.services.notification_service import Message
from notification.services.notification_service_v2 import NotificationServiceV2
from utils.task_transaction import task_transaction

YOU_HAVE_BEEN_ADDED_TO_A_NEW_GROUP = _("you_have_been_added_to_a_new_group")
THE_CHANNEL_CHANNEL_NAME_HAS_BEEN_ADDED_TO_A_GROUP = _("the_channel_%(channel_name)s_has_been_added_to_a_group")
THE_MISSION_MISSION_NAME_HAS_BEEN_ADDED_TO_A_GROUP = _("the_mission_%(mission_name)s_has_been_added_to_a_group")
THE_TRAIL_TRAIL_NAME_HAS_BEEN_ADDED_TO_A_GROUP = _("the_trail_%(trail_name)s_has_been_added_to_a_group")


@shared_task(ignore_result=True)
def notify_user_linked_in_a_new_group(group_user_id):
    with task_transaction("notify_user_linked_in_a_new_group", NotificationServiceV2) as notification_service:
        group_user = GroupUser.objects.get(id=group_user_id)
        message = Message(title=YOU_HAVE_BEEN_ADDED_TO_A_NEW_GROUP, description=group_user.group.name)
        notification_service.create_notification(
            user_ids=[group_user.user_id],
            type_key="USER_LINKED_IN_A_NEW_GROUP",
            action="CREATE",
            object=group_user.group_id,
            workspace_id=group_user.group.workspace_id,
            message=message,
        )


@shared_task(ignore_result=True)
def notify_new_channel_linked_in_group(group_channel_id):
    with task_transaction("notify_new_channel_linked_in_group", NotificationServiceV2) as notification_service:
        group_channel = GroupChannel.objects.get(id=group_channel_id)
        message = Message(
            title=THE_CHANNEL_CHANNEL_NAME_HAS_BEEN_ADDED_TO_A_GROUP,
            title_values={"channel_name": group_channel.channel.name},
            description=group_channel.group.name,
        )
        group_users = group_channel.group.groupuser_set.filter()

        notification_service.create_notification(
            user_ids=[group_user.user_id for group_user in group_users],
            type_key="NEW_CHANNEL_LINKED_IN_THE_GROUP",
            action="CREATE",
            object=group_channel.channel_id,
            message=message,
            workspace_id=group_channel.group.workspace_id,
        )


@shared_task(ignore_result=True)
def notify_new_mission_linked_in_group(group_mission_id):
    with task_transaction("notify_new_mission_linked_in_group", NotificationServiceV2) as notification_service:
        group_mission = GroupMission.objects.get(id=group_mission_id)
        if not group_mission:
            return
        message = Message(
            title=THE_MISSION_MISSION_NAME_HAS_BEEN_ADDED_TO_A_GROUP,
            title_values={"mission_name": group_mission.mission.name},
            description=group_mission.group.name,
        )
        group_users = group_mission.group.groupuser_set.filter()

        notification_service.create_notification(
            user_ids=[group_user.user_id for group_user in group_users],
            type_key="NEW_MISSION_LINKED_IN_THE_GROUP",
            action="CREATE",
            object=group_mission.mission_id,
            message=message,
            workspace_id=group_mission.group.workspace_id,
        )


@shared_task(ignore_result=True)
def notify_new_trail_linked_in_group(group_trail_id: uuid):
    with task_transaction("notify_new_trail_linked_in_group", NotificationServiceV2) as notification_service:
        group_trail = GroupLearningTrail.objects.filter(id=group_trail_id).first()
        message = Message(
            title=THE_TRAIL_TRAIL_NAME_HAS_BEEN_ADDED_TO_A_GROUP,
            title_values={"trail_name": group_trail.learning_trail.name},
            description=group_trail.group.name,
        )
        group_users = group_trail.group.groupuser_set.filter()

        notification_service.create_notification(
            user_ids=[group_user.user_id for group_user in group_users],
            type_key="NEW_TRAIL_LINKED_IN_THE_GROUP",
            action="CREATE",
            object=group_trail.learning_trail_id,
            message=message,
            workspace_id=group_trail.group.workspace_id,
        )
