from django.contrib import admin

from group.models import Group, GroupChannel, GroupMission, GroupUser


@admin.register(Group)
class GroupAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "description", "workspace")
    search_fields = (
        "id",
        "name",
        "workspace__id",
        "workspace__name",
    )
    list_per_page = 50
    list_max_show_all = 50


@admin.register(GroupMission)
class GroupMissionAdmin(admin.ModelAdmin):
    list_display = ("id", "mission", "group")
    search_fields = (
        "id",
        "mission__id",
        "group__id",
        "mission__name",
        "group__name",
    )
    list_per_page = 50
    list_max_show_all = 50


@admin.register(GroupUser)
class GroupUserAdmin(admin.ModelAdmin):
    list_display = ("id", "user", "group")
    search_fields = (
        "id",
        "user__id",
        "group__id",
        "user__name",
        "group__name",
    )
    list_per_page = 50
    list_max_show_all = 50


@admin.register(GroupChannel)
class GroupChannelAdmin(admin.ModelAdmin):
    list_display = ("id", "channel", "group")
    search_fields = (
        "id",
        "channel__id",
        "group__id",
        "channel__name",
        "group__name",
    )
    list_per_page = 50
    list_max_show_all = 50
