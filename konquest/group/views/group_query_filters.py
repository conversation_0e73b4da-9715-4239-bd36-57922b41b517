from rest_framework.filters import BaseFilterBackend

from group.models import GroupChannel, GroupMission


class GroupMissionFilterBackend(BaseFilterBackend):
    def filter_queryset(self, request, queryset, view):
        mission_id = request.query_params.get("mission_id")

        if mission_id:
            ids = GroupMission.objects.filter(mission_id=mission_id).values_list("group_id", flat=True)
            return queryset.filter(id__in=ids)

        return queryset.filter()


class GroupChannelFilterBackend(BaseFilterBackend):
    def filter_queryset(self, request, queryset, view):
        channel_id = request.query_params.get("channel_id")

        if channel_id:
            ids = GroupChannel.objects.filter(channel_id=channel_id).values_list("group_id", flat=True)
            return queryset.filter(id__in=ids)

        return queryset.filter()
