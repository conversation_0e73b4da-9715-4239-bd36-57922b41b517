# -*- coding: utf-8 -*-

from django_filters.rest_framework import DjangoFilter<PERSON><PERSON><PERSON>
from rest_framework import viewsets
from rest_framework.filters import Order<PERSON><PERSON><PERSON><PERSON>, SearchFilter

from authentication.keeps_permissions import ADMIN_PERMISSIONS
from group.models import Group
from group.serializers.group_serializer import GroupSerializer
from group.views.group_query_filters import GroupChannelFilterBackend, GroupMissionFilterBackend
from utils.utils import swagger_safe_queryset


class GroupViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (
        DjangoFilterBackend,
        SearchFilter,
        OrderingFilter,
        GroupMissionFilterBackend,
        GroupChannelFilterBackend,
    )
    filterset_fields = ("name", "description", "workspace")
    search_fields = ("name", "description")
    ordering_fields = ("name", "workspace")
    ordering = ("name",)

    permission_classes = ADMIN_PERMISSIONS

    @swagger_safe_queryset(Group)
    def get_queryset(self):
        workspace_uuid = self.request.user.get("client_id") if self.request.user else None

        # Workspace Groups
        workspace_groups = Group.objects.filter(workspace_id=workspace_uuid).values_list("id", flat=True)
        groups = Group.objects.filter(id__in=workspace_groups).extra(
            select={
                "users": """
                        SELECT COUNT(*)
                        FROM group_user gu
                        WHERE gu.group_id = "group"."id"
                    """,
                "missions": """
                        SELECT COUNT(*)
                        FROM group_mission gm
                        WHERE gm.group_id = "group"."id"
                    """,
                "learning_trails": """
                        SELECT COUNT(*)
                        FROM group_learning_trail glt
                        WHERE glt.group_id = "group"."id"
                    """,
                "channels": """
                        SELECT COUNT(*)
                        FROM group_channel gc
                        WHERE gc.group_id = "group"."id"
                    """,
            }
        )
        return groups

    def get_serializer_class(self):
        return GroupSerializer

    def list(self, request, *args, **kwargs):
        """
        groups list

        Groups are used to link users to private missions and channels.

        ---
            filters:
            name, description, mission_id, channel_id

            search:
            name, description

            order fields:
            name, workspace

            default order: name
        """
        return super(GroupViewSet, self).list(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        request.data["workspace"] = request.user["client_id"]
        response = super(GroupViewSet, self).create(request, *args, **kwargs)
        return response

    def update(self, request, *args, **kwargs):
        request.data["workspace"] = request.user["client_id"]
        response = super(GroupViewSet, self).update(request, *args, **kwargs)
        return response
