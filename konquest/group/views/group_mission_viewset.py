from django.conf import settings
from django_filters.rest_framework import Django<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from injector import Provider, inject
from rest_framework import status, viewsets
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter
from rest_framework.response import Response

from authentication.keeps_permissions import ADMIN_PERMISSIONS
from constants import REGULATORY_COMPLIANCE_CYCLE
from custom import keeps_exception_handler
from group.models import Group, GroupMission, GroupUser
from group.serializers.group_import_serializer import GroupMissionImportInputSerializer
from group.serializers.group_mission_serializer import GroupMissionDetailSerializer, GroupMissionSerializer
from group.serializers.group_serializer import GroupUserImportSerializer
from group.services import GroupImportService
from group.services.group_mission_service import GroupMissionService
from user_activity.models import MissionEnrollment
from utils.utils import load_request_user, storage_file, swagger_safe_queryset


class GroupMissionUnitViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (Djan<PERSON><PERSON>ilter<PERSON>ackend, SearchFilter, OrderingFilter)
    filterset_fields = ("mission", "group")
    search_fields = ("mission", "group")
    ordering_fields = ("mission", "group")
    ordering = ("mission",)

    permission_classes = ADMIN_PERMISSIONS

    def get_queryset(self):
        group_uuid = self.kwargs.get("group_uuid")
        workspace_uuid = self.request.user.get("client_id") if self.request.user else None

        # Workspace Groups
        workspace_groups = Group.objects.filter(workspace_id=workspace_uuid, id=group_uuid).values_list("id", flat=True)

        return GroupMission.objects.filter(group_id__in=workspace_groups)

    def get_serializer_class(self):
        return (
            GroupMissionSerializer if self.request.method in ["POST", "PUT", "PATCH"] else GroupMissionDetailSerializer
        )

    def create(self, request, *args, **kwargs):
        request.data["group"] = self.kwargs.get("group_uuid")
        request.data["mission"] = self.kwargs.get("mission_uuid")

        group = Group.objects.filter(id=request.data["group"]).first()
        if group.name == settings.GROUP_NAME_ALURA:
            raise keeps_exception_handler.NotAllowedAddOrRemoveThisGroupFromMission()

        already_exists = GroupMission.objects.filter(
            group_id=request.data["group"], mission_id=request.data["mission"]
        ).exists()
        if already_exists:
            raise keeps_exception_handler.KeepsBadRequestError(
                detail="Mission Already linked in this group", i18n="group_mission_already_exists"
            )

        response = super().create(request, *args, **kwargs)

        return response

    def destroy(self, request, *args, **kwargs):
        workspace_uuid = self.request.user.get("client_id") if self.request.user else None
        group_id = self.kwargs.get("group_uuid")
        mission_id = self.kwargs.get("mission_uuid")
        delete_enrollment = self.request.query_params.get("delete_enrollment") == "True"

        workspace_has_group = (
            Group.objects.filter(workspace_id=workspace_uuid, id=group_id).values_list("id", flat=True).first()
        )

        if not workspace_has_group:
            return Response(status=status.HTTP_403_FORBIDDEN)

        group_mission = GroupMission.objects.filter(group_id=group_id, mission_id=mission_id).first()

        if not group_mission:
            return Response(status=status.HTTP_404_NOT_FOUND)

        if group_mission.group.name == settings.GROUP_NAME_ALURA:
            raise keeps_exception_handler.NotAllowedAddOrRemoveThisGroupFromMission()

        if delete_enrollment:
            user_ids = GroupUser.objects.filter(group_id=group_id).values_list("user_id", flat=True)
            enrollments = MissionEnrollment.objects.filter(
                mission_id=mission_id, workspace_id=workspace_uuid, user_id__in=user_ids
            ).all()
            for enrollment in enrollments:
                self.perform_destroy(enrollment)

        self.perform_destroy(group_mission)
        return Response(status=status.HTTP_204_NO_CONTENT)


class GroupMissionViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = ("mission__name", "group__name")
    search_fields = ("mission__name", "group__name")
    ordering_fields = ("mission__name", "group__name")
    ordering = ("mission__name",)

    permission_classes = ADMIN_PERMISSIONS

    @inject
    def __init__(self, group_mission_service: GroupMissionService = Provider[GroupMissionService], **kwargs):
        super().__init__(**kwargs)
        self.group_mission_service = group_mission_service

    @swagger_safe_queryset(GroupMission)
    def get_queryset(self):
        group_uuid = self.kwargs.get("group_uuid")
        workspace_uuid = self.request.user.get("client_id") if self.request.user else None

        # Workspace Groups
        workspace_groups = Group.objects.filter(workspace_id=workspace_uuid, id=group_uuid).values_list("id", flat=True)

        return GroupMission.objects.filter(group_id__in=workspace_groups).all()

    def get_serializer_class(self):
        return (
            GroupMissionSerializer if self.request.method in ["POST", "PUT", "PATCH"] else GroupMissionDetailSerializer
        )

    def create(self, request, *args, **kwargs):
        """
        create group_mission

        Linked missions in group and enroll all users of the group in these new linked missions,
        if "enrollment_goal_date" is present in the body's root AND not None

        ---
            body:
                "missions": [mission_id_list],
                "enrollment_goal_date": optional, "YYYY-MM-DD HH:MM:SS",
                "enrollment_required_mission": optional, boll
        """
        workspace_uuid = self.request.user.get("client_id")
        group = self.kwargs.get("group_uuid")
        serializer = GroupMissionImportInputSerializer(data=self.request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        missions = data["missions"]
        enrollment_goal_date = data.get("enrollment_goal_date")
        enrollment_required_mission = data.get("enrollment_required_mission", True)
        regulatory_compliance_cycle_id = self.request.data.get("regulatory_compliance_cycle_id")
        action_user = load_request_user(request)

        response = self.group_mission_service.link_mission_in_group(
            mission_id_list=missions,
            group_id=group,
            workspace_id=workspace_uuid,
            action_user=action_user,
            goal_date=enrollment_goal_date,
            required=enrollment_required_mission,
            regulatory_compliance_cycle_id=regulatory_compliance_cycle_id,
        )

        return Response(response, status=status.HTTP_200_OK)


class GroupMissionImportViewSet(viewsets.ModelViewSet):
    """
    import missions in groups

    Linked missions in groups and enroll all users of the group in these new linked missions,
    if "enrollment_goal_date" is present in the form-data body AND not None

    ---
        form-data body:
            "file": binary, xlsx file;
            "enrollment_goal_date": optional, "YYYY-MM-DD HH:MM:SS",
            "enrollment_required_mission": optional, boll
    """

    permission_classes = ADMIN_PERMISSIONS
    serializer_class = GroupUserImportSerializer

    @inject
    def __init__(self, group_import_service: GroupImportService = Provider[GroupImportService], **kwargs):
        super().__init__(**kwargs)
        self.group_import_service = group_import_service

    def create(self, request, *args, **kwargs):
        workspace_uuid = self.request.user.get("client_id")
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        temp_file = data["file"]
        file_path = storage_file(temp_file)

        enrollment_goal_date = data.get("enrollment_goal_date")
        enrollment_required_mission = data.get("enrollment_required_mission")
        regulatory_compliance_cycle_id = data.get(REGULATORY_COMPLIANCE_CYCLE)

        data_return = self.group_import_service.mission_group_from_xls(
            workspace_id=workspace_uuid,
            file=file_path,
            action_user=load_request_user(request),
            goal_date=enrollment_goal_date,
            required=enrollment_required_mission,
            regulatory_compliance_cycle_id=regulatory_compliance_cycle_id,
        )

        return Response(data_return, status=status.HTTP_200_OK)
