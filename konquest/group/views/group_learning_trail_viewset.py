# -*- coding: utf-8 -*-
from django_filters.rest_framework import DjangoFilterBack<PERSON>
from injector import Provider, inject
from rest_framework import status, viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
from rest_framework.response import Response

from authentication.keeps_permissions import (
    ADMIN_PERMISSIONS,
    KeepsIntegrationPermission,
    KeepsPlatformAdminPermission,
    KeepsSuperAdminPermission,
)
from custom import keeps_exception_handler
from group.models import Group, GroupLearningTrail, GroupUser
from group.serializers.group_import_serializer import GroupLearningTrailImportInputSerializer
from group.serializers.group_learning_trail_serializer import (
    GroupLearningTrailDetailSerializer,
    GroupLearningTrailSerializer,
)
from group.services.group_learning_trail_service import GroupLearningTrailService
from group.services.group_service import GroupService
from user_activity.models import LearningTrailEnrollment
from user_activity.services import LearningTrailEnrollmentService
from utils.utils import swagger_safe_queryset


class GroupLearningTrailUnitViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = ("learning_trail", "group")
    search_fields = ("learning_trail", "group")
    ordering_fields = ("learning_trail", "group")
    ordering = ("learning_trail",)

    permission_classes = (KeepsIntegrationPermission | KeepsSuperAdminPermission | KeepsPlatformAdminPermission,)

    @inject
    def __init__(
        self,
        trail_enrollment_service: LearningTrailEnrollmentService = Provider[LearningTrailEnrollmentService],
        group_service: GroupService = Provider[GroupService],
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.__trail_enrollment_service = trail_enrollment_service
        self._group_service = group_service

    def get_queryset(self):
        return self._group_service.list_group_learning_trail(
            self.request.user.get("client_id"), self.kwargs.get("group_uuid")
        )

    def get_serializer_class(self):
        return (
            GroupLearningTrailSerializer
            if self.request.method in ["POST", "PUT", "PATCH"]
            else GroupLearningTrailDetailSerializer
        )

    def create(self, request, *args, **kwargs):
        request.data["group"] = self.kwargs.get("group_uuid")
        request.data["learning_trail"] = self.kwargs.get("learning_trail_uuid")

        # if GroupLearningTrail.objects.filter(group_id=request.data['group'],
        #                                     learning_trail_id=request.data['learning_trail']).exists():
        # raise keeps_exception_handler.KeepsBadRequestError(detail='')

        response = super(GroupLearningTrailUnitViewSet, self).create(request, *args, **kwargs)

        return response

    def destroy(self, request, *args, **kwargs):
        """
        Group Learning Trail Delete

        Delete a group learning trail

        flags(query_params):
            delete_enrollment --> (default:False),
            if 'true' delete all learning trail enrollments unfinished of group users
        """
        workspace_uuid = self.request.user.get("client_id") if self.request.user else None
        group_id = self.kwargs.get("group_uuid")
        learning_trail_id = self.kwargs.get("learning_trail_uuid")
        delete_enrollment = self.request.query_params.get("delete_enrollment") == "True"
        Group.objects.get(workspace_id=workspace_uuid, id=group_id)

        group_lt_instance = GroupLearningTrail.objects.filter(group_id=group_id, learning_trail_id=learning_trail_id)

        if delete_enrollment:
            user_group = GroupUser.objects.filter(group_id=group_id).values_list("user_id", flat=True)
            enrollments = LearningTrailEnrollment.objects.filter(
                learning_trail_id=learning_trail_id, user_id__in=user_group
            ).all()
            for enrollment in enrollments:
                self.__trail_enrollment_service.delete_enrollment(enrollment)

        if not group_lt_instance:
            return Response(status=status.HTTP_404_NOT_FOUND)

        self.perform_destroy(group_lt_instance)
        return Response(status=status.HTTP_204_NO_CONTENT)


class GroupLearningTrailViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = ["learning_trail__name", "group__name"]
    filterset_fields = ("learning_trail__name", "group__name")
    search_fields = ("learning_trail__name", "group__name")
    ordering_fields = ("learning_trail__name", "group__name")
    ordering = ("learning_trail__name",)

    permission_classes = ADMIN_PERMISSIONS
    serializer_class = GroupLearningTrailImportInputSerializer

    @inject
    def __init__(
        self,
        service: GroupLearningTrailService = Provider[GroupLearningTrailService],
        group_service: GroupService = Provider[GroupService],
        **kwargs,
    ):
        super().__init__(**kwargs)
        self._service = service
        self._group_service = group_service

    @swagger_safe_queryset(GroupLearningTrail)
    def get_queryset(self):
        return self._group_service.list_group_learning_trail(
            self.request.user.get("client_id"), self.kwargs.get("group_uuid")
        )

    def get_serializer_class(self):
        return (
            GroupLearningTrailSerializer
            if self.request.method in ["POST", "PUT", "PATCH"]
            else GroupLearningTrailDetailSerializer
        )

    def create(self, request, *args, **kwargs):
        """
        create group_learning_trail

        Linked learning trails in group and enroll all users of the group in these new linked learning trails,
        if "enrollment_goal_date" is present in the body's root AND not None

        ---
            body:
            {"learning_trails": [learning_trail_id_list], "enrollment_goal_date": "YYYY-MM-DD HH:MM:SS"}
        """
        workspace_uuid = self.request.user.get("client_id")
        group_id = self.kwargs.get("group_uuid")
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        lts = data.get("learning_trails")
        enrollment_goal_date = data.get("enrollment_goal_date")
        regulatory_compliance_cycle_id = request.data.get("regulatory_compliance_cycle_id")
        workspace_group = Group.objects.filter(workspace_id=workspace_uuid, id=group_id)
        if not workspace_group:
            raise keeps_exception_handler.KeepsNotFoundError(detail="Group Not Found", i18n="group_not_found")

        response = self._service.link_learning_trails_in_group(
            lts, group_id, workspace_uuid, enrollment_goal_date, regulatory_compliance_cycle_id
        )

        return Response(response, status=status.HTTP_200_OK)
