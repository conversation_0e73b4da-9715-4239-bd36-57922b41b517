import uuid

from django.db import models

from account.models import Workspace
from custom import KeepsBadRequestError
from utils.models import BaseModel


class Group(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.Char<PERSON>ield(verbose_name="Name", max_length=200)
    description = models.TextField(verbose_name="Description", null=True, blank=True)

    workspace = models.ForeignKey(Workspace, verbose_name="Workspace", on_delete=models.PROTECT, null=True, blank=True)

    is_integration = models.BooleanField(verbose_name="Integration", default=False)

    def delete(self, *args, **kwargs):
        if self.is_integration:
            raise KeepsBadRequestError(
                detail="This group cannot be deleted as it represents an integration",
                i18n="group_integration_cannot_be_deleted",
            )
        else:
            super(Group, self).delete(*args, **kwargs)

    def save(self, *args, **kwargs):
        existing_group = Group.objects.filter(id=self.id)
        if existing_group and existing_group[0].is_integration:
            raise KeepsBadRequestError(
                detail="This group cannot be be updated as it represents an integration",
                i18n="group_integration_cannot_be_updated",
            )
        else:
            super(Group, self).save(*args, **kwargs)

    class Meta:
        unique_together = ["workspace", "name"]
        verbose_name_plural = "Groups"
        db_table = "group"

    def __str__(self):
        short_id = str(self.id).split("-")[0]

        return f"{short_id} - {self.name}"
