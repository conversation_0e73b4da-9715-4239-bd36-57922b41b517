import uuid

from django.db import models

from group.models.group import Group
from learning_trail.models import LearningTrail
from utils.models import BaseModel


class GroupLearningTrail(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    learning_trail = models.ForeignKey(LearningTrail, verbose_name="LearningTrail", on_delete=models.CASCADE)
    group = models.ForeignKey(Group, verbose_name="Group", on_delete=models.CASCADE)

    class Meta:
        verbose_name_plural = "Learning Trails Groups"
        unique_together = ("learning_trail", "group")
        db_table = "group_learning_trail"
