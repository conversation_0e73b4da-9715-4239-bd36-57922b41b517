import uuid

from django.db import models

from group.models.group import Group
from pulse.models import Channel
from utils.models import BaseModel


class GroupChannel(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    channel = models.ForeignKey(Channel, verbose_name="Channel", on_delete=models.CASCADE)
    group = models.ForeignKey(Group, verbose_name="Group", on_delete=models.CASCADE)

    class Meta:
        verbose_name_plural = "Channels Groups"
        unique_together = ("channel", "group")
        db_table = "group_channel"
