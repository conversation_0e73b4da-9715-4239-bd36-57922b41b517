import uuid

from django.db import models

from group.models.group import Group
from mission.models import Mission
from utils.models import BaseModel


class GroupMission(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    mission = models.ForeignKey(Mission, verbose_name="Mission", on_delete=models.CASCADE)
    group = models.ForeignKey(Group, verbose_name="Group", on_delete=models.CASCADE)

    class Meta:
        verbose_name_plural = "Missions Groups"
        unique_together = ("mission", "group")
        db_table = "group_mission"
