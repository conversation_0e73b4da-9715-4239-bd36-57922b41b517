# -*- coding: utf-8 -*-

from rest_framework import serializers

from group.models.group_channel import GroupChannel
from pulse.serializers.channel_serializer import ChannelSerializer


class GroupChannelDetailSerializer(serializers.ModelSerializer):
    channel = ChannelSerializer()

    class Meta:
        model = GroupChannel
        fields = "__all__"


class GroupChannelSerializer(serializers.ModelSerializer):
    class Meta:
        model = GroupChannel
        fields = "__all__"
