from rest_framework import serializers

from group.models.group import Group
from user_activity.serializers.validators.goal_date_field import <PERSON>Date<PERSON><PERSON>
from user_activity.serializers.validators.goal_date_validator import GoalDateValidator


class GroupSerializer(serializers.ModelSerializer):
    users = serializers.ReadOnlyField()
    missions = serializers.ReadOnlyField()
    learning_trails = serializers.ReadOnlyField()
    channels = serializers.ReadOnlyField()

    class Meta:
        model = Group
        fields = "__all__"


class GroupUserImportSerializer(serializers.Serializer):
    file = serializers.FileField()
    enrollment_goal_date = GoalDateField(validators=[GoalDateValidator()], required=False, allow_null=True)
    enrollment_required_mission = serializers.BooleanField(default=True)
    regulatory_compliance_cycle = serializers.UUIDField(required=False)

    def update(self, instance, validated_data):
        """
        This method is empty because updating instances is not required for this serializer.
        """
        pass

    def create(self, validated_data):
        """
        This method is empty because creation instances is not required for this serializer.
        """
        pass
