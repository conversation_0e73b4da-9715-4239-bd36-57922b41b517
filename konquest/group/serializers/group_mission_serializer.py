# -*- coding: utf-8 -*-

from rest_framework import serializers

from group.models.group_mission import GroupMission
from mission.serializers.mission_serializer import MissionSerializer


class GroupMissionDetailSerializer(serializers.ModelSerializer):
    mission = MissionSerializer()

    class Meta:
        model = GroupMission
        fields = "__all__"


class GroupMissionSerializer(serializers.ModelSerializer):
    regulatory_compliance_cycle_id = serializers.UUIDField(required=False)

    class Meta:
        model = GroupMission
        fields = "__all__"
