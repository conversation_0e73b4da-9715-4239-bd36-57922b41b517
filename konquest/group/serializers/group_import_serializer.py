from rest_framework import serializers

from custom.keeps_serializer import KPSerializer
from user_activity.serializers.validators.goal_date_field import <PERSON><PERSON><PERSON><PERSON>ield
from user_activity.serializers.validators.goal_date_validator import GoalDateValidator


class GroupImportInputSerializer(KPSerializer):
    enrollment_goal_date = GoalDateField(validators=[GoalDateValidator()], required=False, allow_null=True)


class GroupLearningTrailImportInputSerializer(GroupImportInputSerializer):
    learning_trails = serializers.ListSerializer(child=serializers.UUIDField(), required=True)


class GroupMissionImportInputSerializer(GroupImportInputSerializer):
    missions = serializers.ListSerializer(child=serializers.UUIDField(), required=True)
    enrollment_required_mission = serializers.BooleanField(default=True)


class GroupUserImportInputSerializer(GroupImportInputSerializer):
    users = serializers.ListSerializer(child=serializers.UUIDField(), required=True)
    enrollment_required_mission = serializers.BooleanField(default=True)
    regulatory_compliance_cycle_id = serializers.CharField(required=False)
