from django.db import transaction
from django.db.models.signals import post_save

from group.models import GroupChannel, GroupLearningTrail, GroupMission, GroupUser
from group.tasks import notifications
from utils.signals import receiver


@receiver(post_save, sender=GroupUser)
def create_user_linked_in_a_group_notification(sender, instance, created, **kwargs):
    if created:
        transaction.on_commit(lambda: notifications.notify_user_linked_in_a_new_group.delay(instance.id))


@receiver(post_save, sender=GroupChannel)
def create_new_channel_linked_in_group_notification(sender, instance, created, **kwargs):
    if created:
        transaction.on_commit(lambda: notifications.notify_new_channel_linked_in_group.delay(instance.id))


@receiver(post_save, sender=GroupMission)
def create_new_mission_linked_in_group_notification(sender, instance, created, **kwargs):
    if created:
        transaction.on_commit(lambda: notifications.notify_new_mission_linked_in_group.delay(instance.id))


@receiver(post_save, sender=GroupLearningTrail)
def create_new_trail_linked_in_group_notification(sender, instance, created, **kwargs):
    if created:
        transaction.on_commit(lambda: notifications.notify_new_trail_linked_in_group.delay(instance.id))
