import uuid

from auditlog.models import LogEntry
from django.test import <PERSON><PERSON>ase
from eventlet.green.threading import current_thread
from model_mommy import mommy

from custom.global_requests import set_request


class StubRequest:
    def __init__(self, user):
        self.user = user


class TestReceiver(TestCase):
    def test_should_modifing_log_entry_before_save(self):
        user = {"sub": str(uuid.uuid4()), "name": "Nome", "email": "<EMAIL>"}
        request = StubRequest(user)
        set_request(current_thread().ident, request)

        log_entry = mommy.make(LogEntry)
        log_entry.refresh_from_db()

        self.assertEqual(
            log_entry.additional_data, {"user_actor": {"id": user["sub"], "name": user["name"], "email": user["email"]}}
        )
