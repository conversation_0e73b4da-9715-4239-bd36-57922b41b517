import datetime
import os

from celery import shared_task
from config import settings
from mission.models import Mission
from mission.services.mission_service import MissionService
from utils.task_transaction import task_transaction


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def republish_delayed_missions():
    missions = Mission.objects.filter(development_status="PROCESSING")
    max_time_minutes = os.getenv("MAX_TIME_MINUTES_MISSION_PROCESSING", 5)
    time5min = datetime.datetime.now().replace(tzinfo=None) - datetime.timedelta(minutes=max_time_minutes)
    for mission in missions:
        if mission.updated_date.replace(tzinfo=None) < time5min:
            mission_id = mission.id
            with task_transaction(republish_delayed_missions.__name__, MissionService) as service:
                service.publish(mission_id=mission_id, user_id=mission.user_creator_id)
