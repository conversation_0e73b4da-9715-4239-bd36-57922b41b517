from celery import shared_task
from mission.models.mission import LIVE, PRESENTIAL
from tasks.notifications.sync_mission_starts_soon import SyncMissionStartSoon
from utils.task_transaction import task_transaction

DAYS_REMAINING_TO_NOTIFY_SYNC_MISSION_STARTS_SOON = [0, 2]


@shared_task(ignore_result=True)
def notify_live_missions_starts_soon() -> None:
    with task_transaction("notify_live_mission_starts_soon"):
        service = SyncMissionStartSoon()
        for days_remaining in DAYS_REMAINING_TO_NOTIFY_SYNC_MISSION_STARTS_SOON:
            service.notify_all(LIVE, days_remaining)


@shared_task(ignore_result=True)
def notify_presential_missions_starts_soon() -> None:
    with task_transaction("notify_presential_mission_starts_soon"):
        service = SyncMissionStartSoon()
        for days_remaining in DAYS_REMAINING_TO_NOTIFY_SYNC_MISSION_STARTS_SOON:
            service.notify_all(PRESENTIAL, days_remaining)
