from datetime import date, datetime, timedelta
from typing import Union

from config import settings
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, ExpressionWrapper, F, Q, QuerySet
from django.forms import model_to_dict
from mission.models import LiveMission, LiveMissionDates, PresentialMission, PresentialMissionDates
from mission.models.mission import LIVE, PRESENTIAL
from pytz import timezone
from user_activity.models import MissionEnrollment
from user_activity.models.mission_enrollment import SYNC_STATUS_ACCEPTS
from utils.email_service.notification import notify_users

INVALID_MISSION_MODEL_CHOICE = "invalid_mission_model_choice"


class SyncMissionStartSoon:
    def __init__(self):
        self._sync_dates_models = {LIVE: LiveMissionDates, PRESENTIAL: PresentialMissionDates}
        self._sync_models = {LIVE: LiveMission, PRESENTIAL: PresentialMission}
        self._messages_keys = {LIVE: "live_mission_starts_soon", PRESENTIAL: "presential_mission_starts_soon"}

    def notify_all(self, mission_model: str, days_remaining: int) -> None:
        missions_ids = self.get_mission_ids_starts_soon(mission_model, days_remaining)
        enrollments = MissionEnrollment.objects.filter(
            mission_id__in=missions_ids, user__email_verified=True, status__in=SYNC_STATUS_ACCEPTS
        )
        for enrollment in enrollments:
            self._send_sync_mission_starts_soon_email(enrollment, days_remaining)

    def get_mission_ids_starts_soon(self, mission_model: str, days_remaining: int) -> QuerySet:
        sync_date_model = self._get_sync_dates_model(mission_model)
        sync_model = self._get_sync_model(mission_model)
        today = date.today()
        expiration_date = today + timedelta(days=days_remaining)
        expiration_datetime = datetime.combine(expiration_date, datetime.max.time())
        sync_ids = (
            sync_date_model.objects.filter(Q(start_at__lte=expiration_datetime))
            .annotate(days_r=ExpressionWrapper(F("start_at") - today, output_field=DurationField()))
            .filter(days_r__gte=timedelta(days=days_remaining), days_r__lte=timedelta(days=days_remaining + 1))
        ).values_list(f"{mission_model.lower()}_id", flat=True)
        mission_ids = sync_model.objects.filter(id__in=sync_ids).values_list("mission_id", flat=True)
        return mission_ids

    def _get_sync_dates_model(self, mission_model: str) -> Union[LiveMissionDates, PresentialMissionDates]:
        try:
            return self._sync_dates_models[mission_model]
        except KeyError:
            raise ValueError(INVALID_MISSION_MODEL_CHOICE)

    def _get_sync_model(self, mission_model: str) -> Union[LiveMission, PresentialMission]:
        try:
            return self._sync_models[mission_model]
        except KeyError:
            raise ValueError(INVALID_MISSION_MODEL_CHOICE)

    def _send_sync_mission_starts_soon_email(self, enrollment: MissionEnrollment, days_remaining: int) -> None:
        user = enrollment.user
        mission = enrollment.mission
        workspace = enrollment.workspace
        sync = mission.sync
        first_date = sync.dates.filter().order_by("start_at").first()
        start_at = None
        if first_date:
            start_at = first_date.start_at.astimezone(tz=timezone(user.time_zone))
        mission_starts_today = days_remaining == 0
        email_data = {
            "mission_name": mission.name,
            "mission_start_date": start_at.date(),
            "mission_start_time": start_at.time(),
            "mission_seats": sync.seats,
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
                enrollment.workspace.hash_id, enrollment.mission_id
            ),
            "user_name": user.name,
            "workspace_name": workspace.name,
            "days_remaining": days_remaining,
            "mission_vertical_cover_image": mission.thumb_image,
        }
        if mission.mission_model == LIVE:
            email_data.update({"mission_url": sync.url if mission_starts_today else None})
        elif mission.mission_model == PRESENTIAL:
            email_data.update({"mission_address": sync.address})

        receivers = [model_to_dict(user)]
        message_key = self._messages_keys[mission.mission_model]
        notify_users.delay(email_data, message_key, workspace.id, receivers)
