import uuid
from datetime import timedelta

import mock
from account.models import User, Workspace
from django.test import TestCase
from mission.models import Mission, MissionStage, MissionStageContent
from model_mommy import mommy
from pulse.models import Pulse
from tasks.learn_content_activity import normalize_all_big_time_in
from user_activity.models import LearnContentActivity

MOCK_KONTENT_CLIENT = "rest_clients.kontent.KontentClient.get_learn_content"


class LearnContentActivityTestCase(TestCase):
    def setUp(self):
        self.company = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps")
        self.user = mommy.make(User, email="user@email", name="Dono de tudo")

        self.pulse = mommy.make(Pulse, name="pulse", duration_time=100)
        self.mission = mommy.make(Mission, name="mission")
        self.mission_stage = mommy.make(MissionStage, mission=self.mission)
        self.mission_stage_content = mommy.make(
            MissionStageContent, learn_content_uuid=uuid.uuid4(), stage=self.mission_stage
        )

    @mock.patch(MOCK_KONTENT_CLIENT)
    def test_normalize_all_big_time_in(self, mock_kontent_client):
        pulse_activity = mommy.make(LearnContentActivity, user=self.user, time_in=timedelta(hours=3), pulse=self.pulse)
        mission_activity = mommy.make(
            LearnContentActivity,
            user=self.user,
            time_in=timedelta(hours=3),
            mission_stage_content=self.mission_stage_content,
        )
        duration = 200
        mock_kontent_client.return_value = {"duration": duration}
        normalize_all_big_time_in()

        pulse_activity_updated = LearnContentActivity.objects.get(id=pulse_activity.id)
        mission_activity_updated = LearnContentActivity.objects.get(id=mission_activity.id)
        self.assertEqual(timedelta(seconds=self.pulse.duration_time), pulse_activity_updated.time_in)
        self.assertEqual(timedelta(seconds=duration), mission_activity_updated.time_in)
