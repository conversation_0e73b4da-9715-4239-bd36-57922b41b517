from datetime import datetime, timedelta

from account.models import User, Workspace
from django.conf import settings
from django.forms import model_to_dict
from django.test import TestCase
from mission.models import LiveMission, LiveMissionDates, Mission, PresentialMission, PresentialMissionDates
from mission.models.mission import LIVE, PRESENTIAL
from mock import mock
from model_mommy import mommy
from pytz import timezone
from tasks.notifications.mission import notify_live_missions_starts_soon, notify_presential_missions_starts_soon
from user_activity.models import MissionEnrollment
from utils.utils import workspace_hash

NOTIFY_USER = "utils.email_service.notification.notify_users.delay"


class NotifyLiveMissionStartsSoonTest(TestCase):
    def setUp(self) -> None:
        self.workspace = mommy.make(Workspace, name="workspace")
        self.user = mommy.make(User, email="user@email", email_verified=True)
        self.mission = mommy.make(Mission, mission_model=LIVE)
        self.live = mommy.make(LiveMission, mission=self.mission, seats=10)
        self.enrollment = mommy.make(MissionEnrollment, mission=self.mission, user=self.user, workspace=self.workspace)

    @mock.patch(NOTIFY_USER)
    def test_notify_live_mission_starts_today(self, notify_user: mock.MagicMock):
        tz = timezone(self.user.time_zone)
        now = datetime.now(tz).replace(microsecond=0)
        later = now + timedelta(minutes=1)

        live_date = mommy.make(LiveMissionDates, live=self.live, start_at=later, end_at=later)

        notify_live_missions_starts_soon()

        start_at = live_date.start_at.astimezone(tz).replace(microsecond=0)

        expected_email_data = {
            "mission_name": self.mission.name,
            "mission_start_date": start_at.date(),
            "mission_start_time": start_at.time(),
            "mission_seats": self.live.seats,
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
                workspace_hash(str(self.enrollment.workspace_id)), self.enrollment.mission_id
            ),
            "user_name": self.user.name,
            "workspace_name": self.workspace.name,
            "days_remaining": 0,
            "mission_vertical_cover_image": self.mission.thumb_image,
            "mission_url": self.live.url,
        }
        notify_user.assert_called_with(
            expected_email_data, "live_mission_starts_soon", self.workspace.id, [model_to_dict(self.user)]
        )

    @mock.patch(NOTIFY_USER)
    def test_notify_live_mission_starts_in_two_days(self, notify_user: mock.MagicMock):
        later = datetime.today() + timedelta(days=2)
        live_date = mommy.make(LiveMissionDates, live=self.live, start_at=later, end_at=later)

        notify_live_missions_starts_soon()
        start_at = live_date.start_at.astimezone(tz=timezone(self.user.time_zone))

        expected_email_data = {
            "mission_name": self.mission.name,
            "mission_start_date": start_at.date(),
            "mission_start_time": start_at.time(),
            "mission_seats": self.live.seats,
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
                workspace_hash(str(self.enrollment.workspace_id)), self.enrollment.mission_id
            ),
            "user_name": self.user.name,
            "workspace_name": self.workspace.name,
            "days_remaining": 2,
            "mission_vertical_cover_image": self.mission.thumb_image,
            "mission_url": None,
        }
        notify_user.assert_called_with(
            expected_email_data, "live_mission_starts_soon", self.workspace.id, [model_to_dict(self.user)]
        )


class NotifyPresentialMissionStartsSoonTest(TestCase):
    def setUp(self) -> None:
        self.workspace = mommy.make(Workspace, name="workspace")
        self.user = mommy.make(User, email="user@email", email_verified=True)
        self.mission = mommy.make(Mission, mission_model=PRESENTIAL)
        self.presential = mommy.make(PresentialMission, mission=self.mission, seats=10)
        self.enrollment = mommy.make(MissionEnrollment, mission=self.mission, user=self.user, workspace=self.workspace)

    @mock.patch(NOTIFY_USER)
    def test_notify_mission_starts_today(self, notify_user: mock.MagicMock):
        tz = timezone(self.user.time_zone)
        now = datetime.now(tz).replace(microsecond=0)
        later = now + timedelta(minutes=1)

        presential_date = mommy.make(PresentialMissionDates, presential=self.presential, start_at=later, end_at=later)

        notify_presential_missions_starts_soon()

        start_at = presential_date.start_at.astimezone(tz).replace(microsecond=0)

        expected_email_data = {
            "mission_address": self.presential.address,
            "mission_name": self.mission.name,
            "mission_start_date": start_at.date(),
            "mission_start_time": start_at.time(),
            "mission_seats": self.presential.seats,
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
                workspace_hash(str(self.enrollment.workspace_id)), self.enrollment.mission_id
            ),
            "user_name": self.user.name,
            "workspace_name": self.workspace.name,
            "days_remaining": 0,
            "mission_vertical_cover_image": self.mission.thumb_image,
        }
        notify_user.assert_called_with(
            expected_email_data, "presential_mission_starts_soon", self.workspace.id, [model_to_dict(self.user)]
        )

    @mock.patch(NOTIFY_USER)
    def test_notify_mission_starts_in_two_days(self, notify_user: mock.MagicMock):
        later = datetime.today() + timedelta(days=2)
        presential_date = mommy.make(PresentialMissionDates, presential=self.presential, start_at=later, end_at=later)

        notify_presential_missions_starts_soon()
        start_at = presential_date.start_at.astimezone(tz=timezone(self.user.time_zone))

        expected_email_data = {
            "mission_name": self.mission.name,
            "mission_start_date": start_at.date(),
            "mission_start_time": start_at.time(),
            "mission_seats": self.presential.seats,
            "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
                workspace_hash(str(self.enrollment.workspace_id)), self.enrollment.mission_id
            ),
            "user_name": self.user.name,
            "workspace_name": self.workspace.name,
            "days_remaining": 2,
            "mission_vertical_cover_image": self.mission.thumb_image,
            "mission_address": self.presential.address,
        }
        notify_user.assert_called_with(
            expected_email_data, "presential_mission_starts_soon", self.workspace.id, [model_to_dict(self.user)]
        )
