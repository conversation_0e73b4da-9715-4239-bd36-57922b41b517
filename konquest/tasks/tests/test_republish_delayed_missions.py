import datetime
import os

from account.models import User
from django.test import TestCase
from mission.models import Mission
from mock import mock
from model_mommy import mommy
from tasks.republish_delayed_missions import republish_delayed_missions


class TestRepublishDelayedMissions(TestCase):
    def setUp(self) -> None:
        self.user = mommy.make(User, email="user@email", name="<PERSON><PERSON> de tudo")
        self.max_time_minutes = os.getenv("MAX_TIME_MINUTES_MISSION_PROCESSING", 5)

    @mock.patch("mission.services.mission_service.MissionService.publish")
    def test_with_mission_delayed(self, mock_publish):
        time_mission = datetime.datetime.now().replace(tzinfo=None) - datetime.timedelta(
            minutes=self.max_time_minutes + 1
        )
        Mission._meta.get_field("updated_date").auto_now = False
        self.mission = mommy.make(
            Mission, development_status="PROCESSING", user_creator=self.user, updated_date=time_mission
        )
        Mission._meta.get_field("updated_date").auto_now = True
        republish_delayed_missions()
        self.assertTrue(mock_publish.called)

    @mock.patch("mission.services.mission_service.MissionService.publish")
    def test_without_mission_delayed(self, mock_publish):
        time_mission = datetime.datetime.now().replace(tzinfo=None) - datetime.timedelta(
            minutes=self.max_time_minutes - 1
        )
        Mission._meta.get_field("updated_date").auto_now = False
        self.mission = mommy.make(
            Mission, development_status="PROCESSING", user_creator=self.user, updated_date=time_mission
        )
        Mission._meta.get_field("updated_date").auto_now = True
        republish_delayed_missions()
        self.assertFalse(mock_publish.called)
