import datetime
import uuid
from datetime import timed<PERSON><PERSON>

from account.models import User, Workspace
from django.test import TestCase
from learn_content.models import Answer, Exam, Question
from learning_trail.models import LearningTrail, LearningTrailStep
from mission.models import Mission, MissionStage
from mission.models.mission_development_status_enum import DONE
from model_mommy import mommy
from pulse.models import Pulse
from tasks.learning_trail_enrollment import (
    check_learning_trail_enrollment_status,
    update_learning_trail_enrollment_progress_by_pulse_consume,
    update_learning_trail_enrollment_progress_by_quiz_answer,
)
from user_activity.models import LearnContentActivity, LearningTrailEnrollment, UserMissionStage


class LearningTrailEnrollmentTaskTestCase(TestCase):
    def setUp(self):
        self.company = mommy.make(
            Workspace,
            id=uuid.uuid4(),
            name="Keeps",
            allow_list_public_mission=True,
            allow_list_paid_mission=True,
            allow_create_public_mission=True,
            allow_create_paid_mission=True,
        )

        self.learning_trail = mommy.make(LearningTrail, id=uuid.uuid4(), duration_time=1100)
        self.mission = mommy.make(Mission, id=uuid.uuid4(), is_active=True, duration_time=1000, development_status=DONE)
        self.mission_stage = mommy.make(MissionStage, id=uuid.uuid4(), mission=self.mission)
        self.pulse = mommy.make(Pulse, id=uuid.uuid4(), duration_time=100)

        mommy.make(LearningTrailStep, id=uuid.uuid4(), mission=self.mission, learning_trail=self.learning_trail)
        mommy.make(LearningTrailStep, id=uuid.uuid4(), pulse=self.pulse, learning_trail=self.learning_trail)

        self.user_consumer_1 = mommy.make(User, email="<EMAIL>")
        self.user_consumer_2 = mommy.make(User, email="<EMAIL>")
        self.user_consumer_3 = mommy.make(User, email="<EMAIL>")
        self.user_consumer_4 = mommy.make(User, email="<EMAIL>")

        # USER CONSUME MISSION
        self.learning_trail_enrollment_1 = mommy.make(
            LearningTrailEnrollment, user=self.user_consumer_1, learning_trail=self.learning_trail
        )
        # USER CONSUME PULSE
        self.learning_trail_enrollment_2 = mommy.make(
            LearningTrailEnrollment, user=self.user_consumer_2, learning_trail=self.learning_trail
        )
        # USER CONSUME ANYTHING
        self.learning_trail_enrollment_3 = mommy.make(
            LearningTrailEnrollment, user=self.user_consumer_3, learning_trail=self.learning_trail
        )
        # USER ALREADY STARTED
        self.learning_trail_enrollment_4 = mommy.make(
            LearningTrailEnrollment, user=self.user_consumer_4, learning_trail=self.learning_trail, status="STARTED"
        )

    def test_check_learning_trail_enrollment_status(self):
        self.user_mission_stage = mommy.make(UserMissionStage, user=self.user_consumer_1, stage=self.mission_stage)
        user_pulse_activity = mommy.make(LearnContentActivity, user=self.user_consumer_2, pulse=self.pulse)

        self.assertEqual(self.learning_trail_enrollment_1.status, "ENROLLED")
        self.assertEqual(self.learning_trail_enrollment_2.status, "ENROLLED")
        self.assertEqual(self.learning_trail_enrollment_3.status, "ENROLLED")

        check_learning_trail_enrollment_status()

        lt_enrollment_1 = LearningTrailEnrollment.objects.filter(id=self.learning_trail_enrollment_1.id).first()
        lt_enrollment_2 = LearningTrailEnrollment.objects.filter(id=self.learning_trail_enrollment_2.id).first()
        lt_enrollment_3 = LearningTrailEnrollment.objects.filter(id=self.learning_trail_enrollment_3.id).first()
        lt_enrollment_4 = LearningTrailEnrollment.objects.filter(id=self.learning_trail_enrollment_4.id).first()

        self.assertEqual(lt_enrollment_1.status, "STARTED")
        self.assertEqual(
            lt_enrollment_1.start_date.replace(tzinfo=None), self.user_mission_stage.created_date.replace(tzinfo=None)
        )
        self.assertEqual(lt_enrollment_2.status, "STARTED")
        self.assertEqual(
            lt_enrollment_2.start_date.replace(tzinfo=None), user_pulse_activity.time_start.replace(tzinfo=None)
        )
        self.assertEqual(lt_enrollment_3.status, "ENROLLED")
        self.assertEqual(lt_enrollment_4.status, "STARTED")

    def test_update_enrollment_progress_by_pulse_consume(self):
        mommy.make(
            LearnContentActivity,
            user=self.user_consumer_1,
            pulse=self.pulse,
            created_date=datetime.datetime.today() - timedelta(hours=0, minutes=10),
        )

        self.assertEqual(self.learning_trail_enrollment_1.status, "ENROLLED")

        result = update_learning_trail_enrollment_progress_by_pulse_consume()

        self.assertEqual("1 learning trail enrollment have been updated", result)

    def test_update_enrollment_progress_by_quiz_answer(self):
        pulse = mommy.make(Pulse, duration_time=300, points=100)
        exam = mommy.make(Exam, id=uuid.uuid4(), pulse=pulse)
        question = mommy.make(Question, id=uuid.uuid4(), exam=exam)
        pulse.learn_content_uuid = exam.id
        pulse.save()
        self.lt_step_pulse = mommy.make(LearningTrailStep, learning_trail=self.learning_trail, pulse=pulse, order=4)
        mommy.make(
            Answer,
            id=uuid.uuid4(),
            exam_has_question=question,
            user=self.user_consumer_1,
            options="1,2,3,4",
            is_ok=True,
        )

        self.assertEqual(self.learning_trail_enrollment_1.status, "ENROLLED")

        result = update_learning_trail_enrollment_progress_by_quiz_answer()
        self.assertEqual("1 learning trail enrollment have been updated", result)
