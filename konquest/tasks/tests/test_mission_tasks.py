import uuid
from datetime import date, timedelta
from unittest import mock

from account.models import Workspace
from config import settings
from constants import CELERY_SEND_TASK_PATH
from django.test import TestCase
from django.utils.timezone import now
from learn_content.models import Exam, Question
from mission.models import (
    LiveMission,
    LiveMissionDates,
    Mission,
    MissionContentResume,
    MissionStage,
    MissionStageContent,
    MissionWorkspace,
    PresentialMission,
    PresentialMissionDates,
)
from mission.models.mission import LIVE, PRESENTIAL
from mission.models.mission_content_type_enum import CONTENT, EXAM, HTML, SCORM
from mission.models.mission_development_status_enum import DONE, IN_REVIEW, INACTIVATED
from model_mommy import mommy
from tasks.mission import complete_sync_missions, disable_expired_mission, process_missions

GET_LEARN_CONTENT = "rest_clients.kontent.KontentClient.get_learn_content"
COMPLETE_SYNC = "mission.services.mission_service.MissionService.complete_sync"


class MissionTasksTestCase(TestCase):
    fixtures = ["mission_category", "mission_type"]

    def setUp(self):
        self.workspace = mommy.make(
            Workspace,
            id=uuid.uuid4(),
            name="Keeps",
            allow_list_public_mission=True,
            allow_list_paid_mission=True,
            allow_create_public_mission=True,
            allow_create_paid_mission=True,
        )

    @mock.patch(CELERY_SEND_TASK_PATH)
    def test_disable_expired_mission(self, send_task: mock.MagicMock):
        mission_id_test = uuid.uuid4()
        self.mission = mommy.make(Mission, id=mission_id_test, is_active=True, expiration_date=date.today())
        MissionWorkspace(mission=self.mission, workspace=self.workspace).save()
        disable_expired_mission(mission_id_test)

        mission = Mission.objects.get(id=mission_id_test)

        self.assertFalse(mission.is_active)
        self.assertEqual(mission.development_status, INACTIVATED)

    @mock.patch(COMPLETE_SYNC)
    def test_should_complete_presential_missions_without_future_dates(self, complete_sync: mock.MagicMock):
        mission = mommy.make(Mission, mission_model=PRESENTIAL, development_status=DONE)
        presential = mommy.make(PresentialMission, mission=mission)
        old_date = self._get_old_date()
        mommy.make(PresentialMissionDates, presential=presential, end_at=old_date)

        complete_sync_missions()

        mission.refresh_from_db()
        complete_sync.assert_called_with(mission)

    @mock.patch(COMPLETE_SYNC)
    def test_should_complete_live_missions_without_future_dates(self, complete_sync: mock.MagicMock):
        mission = mommy.make(Mission, mission_model=LIVE, development_status=DONE)
        live = mommy.make(LiveMission, mission=mission)
        old_date = self._get_old_date()
        mommy.make(LiveMissionDates, live=live, end_at=old_date)

        complete_sync_missions()

        mission.refresh_from_db()
        complete_sync.assert_called_with(mission)

    @mock.patch(COMPLETE_SYNC)
    def test_should_complete_task_ignore_live_missions_with_future_dates(self, complete_sync: mock.MagicMock):
        tomorrow_date = now() + timedelta(days=1)
        mission = mommy.make(Mission, mission_model=LIVE, development_status=DONE)
        live = mommy.make(LiveMission, mission=mission)
        mommy.make(LiveMissionDates, live=live, end_at=tomorrow_date)

        complete_sync_missions()

        mission.refresh_from_db()
        complete_sync.assert_not_called()

    @mock.patch(COMPLETE_SYNC)
    def test_should_complete_task_ignore_presential_missions_with_future_dates(self, complete_sync: mock.MagicMock):
        tomorrow_date = now() + timedelta(days=1)
        mission = mommy.make(Mission, mission_model=PRESENTIAL, development_status=DONE)
        presential = mommy.make(PresentialMission, mission=mission)
        mommy.make(PresentialMissionDates, presential=presential, end_at=tomorrow_date)

        complete_sync_missions()

        mission.refresh_from_db()
        complete_sync.assert_not_called()

    @staticmethod
    def _get_old_date():
        old_date = now() - timedelta(days=settings.DAYS_TO_AUTO_COMPLETE_SYNC_MISSION)
        return old_date


@mock.patch(GET_LEARN_CONTENT)
class MissionProcessTestCase(TestCase):
    def test_mission_process(self, *args):
        mission: Mission = mommy.make(Mission, is_active=True, development_status="PROCESSING")
        mission_stage = mommy.make(MissionStage, mission=mission)
        mommy.make(MissionStageContent, learn_content_uuid=uuid.uuid4(), stage=mission_stage, content_type="SCORM")
        mommy.make(MissionStageContent, learn_content_uuid=uuid.uuid4(), stage=mission_stage, content_type="SCORM")

        duration = 1000
        points = 200
        args[0].return_value = {"duration": duration, "points": points, "analyzed": True}

        process_missions()

        mission.refresh_from_db()

        self.assertEqual(mission.development_status, "IN_REVIEW")
        self.assertEqual(mission.duration_time, duration * 2)
        self.assertEqual(mission.points, points * 2)

    def test_mission_processing_without_contents_and_quizzes(self, *args):
        """
        Test when mission is created without any content or quiz
        In this case, the status must be IN_PROGRESS for admin continue creating mission
        """
        mission = mommy.make(Mission, is_active=True, development_status="PROCESSING")
        process_missions()
        mission.refresh_from_db()
        self.assertEqual(mission.development_status, "IN_PROGRESS")

    def test_mission_processing_with_contents_no_quiz(self, *args):
        args[0].return_value = {"duration": 100, "points": 10, "analyzed": True, "summary": "Text"}
        mission = mommy.make(Mission, is_active=True, development_status="PROCESSING")
        stage = mommy.make(MissionStage, mission=mission)
        mommy.make(MissionStageContent, content_type="CONTENT", stage=stage)
        mommy.make(MissionStageContent, content_type="SCORM", stage=stage)
        process_missions()
        mission.refresh_from_db()
        self.assertEqual(mission.development_status, "IN_REVIEW")
        self.assertEqual(mission.duration_time, 200.0)
        self.assertEqual(mission.points, 20)
        self.assertEqual(mission.summary, "\n\n\nNone\n\nText\n\n\nNone\n\nText")

    def test_mission_processing_with_contents_with_quiz(self, *args):
        args[0].return_value = {"duration": 100, "points": 10, "analyzed": True}
        mission = mommy.make(Mission, is_active=True, development_status="PROCESSING")
        stage = mommy.make(MissionStage, mission=mission)
        mommy.make(MissionStageContent, content_type=HTML, stage=stage)
        mommy.make(MissionStageContent, content_type=CONTENT, stage=stage)
        mommy.make(MissionStageContent, content_type=SCORM, stage=stage)
        mommy.make(MissionStageContent, content_type=EXAM, stage=stage)
        exam = mommy.make(Exam, stage=stage)
        mommy.make(Question, exam=exam)
        mommy.make(Question, exam=exam)

        process_missions()
        mission.refresh_from_db()
        others_contents = mission.content_resume.get(name="Others")
        exam_contents = mission.content_resume.get(name="Question")

        self.assertEqual(mission.development_status, "IN_REVIEW")
        self.assertEqual(mission.duration_time, 340.0)
        self.assertEqual(mission.points, 40)
        self.assertEqual(others_contents.count, 3)
        self.assertIsNone(others_contents.image)
        self.assertIsNone(others_contents.image_cover)
        self.assertEqual(exam_contents.count, 1)

    def test_mission_processing_no_contents_with_quiz(self, *args):
        mission = mommy.make(Mission, is_active=True, development_status="PROCESSING")
        stage = mommy.make(MissionStage, mission=mission)
        mommy.make(MissionStageContent, content_type="EXAM", stage=stage)
        exam = mommy.make(Exam, stage=stage)
        mommy.make(Question, exam=exam)
        mommy.make(Question, exam=exam)

        process_missions()
        mission.refresh_from_db()
        content_resume = mission.content_resume.first()

        self.assertEqual(mission.development_status, "IN_REVIEW")
        self.assertEqual(mission.duration_time, 40.0)
        self.assertEqual(mission.points, 10)
        self.assertIsNotNone(content_resume)
        self.assertEqual(content_resume.name, settings.CONTENT_TYPE_QUESTION_NAME)
        self.assertEqual(content_resume.image, settings.CONTENT_TYPE_QUESTION_IMAGE)
        self.assertEqual(content_resume.image_cover, settings.CONTENT_TYPE_QUESTION_IMAGE_COVER)

    def test_mission_processing_with_contents_with_quiz_not_analyzed(self, *args):
        args[0].return_value = {"analyzed": False}
        mission = mommy.make(Mission, is_active=True, development_status="PROCESSING")
        mommy.make(MissionContentResume, mission=mission, name=settings.CONTENT_TYPE_QUESTION_NAME, count=3)
        stage = mommy.make(MissionStage, mission=mission)
        mommy.make(MissionStageContent, content_type="CONTENT", stage=stage)
        mommy.make(MissionStageContent, content_type="SCORM", stage=stage)
        mommy.make(MissionStageContent, content_type="EXAM", stage=stage)
        exam = mommy.make(Exam, stage=stage)
        mommy.make(Question, exam=exam)
        mommy.make(Question, exam=exam)

        process_missions()
        mission.refresh_from_db()
        others_contents = mission.content_resume.get(name="Others")
        exam_contents = mission.content_resume.get(name="Question")

        self.assertEqual(mission.development_status, IN_REVIEW)
        self.assertEqual(mission.duration_time, 40)
        self.assertEqual(mission.points, 10)
        self.assertEqual(others_contents.count, 2)
        self.assertIsNone(others_contents.image)
        self.assertIsNone(others_contents.image_cover)
        self.assertEqual(exam_contents.count, 1)
