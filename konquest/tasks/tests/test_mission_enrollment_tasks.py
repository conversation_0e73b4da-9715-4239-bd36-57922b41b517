import datetime
import uuid
from unittest import mock

from account.models import User, Workspace
from django.test import TestCase
from mission.models import Mission
from model_mommy import mommy
from notification.models import NotificationType
from tasks.mission_enrollment import clean_duplicated_mission_enrollments, expire_enrollments
from user_activity.models import MissionEnrollment


class MissionEnrollmentDeadlineControlTaskTestCase(TestCase):
    def setUp(self):
        self.workspace = mommy.make(
            Workspace,
            id=uuid.uuid4(),
            name="Keep<PERSON>",
            allow_list_public_mission=True,
            allow_list_paid_mission=True,
            allow_create_public_mission=True,
            allow_create_paid_mission=True,
        )

        self.user_consumer_1 = mommy.make(User, email="<EMAIL>")

        self.mission_1 = mommy.make(Mission, id=uuid.uuid4(), is_active=True, duration_time=1000)
        self.mission_2 = mommy.make(Mission, id=uuid.uuid4(), is_active=True, duration_time=1000)
        self.mission_3 = mommy.make(Mission, id=uuid.uuid4(), is_active=True, duration_time=1000)

        goal_date = datetime.datetime.today() - datetime.timedelta(days=1)
        self.enrollment_to_be_expired = mommy.make(
            MissionEnrollment,
            user=self.user_consumer_1,
            mission=self.mission_1,
            goal_date=goal_date,
            workspace_id=self.workspace.id,
            required=True,
        )

        self.enrollment_without_deadline = mommy.make(
            MissionEnrollment,
            user=self.user_consumer_1,
            mission=self.mission_2,
            goal_date=goal_date,
            required=False,
        )

        self.enrollment_not_expired = mommy.make(
            MissionEnrollment,
            user=self.user_consumer_1,
            mission=self.mission_3,
            goal_date=goal_date,
            required=False,
        )

        NotificationType(id=uuid.uuid4(), object_type="MISSION_ENROLLMENT", action="LIST").save()

    def test_enrollment_deadline_control(self):
        expire_enrollments()
        enrollment_to_be_expired = MissionEnrollment.objects.get(id=self.enrollment_to_be_expired.id)
        enrollment_without_deadline = MissionEnrollment.objects.get(id=self.enrollment_without_deadline.id)
        enrollment_not_expired = MissionEnrollment.objects.get(id=self.enrollment_not_expired.id)

        self.assertEqual("EXPIRED", enrollment_to_be_expired.status)
        self.assertEqual("ENROLLED", enrollment_without_deadline.status)
        self.assertEqual("ENROLLED", enrollment_not_expired.status)

    @mock.patch("user_activity.services.MissionEnrollmentService.verify_integrate_after_create")
    def test_clean_duplicated_mission_enrollments(self, mock_verify_integrate_after_create: mock.MagicMock):
        workspace = mommy.make(Workspace)
        mission = mommy.make(Mission)

        mommy.make(MissionEnrollment, user=self.user_consumer_1, mission=mission, workspace=workspace)
        duplicated_enrollment = mommy.make(
            MissionEnrollment, user=self.user_consumer_1, mission=mission, workspace=workspace
        )

        clean_duplicated_mission_enrollments()

        mock_verify_integrate_after_create.assert_called_once_with(duplicated_enrollment.id)
