import uuid
from datetime import date

import mock
from account.models import User, Workspace
from django.test import TestCase
from learning_trail.models import LearningTrail, LearningTrailType, LearningTrailWorkspace
from model_mommy import mommy
from notification.models import NotificationType
from tasks.learning_trail import disable_expired_learning_trails


class LearningTrailServiceTestCase(TestCase):
    def setUp(self):
        self.workspace = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps")
        self.user_creator = mommy.make(User, email="user@email", name="Dono de tudo")

        self.learning_trail_type_open_for_workspace = mommy.make(LearningTrailType, name="Open For Workspace")

        self.notification = mommy.make(NotificationType, object_type="LEARNING_TRAIL", action="REVIEW")

    @mock.patch(
        "learning_trail.tasks.notifications.notify_learning_trail_has_expired.delay",
        return_value={},
    )
    def test_disable_expired_learning_trails(self, task: mock.MagicMock):
        learning_trail_id_test = uuid.uuid4()
        learning_trail = mommy.make(
            LearningTrail,
            id=learning_trail_id_test,
            user_creator=self.user_creator,
            is_active=True,
            expiration_date=date.today(),
        )
        mommy.make(
            LearningTrailWorkspace, learning_trail=learning_trail, workspace=self.workspace, relationship_type="OWNER"
        )

        disable_expired_learning_trails()

        learning_trail.refresh_from_db()
        self.assertEqual(learning_trail.is_active, False)

        task.assert_called_with(trail_id=learning_trail_id_test)
