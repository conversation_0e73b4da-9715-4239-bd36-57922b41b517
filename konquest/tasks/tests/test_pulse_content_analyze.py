from django.test import TestCase
from mock import mock
from model_mommy import mommy
from pulse.models import Pulse, PulseType
from rest_clients.kontent import KontentClient
from tasks.pulse import check_pulse_content_analyzed


class TestPulseContentAnalyze(TestCase):
    def setUp(self) -> None:
        self.video_type = mommy.make(PulseType)
        self.quiz_type = mommy.make(PulseType, name="Question")

    @mock.patch.object(KontentClient, "get_learn_content")
    def test_process_pulse_content_analyzed(self, get_learn_content: mock.MagicMock):
        pulse = mommy.make(Pulse, status="PROCESSING", pulse_type=self.video_type)
        duration = 30
        points = 10
        get_learn_content.return_value = {"analyzed": True, "duration": duration, "points": points}

        check_pulse_content_analyzed()

        pulse.refresh_from_db()
        self.assertEqual(pulse.duration_time, duration)
        self.assertEqual(pulse.points, points)
        get_learn_content.assert_called_once()
