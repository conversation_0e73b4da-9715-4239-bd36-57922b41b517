from datetime import datetime, timedelta

import pytest
from account.models import User
from django.core.exceptions import ObjectDoesNotExist
from django.test import TestCase
from mission.models import MissionStageContent
from model_mommy import mommy
from tasks.older_activities_aggregation import aggregate_old_learn_content_activity
from user_activity.models import LearnContentActivity


class AggregateOldLearnContentActivityTestCase(TestCase):
    def setUp(self):
        self.now = datetime.now()
        self.older_days = 400
        self.older_date_range = self.now - timedelta(days=self.older_days)
        self.user = mommy.make(User)
        self.mission_stage_content = mommy.make(MissionStageContent)
        self.mission_stage_content_2 = mommy.make(MissionStageContent)

    def test_aggregate_old_learn_content_activity(self):
        older_activity = mommy.make(
            LearnContentActivity,
            mission_stage_content=self.mission_stage_content,
            user=self.user,
            time_start=datetime.now(),
            time_in=timedelta(minutes=10),
            time_stop=datetime.now(),
            created_date=self.older_date_range,
        )
        older_activity.created_date = self.older_date_range
        older_activity.save()
        newest_activity = mommy.make(
            LearnContentActivity,
            mission_stage_content=self.mission_stage_content,
            user=self.user,
            time_in=timedelta(minutes=20),
            time_start=datetime.now(),
            time_stop=datetime.now(),
            created_date=self.older_date_range,
        )
        newest_activity.created_date = self.older_date_range
        newest_activity.save()

        another_activity = mommy.make(
            LearnContentActivity,
            mission_stage_content=self.mission_stage_content_2,
            user=self.user,
            time_in=timedelta(minutes=10),
            time_start=datetime.now(),
            time_stop=datetime.now(),
            created_date=self.older_date_range,
        )
        another_activity.created_date = self.older_date_range
        another_activity.save()

        aggregate_old_learn_content_activity()

        with pytest.raises(ObjectDoesNotExist):
            older_activity.refresh_from_db()

        expected_new_time_in = newest_activity.time_in + older_activity.time_in
        newest_activity.refresh_from_db()
        self.assertEqual(newest_activity.time_in, expected_new_time_in)

        expected_time_in = another_activity.time_in
        another_activity.refresh_from_db()
        self.assertEqual(another_activity.time_in, expected_time_in)

    def test_skip_newest_activities(self):
        older_activity = mommy.make(
            LearnContentActivity,
            mission_stage_content=self.mission_stage_content,
            user=self.user,
            time_start=datetime.now(),
            time_in=timedelta(minutes=10),
            time_stop=datetime.now(),
        )
        newest_activity = mommy.make(
            LearnContentActivity,
            mission_stage_content=self.mission_stage_content,
            user=self.user,
            time_in=timedelta(minutes=20),
            time_start=datetime.now(),
            time_stop=datetime.now(),
        )

        aggregate_old_learn_content_activity()

        expected_new_time_in = newest_activity.time_in
        newest_activity.refresh_from_db()
        self.assertEqual(newest_activity.time_in, expected_new_time_in)

        expected_time_in = older_activity.time_in
        self.assertEqual(older_activity.time_in, expected_time_in)
