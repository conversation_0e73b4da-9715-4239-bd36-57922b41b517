import uuid
from datetime import datetime, timedelta
from random import randrange
from unittest import mock

from account.models import User, Workspace
from config import settings
from django.forms import model_to_dict
from django.test import TestCase, override_settings
from group.models import Group, GroupMission, GroupUser
from mission.models import Mission
from model_mommy import mommy
from myaccount.application.services.myaccount_service import MyAccountService
from notification.models import NotificationType
from notification.models.notification import Message
from notification.services.notification_service_v2 import NotificationServiceV2
from pulse.models import Channel, Pulse, PulseChannel
from tasks.notification import (
    fetchall_mission_enrollment_expiring,
    notify_enrolled_in_group_missions,
    notify_new_missions_by_user,
    notify_new_missions_by_workspace,
    notify_new_pulses_and_channels_by_user,
    notify_users_with_expired_mission_enrollment,
    notify_users_with_mission_enrollment_expiring,
)
from user_activity.models import MissionEnrollment
from user_activity.tasks.notifications import YOUR_MISSION_ENROLLMENT_EXPIRES_IN_X_DAYS
from utils.utils import workspace_hash

NOTIFY_USER = "utils.email_service.notification.notify_users.delay"
MISSION_RECOMMENDATION = "mission.services.mission_recommendation.MissionRecommendationService.get_recommendations"
GET_MISSION_ENROLLMENTS_RESUME = "user_activity.services.user_service.UserService.get_user_mission_enrollments_resume"
GET_TRAIL_ENROLLMENTS_RESUME = "user_activity.services.user_service.UserService.get_user_trail_enrollments_resume"
CHANNEL_RECOMMENDATION = "pulse.services.channel_recommendation.ChannelRecommendationService.get_recommendations"
PULSE_RECOMMENDATION = "pulse.services.pulse_recommendation.PulseRecommendationService.user_pulses_priority"
GET_LEARN_CONTENT = "rest_clients.kontent.KontentClient.get_learn_content"
NOTIFY_NEW_MISSIONS_WORKSPACE = "tasks.notification.notify_new_missions_by_workspace.delay"
NOTIFY_NEW_MISSIONS_BY_USER = "tasks.notification.notify_new_missions_by_user.delay"
NOTIFY_NEW_PULSES_AND_CHANNELS_BY_WORKSPACE = "tasks.notification.notify_new_pulses_and_channels_by_workspace.delay"
NOTIFY_NEW_PULSES_AND_CHANNELS_BY_USER = "tasks.notification.notify_new_pulses_and_channels_by_user.delay"


@override_settings(SUSPEND_SIGNALS=True)
class NotificationMissionEnrollmentExpiringTestCase(TestCase):
    def setUp(self):
        self.workspace = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps")

        self.user_consumer_1 = mommy.make(User, name="user", email="<EMAIL>")
        self.mission_1 = mommy.make(
            Mission, id=uuid.uuid4(), name="The Big Mission", is_active=True, duration_time=1000
        )
        self.mission_2 = mommy.make(
            Mission, id=uuid.uuid4(), name="The Big Mission 2", is_active=True, duration_time=1000
        )
        self.mission_3 = mommy.make(
            Mission, id=uuid.uuid4(), name="The Big Mission 3", is_active=True, duration_time=1000
        )

        today_date = datetime.today() - timedelta(minutes=10)
        self.enrollment_expiring_in_5_days = mommy.make(
            MissionEnrollment,
            user=self.user_consumer_1,
            mission=self.mission_1,
            goal_date=today_date + timedelta(days=5),
            workspace_id=self.workspace.id,
            required=True,
        )

        self.enrollment_expiring_in_3_days = mommy.make(
            MissionEnrollment,
            user=self.user_consumer_1,
            mission=self.mission_2,
            goal_date=today_date + timedelta(days=3),
            workspace_id=self.workspace.id,
            required=True,
        )

        self.enrollment_expiring_today = mommy.make(
            MissionEnrollment,
            user=self.user_consumer_1,
            mission=self.mission_3,
            goal_date=today_date,
            workspace_id=self.workspace.id,
            required=True,
        )

    @mock.patch(NOTIFY_USER)
    @mock.patch.object(NotificationServiceV2, "create_notification")
    def test_notify_users_with_mission_enrollment_expiring(self, create_notification: mock.MagicMock, notify_user):
        mommy.make(NotificationType, object_type="MISSION_ENROLLMENT", action="EXPIRING")

        notify_users_with_mission_enrollment_expiring()

        message = Message(
            title=YOUR_MISSION_ENROLLMENT_EXPIRES_IN_X_DAYS,
            title_values={"days": 5},
            description=self.enrollment_expiring_in_5_days.mission.name,
        )
        create_notification.assert_any_call(
            user_ids=[self.enrollment_expiring_in_5_days.user_id],
            type_key="MISSION_ENROLLMENT_IS_EXPIRING",
            action="EXPIRING",
            workspace_id=self.enrollment_expiring_in_5_days.workspace_id,
            object=self.enrollment_expiring_in_5_days.mission_id,
            message=message,
        )

    def test_fetchall_mission_enrollment_expiring(self):
        query_set = fetchall_mission_enrollment_expiring(days_remaining=3)
        self.assertIn(self.enrollment_expiring_in_3_days, query_set)

        query_set = fetchall_mission_enrollment_expiring(days_remaining=0)
        self.assertIn(self.enrollment_expiring_today, query_set)


@override_settings(SUSPEND_SIGNALS=True)
class NotificationMissionEnrollmentExpired(TestCase):
    def setUp(self):
        self.workspace = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps")

        self.user_consumer_1 = mommy.make(User, email="<EMAIL>", email_verified=True)

        self.mission_1 = mommy.make(Mission, id=uuid.uuid4(), is_active=True, duration_time=1000)
        self.mission_2 = mommy.make(Mission, id=uuid.uuid4(), is_active=True, duration_time=1000)
        self.mission_3 = mommy.make(Mission, id=uuid.uuid4(), is_active=True, duration_time=1000)

        today_date = datetime.today() - timedelta(minutes=10)
        self.enrollment_expired_5_days_ago = mommy.make(
            MissionEnrollment,
            user=self.user_consumer_1,
            mission=self.mission_1,
            status="EXPIRED",
            goal_date=today_date - timedelta(days=5),
            workspace_id=self.workspace.id,
            required=True,
        )

        self.enrollment_expired_2_days_ago = mommy.make(
            MissionEnrollment,
            user=self.user_consumer_1,
            mission=self.mission_2,
            status="EXPIRED",
            goal_date=today_date - timedelta(days=3),
            workspace_id=self.workspace.id,
            required=True,
        )

        self.enrollment_expired_today = mommy.make(
            MissionEnrollment,
            user=self.user_consumer_1,
            mission=self.mission_3,
            goal_date=today_date,
            status="EXPIRED",
            workspace_id=self.workspace.id,
            required=True,
        )

    @mock.patch(NOTIFY_USER)
    def test_notify_users_with_expired_mission_enrollment(self, notify_user: mock.MagicMock):
        notify_users_with_expired_mission_enrollment()
        self.assertEqual(notify_user.call_count, 2)


@override_settings(SUSPEND_SIGNALS=True)
@mock.patch.object(MyAccountService, "list_all_consumer_users")
@mock.patch(MISSION_RECOMMENDATION)
class NotificationNewMissionsTestCase(TestCase):
    def setUp(self):
        self.workspace = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps")

        self.user_consumer_1 = mommy.make(User, email="<EMAIL>", email_verified=True)

        self.mission_1 = mommy.make(Mission, is_active=True, duration_time=1000, development_status="DONE")
        self.mission_2 = mommy.make(Mission, is_active=True, duration_time=1000, development_status="DONE")
        self.mission_3 = mommy.make(Mission, is_active=True, duration_time=1000, development_status="DONE")

    # @mock.patch(NOTIFY_NEW_MISSIONS_WORKSPACE)
    # def test_notify_new_missions_to_user(
    #     self, mock_notify_new_mission_workspace: mock.MagicMock, mock_mission_recommendation, mock_get_workspace_users
    # ):
    #     mommy.make(MissionWorkspace, workspace=self.workspace, mission=self.mission_1)
    #
    #     notify_new_missions()
    #
    #     mock_notify_new_mission_workspace.assert_called_with(self.workspace.id)

    @mock.patch(NOTIFY_NEW_MISSIONS_BY_USER)
    def test_notify_new_missions_to_workspace(
        self,
        mock_notify_new_missions_by_user: mock.MagicMock,
        mock_mission_recommendation,
        mock_list_all_consumer_users,
    ):
        mock_list_all_consumer_users.return_value = User.objects.filter(id=str(self.user_consumer_1.id))
        mock_mission_recommendation.return_value = Mission.objects.filter().all()

        notify_new_missions_by_workspace(self.workspace.id)

        mock_notify_new_missions_by_user.assert_called_with(self.user_consumer_1.id, self.workspace.id)

    @mock.patch(NOTIFY_USER)
    def test_notify_new_missions_by_user(
        self, notify_user: mock.MagicMock, mock_mission_recommendation, mock_get_workspace_users
    ):
        mock_mission_recommendation.return_value = Mission.objects.filter().all()

        notify_new_missions_by_user(self.user_consumer_1.id, self.workspace.id)

        notify_user.assert_called_with(
            email_data={
                "user_name": self.user_consumer_1.name,
                "workspace_name": "Keeps",
                "mission_1_name": self.mission_3.name,
                "mission_1_description": self.mission_3.description,
                "mission_1_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
                    workspace_hash(str(self.workspace.id)), self.mission_3.id
                ),
                "mission_2_name": self.mission_2.name,
                "mission_2_description": self.mission_2.description,
                "mission_2_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
                    workspace_hash(str(self.workspace.id)), self.mission_2.id
                ),
                "mission_3_name": self.mission_1.name,
                "mission_3_description": self.mission_1.description,
                "mission_3_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
                    workspace_hash(str(self.workspace.id)), self.mission_1.id
                ),
            },
            message_key="new_missions",
            workspace_id=self.workspace.id,
            users_receivers=[model_to_dict(self.user_consumer_1)],
        )


@override_settings(SUSPEND_SIGNALS=True)
class NotificationNewPulsesAndChannelsTestCase(TestCase):
    def setUp(self):
        self.workspace = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps", hash_id="keeps-workspace")

        self.user_consumer_1 = mommy.make(User, email="<EMAIL>", email_verified=True)
        self.user_consumer_2 = mommy.make(User, email="<EMAIL>", email_verified=True)

        self.pulse_1 = mommy.make(Pulse)
        self.pulse_2 = mommy.make(Pulse)
        self.pulse_3 = mommy.make(Pulse)

        self.channel_1 = mommy.make(Channel, is_active=True, workspace=self.workspace)
        self.channel_2 = mommy.make(Channel, is_active=True, workspace=self.workspace)
        mommy.make(PulseChannel, pulse=self.pulse_1, channel=self.channel_1)
        mommy.make(PulseChannel, pulse=self.pulse_2, channel=self.channel_1)
        mommy.make(PulseChannel, pulse=self.pulse_3, channel=self.channel_1)

    # @mock.patch(NOTIFY_NEW_PULSES_AND_CHANNELS_BY_WORKSPACE)
    # def test_notify_new_pulses_and_channels(self, mock_notify_new_pulses_and_channels_by_workspace: mock.MagicMock):
    #     notify_new_pulses_and_channels()
    #     mock_notify_new_pulses_and_channels_by_workspace.assert_called_once_with(self.workspace.id)
    @mock.patch(NOTIFY_NEW_PULSES_AND_CHANNELS_BY_USER)
    @mock.patch.object(MyAccountService, "list_all_consumer_users")
    def test_notify_new_pulses_and_channels_to_workspace(
        self, mock_list_all_consumer_users: mock.MagicMock, mock_notify_new_pulse_and_channels_by_user: mock.MagicMock
    ):
        mock_list_all_consumer_users.return_value = [
            self.user_consumer_1,
            self.user_consumer_2,
        ]

    # @mock.patch(NOTIFY_NEW_PULSES_AND_CHANNELS_BY_USER)
    # @mock.patch(GET_COMPANY_USERS_PUBLIC)
    # def test_notify_new_pulses_and_channels_to_workspace(
    #     self, mock_get_workspace_users: mock.MagicMock, mock_notify_new_pulse_and_channels_by_user: mock.MagicMock
    # ):
    #     mock_get_workspace_users.return_value = [
    #         get_account_user_fake(self.user_consumer_1),
    #         get_account_user_fake(self.user_consumer_2),
    #     ]
    #
    #     notify_new_pulses_and_channels_by_workspace(self.workspace.id)
    #
    #     mock_notify_new_pulse_and_channels_by_user.assert_any_call(self.user_consumer_1.id, self.workspace.id)
    #     mock_notify_new_pulse_and_channels_by_user.assert_any_call(self.user_consumer_2.id, self.workspace.id)

    @mock.patch(NOTIFY_USER)
    @mock.patch(CHANNEL_RECOMMENDATION)
    @mock.patch(PULSE_RECOMMENDATION)
    @mock.patch(GET_LEARN_CONTENT)
    def test_notify_new_pulses_and_channels_to_user(
        self,
        mock_get_learn_content,
        mock_pulse_recommendation,
        mock_channel_recommendation,
        notify_user,
    ):
        mock_channel_recommendation.return_value = Channel.objects.filter()
        mock_pulse_recommendation.return_value = Pulse.objects.filter()
        mock_get_learn_content.return_value = {
            "content_type": {
                "image": "https://s3.amazonaws.com/keeps.konquest.media.prd/assets/pulse-type/icon/video.png"
            }
        }

        notify_new_pulses_and_channels_by_user(self.user_consumer_1.id, self.workspace.id)

        self.assertEqual(notify_user.call_count, 1)


@override_settings(SUSPEND_SIGNALS=True)
class NotificationEnrolledInGroupMissionsTestCase(TestCase):
    def setUp(self):
        self.workspace = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps")

        self.user_consumer_1 = mommy.make(User, email="<EMAIL>", email_verified=True)
        self.user_consumer_2 = mommy.make(User, email="<EMAIL>", email_verified=True)

        self.group = mommy.make(Group, workspace=self.workspace)
        self.mission_1 = mommy.make(Mission, is_active=True, duration_time=1000, development_status="DONE")
        self.mission_2 = mommy.make(Mission, is_active=True, duration_time=1000, development_status="DONE")

    @mock.patch.object(MyAccountService, "list_all_consumer_users")
    @mock.patch(NOTIFY_USER)
    def test_notify_enrolled_in_group_missions(self, notify_user, mock_account_user):
        mock_account_user.return_value = get_account_user_fake(self.user_consumer_1)
        mommy.make(GroupUser, group=self.group, user=self.user_consumer_1)
        mommy.make(GroupUser, group=self.group, user=self.user_consumer_2)
        mommy.make(GroupMission, group=self.group, mission=self.mission_1)
        mommy.make(GroupMission, group=self.group, mission=self.mission_2)
        mommy.make(
            MissionEnrollment,
            user=self.user_consumer_1,
            mission=self.mission_1,
            required=True,
            goal_date=datetime.now(),
            workspace=self.workspace,
        )
        mommy.make(
            MissionEnrollment,
            user=self.user_consumer_1,
            mission=self.mission_2,
            required=False,
            goal_date=datetime.now(),
            workspace=self.workspace,
        )

        notify_enrolled_in_group_missions()

        self.assertEqual(notify_user.call_count, 1)


@mock.patch(NOTIFY_USER)
@mock.patch(GET_MISSION_ENROLLMENTS_RESUME)
@mock.patch(GET_TRAIL_ENROLLMENTS_RESUME)
@override_settings(SUSPEND_SIGNALS=True)
class NotificationMonthlyUserActivitiesSummaryTestCase(TestCase):
    def setUp(self):
        self.workspace = mommy.make(Workspace, id=uuid.uuid4(), name="Keeps")

        self.mission_1 = mommy.make(Mission)

        self.user_consumer_1 = mommy.make(User, email="<EMAIL>", email_verified=True)
        self.user_consumer_2 = mommy.make(User, email="<EMAIL>", email_verified=True)

    # def test_notify_with_activities_in_month(
    #     self, mock_get_trail_enrollments_resume, mock_get_mission_enrollments_resume, notify_user
    # ):
    #     today = datetime.today()
    #     actual_month = today.month
    #     if actual_month == 1:
    #         previous_month = 12
    #         year = today.year - 1
    #     else:
    #         previous_month = actual_month - 1
    #         year = today.year
    #     while today.day != 0:
    #         try:
    #             previous_date = today.replace(month=previous_month, year=year)
    #             break
    #         except ValueError:
    #             today = today.replace(day=max([today.day - 1, 1]))
    #
    #     mock_get_trail_enrollments_resume.return_value = get_enrollments_resume_fake()
    #     mock_get_mission_enrollments_resume.return_value = get_enrollments_resume_fake()
    #     mission_enrollment_1 = mommy.make(
    #         MissionEnrollment, user=self.user_consumer_1, mission=self.mission_1, workspace=self.workspace
    #     )
    #     mission_enrollment_2 = mommy.make(
    #         MissionEnrollment, user=self.user_consumer_2, mission=self.mission_1, workspace=self.workspace
    #     )
    #     activity_1 = mommy.make(
    #         LearnContentActivity, user=self.user_consumer_1, mission_enrollment=mission_enrollment_1
    #     )
    #     activity_1.created_date = previous_date
    #     activity_1.save()
    #     activity_2 = mommy.make(
    #         LearnContentActivity, user=self.user_consumer_2, mission_enrollment=mission_enrollment_2
    #     )
    #     activity_2.created_date = previous_date
    #     activity_2.save()
    #
    #     notify_monthly_user_activities_summary()
    #
    #     self.assertEqual(notify_user.call_count, 2)

    # def test_no_notify_without_activities_in_month(
    #     self, mock_get_trail_enrollments_resume, mock_get_mission_enrollments_resume, notify_user
    # ):
    #     mock_get_trail_enrollments_resume.return_value = get_enrollments_resume_fake()
    #     mock_get_mission_enrollments_resume.return_value = get_enrollments_resume_fake()
    #     mission_enrollment_1 = mommy.make(
    #         MissionEnrollment, user=self.user_consumer_1, mission=self.mission_1, workspace=self.workspace
    #     )
    #     mission_enrollment_2 = mommy.make(
    #         MissionEnrollment, user=self.user_consumer_2, mission=self.mission_1, workspace=self.workspace
    #     )
    #     mommy.make(LearnContentActivity, user=self.user_consumer_1, mission_enrollment=mission_enrollment_1)
    #     mommy.make(LearnContentActivity, user=self.user_consumer_2, mission_enrollment=mission_enrollment_2)
    #
    #     notify_monthly_user_activities_summary()
    #
    #     self.assertEqual(notify_user.call_count, 0)


def get_account_user_fake(user: User):
    return {"id": str(user.id), "name": user.name, "email": user.email, "language": {"name": "pt-BR"}}


def get_enrollments_resume_fake():
    return {
        "enrollments_progress": 0.72,
        "enrollments": randrange(20),
        "enrollments_enrolled": randrange(10),
        "enrollments_started": randrange(10),
        "enrollments_completed": randrange(10),
        "enrollments_give_up": randrange(10),
    }
