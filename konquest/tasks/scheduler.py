from learning_trail.tasks.mission_trail_disabled_too_long_notification_task import (
    notify_mission_trail_disabled_too_long,
)
from tasks.learn_content_activity import normalize_all_big_time_in
from tasks.learning_trail import disable_expired_learning_trails
from tasks.learning_trail_enrollment import (
    check_learning_trail_enrollment_status,
    clean_duplicated_trail_enrollments,
    update_learning_trail_enrollment_progress_by_pulse_consume,
    update_learning_trail_enrollment_progress_by_quiz_answer,
)
from tasks.mission import complete_sync_missions
from tasks.mission_enrollment import clean_duplicated_mission_enrollments, expire_enrollments
from tasks.notification import (
    notify_enrolled_in_group_missions,
    notify_users_with_expired_mission_enrollment,
    notify_users_with_mission_enrollment_expiring,
)
from tasks.notifications.mission import notify_live_missions_starts_soon, notify_presential_missions_starts_soon
from tasks.pulse import check_pulse_content_analyzed
from tasks.republish_delayed_missions import republish_delayed_missions
from user_activity.tasks.notifications import notify_all_trail_enrollments_expiring
from .mission import disable_expired_mission