from datetime import datetime

from celery import shared_task
from config import settings
from django.db.models import Q
from user_activity.models import MissionEnrollment
from user_activity.services.duplicated_mission_enrollments_clean_service import DuplicatedMissionEnrollmentsCleanService
from utils.task_transaction import task_transaction


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def expire_enrollments():
    with task_transaction("expire_enrollment"):
        today_date = datetime.today().date()
        expired_mission_enrollments = MissionEnrollment.objects.filter(
            Q(goal_date__lte=today_date, required=True) & ~Q(status__in=["EXPIRED", "COMPLETED", "REPROVED"])
        ).all()
        for enrollment in expired_mission_enrollments:
            enrollment.status = "EXPIRED"
            enrollment.save()


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def clean_duplicated_mission_enrollments():
    with task_transaction("clean_duplicated_mission_enrollments", DuplicatedMissionEnrollmentsCleanService) as service:
        service: DuplicatedMissionEnrollmentsCleanService
        service.clean_enrollments()
