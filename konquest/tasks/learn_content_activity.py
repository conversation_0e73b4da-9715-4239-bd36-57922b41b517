import traceback
from datetime import timedelta
from json import JSONDecodeE<PERSON>r

from celery import shared_task
from config import settings
from config.settings import GRE<PERSON>, KEEPS_SECRET_TOKEN_INTEGRATION, RED, RESET
from django.db.models import Q
from rest_clients.kontent import KontentClient
from user_activity.models import LearnContentActivity
from utils.task_transaction import task_transaction

TIME_IN_OUTLINE = timedelta(hours=2)


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def normalize_all_big_time_in():
    with task_transaction("normalize_all_big_time_in", KontentClient) as kontent_client:
        print("-- Start Task learn content activity - normalize all big time in --")
        activities = LearnContentActivity.objects.filter(
            Q(time_in__gte=TIME_IN_OUTLINE) | Q(time_in__lt=timedelta(0))
        ).all()
        success_count = 0

        for activity in activities:
            try:
                __normalize_big_time_in(activity=activity, kontent_client=kontent_client)
                success_count += 1
            except Exception as e:
                print(RED + f"One Exception Happened: {str(e)} {str(traceback.format_exc())}" + RESET)

        print("   " + GREEN + f"{success_count} Activities Was normalized" + RESET)


def __normalize_big_time_in(activity: LearnContentActivity, kontent_client: KontentClient):
    object_duration = 0
    if activity.pulse:
        object_duration = activity.pulse.duration_time if activity.pulse.duration_time else 0
    if activity.mission_stage_content:
        try:
            object_duration = kontent_client.get_learn_content(
                token=KEEPS_SECRET_TOKEN_INTEGRATION, content_id=str(activity.mission_stage_content.learn_content_uuid)
            ).get("duration", 0)
            object_duration = object_duration if object_duration is not None else 0
        except JSONDecodeError:
            object_duration = 0

    activity.time_in = timedelta(seconds=object_duration)
    activity.save()
    print(GREEN + f"   Activity Normalized:\n   id:{activity.id}\n   time_in:{activity.time_in}\n")
