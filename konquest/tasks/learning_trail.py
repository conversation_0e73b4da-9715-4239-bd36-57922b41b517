from datetime import datetime

from celery import shared_task
from config import settings
from learning_trail.models import LearningTrail
from learning_trail.tasks.notifications import notify_learning_trail_has_expired
from utils.task_transaction import task_transaction


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def disable_expired_learning_trails():
    with task_transaction("disable_expired_learning_trails"):
        now = datetime.today()
        expired_learning_trails = LearningTrail.objects.filter(expiration_date__lte=now, is_active=True).all()
        for learning_trail in expired_learning_trails:
            learning_trail.is_active = False
            learning_trail.save()
            notify_learning_trail_has_expired.delay(trail_id=learning_trail.id)
