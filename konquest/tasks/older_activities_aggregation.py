import logging
from datetime import datetime, timedelta

from celery import shared_task
from config import settings
from config.settings import LEARN_CONTENT_AGGREGATION_CUTOFF_DAYS
from django.db.models import Count, Min, Sum
from gamification.models.gamification_history import GamificationHistory
from user_activity.models import LearnContentActivity
from utils.task_transaction import task_transaction

logger = logging.getLogger()


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def aggregate_old_learn_content_activity():
    with task_transaction(aggregate_old_learn_content_activity.__name__):
        logging.info("Starting aggregation of old learn content activities")
        if LEARN_CONTENT_AGGREGATION_CUTOFF_DAYS < 365:
            raise RuntimeError("Cutoff days too low. Min value is 365 days")
        end_date_range = datetime.now() - timedelta(days=LEARN_CONTENT_AGGREGATION_CUTOFF_DAYS)

        activities = LearnContentActivity.objects.filter(created_date__lte=end_date_range)

        aggregated_activities = (
            activities.values("user", "mission_stage_content", "mission_enrollment", "pulse", "workspace")
            .annotate(total_time_in=Sum("time_in"), earliest_time_start=Min("time_start"), count_activities=Count("id"))
            .filter(count_activities__gt=1)
        )

        for activity in aggregated_activities:
            print("Aggregating activities for activity: " + str(activity))

            user = activity["user"]
            mission_stage_content = activity["mission_stage_content"]
            mission_enrollment = activity["mission_enrollment"]
            pulse = activity["pulse"]
            workspace = activity["workspace"]
            total_time_in = activity["total_time_in"] or timedelta(seconds=0)
            earliest_time_start = activity["earliest_time_start"]

            latest_activity = (
                LearnContentActivity.objects.filter(
                    user=user,
                    mission_stage_content=mission_stage_content,
                    mission_enrollment=mission_enrollment,
                    pulse=pulse,
                    workspace=workspace,
                )
                .order_by("-time_stop")
                .first()
            )

            if latest_activity:
                latest_activity.time_in = total_time_in
                latest_activity.time_start = earliest_time_start
                latest_activity.save()

                activities_to_delete = activities.filter(
                    user=user,
                    mission_stage_content=mission_stage_content,
                    mission_enrollment=mission_enrollment,
                    pulse=pulse,
                    workspace=workspace,
                ).exclude(id=latest_activity.id)

                GamificationHistory.objects.filter(
                    learn_content_activity__in=activities_to_delete.values_list("id", flat=True)
                ).update(learn_content_activity_id=latest_activity.id)

                activities_to_delete.delete()

        logging.info("Completed aggregation of old learn content activities")
