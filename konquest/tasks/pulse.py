# -*- coding: utf-8 -*-
from celery import shared_task
from config import settings
from config.settings import KEEPS_SECRET_TOKEN_INTEGRATION
from custom.discord_webhook import DiscordWebhookLogger
from learn_content.models import Question
from pulse.models import Pulse, PulseTag, PulseType
from rest_clients.kontent import KontentClient
from utils.task_transaction import task_transaction


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def check_pulse_content_analyzed():
    with task_transaction("check_pulse_content_analyzed"):
        pulses = Pulse.objects.filter(status__in=["PROCESSING", "IN_REVIEW"]).all()
        logger = DiscordWebhookLogger()

        for pulse in pulses:
            try:
                pulse_quiz = PulseType.objects.filter(name="Question").first()
                content_uuid = str(pulse.learn_content_uuid)
                kontent_client = KontentClient()

                if pulse.pulse_type.id == pulse_quiz.id:
                    question_qtd = Question.objects.filter(exam_id=content_uuid).count()
                    pulse.points = question_qtd * 5
                    pulse.duration_time = question_qtd * 30
                    pulse.status = "DONE"
                    pulse.save()

                else:
                    print("checking learn content {}".format(content_uuid))
                    response = kontent_client.get_learn_content(KEEPS_SECRET_TOKEN_INTEGRATION, content_uuid)

                    if response.__len__() == 0:
                        pulse.status = "DONE"
                        pulse.save()

                    elif "analyzed" in response and response["analyzed"] is True:
                        pulse.points = int(response["points"]) if "points" in response else 10
                        pulse.duration_time = int(response["duration"]) if "duration" in response else 10

                        if "tag" in response:
                            for tag in response["tag"]:
                                if tag["relevance"] > 0.5:
                                    instance_tag = PulseTag()
                                    instance_tag.name = tag["text"]
                                    instance_tag.relevance = tag["relevance"]
                                    instance_tag.pulse = pulse
                                    try:
                                        instance_tag.save()
                                    except Exception as e:
                                        print(e)

                        pulse.status = "DONE"
                        pulse.save()

            except Exception as error:
                logger.emit_short_message(f"KONQUEST WORKER | Pulse processing error: {pulse.id}", error)
