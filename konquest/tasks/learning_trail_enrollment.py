from datetime import datetime, timedelta

from celery import shared_task
from config import settings
from learn_content.models import Answer, Exam
from learning_trail.models import LearningTrailStep
from mission.models import MissionStage
from user_activity.models import LearnContentActivity, LearningTrailEnrollment, UserMissionStage
from user_activity.services.duplicated_trail_enrollments_clean_service import DuplicatedTrailEnrollmentsCleanService
from user_activity.tasks.learning_trail_enrollment_task import update_enrollments_progress
from utils.task_transaction import task_transaction


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def update_learning_trail_enrollment_progress_by_pulse_consume():
    # check if exist a new pulse consume in the last 30 minutes
    with task_transaction("update_learning_trail_enrollment_progress_by_pulse_consume"):
        datetime_to_filter = datetime.today() - timedelta(
            minutes=settings.UPDATE_LEARNING_TRAIL_ENROLLMENT_PROGRESS_BY_PULSE_CONSUME_MINUTES + 1
        )
        activities = LearnContentActivity.objects.filter(
            pulse__isnull=False, created_date__gte=datetime_to_filter
        ).all()
        if not activities:
            return
        activities_pulse_ids = activities.values_list("pulse_id", flat=True)
        activities_user_ids = activities.values_list("user_id", flat=True)
        lt_ids = LearningTrailStep.objects.filter(pulse_id__in=activities_pulse_ids).values_list(
            "learning_trail_id", flat=True
        )

        lt_enrollment_ids = LearningTrailEnrollment.objects.filter(
            learning_trail_id__in=lt_ids, user_id__in=activities_user_ids, status__in=["STARTED", "ENROLLED"]
        ).values_list("id", flat=True)
        for enrollment_id in lt_enrollment_ids:
            update_enrollments_progress(enrollment_id)

        return f"{len(lt_enrollment_ids)} learning trail enrollment have been updated"


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def update_learning_trail_enrollment_progress_by_quiz_answer():
    with task_transaction("update_learning_trail_enrollment_progress_by_quiz_answer"):
        datetime_to_filter = datetime.today() - timedelta(hours=0, minutes=31)
        answers = Answer.objects.filter(created_date__gte=datetime_to_filter).all()

        if not answers.exists():
            return
        exam_ids = answers.values_list("exam_has_question__exam_id", flat=True)
        exams = Exam.objects.filter(id__in=exam_ids).all()

        trail_ids = LearningTrailStep.objects.filter(pulse_id__in=exams.values_list("pulse_id", flat=True)).values_list(
            "learning_trail_id", flat=True
        )

        enrollment_ids = LearningTrailEnrollment.objects.filter(
            learning_trail_id__in=trail_ids,
            user_id__in=answers.values_list("user_id", flat=True),
            status__in=["STARTED", "ENROLLED"],
        ).values_list("id", flat=True)

        for enrollment_id in enrollment_ids:
            update_enrollments_progress(enrollment_id)

        return f"{len(enrollment_ids)} learning trail enrollment have been updated"


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def check_learning_trail_enrollment_status():
    with task_transaction("check_learning_trail_enrollment_status"):
        trail_enrollments_enrolled = LearningTrailEnrollment.objects.filter(status="ENROLLED").all()
        for enrollment in trail_enrollments_enrolled:
            updated = __update_trail_enrollment_status_by_mission_consume(enrollment)
            if updated:
                continue
            __update_trail_enrollment_status_by_pulse_consume(enrollment)


def __update_trail_enrollment_status_by_mission_consume(enrollment: LearningTrailEnrollment):
    """
    If user already consume some trail mission update enrollment and return True
    """
    lt_missions = LearningTrailStep.objects.filter(
        learning_trail=enrollment.learning_trail, mission_id__isnull=False
    ).values_list("mission_id", flat=True)
    missions_stages = MissionStage.objects.filter(mission_id__in=lt_missions).values_list("id", flat=True)
    user_mission_stage = UserMissionStage.objects.filter(user=enrollment.user, stage_id__in=missions_stages).first()
    if user_mission_stage:
        enrollment.start_date = user_mission_stage.created_date
        enrollment.status = "STARTED"
        enrollment.save()
        return True
    return False


def __update_trail_enrollment_status_by_pulse_consume(enrollment: LearningTrailEnrollment):
    """
    If user already consume some trail pulse update enrollment and return True
    """
    lt_pulses = LearningTrailStep.objects.filter(
        learning_trail=enrollment.learning_trail, pulse_id__isnull=False
    ).values_list("pulse_id", flat=True)
    user_lc_activity = LearnContentActivity.objects.filter(user=enrollment.user, pulse_id__in=lt_pulses).first()
    if user_lc_activity:
        enrollment.start_date = user_lc_activity.time_start
        enrollment.status = "STARTED"
        enrollment.save()
        return True
    return False


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def clean_duplicated_trail_enrollments():
    with task_transaction("clean_duplicated_trail_enrollments", DuplicatedTrailEnrollmentsCleanService) as service:
        service: DuplicatedTrailEnrollmentsCleanService
        service.clean_enrollments()
