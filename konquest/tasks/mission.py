from datetime import datetime, timedelta

from celery import shared_task
from config import settings
from custom.discord_webhook import DiscordWebhookLogger
from django.db.models import Q
from django.utils.timezone import now
from mission.models import LiveMissionDates, Mission, PresentialMissionDates
from mission.models.mission import LIVE, PRESENTIAL
from mission.models.mission_development_status_enum import DONE
from mission.services.mission_processing_service import MissionProcessingService
from mission.services.mission_service import MissionService
from mission.task_names import DISABLE_EXPIRED_MISSION
from utils.task_transaction import task_transaction


def process_missions():
    with task_transaction("process_missions", MissionProcessingService) as service:
        print(f"{datetime.now()} | starting check missions contents")
        missions = Mission.objects.filter(development_status="PROCESSING").all()

        for mission in missions:
            print(f"{datetime.now()} | analyzing mission {mission.id}")
            try:
                service.process(mission)
            except Exception as error:
                logger(mission, error)


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True)
def complete_sync_missions():
    with task_transaction("close_sync_missions", MissionService) as service:
        old_date = now() - timedelta(days=settings.DAYS_TO_AUTO_COMPLETE_SYNC_MISSION)
        live_ids_with_future_dates = LiveMissionDates.objects.filter(end_at__gt=old_date).values_list(
            "live__mission_id", flat=True
        )
        presential_ids_with_future_dates = PresentialMissionDates.objects.filter(end_at__gt=old_date).values_list(
            "presential__mission_id", flat=True
        )
        missions = Mission.objects.filter(mission_model__in=[PRESENTIAL, LIVE], development_status=DONE).exclude(
            Q(id__in=live_ids_with_future_dates) | Q(id__in=presential_ids_with_future_dates)
        )
        for mission in missions:
            service.complete_sync(mission)


@shared_task(queue=settings.CELERY_QUEUE, ignore_result=True, name=DISABLE_EXPIRED_MISSION)
def disable_expired_mission(mission_id: str):
    with task_transaction("disable_expired_mission", MissionService) as service:
        expired_mission = Mission.objects.get(id=mission_id)
        service.update_is_activated(expired_mission, False)


def logger(mission, error):
    logger_client = DiscordWebhookLogger()
    logger_client.emit_short_message(f"KONQUEST WORKER | Mission processing error: mission_id ({mission.id})", error)
