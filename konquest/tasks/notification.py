import calendar
from datetime import date, datetime, timedelta
from unittest.mock import <PERSON><PERSON><PERSON>

from account.models import User, Workspace
from celery import shared_task
from config import settings
from config.settings import (
    NOTIFY_NEW_MISSIONS_BY_USER_TASK_RATE,
    NOTIFY_NEW_MISSIONS_BY_WORKSPACE_TASK_RATE,
    NOTIFY_NEW_PULSES_AND_CHANNELS_BY_USERS_TASK_RATE,
)
from custom.discord_webhook import DiscordWebhookLogger
from django.db.models import Du<PERSON><PERSON><PERSON>, ExpressionWrapper, F, Q, QuerySet
from django.forms import model_to_dict
from group.models import GroupUser
from mission.models import MissionWorkspace
from mission.services.mission_recommendation import MissionRecommendationService
from myaccount.application.services.myaccount_service import MyAccountService
from pulse.models import Channel, Pulse, PulseChannel
from pulse.services.channel_recommendation import ChannelRecommendationService
from pulse.services.channel_service import ChannelService
from pulse.services.pulse_recommendation import PulseRecommendationService
from rest_clients.kontent import Konte<PERSON><PERSON><PERSON>
from user_activity.models import LearnContentActivity, MissionEnrollment
from user_activity.models.mission_enrollment import ENR<PERSON>LED, STARTED
from user_activity.services import UserService
from user_activity.tasks.notifications import notify_mission_enrollment_is_expiring
from utils.email_service.notification import notify_users
from utils.task_transaction import task_transaction

DAYS_REMAINING_TO_NOTIFY_ENROLLMENT_EXPIRING = [5, 4, 3, 2, 1, 0]
LIMIT_EXPIRED_DAYS_TO_NOTIFY_ENROLLMENT_EXPIRED = 3
NEW_MISSIONS_COUNT = 3


@shared_task(ignore_result=True)
def notify_users_with_mission_enrollment_expiring():
    with task_transaction("notify_users_with_mission_enrollment_expiring"):
        for days_remaining in DAYS_REMAINING_TO_NOTIFY_ENROLLMENT_EXPIRING:
            enrollments_expiring = fetchall_mission_enrollment_expiring(days_remaining)
            for enrollment in enrollments_expiring:
                notify_mission_enrollment_is_expiring(enrollment.id, days_remaining)
                __send_email_for_mission_enrollment_expiring(enrollment, days_remaining)


def fetchall_mission_enrollment_expiring(days_remaining: int):
    today_date = date.today()
    expiration_date = today_date + timedelta(days=days_remaining)
    expiration_datetime = datetime.combine(expiration_date, datetime.max.time())
    status_to_notify = [STARTED, ENROLLED]

    enrollments_expiring = MissionEnrollment.objects.filter(
        Q(required=True, goal_date__lte=expiration_datetime, status__in=status_to_notify)
    )
    enrollments_expiring = enrollments_expiring.annotate(
        days_r=ExpressionWrapper(F("goal_date") - today_date, output_field=DurationField())
    )
    enrollments_expiring = enrollments_expiring.filter(
        days_r__gte=timedelta(days=days_remaining), days_r__lte=timedelta(days=days_remaining + 1)
    )
    return enrollments_expiring


def __send_email_for_mission_enrollment_expiring(enrollment: MissionEnrollment, days_remaining: int):
    email_data = {
        "user_name": enrollment.user.name,
        "mission_name": enrollment.mission.name,
        "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
            enrollment.workspace.hash_id, enrollment.mission_id
        ),
        "mission_vertical_cover_image": enrollment.mission.thumb_image,
        "days_to_expire": days_remaining,
        "enrollment_goal_date": enrollment.goal_date,
        "enrollment_created_date": enrollment.created_date.date(),
        "workspace_name": enrollment.workspace.name,
    }
    user_data = model_to_dict(enrollment.user)
    return notify_users.delay(email_data, "mission_enrollment_expiring", enrollment.workspace_id, [user_data])


# @shared_task(ignore_result=True)
# def notify_new_missions() -> None:
#     """
#     Send notifications recommending recently created missions
#     for all active workspace users. Only send for the workspaces
#     with new missions created in the last settings.MISSION_OLDER_DAYS
#     """
#     with task_transaction(notify_new_missions.__name__):
#         workspaces = __get_workspaces_with_new_missions()
#         for workspace in workspaces:
#             notify_new_missions_by_workspace.delay(workspace.id)


@shared_task(ignore_result=True, rate_limit=NOTIFY_NEW_MISSIONS_BY_WORKSPACE_TASK_RATE)
def notify_new_missions_by_workspace(workspace_id: str) -> None:
    with task_transaction(notify_new_missions_by_workspace.__name__, MyAccountService) as service:
        service: MyAccountService
        workspace = Workspace.objects.get(id=workspace_id)
        users = __fetchall_workspace_users(workspace, service)

        for user in users:
            notify_new_missions_by_user.delay(user.id, workspace_id)


@shared_task(ignore_result=True, rate_limit=NOTIFY_NEW_MISSIONS_BY_USER_TASK_RATE, acks_late=False)
def notify_new_missions_by_user(user_id: str, workspace_id: str) -> None:
    with task_transaction(notify_new_missions_by_workspace.__name__, MissionRecommendationService) as service:
        service: MissionRecommendationService
        user = User.objects.get(id=user_id)
        workspace = Workspace.objects.get(id=workspace_id)

        mission_recommendations = service.get_recommendations(user.id, workspace.id, user.language).order_by(
            "-created_date"
        )[:NEW_MISSIONS_COUNT]

        __send_email_for_mission_recommendations(workspace, mission_recommendations, user)


def __fetchall_workspace_users(workspace: Workspace, myaccount_service: MyAccountService) -> QuerySet:
    return myaccount_service.list_all_consumer_users(workspace.id)


def __send_email_for_mission_recommendations(workspace: Workspace, missions: QuerySet, user: User):
    user_data = model_to_dict(user)
    if len(missions) != NEW_MISSIONS_COUNT:
        return

    email_data = {"user_name": user.name, "workspace_name": workspace.name}
    for pos, mission in enumerate(missions):
        pos += 1
        email_data.update({f"mission_{pos}_name": mission.name})
        email_data.update({f"mission_{pos}_description": mission.description})
        email_data.update(
            {
                f"mission_{pos}_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
                    workspace.hash_id, mission.id
                )
            }
        )

    notify_users.delay(
        email_data=email_data, message_key="new_missions", workspace_id=workspace.id, users_receivers=[user_data]
    )


def __get_workspaces_with_new_missions():
    today_date = date.today()
    min_created_date = today_date - timedelta(days=settings.MISSION_OLDER_DAYS)

    workspace_ids_with_new_missions = MissionWorkspace.objects.filter(created_date__gte=min_created_date).values_list(
        "workspace_id", flat=True
    )
    workspaces = Workspace.objects.filter(id__in=workspace_ids_with_new_missions, status=True)
    return workspaces


@shared_task(ignore_result=True)
def notify_users_with_expired_mission_enrollment() -> None:
    with task_transaction("notify_users_with_mission_enrollment_expired"):
        expired_enrollments = __fetchall_expired_mission_enrollments()
        logger = DiscordWebhookLogger()
        for enrollment in expired_enrollments:
            try:
                email_data = __build_email_data_for_expired_mission_enrollment(enrollment)
                user_data = model_to_dict(enrollment.user)
                notify_users.delay(
                    email_data=email_data,
                    message_key="mission_enrollment_expired",
                    workspace_id=enrollment.workspace_id,
                    users_receivers=[user_data],
                )
            except Exception as error:
                logger.emit_short_message(notify_users_with_expired_mission_enrollment.__name__, error)


def __fetchall_expired_mission_enrollments() -> QuerySet:
    min_expiration_date = date.today() - timedelta(days=LIMIT_EXPIRED_DAYS_TO_NOTIFY_ENROLLMENT_EXPIRED)
    min_expiration_datetime = datetime.combine(min_expiration_date, datetime.min.time())
    return MissionEnrollment.objects.filter(status="EXPIRED", goal_date__gte=min_expiration_datetime)


def __build_email_data_for_expired_mission_enrollment(enrollment: MissionEnrollment) -> dict:
    expired_days = (datetime.today().date() - enrollment.goal_date).days
    email_data = {
        "user_name": enrollment.user.name,
        "mission_name": enrollment.mission.name,
        "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format(
            enrollment.workspace.hash_id, enrollment.mission_id
        ),
        "mission_vertical_cover_image": enrollment.mission.thumb_image,
        "expired_days": expired_days,
        "enrollment_goal_date": enrollment.goal_date,
        "enrollment_created_date": enrollment.created_date.date(),
        "workspace_logo": enrollment.workspace.logo_url,
    }
    return email_data


@shared_task(ignore_result=True)
def notify_enrolled_in_group_missions():
    with task_transaction("notify_enrolled_in_group_missions"):
        min_user_group_created_date = datetime.now() - timedelta(
            minutes=settings.NOTIFY_ENROLLED_IN_GROUP_MISSIONS_INTERVAL_MINUTES
        )
        new_users_group = GroupUser.objects.filter(created_date__gte=min_user_group_created_date).all()

        for user_group in new_users_group:
            enrollments = __fetchall_user_group_mission_enrollments(min_user_group_created_date, user_group)
            if not enrollments:
                continue
            email_data = __build_email_data_for_enrolled_in_group_missions(enrollments)
            user_data = model_to_dict(user_group.user)
            notify_users.delay(
                email_data=email_data,
                message_key="enrolled_in_group_missions",
                workspace_id=user_group.group.workspace_id,
                users_receivers=[user_data],
            )


def __fetchall_user_group_mission_enrollments(min_user_group_created_date: datetime, group_user: GroupUser):
    group_missions = group_user.group.groupmission_set.filter().values_list("mission_id", flat=True)
    enrollments = MissionEnrollment.objects.filter(
        user=group_user.user, mission_id__in=group_missions, created_date__gte=min_user_group_created_date
    ).all()
    return enrollments


def __build_email_data_for_enrolled_in_group_missions(enrollments: QuerySet):
    enrollment = enrollments.first()
    user_name = enrollment.user.name
    workspace_logo = enrollment.workspace.logo_url
    missions_data = []
    for enrollment in enrollments:
        if enrollment.required:
            enrollment_goal_date = enrollment.goal_date
        else:
            enrollment_goal_date = None
        missions_data.append({"name": enrollment.mission.name, "enrollment_goal_date": enrollment_goal_date})

    email_data = {
        "user_name": user_name,
        "missions": missions_data,
        "workspace_logo": workspace_logo,
        "mission_vertical_cover_image": enrollment.mission.thumb_image,
    }
    return email_data


# @shared_task(ignore_result=True)
# def notify_monthly_user_activities_summary(month: int = None, year: int = None) -> None:
#     """
#     Send monthly activities summary for all users. Perform this task
#     only the first day of the successor month of the desired month
#     """
#     with task_transaction("notify_monthly_user_activities_summary", UserService) as user_service:
#         date_range = build_datetime_range(month, year)
#         workspaces = Workspace.objects.filter(status=True).all()
#         logger = DiscordWebhookLogger()
#
#         for workspace in workspaces:
#             users_with_activities = get_users_with_enrollment_activities(date_range, workspace.id)
#
#             for user in users_with_activities:
#                 try:
#                     user_data = model_to_dict(user)
#                     email_data = __build_email_data_for_monthly_user_activities_summary(
#                         user_service, user, workspace, date_range
#                     )
#                     notify_users.delay(email_data, "monthly_user_summary", workspace.id, [user_data])
#                 except Exception as error:
#                     logger.emit_short_message("notify_monthly_user_activities_summary", error)


def build_datetime_range(month: int = None, year: int = None) -> tuple:
    """
    Build a datetime range (min, max) of a specific month
    month_default: month preceding the current
    """
    if not month or not year:
        month = datetime.today().month.numerator
        year = datetime.today().year.numerator
        if month == 1:
            year = year - 1
            month = 12
        else:
            month = month - 1
    else:
        month = month if month else datetime.today().month.numerator
        year = year if year else datetime.today().year.numerator

    first_month_datetime = datetime(year=year, month=month, day=1)
    last_month_datetime = datetime(year=year, month=month, day=calendar.monthrange(year=year, month=month)[-1])
    last_month_datetime = datetime.combine(last_month_datetime, datetime.max.time())
    return first_month_datetime, last_month_datetime


def get_users_with_enrollment_activities(date_range: tuple, workspace_id: str) -> QuerySet:
    users_with_activities_ids = (
        LearnContentActivity.objects.filter(
            created_date__gte=date_range[0],
            created_date__lte=date_range[1],
            mission_enrollment__workspace_id=workspace_id,
        )
        .distinct("user_id")
        .values_list("user_id", flat=True)
    )
    return User.objects.filter(id__in=users_with_activities_ids)


def __build_email_data_for_monthly_user_activities_summary(
    user_service: UserService, user: User, workspace: Workspace, date_range: tuple
) -> dict:
    mission_enrollments_resume = user_service.get_user_mission_enrollments_resume(user.id, workspace.id, date_range)
    trail_enrollments_resume = user_service.get_user_trail_enrollments_resume(user.id, workspace.id, date_range)
    email_data = {
        "user_name": user.name,
        "mission_enrollment": {
            "enrolled": mission_enrollments_resume.get("enrollments_enrolled"),
            "started": mission_enrollments_resume.get("enrollments_started"),
            "completed": mission_enrollments_resume.get("enrollments_completed"),
            "give_up": mission_enrollments_resume.get("enrollments_give_up"),
        },
        "trail_enrollment": {
            "enrolled": trail_enrollments_resume.get("enrollments_enrolled"),
            "started": trail_enrollments_resume.get("enrollments_started"),
            "completed": trail_enrollments_resume.get("enrollments_completed"),
            "give_up": trail_enrollments_resume.get("enrollments_give_up"),
        },
        "workspace_logo": workspace.logo_url,
    }
    return email_data


# @shared_task(ignore_result=True, acks_late=False)
# def notify_new_pulses_and_channels():
#     """Send notifications for all users recommending recently created pulses and channels"""
#     with task_transaction(notify_new_pulses_and_channels.__name__):
#         workspaces = __workspaces_with_new_pulses()
#         for workspace in workspaces:
#             notify_new_pulses_and_channels_by_workspace.delay(workspace.id)


@shared_task(ignore_result=True, acks_late=False)
def notify_new_pulses_and_channels_by_workspace(workspace_id: str):
    with task_transaction(notify_new_pulses_and_channels_by_workspace.__name__, MyAccountService) as service:
        service: MyAccountService
        workspace = Workspace.objects.get(id=workspace_id)
        users = __fetchall_workspace_users(workspace, service)
        for user in users:
            notify_new_pulses_and_channels_by_user.delay(user.id, workspace.id)


@shared_task(ignore_result=True, acks_late=False, rate_limit=NOTIFY_NEW_PULSES_AND_CHANNELS_BY_USERS_TASK_RATE)
def notify_new_pulses_and_channels_by_user(user_id: str, workspace_id: str):
    with task_transaction(notify_new_pulses_and_channels_by_user.__name__, PulseRecommendationService) as service:
        service: PulseRecommendationService
        channel_service = MagicMock(spec=ChannelService)
        channel_recommendation_service = ChannelRecommendationService(channel_service)
        user = User.objects.get(id=user_id)
        workspace = Workspace.objects.get(id=workspace_id)

        pulses = service.user_pulses_priority(user_id, workspace_id, user.language)[:3]
        channels = channel_recommendation_service.get_recommendations(user.id, workspace_id, user.language).order_by(
            "-created_date"
        )[:3]
        if not pulses or not channels:
            return

        email_data = __build_email_data_for_new_pulses_and_channels(workspace, user.name, pulses, channels)
        user_data = model_to_dict(user)
        notify_users.delay(email_data, "new_pulses_and_channels", workspace_id, [user_data])


def __workspaces_with_new_pulses():
    today_date = date.today()
    min_created_date = today_date - timedelta(days=settings.PULSE_OLDER_DAYS)
    new_pulse_ids = Pulse.objects.filter(created_date__gte=min_created_date).values_list("id", flat=True)
    workspace_ids = PulseChannel.objects.filter(pulse_id__in=new_pulse_ids).values_list(
        "channel__workspace_id", flat=True
    )

    return Workspace.objects.filter(id__in=workspace_ids, status=True)


def __build_email_data_for_new_pulses_and_channels(
    workspace: Workspace, user_name: str, pulses: [Pulse], channels: [Channel]
):
    pulses_data = []
    channels_data = []

    for pulse in pulses:
        content = KontentClient().get_learn_content(
            content_id=pulse.learn_content_uuid, token=settings.KEEPS_SECRET_TOKEN_INTEGRATION
        )
        try:
            content_type_image = content.get("content_type").get("image")
        except AttributeError:
            content_type_image = None
        pulses_data.append(
            {
                "name": pulse.name,
                "content_type_image": content_type_image,
                "link": settings.KONQUEST_WEB_PULSE_DETAIL_URL.format(workspace.hash_id, pulse.id),
            }
        )

    for channel in channels:
        channels_data.append(
            {
                "name": channel.name,
                "link": settings.KONQUEST_WEB_CHANNEL_DETAIL_URL.format(workspace.hash_id, channel.id),
            }
        )

    email_data = {
        "user_name": user_name,
        "workspace_logo": workspace.logo_url,
        "pulses": pulses_data,
        "channels": channels_data,
    }

    return email_data
