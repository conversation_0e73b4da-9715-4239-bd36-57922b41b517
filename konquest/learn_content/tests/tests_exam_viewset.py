import uuid
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from account.models import User, Workspace
from authentication.keeps_permissions import ADMIN
from group.models import Group, GroupChannel
from learn_content.models import Answer, Exam, Question
from mission.models import Mission, MissionStage, MissionWorkspace
from pulse.models import Channel, ChannelType, Pulse, PulseChannel
from pulse.models.channel_type import ChannelTypeEnum

GET_PRIORITY_USER_ROLE = "authentication.keeps_permissions.KeepsBasePermission.get_priority_user_role_by_token"


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info", return_value={})
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class ExamViewSetTestCase(TestCase):
    fixtures = ["user"]

    def setUp(self):
        self.client = APIClient()

        self.user_1 = mommy.make(User, id=uuid.uuid4())
        self.user_2 = mommy.make(User, id=uuid.uuid4())

        self.workspace_1 = mommy.make(Workspace, id=uuid.uuid4())
        self.workspace_2 = mommy.make(Workspace, id=uuid.uuid4())

        self.client.force_authenticate(user={"sub": self.user_1.id, "client_id": str(self.workspace_1.id)})

        self.mission_1 = mommy.make(Mission, id=uuid.uuid4())
        self.mission_2 = mommy.make(Mission, id=uuid.uuid4())

        self.mission_workspace_1 = mommy.make(
            MissionWorkspace, id=uuid.uuid4(), mission=self.mission_1, workspace=self.workspace_1
        )
        self.mission_workspace_2 = mommy.make(
            MissionWorkspace, id=uuid.uuid4(), mission=self.mission_2, workspace=self.workspace_2
        )

        self.stage_1 = mommy.make(MissionStage, id=uuid.uuid4(), mission=self.mission_1)
        self.stage_2 = mommy.make(MissionStage, id=uuid.uuid4(), mission=self.mission_2)

        self.exam_1 = mommy.make(Exam, id=uuid.uuid4(), stage=self.stage_1)
        self.exam_2 = mommy.make(Exam, id=uuid.uuid4(), stage=self.stage_2)

        self.exam_has_question_1 = mommy.make(Question, id=uuid.uuid4(), exam=self.exam_1)
        self.exam_has_question_2 = mommy.make(Question, id=uuid.uuid4(), exam=self.exam_2)

        self.answer_1 = mommy.make(
            Answer, id=uuid.uuid4(), user=self.user_1, exam_has_question=self.exam_has_question_1
        )
        self.answer_2 = mommy.make(
            Answer, id=uuid.uuid4(), user=self.user_2, exam_has_question=self.exam_has_question_2
        )

        self.headers = {"HTTP_X_CLIENT": str(self.workspace_1.id)}
        self.url = reverse("exams-list")

        self.channel_type_close_for_workspace = mommy.make(ChannelType, id=ChannelTypeEnum.CLOSE_FOR_COMPANY.value)
        self.user_creator_id = "489fb596-478f-40b5-b717-4931f20e4cc6"
        self.user_consumer_id = "244ef26a-eb00-4f80-9800-5e976e234452"

    def test_exams_list(self, mock_role, mock_return_roles):
        response = self.client.get(self.url, **self.headers, format="json").json()

        self.assertEqual(len(response["results"]), 1)

    def test_exam_random_questions_list(self, mock_role, mock_return_roles):
        mommy.make(Question, exam=self.exam_1)
        mommy.make(Question, exam=self.exam_1)
        mommy.make(Question, exam=self.exam_1)
        mommy.make(Question, exam=self.exam_1)
        mommy.make(Question, exam=self.exam_1)
        mommy.make(Question, exam=self.exam_1)
        response = self.client.get(self.url, **self.headers, format="json").json()
        results_1 = response["results"][0]["questions"]
        response = self.client.get(self.url, **self.headers, format="json").json()
        results_2 = response["results"][0]["questions"]

        self.assertNotEqual(results_1, results_2)

    def test_exams_list_filter_by_stage(self, mock_role, mock_return_roles):
        response = self.client.get(self.url + "?stage=" + str(self.stage_1.id), **self.headers, format="json").json()

        self.assertEqual(len(response["results"]), 1)

    def test_exams_list_filter_by_mission(self, mock_role, mock_return_roles):
        response = self.client.get(
            self.url + "?stage__mission=" + str(self.mission_1.id), **self.headers, format="json"
        ).json()

        self.assertEqual(len(response["results"]), 1)

    def test_exams_get_success(self, mock_role, mock_return_roles):
        response = self.client.get(reverse("exams-detail", args=[str(self.exam_1.id)]), **self.headers, format="json")

        response_json = response.json()
        self.assertEqual(response_json["id"], str(self.exam_1.id))
        self.assertEqual(response.status_code, 200)

    def test_exams_get_not_found(self, mock_role, mock_return_roles):
        response = self.client.get(reverse("exams-detail", args=[str(uuid.uuid4())]), **self.headers, format="json")
        response_json = response.json()

        self.assertEqual(response_json["detail"], "No Exam matches the given query.")
        self.assertEqual(response.status_code, 404)

    def test_exams_post_success(self, mock_return, mock_return_roles):
        data = {
            "title": "Test",
            "stage": str(self.stage_1.id),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)

    def test_exams_post_required_field(self, mock_return, mock_return_roles):
        data = {}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response_json["title"], ["This field is required."])
        self.assertEqual(response.status_code, 400)

    def test_exams_post_invalid_foreign_key(self, mock_return, mock_return_roles):
        data = {
            "title": "Test",
            "stage": str(uuid.uuid4()),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertIsNotNone(response_json.get("stage"))
        self.assertEqual(response.status_code, 400)

    def test_exams_patch_success(self, mock_return, mock_return_roles):
        data = {
            "stage": str(self.stage_1.id),
        }

        response = self.client.patch(
            reverse("exams-detail", args=[str(self.exam_1.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["stage"], data["stage"])
        self.assertEqual(response.status_code, 200)

    def test_exams_put_success(self, mock_return, mock_return_roles):
        data = {
            "title": "Test",
            "stage": str(self.stage_1.id),
        }

        response = self.client.put(
            reverse("exams-detail", args=[str(self.exam_1.id)]), **self.headers, data=data, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["stage"], data["stage"])
        self.assertEqual(response.status_code, 200)

    def test_exams_delete_not_found(self, mock_return, mock_return_roles):
        response = self.client.delete(reverse("exams-detail", args=[str(uuid.uuid4())]), **self.headers, format="json")
        response_json = response.json()

        self.assertEqual(response_json["detail"], "No Exam matches the given query.")
        self.assertEqual(response.status_code, 404)

    @mock.patch(GET_PRIORITY_USER_ROLE)
    def test_exams_get_success_channel_close(self, get_priority_user_role, check_role, get_token_info):
        self.headers = {"HTTP_X_CLIENT": self.workspace_1.id}
        self.client.force_authenticate(user={"sub": self.user_consumer_id, "client_id": self.workspace_1.id})
        get_priority_user_role.return_value = ADMIN

        closed_channel = mommy.make(
            Channel,
            id=uuid.uuid4(),
            name="Keeps close for workspace channel",
            is_active=True,
            channel_type=self.channel_type_close_for_workspace,
            user_creator_id=self.user_creator_id,
            workspace_id=self.workspace_1.id,
        )
        closed_pulse = mommy.make(Pulse, name="Pulse in Channel Close for Workspace")
        mommy.make(PulseChannel, channel=closed_channel, pulse=closed_pulse)
        group = mommy.make(Group, workspace_id=self.workspace_1.id)
        mommy.make(GroupChannel, group=group, channel=closed_channel)

        exam_close_channel = mommy.make(Exam, id=uuid.uuid4(), channel_id=closed_channel.id, pulse=closed_pulse)

        response = self.client.get(
            reverse("exams-detail", args=[str(exam_close_channel.id)]), **self.headers, format="json"
        )
        self.assertEqual(response.status_code, 200)
