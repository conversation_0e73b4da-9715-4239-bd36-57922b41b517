import uuid

from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from learn_content.models import Exam, Question, QuestionOption


class AnswerViewsetTestCase(TestCase):
    fixtures = ["user", "workspace", "mission_type", "mission_category", "mission"]

    def setUp(self):
        self.client = APIClient()
        user_id = "489fb596-478f-40b5-b717-4931f20e4cc6"
        workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"
        stage_id = "4b5880b1-ddaf-4b4f-b72b-1aa0cf49fcf1"

        self.client.force_authenticate(user={"sub": user_id})
        self.exam = mommy.make(Exam, id=uuid.uuid4(), stage_id=stage_id)

        self.question_one_correct = mommy.make(
            Question, id=uuid.uuid4(), exam=self.exam, question_type="correct_choices"
        )
        self.option_true = mommy.make(
            QuestionOption, id=uuid.uuid4(), question=self.question_one_correct, correct_answer=True
        )
        self.option_false = mommy.make(
            QuestionOption, id=uuid.uuid4(), question=self.question_one_correct, correct_answer=False
        )

        self.question_two_correct = mommy.make(
            Question, id=uuid.uuid4(), exam=self.exam, question_type="correct_choices"
        )
        self.option_true_1 = mommy.make(
            QuestionOption, id=uuid.uuid4(), question=self.question_two_correct, correct_answer=True
        )

        self.option_true_2 = mommy.make(
            QuestionOption, id=uuid.uuid4(), question=self.question_two_correct, correct_answer=True
        )

        self.option_false = mommy.make(
            QuestionOption, id=uuid.uuid4(), question=self.question_two_correct, correct_answer=False
        )

        self.headers = {"HTTP_X_CLIENT": str(workspace_id)}
        self.url_batch = reverse("answers-batch", args=[str(self.exam.id)])
        self.url = reverse("answers", args=[str(self.exam.id)])

    def test_answer_post_batch_success_true(self):
        """
        save user answer and return true (correct answer)
        """
        data = {"questions": [{"id": str(self.question_one_correct.id), "options": [str(self.option_true.id)]}]}

        response = self.client.post(self.url_batch, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data[0]["is_ok"], True)

    def test_answer_post_batch_success_false(self):
        """
        save user answer and return false (wrong answer)
        """
        data = {"questions": [{"id": str(self.question_one_correct.id), "options": [str(self.option_false.id)]}]}

        response = self.client.post(self.url_batch, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data[0]["is_ok"], False)

    def test_answer_post_success_true(self):
        """
        save user answer and return true (correct answer)
        """
        data = {"question": str(self.question_one_correct.id), "options": [str(self.option_true.id)]}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["is_ok"], True)

    def test_answer_post_success_false(self):
        """
        save user answer and return true (correct answer)
        """
        data = {"question": str(self.question_one_correct.id), "options": [str(self.option_false.id)]}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["is_ok"], False)

    def test_answer_post_success_true_two_options(self):
        """
        save user answer and return true (correct answer) when question have 2 correct options
        """
        data = {
            "question": str(self.question_two_correct.id),
            "options": [str(self.option_true_1.id), str(self.option_true_2.id)],
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["is_ok"], True)

    def test_answer_post_success_false_two_options(self):
        """
        save user answer and return false (wrong answer) when question have 2 correct options
        """

        # selected one true and other false
        data = {
            "question": str(self.question_two_correct.id),
            "options": [str(self.option_true_1.id), str(self.option_false.id)],
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["is_ok"], False)

        # just selected the false option
        data = {"question": str(self.question_two_correct.id), "options": [str(self.option_false.id)]}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["is_ok"], False)

        # selected just one correct option
        data = {"question": str(self.question_two_correct.id), "options": [str(self.option_true_1.id)]}

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data["is_ok"], False)

    def test_answer_post_error_no_options_empty(self):
        data = {"question": str(self.question_two_correct.id), "options": []}
        error_message = {"i18n": "set_options_value", "detail": "Need inform answer option value", "status_code": 400}
        response = self.client.post(self.url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, error_message)

    def test_answer_post_error_no_options_set(self):
        # selected one true and other false
        data = {
            "question": str(self.question_two_correct.id),
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        self.assertEqual(response.status_code, 400)
