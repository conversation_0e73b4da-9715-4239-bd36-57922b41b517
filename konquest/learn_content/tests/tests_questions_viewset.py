from unittest import mock

from django.test import TestCase
from django.utils.translation import gettext as _
from model_mommy import mommy
from rest_framework.reverse import reverse
from rest_framework.test import APIClient

from learn_content.models import Exam, Question, QuestionOption


@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class ExamViewSetTestCase(TestCase):
    fixtures = ["user", "workspace"]

    def setUp(self):
        self.client = APIClient()
        self.exam = mommy.make(Exam)
        self.user_id = "489fb596-478f-40b5-b717-4931f20e4cc6"
        self.workspace_id = "e76b5082-f4fe-4f41-be79-1977840e16a8"

        self.url_detail_name = "questions-detail"

        self.headers = {"HTTP_X_CLIENT": str(self.workspace_id)}
        self.client.force_authenticate(user={"sub": self.user_id, "client_id": str(self.workspace_id)})
        self.url = reverse("questions-list", args=[str(self.exam.id)])

    def test_create_question(self, check_role):
        data = {
            "exam_question": "Question Name",
            "options": [
                {"correct_answer": False, "option": "Option 1"},
                {"correct_answer": True, "option": "Option 2"},
            ],
            "points": "5",
            "question_type": "correct_choices",
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")

        self.assertEqual(response.status_code, 201)
        self.assertEqual(len(response.data.get("options")), 2)

        total_correct_answers = sum(option["correct_answer"] for option in response.data["options"])
        self.assertEqual(total_correct_answers, 1)

    def test_delete_question(self, check_role):
        question = mommy.make(Question, exam=self.exam)
        url = reverse(self.url_detail_name, args=[str(self.exam.id), str(question.id)])

        response = self.client.delete(url, **self.headers, format="json")
        self.assertEqual(response.status_code, 204)
        self.assertFalse(Question.objects.filter(id=question.id).exists())

    def test_update_question(self, check_role):
        question = mommy.make(Question, exam=self.exam)
        url = reverse(self.url_detail_name, args=[str(self.exam.id), str(question.id)])
        payload = {
            "exam_question": "New Name",
        }

        response = self.client.patch(url, **self.headers, data=payload, format="json")
        self.assertEqual(response.status_code, 200)
        question.refresh_from_db()
        question.exam_question = payload.get("exam_question")
        self.assertEqual(response.data.get("exam_question"), payload.get("exam_question"))

    def test_payload_with_invalid_format_error(self, check_role):
        question = mommy.make(Question, exam=self.exam)
        url = reverse(self.url_detail_name, args=[str(self.exam.id), str(question.id)])
        payload = {
            "exam_question": {},
        }

        response = self.client.patch(url, **self.headers, data=payload, format="json")
        self.assertEqual(response.status_code, 400)

    def test_update_correct_answers(self, check_role):
        question = mommy.make(Question, exam=self.exam)
        option_1 = mommy.make(QuestionOption, question=question, correct_answer=True)
        option_2 = mommy.make(QuestionOption, question=question, correct_answer=False)
        url = reverse(self.url_detail_name, args=[str(self.exam.id), str(question.id)])

        payload = {
            "options": [
                {"id": option_1.id, "correct_answer": False},
                {"id": option_2.id, "correct_answer": True},
            ]
        }

        response = self.client.patch(url, **self.headers, data=payload, format="json")
        print(response.data)
        self.assertEqual(response.status_code, 200)
        option_1.refresh_from_db()
        option_2.refresh_from_db()

        self.assertFalse(option_1.correct_answer)
        self.assertTrue(option_2.correct_answer)

    def test_less_a_correct_option_error(self, check_role):
        question = mommy.make(Question, exam=self.exam, question_type="correct_choices")
        option_1 = mommy.make(QuestionOption, question=question, correct_answer=True)
        option_2 = mommy.make(QuestionOption, question=question, correct_answer=False)
        url = reverse(self.url_detail_name, args=[str(self.exam.id), str(question.id)])

        payload = {
            "options": [
                {"id": option_1.id, "correct_answer": False},
                {"id": option_2.id, "correct_answer": False},
            ]
        }

        response = self.client.patch(url, **self.headers, data=payload, format="json")
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data.get("detail"), [_("question_must_have_one_or_more_correct_options")])
