from django.urls import path

from constants import URL_EXAM_ID
from learn_content.views.answer_viewset import AnswerBatchViewSet, AnswerViewSet
from learn_content.views.exam_viewset import ExamViewSet
from learn_content.views.question_viewset import QuestionViewSet

_READ_ONLY = {"get": "list"}
_LIST = {"get": "list", "post": "create"}
_DETAIL = {"get": "retrieve", "put": "update", "patch": "partial_update", "delete": "destroy"}
_ONLY_DELETE = {"delete": "destroy"}
_SAVE_ONLY = {"post": "create"}


urlpatterns = [
    path("", ExamViewSet.as_view(_LIST), name="exams-list"),
    path("/<uuid:pk>", ExamViewSet.as_view(_DETAIL), name="exams-detail"),
    path("/<uuid:exam_id>/questions", QuestionViewSet.as_view(_LIST), name="questions-list"),
    path("/<uuid:exam_id>/questions/<uuid:pk>", QuestionViewSet.as_view(_DETAIL), name="questions-detail"),
    path("/<uuid:exam_id>/answers/batch", AnswerBatchViewSet.as_view(), name="answers-batch"),
    path(f"/<uuid:{URL_EXAM_ID}>/answers", AnswerViewSet.as_view(_LIST), name="answers"),
]
