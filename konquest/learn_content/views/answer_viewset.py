from django.db import transaction
from rest_framework import status, viewsets
from rest_framework.response import Response
from rest_framework.views import APIView

from authentication.keeps_permissions import IsAuthenticatedWithoutXClient
from constants import URL_EXAM_ID
from learn_content.models import Answer
from learn_content.serializers.answer_serializer import AnswerSerializer
from learn_content.services.answer_service import AnswerService
from utils.utils import swagger_safe_queryset


class AnswerViewSet(viewsets.ModelViewSet):
    filterset_fields = ("user__id", "enrollment__id")
    search_fields = ("user__id", "enrollment__id")

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._service = AnswerService()

    @swagger_safe_queryset(Answer)
    def get_queryset(self):
        exam_id = self.kwargs[URL_EXAM_ID]
        user_id = self.request.user.get("sub")
        return Answer.objects.filter(exam_has_question__exam_id=exam_id).filter(user_id=user_id)

    def get_serializer_class(self):
        return AnswerSerializer

    def create(self, request, *args, **kwargs):
        user_id = self.request.user.get("sub")
        question_id = request.data.get("question")
        user_answer = self._service.save_answer(request.data, user_id, question_id)
        serializer = self.get_serializer(instance=user_answer)
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class AnswerBatchViewSet(APIView):
    permission_classes = (IsAuthenticatedWithoutXClient,)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._service = AnswerService()

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        user_answers = self._service.save_batch_answer(request.data, request.user.get("sub"))
        data = [AnswerSerializer(answer).data for answer in user_answers]
        return Response(data, status=status.HTTP_201_CREATED)

    def put(self, request, *args, **kwargs):
        return Response({"message": "Method is not authorize"}, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, *args, **kwargs):
        return Response({"message": "Method is not authorize"}, status=status.HTTP_400_BAD_REQUEST)
