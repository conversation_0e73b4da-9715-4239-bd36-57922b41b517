from django_filters.rest_framework import Django<PERSON>ilterBackend
from injector import Provider, inject
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter

from authentication.keeps_permissions import ALL_PERMISSIONS, KeepsBasePermission
from learn_content.models import Exam
from learn_content.serializers.exam_serializer import ExamInputSerializer, ExamOutputSerializer
from learn_content.services.exam_service import ExamService
from utils.utils import swagger_safe_queryset


class ExamViewSet(viewsets.ModelViewSet):
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = ("stage", "stage__mission", "stage__order")
    search_fields = ("stage__name", "stage__description", "stage__mission__name")
    ordering_fields = (
        "created_date",
        "updated_date",
    )
    ordering = ("updated_date",)

    permission_classes = ALL_PERMISSIONS

    @inject
    def __init__(self, exam_service: ExamService = Provider[ExamService], **kwargs):
        super().__init__(**kwargs)
        self._service = exam_service
        self._kp_permission = KeepsBasePermission()

    @swagger_safe_queryset(Exam)
    def get_queryset(self):
        user_id = self.request.user.get("sub") if self.request.user else None
        workspace_uuid = self.request.user.get("client_id") if self.request.user else None
        token = self.request.META.get("HTTP_AUTHORIZATION")
        role = self._kp_permission.get_priority_user_role_by_token(token, workspace_uuid)
        return self._service.get_exam(workspace_uuid, user_id, role)

    def get_serializer_class(self):
        return ExamOutputSerializer if self.request.method == "GET" else ExamInputSerializer
