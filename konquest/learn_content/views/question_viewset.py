from django.core.exceptions import ObjectDoes<PERSON>otExist, ValidationError
from django_filters.rest_framework import DjangoFilterBackend
from injector import Provider, inject
from rest_framework import status, viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
from rest_framework.response import Response

from authentication.keeps_permissions import ALL_PERMISSIONS, KeepsBasePermission
from custom import KeepsBadRequestError
from custom.keeps_exception_handler import KeepsNotFoundError
from learn_content.models import Question
from learn_content.serializers.question_serializer import (
    QuestionForCreatorSerializer,
    QuestionListSerializer,
    QuestionPostSerializer,
    QuestionSerializer,
)
from learn_content.services.exam_service import ExamService
from pulse.services.channel_service import ChannelService
from utils.utils import swagger_safe_queryset


class QuestionViewSet(viewsets.ModelViewSet):
    """
    A viewset that provides the standard actions
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = ("question_type", "user_creator", "exam")
    search_fields = ("exam_question",)
    ordering_fields = (
        "created_date",
        "updated_date",
    )
    ordering = ("updated_date",)

    permission_classes = ALL_PERMISSIONS

    @inject
    def __init__(
        self,
        exam_service: ExamService = Provider[ExamService],
        channel_service: ChannelService = Provider[ChannelService],
        **kwargs,
    ):
        super().__init__(**kwargs)
        self._exam_service = exam_service
        self._service_channel = channel_service
        self._kp_permission = KeepsBasePermission()

    @swagger_safe_queryset(Question)
    def get_queryset(self):
        user_id = self.request.user.get("sub") if self.request.user else None
        workspace_id = self.request.user.get("client_id") if self.request.user else None
        exam_id = self.kwargs.get("exam_id")
        token = self.request.META.get("HTTP_AUTHORIZATION")
        role = self._kp_permission.get_priority_user_role_by_token(token, workspace_id)
        return self._exam_service.get_questions(workspace_id, user_id, role).filter(exam_id=exam_id)

    def get_serializer_class(self):
        return QuestionListSerializer if self.request.method == "GET" else QuestionSerializer

    def create(self, request, *args, **kwargs):
        serializer = QuestionPostSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        data["exam_id"] = self.kwargs.get("exam_id")
        data["user_creator_id"] = request.user["sub"]

        try:
            options = data.pop("options")
        except KeyError:
            options = None
        question = self._exam_service.save_question(data, options)

        return Response(QuestionForCreatorSerializer(question).data, status=status.HTTP_201_CREATED)

    def update(self, request, *args, **kwargs):
        question_id = kwargs.get("pk")
        data = request.data
        data["workspace"] = request.user["client_id"]

        try:
            question = self.get_queryset().get(id=question_id)
        except ObjectDoesNotExist as exc:
            raise KeepsNotFoundError(detail="Question not found", i18n="question_not_found") from exc

        serializer = QuestionPostSerializer(instance=question, data=data, partial=True)
        serializer.is_valid(raise_exception=True)

        try:
            options = data.pop("options")
        except KeyError:
            options = None

        try:
            question = self._exam_service.update_question(question_id, serializer.validated_data, options)
        except ValidationError as exc:
            raise KeepsBadRequestError(detail=exc.messages, i18n="validation_error") from exc

        return Response(QuestionForCreatorSerializer(question).data, status=status.HTTP_200_OK)
