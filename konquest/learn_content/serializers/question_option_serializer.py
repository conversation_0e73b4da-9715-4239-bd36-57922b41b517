from rest_framework import serializers

from learn_content.models import QuestionOption


class QuestionOptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuestionOption
        fields = ("id", "option", "correct_answer")


class QuestionOptionListSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuestionOption
        fields = ("id", "option", "correct_answer")
        depth = 1
