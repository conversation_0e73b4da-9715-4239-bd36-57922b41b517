from rest_framework import serializers

from learn_content.models import Question
from learn_content.serializers.question_option_serializer import QuestionOptionListSerializer, QuestionOptionSerializer


class QuestionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Question
        fields = "__all__"


class QuestionForCreatorSerializer(serializers.ModelSerializer):
    options = serializers.SerializerMethodField()

    @staticmethod
    def get_options(obj):
        question_options = obj.questionoption_set.all()
        return QuestionOptionSerializer(question_options, many=True).data

    class Meta:
        model = Question
        fields = "__all__"
        depth = 1


class QuestionListSerializer(serializers.ModelSerializer):
    options = serializers.SerializerMethodField()

    def get_options(self, obj):
        question_options = obj.questionoption_set.all()

        if self.context.get("request") and str(obj.user_creator_id) == self.context["request"].user.get("sub"):
            return QuestionOptionSerializer(question_options, many=True).data

        return QuestionOptionListSerializer(question_options, many=True).data

    class Meta:
        model = Question
        fields = "__all__"
        depth = 1


class QuestionPostSerializer(serializers.ModelSerializer):
    options = QuestionOptionSerializer(many=True)

    class Meta:
        model = Question
        fields = (
            "exam_question",
            "question_type",
            "points",
            "options",
        )
