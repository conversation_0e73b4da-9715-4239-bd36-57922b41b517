from rest_framework import serializers

from learn_content.models import Answer, Exam, Question, QuestionOption
from learn_content.serializers.question_serializer import QuestionPostSerializer


class AnswerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Answer
        fields = ("options", "is_ok", "enrollment", "created_date")


class OptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuestionOption
        fields = ("id", "option")


class QuestionSerializer(serializers.ModelSerializer):
    options = OptionSerializer(many=True)

    class Meta:
        model = Question
        fields = ("id", "exam_question", "question_type", "points", "created_date", "options", "count_correct_options")
        ref_name = "Exam Question Serializer"


class ExamOutputSerializer(serializers.ModelSerializer):
    questions = QuestionSerializer(many=True)

    class Meta:
        model = Exam
        fields = "__all__"


class ExamInputSerializer(serializers.ModelSerializer):
    class Meta:
        model = Exam
        fields = "__all__"


class ExamQuestionsSerializer(serializers.ModelSerializer):
    questions = QuestionPostSerializer(many=True)

    class Meta:
        model = Exam
        fields = "__all__"
