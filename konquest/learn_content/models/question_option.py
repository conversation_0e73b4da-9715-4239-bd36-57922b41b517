import uuid

from django.db import models

from learn_content.models.question import Question
from utils.models import BaseModel


class QuestionOption(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    question = models.ForeignKey(Question, verbose_name="Type", on_delete=models.CASCADE, null=False, blank=False)
    option = models.TextField(verbose_name="Option", null=False, blank=False)
    correct_answer = models.BooleanField(default=False)

    class Meta:
        app_label = "learn_content"
        verbose_name_plural = "Question Options"
        db_table = "question_option"
