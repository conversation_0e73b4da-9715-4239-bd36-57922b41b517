import uuid

from django.db import models

from mission.models.mission_stage import Mission<PERSON><PERSON>
from pulse.models import Pulse
from pulse.models.channel import Channel
from utils.models import BaseModel


class Exam(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(verbose_name="Title", max_length=100)

    stage = models.ForeignKey(MissionStage, verbose_name="Stage", on_delete=models.SET_NULL, null=True, blank=True)
    channel = models.ForeignKey(Channel, verbose_name="Channel", on_delete=models.SET_NULL, null=True, blank=True)
    pulse = models.ForeignKey(Pulse, verbose_name="Pulse", on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        verbose_name_plural = "Exam"
        db_table = "exam"

    @property
    def questions(self):
        return self.question_set.order_by("?").all()
