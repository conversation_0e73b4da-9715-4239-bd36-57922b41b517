import csv
import json
import logging
import os

import django
from django.db.models import Q

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from mission.models import LiveMission, LiveMissionDates, Mission, PresentialMission, PresentialMissionDates


def run():
    logging.info("start script")

    live_missions = LiveMission.objects.filter(mission__duration_time=None)
    for live_mission in live_missions:
        mission = live_mission.mission
        live_mission_dates = LiveMissionDates.objects.filter(live=live_mission)
        mission_duration_time = 0
        for live_mission_date in live_mission_dates:
            mission_duration_time += (live_mission_date.end_at - live_mission_date.start_at).seconds
        mission.duration_time = mission_duration_time
        mission.save()
    presential_missions = PresentialMission.objects.filter(mission__duration_time=None)
    for presential_mission in presential_missions:
        mission = presential_mission.mission
        presential_mission_dates = PresentialMissionDates.objects.filter(presential=presential_mission)
        mission_duration_time = 0
        for presential_mission_date in presential_mission_dates:
            mission_duration_time += (presential_mission_date.end_at - presential_mission_date.start_at).seconds
        mission.duration_time = mission_duration_time
        mission.save()

    logging.info("done")


if __name__ == "__main__":
    run()
