import os
import traceback

import django

import config

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from django.db import connections

from mission.models import MissionWorkspace
from user_activity.models import LearnContentActivity, MissionEnrollment, UserMissionContent, UserMissionStage

mission_enrollments = MissionEnrollment.objects.filter(company_id__isnull=True).all()

print("--- Normalize Mission Enrollment Script is Running ---\n")
print(f"   {mission_enrollments.count()} Mission Enrollments Without Workspace id:")
success_count = 0
keeps_token = config.settings.KEEPS_SECRET_TOKEN_INTEGRATION

RED = "\033[1;31m"
RESET = "\033[0;0m"
GREEN = "\033[0;32m"


def get_user_role_in_companies(user_id, _company_ids):
    sql = f"SELECT DISTINCT(user_role_company.company_id) FROM user_role_company WHERE user_role_company.user_id = '{str(user_id)}' AND user_role_company.company_id in ({str(_company_ids)})"

    with connections["account"].cursor() as cur:
        cur.execute(sql)
        result = cur.fetchall()

        return result


for enrollment in mission_enrollments:
    try:
        mission = enrollment.mission
        mission_companies = MissionWorkspace.objects.filter(mission=mission).all()

        # First, check if the mission is linked in more than one company
        if len(mission_companies) > 1:
            company_ids = (
                str([str(mission_company.company_id) for mission_company in mission_companies])
                .replace("[", "")
                .replace("]", "")
            )
            user_companies = get_user_role_in_companies(user_id=enrollment.user_consumer_1_id, _company_ids=company_ids)

            # Now, Check if the user is linked to more than one company, if not, save the only company he have role in the enrollment
            if len(user_companies) == 1:
                enrollment.company_id = user_companies[0][0]

            # If the user have role and many mission companies, print alert
            else:
                print("   " + RED + f"ALERT: User have role in many Mission Companies:")
                print("      " + f"Enrollment: id:{enrollment.id}")
                print("      " + f"User: id:{enrollment.user.id}, email:{enrollment.user.email}")
                print("      " + f"Mission: id:{enrollment.mission.id}\n")
                continue

        elif len(mission_companies) == 1:
            enrollment.company = mission_companies.first().company

        success_count += 1
        enrollment.save()
    except Exception as e:
        print(RED + f"One Exception Happened: {str(e)} {str(traceback.format_exc())}" + RESET)

print("   " + GREEN + f"{success_count} Mission Enrolments Was normalized" + RESET)

# Normalize Learn Content Activities
learn_content_activities = LearnContentActivity.objects.filter(
    mission_enrollment__isnull=True, mission_stage_content__isnull=False
).all()
print("\n--- Normalize Learn Content Activities Script is Running ---\n")
print(f"   {learn_content_activities.count()} Activities Without Enrollment:")
success_count = 0

for activity in learn_content_activities:
    try:
        mission = activity.mission_stage_content.stage.mission

        mission_enrollment = MissionEnrollment.objects.filter(mission=mission, user=activity.user).first()

        if mission_enrollment:
            activity.mission_enrollment = mission_enrollment
            activity.save()
            success_count += 1
        else:
            print(
                "   "
                + RED
                + f"ALERT: Learn Content Activity have a mission stage content, but was not found enrollment:"
            )
            print("      " + f"Learn_Content_Activity: id:{activity.id}")
            print("      " + f"User: id:{activity.user.id}, email:{activity.user.email}")
            print("      " + f"Mission: id:{mission.id}\n")
            continue

    except Exception as e:
        print(RED + f"One Exception Happened: {str(e)} {str(traceback.format_exc())}" + RESET)

print("   " + GREEN + f"{success_count} Learn Content Activities Was normalized" + RESET)

# Normalize User Mission Stage
user_mission_stages = UserMissionStage.objects.filter(mission_enrollment__isnull=True)
print("\n--- Normalize User Mission Stage Script is Running ---\n")
print(f"   {user_mission_stages.count()} User Mission Stages Without Enrollment:")
success_count = 0

for user_mission_stage in user_mission_stages:
    try:
        mission = user_mission_stage.stage.mission
        mission_enrollment = MissionEnrollment.objects.filter(mission=mission, user=user_mission_stage.user).first()

        if mission_enrollment:
            user_mission_stage.mission_enrollment = mission_enrollment
            user_mission_stage.save()
            success_count += 1
        else:
            print("   " + RED + f"ALERT: User Mission Stage, but was not found enrollment:")
            print("      " + f"User Mission Stage: id:{user_mission_stage.id}")
            continue

    except Exception as e:
        print(RED + f"One Exception Happened: {str(e)} {str(traceback.format_exc())}" + RESET)

print("   " + GREEN + f"{success_count} User Mission Stage Was normalized" + RESET)

# Normalize User Mission Content
user_mission_contents = UserMissionContent.objects.filter(mission_enrollment__isnull=True)
print("\n--- Normalize User Mission Content Script is Running ---\n")
print(f"   {user_mission_contents.count()} User Mission Contents Without Enrollment:")
success_count = 0

for user_mission_content in user_mission_contents:
    try:
        mission = user_mission_content.content.stage.mission
        mission_enrollment = MissionEnrollment.objects.filter(mission=mission, user=user_mission_content.user).first()

        if mission_enrollment:
            user_mission_content.mission_enrollment = mission_enrollment
            user_mission_content.save()
            success_count += 1
        else:
            print("   " + RED + f"ALERT: User Mission Content, but was not found enrollment:")
            print("      " + f"User Mission Content: id:{user_mission_content.id}")
            continue

    except Exception as e:
        print(RED + f"One Exception Happened: {str(e)} {str(traceback.format_exc())}" + RESET)

print("   " + GREEN + f"{success_count} User Mission Content Was normalized" + RESET)
