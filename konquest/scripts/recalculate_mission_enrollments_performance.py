import logging
import os

import django
from django.forms import model_to_dict

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from config.settings import SCRIPT_LOG_FOLDER, SCRIPT_LOG_FORMAT
from mission.models import MissionStage, MissionStageContent
from mission.models.mission_content_type_enum import HTML
from user_activity.models import MissionEnrollment
from utils.task_transaction import task_transaction
from user_activity.services.mission_enrollment_finish_service import MissionEnrollmentFinishService 

def run():
    logging.basicConfig(
        filename=f"{SCRIPT_LOG_FOLDER}/recalculate_mission_enrollments_performance_mandatorios.log",
        level=logging.INFO,
        format=SCRIPT_LOG_FORMAT,
    )
    logger = logging.getLogger()
    logger.info("script started")

    success_count = 0
    stage_ids = MissionStageContent.objects.filter(content_type=HTML).values_list("stage_id", flat=True)
    mission_ids = ['18c8ff8b-8091-41a5-a661-93c31bdf061d','36a798fb-eaf7-4535-973e-3e7e8ea7dc24','8528f0e2-4bef-4a06-820b-e5a5bac25661','92017503-e81d-4a1d-8f23-2d09a3b63fcd','9dfa6150-d0d0-4276-baef-b237323dbb5d','cd9a1ebc-b8f5-4e4a-8486-7b68712c83e9']
    # mission_ids = ['36a798fb-eaf7-4535-973e-3e7e8ea7dc24','92017503-e81d-4a1d-8f23-2d09a3b63fcd','8528f0e2-4bef-4a06-820b-e5a5bac25661'] # MissionStage.objects.filter(id__in=stage_ids).values_list("mission_id", flat=True)
    mission_enrollments = MissionEnrollment.objects.filter(
        # status__in=["COMPLETED", "REPROVED"], mission_id__in=mission_ids, end_date__gte="2022-01-01"
        status__in=["COMPLETED", "REPROVED"], mission_id__in=mission_ids
    )
    logger.info(f"{mission_enrollments.count()} enrollments founded")
    
    with task_transaction('SCRIPT - RECALCULATE MISSION ENROLLMENTS PERFORMANCE', MissionEnrollmentFinishService) as service:
        mission_enrollment_finish_service = service 


    for enrollment in mission_enrollments:
        old_enrollment = model_to_dict(enrollment)
        enrollment.status = "STARTED"
        old_date = enrollment.end_date
        # old_performance = enrollment.performance
        try:
            print(enrollment)
            mission_enrollment_finish_service.process(enrollment)
        except Exception as exception:
            logger.error(f"{str(exception)} for enrollment ({enrollment.id})")
            continue
        # if enrollment.performance < old_performance:
        #     continue
        enrollment.certificate_url = None
        enrollment.end_date = old_date
        enrollment.save()
        success_count += 1
        logger.info(
            f"updated enrollment({enrollment.id}) old enrollment {old_enrollment} updated {model_to_dict(enrollment)}"
        )

    logger.info(f"{success_count} Mission Enrollments Was recalculate")
    logger.info("script done")


if __name__ == "__main__":
    run()
