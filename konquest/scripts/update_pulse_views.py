import logging
import os

import django
from django.db.models import Count

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from config import settings
from pulse.models.pulse import Pulse
from user_activity.models import LearnContentActivity


def run(*args):
    pulses = Pulse.objects.filter(views__isnull=True)
    logging.debug(f"Updating {pulses.count()} pulses views")
    for pulse in pulses:
        learn_content_activities_count = (
            LearnContentActivity.objects.filter(pulse=pulse).values("user").annotate(dcount=Count("user")).count()
        )
        pulse.views = learn_content_activities_count
    Pulse.objects.bulk_update(pulses, fields=["views"])
    logging.debug(f"Done")


if __name__ == "__main__":
    run()
