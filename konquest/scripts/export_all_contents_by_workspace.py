"""
!!!READ BEFORE RUNNING THE SCRIPT!!!

This script will download all Konquest contents of a specific workspace.
Before running this script, you need to add the following database configuration to the config.settings file:
    DATABASES = {
        ...
        "kontent": {
            "ENGINE": "django.db.backends.postgresql_psycopg2",
            "NAME": os.environ.get("DATABASE_KONTENT", "kontent_db"),
            "USER": os.environ.get("DATABASE_USER", "postgres"),
            "PASSWORD": os.environ.get("DATABASE_PASSWORD", "postgres"),
            "HOST": os.environ.get("DATABASE_HOST", "localhost"),
            "PORT": os.environ.get("DATABASE_PORT", "5432"),
        }
        ...
    }

all-contents-export/workspaces/
├── <workspace_id>_plan.xlsx
├── <workspace_id>/
│   ├── conteudos/
│   │   └── (arquivos baixados)
│   ├── links.xlsx
│   ├── error_log.csv
│   ├── summary.xlsx
│   └── quizzes/
│       └── (arquivos de exames individuais)
└── <workspace_id>.zip
"""

import os
import uuid
import zipfile
import pandas as pd
import shutil
import boto3
from urllib.parse import urlparse
from pathlib import Path

import django
from django.conf import settings
import requests
from django.db import connections

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from mission.models.mission_workspace import OWNER
from pulse.models import Pulse, PulseChannel
from mission.models import MissionStageContent
from learn_content.models import Exam

BASE_EXPORT_DIR_NAME = "all-contents-export"
WORKSPACES_DIR_NAME = "workspaces"
CONTEUDOS_DIR_NAME = "conteudos"
QUIZZES_DIR_NAME = "quizzes"
LINKS_FILE_NAME = "links.xlsx"
ERROR_LOG_FILE_NAME = "erros.csv"
SUMMARY_FILE_NAME = "summary.xlsx"
PLAN_FILE_NAME = "plan.xlsx"
QUIZZES_FILE_NAME = "quizzes.xlsx"
ZIP_EXTENSION = ".zip"

S3_DOMAIN = "s3.amazonaws.com"
COMMON_CDN_DOMAINS = [
    "media.keepsdev.com",
    "media-stage.keepsdev.com",
    "keeps.kontent.media.prd",
    "keeps.konquest.media.prd",
]
SCORM_CONTENT_TYPE_ID = 'ee9855a5-3a65-4dcb-81b9-ac9f16e01831'
INVALID_FILENAME_CHARS = '<>:"/\\|?*'
MAX_FILENAME_LENGTH = 240
DEFAULT_ZIP_COMPRESSION = zipfile.ZIP_DEFLATED

CONTENT_TYPE_MAPPING = {
    '0faac34b-2393-4352-8a94-a9ee0659f824': 'PDF',
    '569cc389-ac1d-4fa0-9692-f715b475b59b': 'Video',
    '673e4c02-ae1c-4e61-830b-706d35bd0b11': 'Spreadsheet',
    '7a41a8e0-ee37-4d0b-ad4f-35bada67134d': 'Question',
    '7ee375e4-b781-46e6-b0de-0323ebb94b96': 'Presentation',
    'b7094e27-b263-4fed-a928-6f0a78439cbe': 'Text',
    'e459d983-5c61-4e7d-8310-1eec2dc8f369': 'Blog',
    'ee9855a5-3a65-4dcb-81b9-ac9f16e01830': 'HTML File',
    'ee9855a5-3a65-4dcb-81b9-ac9f16e01831': 'SCORM',
    '799f766c-a956-4c03-b5aa-bde9ba357de8': 'Podcast',
    'bda0cca5-ac84-4257-8b83-defac7f96738': 'HTML',
    '2284bfce-fdfc-4477-9143-39c380cc653c': 'Image'
}

OLD_LOG = """"""

def get_workspace_paths(workspace_id_str: str) -> dict:
    base_export_root = Path(settings.BASE_DIR) / "temp" / BASE_EXPORT_DIR_NAME / WORKSPACES_DIR_NAME
    workspace_path = base_export_root / workspace_id_str
    quizzes_dir = workspace_path / QUIZZES_DIR_NAME
    return {
        "workspace_path": workspace_path,
        "base_export_root": base_export_root,
        "workspace": workspace_path,
        "conteudos": workspace_path / CONTEUDOS_DIR_NAME,
        "quizzes_dir": quizzes_dir,
        "summary": workspace_path / SUMMARY_FILE_NAME,
        "plan": base_export_root / f"{workspace_id_str}_{PLAN_FILE_NAME}",
        "links": workspace_path / LINKS_FILE_NAME,
        "error_log": workspace_path / ERROR_LOG_FILE_NAME,
        "quizzes": workspace_path / QUIZZES_FILE_NAME,
        "zip_output": base_export_root / f"{workspace_id_str}{ZIP_EXTENSION}",
    }

def ensure_directory_exists(path: Path):
    os.makedirs(path, exist_ok=True)

def compress_directory(directory_path: Path, output_zip_path: Path):
    print(f"Creating zip file at: {output_zip_path}")
    with zipfile.ZipFile(output_zip_path, mode="w", compression=DEFAULT_ZIP_COMPRESSION) as zf:
        for item_path in directory_path.rglob("*"):
            if item_path.is_file():
                arcname = item_path.relative_to(directory_path.parent)
                zf.write(item_path, arcname)
    print(f"Zip file created successfully at: {output_zip_path}")

def check_file_exists(file_path: Path) -> bool:
    return file_path.is_file()

def download_file_http(url: str, target_path: Path) -> bool:
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        with open(target_path, 'wb') as file:
            for chunk in response.iter_content(chunk_size=8192):
                file.write(chunk)
        return True
    except requests.RequestException as e:
        print(f"Error downloading {url}: {e}")
        return False
    except IOError as e:
        print(f"Error saving to {target_path}: {e}")
        return False

def save_dataframe_to_file(df: pd.DataFrame, output_path: Path, data_description: str, file_type: str = 'excel', index: bool = False, sheet_name: str = 'Sheet1'):
    if df.empty:
        print(f"No {data_description} to save to {output_path}.")
        if output_path.exists():
             output_path.unlink(missing_ok=True)
        if file_type == 'excel':
            pd.DataFrame().to_excel(str(output_path), index=index, sheet_name=sheet_name)
        elif file_type == 'csv':
             with open(output_path, 'w') as f:
                if not df.columns.empty:
                    f.write(','.join(df.columns) + '\n')
        return

    print(f"Writing {len(df)} {data_description} to {output_path}...")
    ensure_directory_exists(output_path.parent)

    if file_type == 'excel':
        df.to_excel(str(output_path), index=index, sheet_name=sheet_name)
    elif file_type == 'csv':
        df.to_csv(str(output_path), index=index)
    else:
        print(f"Unsupported file type: {file_type} for {output_path}")
        return
    print(f"File {output_path} created successfully.")

def get_content_ids_for_workspace(workspace_id: str) -> list:
    mission_learn_content_uuids = MissionStageContent.objects.filter(
        stage__mission__missionworkspace__workspace_id=workspace_id,
        stage__mission__missionworkspace__relationship_type=OWNER
    ).values_list('learn_content_uuid', flat=True)

    pulse_ids = PulseChannel.objects.filter(channel__workspace_id=workspace_id).values_list("pulse_id", flat=True)
    pulse_learn_content_uuids = Pulse.objects.filter(id__in=pulse_ids).values_list('learn_content_uuid', flat=True)

    return list(mission_learn_content_uuids) + list(pulse_learn_content_uuids)

def get_learn_content_data(learn_content_ids: list) -> list:
    if not learn_content_ids:
        return []
    formatted_ids = ", ".join(f"'{learn_content_id}'" for learn_content_id in learn_content_ids)
    query = f"SELECT id, name, url, content_type_id FROM learn_content WHERE id IN ({formatted_ids})"
    with connections['kontent'].cursor() as cursor:
        cursor.execute(query)
        columns = [col[0] for col in cursor.description]
        return [dict(zip(columns, row)) for row in cursor.fetchall()]

def get_mission_names_for_contents(learn_content_uuids: list) -> dict:
    if not learn_content_uuids:
        return {}
    mission_stage_contents = MissionStageContent.objects.filter(
        learn_content_uuid__in=learn_content_uuids
    ).select_related('stage__mission')
    return {str(content.learn_content_uuid): content.stage.mission.name for content in mission_stage_contents}

def export_exams_to_excel(workspace_id: str, paths: dict) -> None:
    print("Exporting exams data to Excel...")
    mission_stages = MissionStageContent.objects.filter(
        stage__mission__missionworkspace__workspace_id=workspace_id,
        stage__mission__missionworkspace__relationship_type=OWNER,
        content_type="EXAM"
    ).values_list('learn_content_uuid', flat=True)
    pulse_ids = PulseChannel.objects.filter(
        channel__workspace_id=workspace_id
    ).values_list("pulse_id", flat=True)
    pulse_exams = Pulse.objects.filter(
        id__in=pulse_ids,
        learn_content_uuid__isnull=False
    ).values_list('learn_content_uuid', flat=True)

    exam_ids = list(mission_stages) + list(pulse_exams)

    if not exam_ids:
        print(f"No exams found for workspace {workspace_id}")
        save_dataframe_to_file(pd.DataFrame(), paths["quizzes"], "exams")
        return

    exams = Exam.objects.filter(id__in=exam_ids).prefetch_related('question_set')

    ensure_directory_exists(paths["quizzes_dir"])

    consolidated_exam_data = []

    for exam in exams:
        related_content_name = "N/A"

        if exam.stage:
            related_content_name = f"MissionStage: {exam.stage.name}"
        elif exam.pulse:
            related_content_name = f"Pulse: {exam.pulse.title}"
        elif exam.channel:
            related_content_name = f"Channel: {exam.channel.name}"

        questions = exam.question_set.all()

        individual_exam_data = []

        for question in questions:
            options = question.questionoption_set.all()
            all_options = []
            correct_options_indices = []

            for i, option in enumerate(options):
                option_text = option.option
                all_options.append(option_text)
                if option.correct_answer:
                    correct_options_indices.append(str(i + 1))

            all_options_str = ""
            for i, option_text in enumerate(all_options):
                is_correct = "✓" if str(i + 1) in correct_options_indices else " "
                all_options_str += f"{i + 1}. [{is_correct}] {option_text}\n"

            correct_options_str = ", ".join(correct_options_indices)

            question_data = {
                'Exam Title': exam.title,
                'Related Content': related_content_name,
                'Question': question.exam_question,
                'All Options': all_options_str.strip(),
                'Correct Option(s)': correct_options_str,
                'Points': question.points,
                'Question Type': question.question_type
            }

            consolidated_exam_data.append(question_data)
            individual_exam_data.append(question_data)

        if individual_exam_data:
            sanitized_title = sanitize_filename(exam.title)
            exam_filename = f"{sanitized_title}_{exam.id}.xlsx"
            exam_file_path = paths["quizzes_dir"] / exam_filename

            df_individual = pd.DataFrame(individual_exam_data)
            save_dataframe_to_file(df_individual, exam_file_path, f"exam {exam.title}")
            print(f"Exported exam '{exam.title}' to {exam_file_path}")

    if consolidated_exam_data:
        df_consolidated = pd.DataFrame(consolidated_exam_data)
        save_dataframe_to_file(df_consolidated, paths["quizzes"], "all exams")
        print(f"Exported {len(consolidated_exam_data)} exam questions to {paths['quizzes']}")
    else:
        print(f"No exam data found for workspace {workspace_id}")
        save_dataframe_to_file(pd.DataFrame(), paths["quizzes"], "exams")

def sanitize_filename(filename: str) -> str:
    for char in INVALID_FILENAME_CHARS:
        filename = filename.replace(char, '_')

    filename = filename.rstrip('. ')

    if len(filename) > MAX_FILENAME_LENGTH + 100:
        name_part, ext_part = os.path.splitext(filename)
        name_part = name_part[:MAX_FILENAME_LENGTH]
        filename = name_part + ext_part
    return filename

def get_download_filename_details(original_name: str, url: str, is_scorm_type: bool) -> tuple[str, bool]:
    name_part = os.path.splitext(original_name.replace("/", ""))[0]

    if is_scorm_type:
        return sanitize_filename(name_part + ".zip"), True

    if is_html_content(url):
        return sanitize_filename(name_part + ".zip"), False

    parsed_url_path = urlparse(url).path
    _, url_extension = os.path.splitext(parsed_url_path)

    final_filename = name_part + url_extension if url_extension else original_name
    return sanitize_filename(final_filename), False

def is_scorm_content_type(content_data: dict) -> bool:
    return str(content_data.get('content_type_id')) == SCORM_CONTENT_TYPE_ID

def is_html_content(url: str) -> bool:
    if not url: return False
    parsed_url = urlparse(url)
    return parsed_url.path.lower().endswith('.html')

def is_s3_or_cdn_url(url: str) -> bool:
    if not url: return False
    if S3_DOMAIN in url:
        return True

    cdn_base_url = getattr(settings, 'AWS_CDN_BASE_URL', None)
    if cdn_base_url and cdn_base_url in url:
        return True

    return any(domain in url for domain in COMMON_CDN_DOMAINS)

def get_s3_client():
    return boto3.client(
        's3',
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        region_name=settings.AWS_REGION_NAME
    )

def list_s3_objects(s3_client, bucket_name: str, prefix: str) -> list:
    prefix = prefix.lstrip('/')

    print(f"Listing objects in bucket: {bucket_name}, prefix: {prefix}")
    objects = []
    paginator = s3_client.get_paginator('list_objects_v2')
    try:
        for page in paginator.paginate(Bucket=bucket_name, Prefix=prefix):
            if 'Contents' in page:
                objects.extend(item['Key'] for item in page['Contents'])
    except Exception as e:
        print(f"Error listing S3 objects for bucket {bucket_name}, prefix {prefix}: {e}")
        return []
    print(f"Found {len(objects)} objects.")
    return objects

def download_s3_objects(s3_client, bucket_name: str, object_keys: list, local_base_dir: Path, s3_prefix_to_strip: str):
    s3_prefix_to_strip = s3_prefix_to_strip.lstrip('/')
    ensure_directory_exists(local_base_dir)
    for key in object_keys:
        try:
            if not '.' in key:
                print(f"Skipping key without extension: {key}")
                continue

            relative_path = key[len(s3_prefix_to_strip):].lstrip('/')
            local_file_path = local_base_dir / relative_path

            ensure_directory_exists(local_file_path.parent)

            try:
                print(f"Downloading s3://{bucket_name}/{key} to {local_file_path}")
                s3_client.download_file(bucket_name, key, str(local_file_path))
            except Exception as e:
                print(f"Error downloading {key}: {e}")
        except Exception as e:
            print(f"Error processing key {key}: {e}")

def process_scorm_package(url: str, output_dir: Path, final_zip_name: str) -> bool:
    s3_client = get_s3_client()
    bucket_name, s3_prefix = get_s3_details_from_url(url, is_scorm=True)

    if not bucket_name:
        print(f"Could not determine bucket name from URL: {url}")
        return False

    object_keys = list_s3_objects(s3_client, bucket_name, s3_prefix)
    if not object_keys:
        print(f"No files found for SCORM package at s3://{bucket_name}/{s3_prefix}")
        return False

    temp_scorm_dir = output_dir / f"scorm_temp_{uuid.uuid4()}"
    ensure_directory_exists(temp_scorm_dir)

    download_s3_objects(s3_client, bucket_name, object_keys, temp_scorm_dir, s3_prefix)

    final_zip_path = output_dir / final_zip_name
    print(f"Creating SCORM zip package at: {final_zip_path}")
    with zipfile.ZipFile(final_zip_path, 'w', DEFAULT_ZIP_COMPRESSION) as zipf:
        for item in temp_scorm_dir.rglob("*"):
            if item.is_file():
                zipf.write(item, item.relative_to(temp_scorm_dir))

    shutil.rmtree(temp_scorm_dir)
    print(f"Successfully created SCORM zip: {final_zip_path}")
    return True

def process_html_package(url: str, output_dir: Path, final_zip_name: str) -> bool:
    s3_client = get_s3_client()
    bucket_name, s3_prefix = get_s3_details_from_url(url, is_scorm=False)

    if not bucket_name:
        print(f"Could not determine bucket name from URL: {url}")
        return False

    print(f"Looking for HTML content in bucket: {bucket_name}, prefix: {s3_prefix}")
    object_keys = list_s3_objects(s3_client, bucket_name, s3_prefix)
    if not object_keys:
        print(f"No files found for HTML content at s3://{bucket_name}/{s3_prefix}")
        return False

    content_uuid = s3_prefix.split('/')[-1] if '/' in s3_prefix else 'html_content'
    temp_html_dir = output_dir / f"html_temp_{content_uuid}"
    ensure_directory_exists(temp_html_dir)

    download_s3_objects(s3_client, bucket_name, object_keys, temp_html_dir, s3_prefix)

    final_zip_path = output_dir / final_zip_name
    print(f"Creating HTML content zip package at: {final_zip_path}")
    with zipfile.ZipFile(final_zip_path, 'w', DEFAULT_ZIP_COMPRESSION) as zipf:
        for item in temp_html_dir.rglob("*"):
            if item.is_file():
                zipf.write(item, item.relative_to(temp_html_dir))

    shutil.rmtree(temp_html_dir)
    print(f"Successfully created HTML content zip: {final_zip_path}")
    return True

def get_s3_details_from_url(url: str, is_scorm: bool) -> tuple[str, str]:
    parsed_url = urlparse(url)
    path_no_leading_slash = parsed_url.path.lstrip('/')
    bucket_name = ""
    s3_prefix = ""

    if S3_DOMAIN in parsed_url.netloc:
        parts = path_no_leading_slash.split('/')
        bucket_name = parts[0]
        if is_scorm:
            s3_prefix = '/'.join(parts[1:]).rsplit('/', 1)[0]
        else:
            s3_prefix = '/'.join(parts[1:-1])
    else:
        bucket_name = settings.AWS_BUCKET_NAME
        bucket_path = getattr(settings, 'AWS_BUCKET_PATH', '').strip('/')
        parts = path_no_leading_slash.split('/')
        content_path = '/'.join(parts[1:-1]) if not is_scorm else '/'.join(parts[1:]).rsplit('/', 1)[0]
        s3_prefix = f"{bucket_path}/{content_path}" if bucket_path else content_path

    s3_prefix = s3_prefix.strip('/')
    return bucket_name, s3_prefix

def generate_action_plan(content_items: list, paths: dict) -> pd.DataFrame:
    print("Generating action plan...")
    # Get all items, not just S3/CDN items
    all_items = content_items
    learn_content_uuids = [item.get('id') for item in all_items if item.get('id')]
    mission_names_map = get_mission_names_for_contents(learn_content_uuids)

    plan_data = []
    for item in all_items:
        item_id = item.get('id')
        original_name = item.get('name', '')
        url = item.get('url', '')
        content_type_id = item.get('content_type_id', '')
        is_scorm_type = is_scorm_content_type(item)
        is_html_type = is_html_content(url)

        final_filename, _ = get_download_filename_details(original_name, url, is_scorm_type)
        mission_name = mission_names_map.get(str(item_id), "N/A")

        # Get content type from mapping or use a default
        content_type = CONTENT_TYPE_MAPPING.get(str(content_type_id), "Unknown")

        # For backward compatibility with existing code
        display_type = "SCORM" if is_scorm_type else "HTML" if is_html_type else "Arquivo"

        # Determine if it's a file or a link
        is_file = is_s3_or_cdn_url(url)
        file_or_link = "Arquivo" if is_file else "Link"

        plan_data.append({
            'id': item_id,
            'nome_original': original_name,
            'nome_arquivo_final': final_filename,
            'missao/pulse': mission_name,
            'tipo_conteudo': content_type,
            'tipo': file_or_link,
            'status_download': 'Pendente'
        })
        print(f"Added to plan: {original_name} -> {final_filename} (Tipo: {display_type}, Content Type: {content_type}, File/Link: {file_or_link}, Trilha: {mission_name})")

    plan_df = pd.DataFrame(plan_data)
    # Save to both plan and summary files
    save_dataframe_to_file(plan_df, paths["plan"], "action plan entries")
    save_dataframe_to_file(plan_df, paths["summary"], "action plan entries")
    return plan_df

def process_single_content_item(content_data: dict, conteudos_output_path: Path, error_list: list) -> tuple:
    content_id = content_data.get('id')
    original_name = content_data.get('name', 'untitled')
    url = content_data.get('url')

    if not url or not is_s3_or_cdn_url(url):
        # Get content type from mapping or use a default
        content_type = CONTENT_TYPE_MAPPING.get(str(content_data.get('content_type_id', '')), "Unknown")
        print(f"Skipping non-S3/CDN URL or invalid URL for '{original_name}': {url}")
        return {'id': content_id, 'name': original_name, 'url': url, 'tipo_conteudo': content_type, 'tipo': 'Link'}, None

    if url in OLD_LOG:
        print(f"Skipping already processed URL (OLD_LOG): {url}")
        return None, 'skipped_old_log'

    is_scorm_type = is_scorm_content_type(content_data)
    is_html_type = is_html_content(url)
    final_filename, _ = get_download_filename_details(original_name, url, is_scorm_type)
    target_file_path = conteudos_output_path / final_filename

    if check_file_exists(target_file_path):
        print(f"File already exists, skipping download: {target_file_path}")
        return None, 'exists'

    print(f"Processing: '{original_name}' from {url}")
    downloaded_successfully = False
    try:
        if is_scorm_type:
            print(f"Processing as SCORM package: {final_filename}")
            downloaded_successfully = process_scorm_package(url, conteudos_output_path, final_filename)
        elif is_html_type:
            print(f"Processing as HTML package: {final_filename}")
            downloaded_successfully = process_html_package(url, conteudos_output_path, final_filename)
        else:
            print(f"Downloading as regular file: {final_filename}")
            downloaded_successfully = download_file_http(url, target_file_path)

        if not downloaded_successfully:
            raise Exception("Download or content processing failed.")

        return None, 'success'

    except Exception as e:
        print(f"Error processing '{original_name}' from {url}: {e}")
        error_list.append({'id': content_id, 'name': original_name, 'url': url, 'error': str(e)})
        return None, 'error'

def run_export():
    print("Starting Export All Contents by Workspace Script")
    workspace_id = str(input("Insert the workspace Id: "))
    paths = get_workspace_paths(workspace_id)

    ensure_directory_exists(paths["workspace"])
    ensure_directory_exists(paths["conteudos"])
    ensure_directory_exists(paths["quizzes_dir"])

    # Export exams data to Excel
    export_exams_to_excel(workspace_id, paths)

    learn_content_ids = get_content_ids_for_workspace(workspace_id)
    if not learn_content_ids:
        print(f"No content IDs found for workspace {workspace_id}.")
        return

    print(f"Found {len(learn_content_ids)} content items to potentially process.")
    all_content_data = get_learn_content_data(learn_content_ids)

    action_plan_df = generate_action_plan(all_content_data, paths)

    print(f"\nAction plan generated at: {paths['summary']}")
    confirmation = input("Review the action plan. Type 'y' to proceed with downloads: ")
    if confirmation.lower() != 'y':
        print("Operation cancelled by user.")
        return

    print("\nStarting content download process...")
    error_items_log = []
    download_summary = {'total': 0, 'success': 0, 'error': 0, 'skipped': 0, 'exists': 0}

    for item_data in all_content_data:
        non_s3_info, status = process_single_content_item(item_data, paths["conteudos"], error_items_log)
        download_summary['total'] +=1

        if non_s3_info:
            # Non-S3 URLs are already included in the action plan
            download_summary['skipped'] +=1
            continue
        if status == 'success':
            download_summary['success'] +=1
        elif status == 'error':
            download_summary['error'] +=1
        elif status == 'skipped_old_log':
            download_summary['skipped'] +=1
        elif status == 'exists':
            download_summary['exists'] +=1

        print(f"Progress: {download_summary['success'] + download_summary['error'] + download_summary['skipped'] + download_summary['exists']}/{download_summary['total']} items processed.")

    # We don't need to save non_s3_url_items separately anymore as they're included in the action plan
    # Create an empty file to maintain compatibility with existing code
    save_dataframe_to_file(pd.DataFrame(), paths["links"], "non-S3/CDN URLs")
    save_dataframe_to_file(pd.DataFrame(error_items_log), paths["error_log"], "download errors", file_type='csv')
    os.remove(paths["summary"])
    try:
        # Update the status in the action plan DataFrame before saving as summary
        for index, row in action_plan_df.iterrows():
            item_id = row['id']
            item_status = 'Sucesso'

            # Check if item is in error list
            if any(error_item['id'] == item_id for error_item in error_items_log):
                item_status = 'Erro'

            # Update status
            action_plan_df.at[index, 'status_download'] = item_status

        save_dataframe_to_file(action_plan_df, paths["workspace_path"] / f"summary.xlsx", "final summary")
    except Exception as e:
        print(f"Error saving final action plan: {e}")

    print("\nDownload Summary:")
    print(f"  Total items considered: {len(all_content_data)}")
    print(f"  Successfully processed/downloaded: {download_summary['success']}")
    print(f"  Skipped (non-S3/CDN or old log): {download_summary['skipped']}")
    print(f"  Skipped (already exists): {download_summary['exists']}")
    print(f"  Errors: {download_summary['error']}")

    if download_summary['success'] > 0 or download_summary['exists'] > 0 :
        compress_directory(paths["workspace"], paths["zip_output"])
    else:
        print("No content downloaded, skipping zip creation.")

    print(f"\nExport for workspace {workspace_id} completed.")
    print(f"  Action plan: {paths['plan']}")
    print(f"  Summary: {paths['summary']}")
    print(f"  Non-S3/CDN links: {paths['links']}")
    print(f"  Error log: {paths['error_log']}")
    print(f"  Consolidated exams data: {paths['quizzes']}")
    print(f"  Individual exam files: {paths['quizzes_dir']}")
    if (paths["zip_output"]).exists():
        print(f"  Zipped content: {paths['zip_output']}")


if __name__ == "__main__":
    run_export()