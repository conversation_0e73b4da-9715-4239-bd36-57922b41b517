from django.db.models import Count, OuterRef
import os

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from user_activity.models import MissionEnrollment


if __name__ == "__main__":
    enrollment_counts_subquery = MissionEnrollment.objects.filter(
        user_id=OuterRef('user_id'),
        mission_id=OuterRef('mission_id'),
        workspace_id=OuterRef('workspace_id'),
        deleted=False
    ).values('user_id').annotate(enrolled_count=Count('id')).values('enrolled_count')

    enrollments = MissionEnrollment.objects.filter().annotate(
        enrolled_count_agg=enrollment_counts_subquery
    ).filter(enrolled_count_agg__gt=1).order_by("-created_date")

    for enrollment in enrollments:
        enrollment.enrolled_count = enrollment.enrolled_count_agg

    MissionEnrollment.objects.bulk_update(enrollments, fields=['enrolled_count'])
