import os

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from account.models import Workspace
from di import Container
from user_activity.models import LearningTrailEnrollment


def _get_company():
    company_id = input("Input a company id: ")
    company = Workspace.objects.filter(id=company_id).first()
    while not company:
        company_id = input("Input a valid company id: ")
        company = Workspace.objects.filter(id=company_id).first()
    return company


def run():
    print("Starting script")
    container = Container()
    company_id = _get_company()
    learning_trail_enrollments = LearningTrailEnrollment.objects.filter(company_id=company_id, status="COMPLETED")
    for enrollment in learning_trail_enrollments:
        enrollment.certificate_url = None
        container.certificate_service().generate_learning_trail_certificate(enrollment)
        print("Generated certificate")


if __name__ == "__main__":
    run()
