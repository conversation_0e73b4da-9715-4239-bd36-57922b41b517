import os

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from gamification.services.gamification_service import GamificationService


workspaces = [
    "4e03bcd2-5275-4f41-954c-5cf53502e460",
    "943ca726-03cf-44ed-b8a7-ed10f6198475",
    "9bf66f38-fce9-4041-b4cc-deb810196328",
    "df104559-1ffd-485e-bbb9-049bbb089b55",
    "05f1acab-b45d-425a-8df3-b766272c1db9",
    "63870e51-d104-4080-a2e0-7ab87181655a",
    "7a50ca74-3a75-4f31-ad35-2a04b59e3450",
    "873101a2-e3c6-472d-a8ba-2cac1ef970d0",
]

users = [
    {"from": "93c437ee-da84-4f01-8c6b-2513fa523995", "to": "008a6ade-d411-4343-8d95-9f6d92594bce"},
    {"from": "fa5a5ba8-a961-4298-957a-990ef1de7777", "to": "dc9c8c72-5d28-4ab6-afef-ed5b2f6991ed"},
    {"from": "faf23d2b-9985-4ea0-8979-8d4d9a7e31b0", "to": "e9eddb95-c660-4e1d-a229-0f96e19193cf"},
    {"from": "9839724b-cbc0-4ca0-9f3f-05eda5480355", "to": "8182d8f1-2715-4cec-bd68-5e2c0b1fbf0d"},
]


for user in users:
    source_user_id = user["from"]
    new_user_id = user["to"]
    for workspace_id in workspaces:
        GamificationService.transfer_points(source_user_id, new_user_id, workspace_id)
        print(f"Successfully transferred points from {source_user_id} to {new_user_id} in workspace {workspace_id}.")
