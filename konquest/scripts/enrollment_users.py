# -*- coding: utf-8 -*-
"""
Script to enroll the users into missions
"""

import csv
import os
from datetime import datetime

import django

from config import settings

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from account.models import User
from mission.models import Mission
from user_activity.models import MissionEnrollment

STARTED = ""
COMPLETED = "COMPLETED"

status_map = {
    "DONE_100_PERFORMANCE": ("COMPLETED", 100),
    "DONE_0_PERFORMANCE": ("COMPLETED", 0),
    "ENROLLED_0_PROGRESS": ("STARTED", 0),
}


def read_csv(file):
    """
    Read an CSV file and return into dict

    :param file: path to file
    :return: users info

    """
    with open(file) as f:
        d = [{k: v for k, v in row.items()} for row in csv.DictReader(f, skipinitialspace=True)]

    return d


user_to_enroll = read_csv(f"{settings.BASE_DIR}/scripts/users.csv")

for _user in user_to_enroll:
    mission_exist = Mission.objects.filter(id=_user["MISSION_ID"]).exists()
    user_exist = User.objects.filter(email=_user["EMAIL"])

    if mission_exist and user_exist:
        user = User.objects.filter(email=_user["EMAIL"]).first()
        mission = Mission.objects.filter(id=_user["MISSION_ID"]).first()

        instance = MissionEnrollment()
        instance.user = user
        instance.mission = mission
        instance.points = mission.points if status_map[_user["KONQUEST_STATUS"]][0] == "COMPLETED" else None
        instance.start_date = django.utils.timezone.now()
        instance.goal_date = django.utils.timezone.now()
        instance.end_date = (
            django.utils.timezone.now() if status_map[_user["KONQUEST_STATUS"]][0] == "COMPLETED" else None
        )

        instance.performance = status_map[_user["KONQUEST_STATUS"]][1]
        instance.status = status_map[_user["KONQUEST_STATUS"]][0]
        try:
            instance.save()
            print(f'{_user["EMAIL"]},{_user["MISSION_ID"]},{instance.id}')
        except:
            print(f'{_user["EMAIL"]},{_user["MISSION_ID"]},user already enrolled')
    else:
        print(f'{_user["EMAIL"]},{_user["MISSION_ID"]},user or mission not found')
