import logging
import os

import django

from user_activity.services.mission_enrollment_service import MissionEnrollmentService

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from config.settings import SCRIPT_LOG_FOLDER, SCRIPT_LOG_FORMAT
from user_activity.models import MissionEnrollment
from utils.task_transaction import task_transaction
import csv
import logging

def run():
    logging.basicConfig(
        filename=f"{SCRIPT_LOG_FOLDER}/set_performance_and_finish_enrollments_by_csv.log",
        level=logging.INFO,
        format=SCRIPT_LOG_FORMAT,
    )
    logger = logging.getLogger()
    print("script started")


    arquivo_csv = 'konquest/scripts/enroll.csv'
    performance = 1
    with open(arquivo_csv, mode='r', encoding='utf-8') as file:
        reader = csv.reader(file)
        with task_transaction('SCRIPT - FINISH ENROLLMENTS', MissionEnrollmentService) as service:
            for row in reader:
                enrollment = MissionEnrollment.objects.filter(id=row[0])
                if not enrollment.exists():
                    logger.error(f"Enrollment {row[0]} not found")
                    continue
                enrollment = enrollment.first()
                service.finish_manually(enrollment, performance)
                logger.info(
                    f"updated enrollment({enrollment.id}) to performance {performance}"
                )


if __name__ == "__main__":
    run()
