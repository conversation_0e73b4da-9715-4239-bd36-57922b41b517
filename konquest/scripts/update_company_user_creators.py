import logging
import os

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from mission.models import Mission, MissionWorkspace
from pulse.models import Channel


def _change_pulse_user_creator(channel: Channel, user_id: str) -> None:
    pulse_channels = channel.pulsechannel_set.filter()
    logging.debug(f"Updating {pulse_channels.count()} missions")
    for pulse_channel in pulse_channels:
        pulse = pulse_channel.pulse
        pulse.user_creator_id = user_id
        pulse.save()


def run() -> None:
    company_id = input("Input a valid company id:")
    user_id = input("Input the new user creator id:")

    mission_ids = MissionWorkspace.objects.filter(company_id=company_id, relationship_type="OWNER").values_list(
        "mission_id", flat=True
    )
    missions = Mission.objects.filter(id__in=mission_ids)
    logging.debug(f"Updating {len(mission_ids)} missions")
    for mission in missions:
        mission.user_creator_id = user_id
        mission.save()

    channels = Channel.objects.filter(company_id=company_id)
    logging.debug(f"Updating {channels.count()} channels")
    for channel in channels:
        channel.user_creator_id = user_id
        channel.save()
        _change_pulse_user_creator(channel, user_id)

    logging.debug(f"Done")


if __name__ == "__main__":
    run()
