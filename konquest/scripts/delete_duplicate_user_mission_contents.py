import os

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from django.db.models import Count

from config import settings
from user_activity.models.user_mission_content import STARTED, UserMissionContent

grouped_data = (
    UserMissionContent.objects.values("content", "user", "mission_enrollment")
    .annotate(count=Count("id"))
    .filter(count__gt=1)
)

user_mission_contents_to_delete = []

for item in grouped_data:
    content_id = item["content"]
    user_id = item["user"]
    mission_enrollment_id = item["mission_enrollment"]
    count = item["count"]
    user_mission_contents = UserMissionContent.objects.filter(
        content_id=content_id, user_id=user_id, mission_enrollment_id=mission_enrollment_id
    ).order_by("-created_date")[1:]
    user_mission_contents_to_delete = [str(umc.id) for umc in user_mission_contents]

UserMissionContent.objects.filter(id__in=user_mission_contents_to_delete).delete()
