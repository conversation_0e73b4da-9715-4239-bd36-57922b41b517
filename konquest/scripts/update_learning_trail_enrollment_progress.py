import logging
import os

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from user_activity.models import LearningTrailEnrollment
from user_activity.tasks.learning_trail_enrollment_task import update_enrollments_progress
from utils.task_transaction import task_transaction



def update_learning_trail_enrollment_progress(trail_id):
    with task_transaction("update_learning_trail_enrollment_progress"):
        lt_enrollment_ids = LearningTrailEnrollment.objects.filter(
            learning_trail_id=trail_id, status__in=["STARTED", "ENROLLED"]
        ).values_list("id", flat=True)

        for enrollment_id in lt_enrollment_ids:
            update_enrollments_progress(enrollment_id)

        return f"{len(lt_enrollment_ids)} learning trail enrollment have been updated"

if __name__ == "__main__":
    update_learning_trail_enrollment_progress('ebd3037e-5221-43d0-8356-a8af767fde38')


# python konquest/manage.py shell
# from scripts.update_learning_trail_enrollment_progress import update_learning_trail_enrollment_progress
# update_learning_trail_enrollment_progress(idtrilha)