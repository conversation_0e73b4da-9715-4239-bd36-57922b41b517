import os
import uuid
from datetime import date, datetime

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from config import settings
from tasks import notification
from utils.utils import workspace_hash

email_data = {
    "mission_url": "https://app.clickup.com/t/3004080/DEV-2040",
    "mission_name": "Missão Teste",
    "mission_start_date": date.today(),
    "mission_start_time": datetime.today().time(),
    "mission_seats": 100,
    "user_name": "<PERSON>u<PERSON><PERSON>",
    "workspace_name": "EMPRESA MUY LOUCA",
    "mission_vertical_holder_image": "",
    "mission_link": settings.KONQUEST_WEB_MISSION_DETAIL_URL_WITH_WORKSPACE.format("a12s5de8", uuid.uuid4()),
}

users = [{"email": "rod<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "language": "pt-BR", "email_verified": True}]

response = notification.notify_users(email_data, "live_mission_are_today", "", users, settings.APPLICATION_ID)
message = response.get("messages")[0]
