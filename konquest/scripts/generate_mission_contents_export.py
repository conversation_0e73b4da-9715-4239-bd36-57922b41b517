import os

import django
import pandas as pd

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from config import settings
from di import Container
from mission.models import Mission
from tasks.mission import get_mission_learn_contents_ids


def run():
    print("--- Generate Mission Contents Export ---\n")
    container = Container()
    kontent_client = container.kontent_client
    s3_client = container.aws_s3_client
    mission = None
    while not mission:
        mission_id = input("Insert a valid mission id:")
        mission = Mission.objects.filter(id=mission_id).first()
        if not mission:
            print(f"{settings.RED}mission not found{settings.RESET}")
        else:
            break
    output_file_path = f"{settings.TEMP_FOLDER}/mission_{mission.id}_contents.csv"
    learn_contents_ids = get_mission_learn_contents_ids(mission)
    contents_dict = {
        "name": [],
        "content_type": [],
        "content_url": [],
        "created_date": [],
        "updated_date": [],
        "duration": [],
        "points": [],
    }
    for learn_content_id in learn_contents_ids:
        content = kontent_client.get_learn_content(settings.KEEPS_SECRET_TOKEN_INTEGRATION, learn_content_id)
        content["content_type"] = content["content_type"]["name"]
        for field in contents_dict.keys():
            contents_dict.get(field).append(content.get(field))
        print(content)
    data_frame = pd.DataFrame.from_dict(contents_dict)
    data_frame.to_csv(output_file_path, index=False)
    response = s3_client.send_file_path(output_file_path, f"reports/mission_{mission.id}_contents.csv", "text/csv")
    print(f'File generated: {response["url"]}\n')


if __name__ == "__main__":
    run()
