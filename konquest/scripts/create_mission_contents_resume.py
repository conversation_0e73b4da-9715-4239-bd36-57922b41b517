import logging
import os

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from typing import Sequence

from django.db import transaction

from config import settings
from config.settings import KEEPS_SECRET_TOKEN_INTEGRATION
from learn_content.models import Exam
from mission.models import Mission, MissionContentResume, MissionStageContent
from rest_clients.kontent import KontentClient


class MissionContentResumeProcessing:
    def __init__(self):
        self.kontent_client = KontentClient()
        self._mission = None
        self._contents = []
        self._exams = []

    def process(self, mission: Mission) -> Mission:
        self._mission = mission
        learn_contents_ids = self.get_mission_learn_contents_ids(mission)
        self._contents = self._list_contents(learn_contents_ids)
        self._exams = self._list_exams(mission)
        self._save_contents_resume()

        return mission

    @transaction.atomic()
    def _save_contents_resume(self) -> Sequence[MissionContentResume]:
        count_contents = self._count_contents_by_type()
        contents_resume = []
        self._mission.content_resume.filter().delete()
        for content_type in count_contents:
            count = count_contents[content_type]
            contents_resume.append(
                MissionContentResume(
                    mission=self._mission,
                    name=count.get("name"),
                    image=count.get("image"),
                    image_cover=count.get("image_cover"),
                    count=count.get("count"),
                )
            )
        MissionContentResume.objects.bulk_create(contents_resume)
        return contents_resume

    def _count_contents_by_type(self) -> dict:
        count_by_content_type = {}
        for content in self._contents:
            content_type = content.get("content_type", {"name": "Others"})
            content_type_id = content_type.get("id")
            if content_type_id in count_by_content_type:
                count_by_content_type[content_type_id]["count"] += 1
            else:
                count_by_content_type[content_type_id] = {
                    "count": 1,
                    "image": content_type.get("image"),
                    "image_cover": content_type.get("image_cover"),
                    "name": content_type.get("name"),
                }
        if self._exams:
            count_by_content_type[settings.CONTENT_TYPE_QUESTION_NAME] = {
                "name": settings.CONTENT_TYPE_QUESTION_NAME,
                "count": self._exams.__len__(),
                "image": settings.CONTENT_TYPE_QUESTION_IMAGE,
                "image_cover": settings.CONTENT_TYPE_QUESTION_IMAGE_COVER,
            }
        return count_by_content_type

    @staticmethod
    def get_mission_learn_contents_ids(mission: Mission):
        stages = mission.missionstage_set.filter().values_list("id", flat=True)
        contents = MissionStageContent.objects.filter(stage__in=stages, content_type__in=["CONTENT", "SCORM"])[::1]
        learn_content_uuid_list = [str(content.learn_content_uuid) for content in contents]
        return learn_content_uuid_list

    @staticmethod
    def _list_exams(mission: Mission):
        stages = mission.missionstage_set.filter()
        exam_stages = MissionStageContent.objects.filter(stage__in=stages, content_type__in=["EXAM"]).values_list(
            "stage_id", flat=True
        )
        exams = Exam.objects.filter(stage__in=exam_stages)
        return exams

    def _list_contents(self, learn_content_ids: Sequence[str]) -> Sequence[dict]:
        kontent_client = self.kontent_client
        contents = []

        for uuid in learn_content_ids:
            response = kontent_client.get_learn_content(KEEPS_SECRET_TOKEN_INTEGRATION, uuid)
            if response.__len__() != 0:
                contents.append(response)
        return contents


def run():
    logging.info("Start Script to process Content Resume of the missions")
    service = MissionContentResumeProcessing()
    missions = Mission.objects.filter(
        mission_model__in=["INTERNAL", "SCORM"], development_status__in=["IN_REVIEW", "DONE"]
    ).order_by("-created_date")
    total = missions.count()

    for mission in missions:
        try:
            service.process(mission)
        except Exception as error:
            message = f"process the content resume of the mission ({mission.id})"
            logging.error(message, exc_info=error)
        total -= 1
        logging.info(f"{total} missions to process")
    logging.info(f"Done")


if __name__ == "__main__":
    run()
