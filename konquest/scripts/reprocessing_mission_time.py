import os
from datetime import datetime

import django

from tasks.mission import check_content

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from config.settings import KEEPS_SECRET_TOKEN_INTEGRATION
from mission.models import Mission, MissionStage, MissionStageContent
from rest_clients.kontent import KontentClient


def check_mission_content_analyzed():
    missions = Mission.objects.filter(created_date__range=("2020-12-01", "2020-12-31")).all()

    for instance in missions:
        print(f"mission analyzing: {instance.id} | {instance.name}")
        try:
            stages = MissionStage.objects.filter(mission=instance).values_list("id", flat=True)
            contents = MissionStageContent.objects.filter(stage__in=stages, content_type="CONTENT")[::1]
            learn_content_uuid_list = [str(x.learn_content_uuid) for x in contents]

            if learn_content_uuid_list.__len__() == 0:
                continue
            else:
                sum_points = 0
                sum_duration_time = 0
                contents_analyzed = check_content(learn_content_uuid_list)

                if len(contents_analyzed) != 0:
                    for _content in contents_analyzed:
                        sum_points += _content["points"] if "points" in _content else 0
                        sum_duration_time += _content["duration"] if "duration" in _content else 0

                    instance.points = int(sum_points)
                    instance.duration_time = int(sum_duration_time)
                    instance.save()

        except:
            print(datetime.now(), f"error to analyze mission id: {str(instance.id)}")

    print(datetime.now(), "all missions analyzed")


def check_content(learn_content_list):
    """
    Check if all mission content are analyzed (Kontent).

    If any content already in analyze, return empty and waiting
    for next scheduled task to process.

    :param learn_content_list: UUID linked to content into Kontent service
    :return: list of contents analyzed or an empty array.

    """
    kontent_client = KontentClient()
    contents = []

    for uuid in learn_content_list:
        response = kontent_client.get_learn_content(KEEPS_SECRET_TOKEN_INTEGRATION, uuid)

        if response.__len__() == 0:
            continue

        elif "analyzed" in response and response["analyzed"] is True:
            contents.append(response)
        else:
            return []

    return contents


check_mission_content_analyzed()
