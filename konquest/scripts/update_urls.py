import os

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from achievement.models import AchievementType
from learning_trail.models import LearningTrail, LearningTrailType
from mission.models import Mission, MissionCategory, MissionContentResume, MissionType
from mission.models.mission_provider import <PERSON><PERSON>rovider
from notification.models import NotificationType
from pulse.models import Channel, ChannelCategory, ChannelType, Pulse, PulseType

URL_FIELDS = [
    {"model": Mission, "fields": ["thumb_image", "holder_image", "vertical_holder_image"]},
    {"model": AchievementType, "fields": ["image"]},
    {"model": LearningTrail, "fields": ["holder_image", "thumb_image"]},
    {"model": LearningTrailType, "fields": ["image"]},
    {"model": MissionCategory, "fields": ["image"]},
    {"model": MissionContentResume, "fields": ["image", "image_cover"]},
    {"model": MissionProvider, "fields": ["icon"]},
    {"model": MissionType, "fields": ["image"]},
    {"model": NotificationType, "fields": ["image"]},
    {"model": Channel, "fields": ["holder_image"]},
    {"model": ChannelCategory, "fields": ["image"]},
    {"model": ChannelType, "fields": ["image"]},
    {"model": Pulse, "fields": ["holder_image"]},
    {"model": PulseType, "fields": ["image", "image_cover"]},
]

OLD_URL_1 = f"https://s3.amazonaws.com/keeps.konquest.media.hml/"
OLD_URL_2 = f"https://s3.amazonaws.com/keeps.konquest.media.prd/"
OLD_URL_3 = f"https://s3.amazonaws.com/keeps.reports/"

NEW_URL = f"https://media-stage.keepsdev.com/konquest/"


def run():
    for _url_fields in URL_FIELDS:
        model = _url_fields["model"]
        fields = _url_fields["fields"]
        instances = model.objects.filter()
        for instance in instances:
            for field in fields:
                url = instance.__getattribute__(field)
                if not url:
                    continue
                print(url)

                for new_base_url in [OLD_URL_1, OLD_URL_2, OLD_URL_3]:
                    url = url.replace(new_base_url, NEW_URL)
                print(url)
                instance.__setattr__(field, url)

        # model.objects.filter(instances)


if __name__ == "__main__":
    run()
