import os
from datetime import datetime

import django
import pytz

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from user_activity.models import MissionEnrollment

external_mission_enrollments = MissionEnrollment.objects.filter(
    certificate_provider_url__isnull=False, status="COMPLETED", end_date__isnull=True
)
print("--- Normalize External Enrollment Script is Running ---")
print(f"{external_mission_enrollments.count()} External Enrollments Completed Without End Date:")
for enrollment in external_mission_enrollments:
    approve_msg = enrollment.approve_msg
    try:
        log_enrollment = approve_msg.split("\n")
        last_log = log_enrollment[-1]
        end_date_str = last_log[:10]
        end_date = datetime.strptime(end_date_str, "%d/%m/%Y")
        end_date = end_date.tzinfo.tzname(pytz.UTC)
        enrollment.end_date = end_date
    except Exception:
        enrollment.end_date = enrollment.start_date
    finally:
        print(enrollment.end_date)
        enrollment.save()
print(f"All External Enrollments End Date was Normalized")
