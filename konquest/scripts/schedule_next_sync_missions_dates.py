from mission.models import LiveMission, PresentialMission, LiveMissionDates, PresentialMissionDates
from mission.services.mission_service import MissionService
from utils.task_transaction import task_transaction
from mission.models.mission_development_status_enum import DONE, PROCESSING, IN_REVIEW, IN_PROGRESS
from django.db.models import Prefetch

def schedule_next_sync_missions_dates():
    live_missions_prefetched = LiveMission.objects.prefetch_related(
        Prefetch('dates', queryset=LiveMissionDates.objects.all(), to_attr='all_dates')
    ).filter(mission__development_status__in=[DONE, IN_REVIEW, PROCESSING, IN_PROGRESS])

    presential_missions_prefetched = PresentialMission.objects.prefetch_related(
        Prefetch('dates', queryset=PresentialMissionDates.objects.all(), to_attr='all_dates')
    ).filter(mission__development_status__in=[DONE, IN_REVIEW, PROCESSING, IN_PROGRESS])

    with task_transaction(schedule_next_sync_missions_dates.__name__, MissionService) as service:
        for mission in live_missions_prefetched:
            print(mission)
            for date in mission.all_dates:
                service.schedule_next_sync_mission_date(date)
        
        for mission in presential_missions_prefetched:
            print(mission)
            for date in mission.all_dates:
                service.schedule_next_sync_mission_date(date)
