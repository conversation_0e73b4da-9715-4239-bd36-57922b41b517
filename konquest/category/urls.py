from django.urls import path

from category.views.category_viewset import CategoryViewSet

_READY = {"get": "list"}

_LIST = {"get": "list", "post": "create"}

_DETAIL = {"get": "retrieve", "put": "update", "patch": "partial_update", "delete": "destroy"}


urlpatterns = [
    path("", CategoryViewSet.as_view(_LIST), name="categories-list"),
    path("/<uuid:pk>", CategoryViewSet.as_view(_DETAIL), name="categories-detail"),
]
