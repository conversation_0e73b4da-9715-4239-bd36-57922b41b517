from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets
from rest_framework.filters import Ordering<PERSON>ilter, SearchFilter
from rest_framework.response import Response
from rest_framework.status import HTTP_201_CREATED, HTTP_204_NO_CONTENT

from authentication.keeps_permissions import ADMIN_PERMISSIONS
from category.models import CustomCategory
from category.serializers.category_serializer import CustomCategoryPostSerializer, CustomCategorySerializer
from category.services.category_service import CategoryService
from utils.utils import swagger_safe_queryset


class CategoryViewSet(viewsets.ModelViewSet):
    """
    Manage account (workspace) categories.
    ___

    This API allowed companies create your own categories to use to classify missions and channels.

    For each category created/updated/deleted, the mission_category and channel_category tables will be change.

    ---
        Permissions Roles: 'Platform Admin', 'Super Admin', 'User Workspace Admin'
    ---
        filters:
        name, created_date, updated_date

        search:
        name

        order fields:
        name, created_date, updated_date

        default order: name, updated_date
    """

    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter)
    filterset_fields = (
        "name",
        "created_date",
        "updated_date",
    )
    search_fields = ("name",)
    ordering_fields = ("created_date", "updated_date", "name")
    ordering = (
        "name",
        "updated_date",
    )

    permission_classes = ADMIN_PERMISSIONS

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._service = CategoryService()

    @swagger_safe_queryset(CustomCategory)
    def get_queryset(self):
        workspace_id = self.request.user.get("client_id")
        return CustomCategory.objects.filter(workspace=workspace_id)

    def get_serializer_class(self):
        return CustomCategorySerializer

    @swagger_auto_schema(request_body=CustomCategoryPostSerializer, responses={"201": CustomCategorySerializer})
    def create(self, request, *args, **kwargs):
        workspace_id = self.request.user.get("client_id")
        request.data["workspace"] = workspace_id
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            category = CustomCategory(**serializer.validated_data)
            category = self._service.create_category(category)
            return Response(self.get_serializer(category).data, status=HTTP_201_CREATED)

    @swagger_auto_schema(request_body=CustomCategoryPostSerializer, responses={"200": CustomCategorySerializer})
    def update(self, request, *args, **kwargs):
        category_id = self.kwargs.get("pk")
        category = self.get_queryset().get(id=category_id)

        serializer = self.get_serializer_class()(category, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        category = self._service.update_category(category, serializer.validated_data)

        return Response(self.get_serializer_class()(category).data)

    def destroy(self, request, *args, **kwargs):
        custom_category = self.get_queryset().get(id=kwargs["pk"])
        self._service.delete_category(custom_category)
        return Response(None, status=HTTP_204_NO_CONTENT)
