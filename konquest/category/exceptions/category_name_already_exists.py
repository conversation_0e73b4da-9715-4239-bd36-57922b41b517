from django.utils.translation import gettext as _
from django.utils.translation import gettext_noop

from custom.keeps_exception_handler import KeepsServiceError

CATEGORY_NAME_ALREADY_EXISTS = gettext_noop("category_name_already_exists")


class CategoryNameAlreadyExists(KeepsServiceError):
    def __init__(self):
        super().__init__(CATEGORY_NAME_ALREADY_EXISTS, _(CATEGORY_NAME_ALREADY_EXISTS))
