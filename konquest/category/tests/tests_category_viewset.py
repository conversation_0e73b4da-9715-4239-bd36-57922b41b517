import uuid

import mock
from django.test import TestCase
from django.urls import reverse
from model_mommy import mommy
from rest_framework.test import APIClient

from account.models import User, Workspace
from category.models import CustomCategory
from mission.models import Mission, MissionCategory, MissionWorkspace
from pulse.models import Channel, ChannelCategory


@mock.patch("authentication.keeps_authentication.KeepsAuthentication._get_token_info")
@mock.patch("authentication.keeps_permissions.KeepsBasePermission._check_role", return_value=True)
class CategoryViewsetTestCase(TestCase):
    fixtures = ["mission_category", "channel_category"]

    def setUp(self):
        self.client = APIClient()
        self.user = mommy.make(User, id=uuid.uuid4())
        self.workspace = mommy.make(Workspace, id=uuid.uuid4())
        self.workspace2 = mommy.make(Workspace, id=uuid.uuid4())

        self.custom_category = mommy.make(
            CustomCategory, id=uuid.uuid4(), workspace=self.workspace, name="AA Custom 1", image="image_1"
        )
        self.custom_category2 = mommy.make(
            CustomCategory, id=uuid.uuid4(), workspace=self.workspace2, name="AA Custom 2", image="image_2"
        )

        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})
        self.url = reverse("categories-list")

    def test_category_list_workspace_1(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)
        self.assertEqual(response["results"][0]["name"], self.custom_category.name)
        self.assertEqual(response["results"][0]["description"], self.custom_category.description)
        self.assertEqual(response["results"][0]["workspace"], str(self.workspace.id))

    def test_category_list_workspace_2(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace2.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace2.id)})

        response = self.client.get(self.url, **self.headers, format="json").json()
        self.assertEqual(len(response["results"]), 1)
        self.assertEqual(response["results"][0]["name"], self.custom_category2.name)
        self.assertEqual(response["results"][0]["description"], self.custom_category2.description)
        self.assertEqual(response["results"][0]["workspace"], str(self.workspace2.id))

    def test_category_list_filter_by_name(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})

        response = self.client.get(
            self.url + "?name=" + str(self.custom_category.name), **self.headers, format="json"
        ).json()
        self.assertEqual(len(response["results"]), 1)

    def test_category_list_filter_by_name_not_found(self, mock_return, mock_return_roles):
        """
        Make sure that workspace A should not search the workspace B category by search
        (name exist for workspace B but not for workspace A)
        """
        self.headers = {"HTTP_X_CLIENT": str(self.workspace2.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace2.id)})

        response = self.client.get(
            self.url + "?name=" + str(self.custom_category.name), **self.headers, format="json"
        ).json()
        self.assertEqual(len(response["results"]), 0)

    def test_category_get_success(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})

        response = self.client.get(
            reverse("categories-detail", args=[str(self.custom_category.id)]), **self.headers, format="json"
        )
        response_json = response.json()
        self.assertEqual(response_json["id"], str(self.custom_category.id))
        self.assertEqual(response.status_code, 200)

    def test_category_get_not_allowed(self, mock_return, mock_return_roles):
        """
        Make sure that workspace A should not get the workspace B category detail
        (category exist for workspace B but not for workspace A)
        """

        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})

        response = self.client.get(
            reverse("categories-detail", args=[str(self.custom_category2.id)]), **self.headers, format="json"
        )
        response_json = response.json()
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response_json["detail"], "No CustomCategory matches the given query.")

    def test_category_get_not_found(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})

        response = self.client.get(
            reverse("categories-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["detail"], "No CustomCategory matches the given query.")
        self.assertEqual(response.status_code, 404)

    def test_category_post_success(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})

        data = {
            "name": "new category",
            "image": "http://keeps.com.br",
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        mission_category = MissionCategory.objects.get(id=str(response_json["id"]))
        channel_category = ChannelCategory.objects.get(id=str(response_json["id"]))

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response_json["name"], data["name"])
        self.assertEqual(response_json["image"], data["image"])
        self.assertEqual(response_json["workspace"], str(self.workspace.id))

        self.assertEqual(response_json["name"], mission_category.name)
        self.assertEqual(response_json["image"], mission_category.image)

        self.assertEqual(response_json["name"], channel_category.name)
        self.assertEqual(response_json["image"], channel_category.image)

    def test_category_post_required_field(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})

        data = {
            "image": "http://keeps.com.br",
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response_json["name"], ["This field is required."])
        self.assertEqual(response.status_code, 400)

    def test_category_put_success(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})

        data = {"name": "new name", "image": "admin", "workspace": str(self.workspace.id)}

        response = self.client.put(
            reverse("categories-detail", args=[str(self.custom_category.id)]), **self.headers, data=data, format="json"
        )

        response_json = response.json()
        mission_category = MissionCategory.objects.get(id=str(self.custom_category.id))
        channel_category = ChannelCategory.objects.get(id=str(self.custom_category.id))

        self.assertEqual(response_json["name"], data["name"])
        self.assertEqual(response.status_code, 200)

        self.assertEqual(response_json["name"], mission_category.name)
        self.assertEqual(response_json["name"], channel_category.name)

    def test_category_put_not_allowed(self, mock_return, mock_return_roles):
        """
        Make sure that workspace A should not update the workspace B category
        (category exist for workspace B but not for workspace A)
        """

        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})

        data = {"name": "New name"}

        response = self.client.put(
            reverse("categories-detail", args=[str(self.custom_category2.id)]), **self.headers, data=data, format="json"
        )

        response_json = response.json()
        self.assertEqual(response_json["detail"], "custom_category_not_found")
        self.assertEqual(response.status_code, 404)

    def test_category_delete_success(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})

        response = self.client.delete(
            reverse("categories-detail", args=[str(self.custom_category.id)]), **self.headers, format="json"
        )
        self.assertEqual(response.status_code, 204)

    def test_category_delete_not_found(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})

        response = self.client.delete(
            reverse("categories-detail", args=[str(uuid.uuid4())]), **self.headers, format="json"
        )
        response_json = response.json()

        self.assertEqual(response_json["i18n"], "custom_category_not_found")
        self.assertEqual(response.status_code, 404)

    def test_category_delete_not_allowed(self, mock_return, mock_return_roles):
        """
        Make sure that workspace A should not delete the workspace B category
        (category exist for workspace B but not for workspace A)
        """

        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})

        response = self.client.delete(
            reverse("categories-detail", args=[str(self.custom_category2.id)]), **self.headers, format="json"
        )

        self.assertEqual(response.json()["i18n"], "custom_category_not_found")
        self.assertEqual(response.status_code, 404)

    def test_category_mission_list_workspace_1(self, mock_return, mock_return_roles):
        """
        List mission categories including workspace custom categories
        """
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})
        url = reverse("mission-categories-list")

        response = self.client.get(url, **self.headers, format="json")
        response_json = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response_json["results"]), 22)
        self.assertEqual(response_json["results"][0]["workspace"], str(self.workspace.id))
        self.assertEqual(response_json["results"][0]["name"], self.custom_category.name)
        self.assertEqual(response_json["results"][0]["is_public"], False)
        self.assertEqual(response_json["results"][1]["workspace"], None)
        self.assertEqual(response_json["results"][1]["is_public"], True)

    def test_category_mission_list_workspace_2(self, mock_return, mock_return_roles):
        """
        List mission categories including workspace custom categories
        """
        self.headers = {"HTTP_X_CLIENT": str(self.workspace2.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace2.id)})
        url = reverse("mission-categories-list")

        response = self.client.get(url, **self.headers, format="json")
        response_json = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response_json["results"]), 22)
        self.assertEqual(response_json["results"][0]["workspace"], str(self.workspace2.id))
        self.assertEqual(response_json["results"][0]["name"], self.custom_category2.name)
        self.assertEqual(response_json["results"][0]["is_public"], False)
        self.assertEqual(response_json["results"][1]["workspace"], None)
        response_json = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_json["results"][1]["is_public"], True)

    def test_category_channel_list_workspace_1(self, mock_return, mock_return_roles):
        """
        List mission categories including workspace custom categories
        """
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})
        url = reverse("channel-categories-list")

        response = self.client.get(url, **self.headers, format="json")
        response_json = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response_json["results"]), 22)
        self.assertEqual(response_json["results"][0]["workspace"], str(self.workspace.id))
        self.assertEqual(response_json["results"][0]["name"], self.custom_category.name)
        self.assertEqual(response_json["results"][0]["is_public"], False)
        self.assertEqual(response_json["results"][1]["workspace"], None)
        self.assertEqual(response_json["results"][1]["is_public"], True)

    def test_category_channel_list_workspace_2(self, mock_return, mock_return_roles):
        """
        List mission categories including workspace custom categories
        """
        self.headers = {"HTTP_X_CLIENT": str(self.workspace2.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace2.id)})
        url = reverse("channel-categories-list")

        response = self.client.get(url, **self.headers, format="json")
        response_json = response.json()
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response_json["results"]), 22)
        self.assertEqual(response_json["results"][0]["workspace"], str(self.workspace2.id))
        self.assertEqual(response_json["results"][0]["name"], self.custom_category2.name)
        self.assertEqual(response_json["results"][0]["is_public"], False)
        self.assertEqual(response_json["results"][1]["workspace"], None)
        self.assertEqual(response_json["results"][1]["is_public"], True)

    def test_category_serializer_missions_and_channels(self, mock_return, mock_return_roles):
        """ """
        mission = mommy.make(
            Mission,
            id=uuid.uuid4(),
            mission_category_id=str(self.custom_category.id),
        )
        mommy.make(MissionWorkspace, id=uuid.uuid4(), workspace=self.workspace, mission=mission)
        channel = mommy.make(
            Channel, id=uuid.uuid4(), channel_category_id=str(self.custom_category.id), workspace=self.workspace
        )

        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})
        url = reverse("categories-list")

        response = self.client.get(url, **self.headers, format="json")
        response_json = response.json()
        self.assertEqual(response.status_code, 200)

        self.assertEqual(response_json["results"][0]["missions"][0]["name"], mission.name)
        self.assertEqual(response_json["results"][0]["channels"][0]["name"], channel.name)

    def test_category_post_success_name_keep_case(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})

        data = {
            "name": "New CaTegory",
            "image": "http://keeps.com.br",
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response_json["name"], data["name"])

    def test_category_post_fail_name_already_exists(self, mock_return, mock_return_roles):
        self.headers = {"HTTP_X_CLIENT": str(self.workspace.id)}
        self.client.force_authenticate(user={"sub": str(self.user.id), "client_id": str(self.workspace.id)})
        data = {
            "name": "aa Custom 1",
            "image": "http://keeps.com.br",
        }

        response = self.client.post(self.url, **self.headers, data=data, format="json")
        response_json = response.json()

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response_json["i18n"], "category_name_already_exists")
