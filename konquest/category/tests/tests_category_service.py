import pytest
from django.test import TestCase
from model_mommy import mommy

from account.models import Workspace
from category.models import CustomCategory
from category.services.category_service import CategoryService
from custom.keeps_exception_handler import KPNotAllowedDeleteCategoryLinkedToAChannelOrMission
from mission.models import Mission, MissionCategory
from pulse.models import ChannelCategory


class TestCategoryService(TestCase):
    def setUp(self) -> None:
        self._service = CategoryService()
        self._workspace = mommy.make(Workspace)

    def test_should_delete_category(self):
        category = mommy.make(CustomCategory, workspace_id=self._workspace.id)

        self._service.delete_category(category)

        self.assertFalse(MissionCategory.objects.filter(name=category.name).exists())
        self.assertFalse(ChannelCategory.objects.filter(name=category.name).exists())
        self.assertFalse(CustomCategory.objects.filter(id=category.id).exists())

    def test_should_raise_not_allowed_delete_category_linked_to_a_channel_or_mission(self):
        category = mommy.make(CustomCategory, workspace_id=self._workspace.id)
        mission_category = MissionCategory.objects.get(name=category.name)
        mommy.make(Mission, mission_category=mission_category)

        with pytest.raises(KPNotAllowedDeleteCategoryLinkedToAChannelOrMission):
            self._service.delete_category(category)

        self.assertTrue(MissionCategory.objects.filter(name=category.name).exists())
        self.assertTrue(ChannelCategory.objects.filter(name=category.name).exists())
        self.assertTrue(CustomCategory.objects.filter(id=category.id).exists())

    def test_category_exists_with_deleted_flag_true(self):
        category = mommy.make(CustomCategory, workspace_id=self._workspace.id, deleted=True)
        self._service.create_category(category)
        self.assertTrue(CustomCategory.objects.filter(name=category.name, deleted=False).exists())
