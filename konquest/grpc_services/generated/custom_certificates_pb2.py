# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: custom_certificates.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC, 5, 29, 0, "", "custom_certificates.proto"
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x19\x63ustom_certificates.proto\x12\x13\x63ustom_certificates"\xd1\x02\n\x0f\x43\x65rtificateData\x12\x1d\n\x10learn_content_id\x18\x01 \x01(\tH\x00\x88\x01\x01\x12:\n\x08template\x18\x02 \x01(\x0e\x32(.custom_certificates.CertificateTemplate\x12\x15\n\x08language\x18\x03 \x01(\tH\x01\x88\x01\x01\x12\x11\n\tuser_name\x18\x04 \x01(\t\x12\x13\n\x0bperformance\x18\x05 \x01(\t\x12\x0c\n\x04time\x18\x06 \x01(\t\x12\x13\n\x0b\x64\x61te_finish\x18\x07 \x01(\t\x12\x13\n\x0b\x63ourse_name\x18\x08 \x01(\t\x12\x34\n\x08\x63ontents\x18\t \x03(\x0b\x32".custom_certificates.CourseContent\x12\x14\n\x0cworkspace_id\x18\n \x01(\tB\x13\n\x11_learn_content_idB\x0b\n\t_language"V\n\rCourseContent\x12\r\n\x05title\x18\x01 \x01(\t\x12\x0c\n\x04time\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x13\n\x0bperformance\x18\x04 \x01(\t"*\n\x1bGenerateCertificateResponse\x12\x0b\n\x03url\x18\x01 \x01(\t*-\n\x13\x43\x65rtificateTemplate\x12\x0b\n\x07MISSION\x10\x00\x12\t\n\x05TRAIL\x10\x01\x32{\n\nPdfService\x12m\n\x13GenerateCertificate\x12$.custom_certificates.CertificateData\x1a\x30.custom_certificates.GenerateCertificateResponseb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "custom_certificates_pb2", _globals)
if not _descriptor._USE_C_DESCRIPTORS:
    DESCRIPTOR._loaded_options = None
    _globals["_CERTIFICATETEMPLATE"]._serialized_start = 522
    _globals["_CERTIFICATETEMPLATE"]._serialized_end = 567
    _globals["_CERTIFICATEDATA"]._serialized_start = 51
    _globals["_CERTIFICATEDATA"]._serialized_end = 388
    _globals["_COURSECONTENT"]._serialized_start = 390
    _globals["_COURSECONTENT"]._serialized_end = 476
    _globals["_GENERATECERTIFICATERESPONSE"]._serialized_start = 478
    _globals["_GENERATECERTIFICATERESPONSE"]._serialized_end = 520
    _globals["_PDFSERVICE"]._serialized_start = 569
    _globals["_PDFSERVICE"]._serialized_end = 692
# @@protoc_insertion_point(module_scope)
