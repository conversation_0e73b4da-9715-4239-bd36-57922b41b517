import json
from abc import ABC, abstractmethod

from google.protobuf.json_format import Parse

import grpc_services
from grpc_services.utils import message_to_orm_filters


class GenericServiceServicer(ABC):
    """
    A generic service base class for gRPC services, making it reusable for different packages.
    Extend this class for specific services and provide relevant message types, queryset methods,
    and serializers dynamically.

    Args:
        message_pb2: The generated proto message class.
        filters_class: The class responsible for filtering the queryset.
        serializer_class: The serializer class to convert the ORM queryset to JSON-like dicts.
        response_message_type: The message type for the response (proto message).
        response_message_entity_type: The entity type for individual items within the response.
    """

    def __init__(
        self, message_pb2, filters_class, serializer_class, response_message_type, response_message_entity_type
    ):
        self.message_pb2 = message_pb2
        self.filters_class = filters_class
        self.serializer_class = serializer_class
        self.response_message_type = response_message_type
        self.response_message_entity_type = response_message_entity_type

    @abstractmethod
    def get_queryset(self, workspace_id: str):
        """
        Abstract method to return the main queryset for this service.

        Args:
            workspace_id (str): The workspace ID from which to retrieve data.

        Returns:
            queryset: The queryset for the given workspace.
        """
        raise NotImplementedError("You must implement `get_queryset` in the subclass.")

    def Get(self, request, context):
        """
        The gRPC Get method to handle requests and return filtered data.

        Args:
            request: The gRPC request containing filters and workspace metadata.
            context: The gRPC context object, used for passing metadata or raising errors.

        Returns:
            response: A gRPC response containing the filtered data.
        """
        # Extract metadata (workspace_id) from the gRPC context
        metadata = dict(context.invocation_metadata())
        workspace_id = metadata.get("workspace_id")

        if not workspace_id:
            context.abort(grpc_services.StatusCode.INVALID_ARGUMENT, "workspace_id is required")

        # Convert gRPC filters into ORM filters
        filters = {}
        if request.filters:
            try:
                filters = message_to_orm_filters(request.filters)
            except Exception as e:
                context.abort(grpc_services.StatusCode.INVALID_ARGUMENT, f"Invalid filters provided: {e}")

        # Retrieve and filter the queryset
        try:
            queryset = self.get_queryset(workspace_id)
        except Exception as e:
            context.abort(grpc_services.StatusCode.INTERNAL, f"Error retrieving data: {e}")

        filtered_objects = self.filters_class(filters, queryset=queryset).qs

        # Serialize the filtered objects
        serializer = self.serializer_class(filtered_objects, many=True)

        # Build the response
        response = self.response_message_type()
        for obj in serializer.data:
            obj_message = Parse(
                json.dumps(obj, default=str), self.response_message_entity_type(), ignore_unknown_fields=True
            )

            response.items.append(obj_message)

        return response
