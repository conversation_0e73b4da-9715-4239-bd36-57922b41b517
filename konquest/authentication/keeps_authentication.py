import logging

from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from jose.exceptions import ExpiredSignatureError
from keycloak.keycloak_openid import KeycloakOpenID
from rest_framework import authentication
from rest_framework.exceptions import NotAuthenticated

from account.models.user import User

logger = logging.getLogger(__name__)

SESSION_CACHE_TTL = 10 * 60  # 10 minutes


class KeepsAuthentication(authentication.TokenAuthentication):
    user_cache = None

    def __init__(self):
        self._config = getattr(settings, "KEYCLOAK_CONFIG")

        try:
            self._server_url = self._config["KEYCLOAK_SERVER_URL"]
            self._client_id = self._config["KEYCLOAK_CLIENT_ID"]
            self._realm = self._config["KEYCLOAK_REALM"]
        except KeyError as exc:
            raise Exception("KEYCLOAK_SERVER_URL, KEY<PERSON>OAK_CLIENT_ID or <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_REALM not found.") from exc

        self._client_secret_key = self._config.get("KEYCLOAK_CLIENT_SECRET_KEY", None)
        self._client_public_key = self._config.get("KEYCLOAK_CLIENT_PUBLIC_KEY", None)

        self._keycloak = KeycloakOpenID(
            server_url=self._server_url,
            client_id=self._client_id,
            realm_name=self._realm,
            client_secret_key=self._client_secret_key,
            verify=False,
        )

        self.user_cache = cache

    def authenticate(self, request):
        if self._skip_urls(request):
            return {}, None

        user = self._get_token_info(request)
        user["token"] = request.META.get("HTTP_AUTHORIZATION")
        user["client_id"] = self._get_profile(request)

        return user, None

    def _get_token_info(self, request):
        """Decode token"""
        if "HTTP_AUTHORIZATION" not in request.META or request.META["HTTP_AUTHORIZATION"] == 0:
            raise NotAuthenticated("HTTP_AUTHORIZATION not found in the request")

        jwt = request.META.get("HTTP_AUTHORIZATION")

        if jwt == settings.KEEPS_SECRET_TOKEN_INTEGRATION:
            return {"token_integration": jwt}

        try:
            token_info = self._keycloak.decode_token(jwt)
        except ExpiredSignatureError as exc:
            token_info = self._keycloak.decode_token(jwt)

            if "offline_access" in token_info["realm_access"]["roles"]:
                return token_info

            raise ExpiredSignatureError("Login expired") from exc

        self.update_user_access_time(token_info["sub"])
        return token_info

    @staticmethod
    def _get_profile(request):
        return request.META.get("HTTP_X_CLIENT") or None

    @staticmethod
    def _skip_urls(request):
        if "/docs" in request.path or "/swagger" in request.path or "/redoc" in request.path:
            return True
        return False

    def update_user_access_time(self, user_id):
        cache_key = f"user_access_time:{user_id}"

        already_updated = self.user_cache.add(cache_key, True, timeout=SESSION_CACHE_TTL)

        if not already_updated:
            logger.debug(f"User {user_id} has already been recently updated. Skipping the update to last access date.")
            return

        try:
            User.objects.filter(pk=user_id).update(last_access_date=timezone.now())
            logger.debug(f"Last access date updated for user {user_id}.")
        except Exception as e:
            logger.warning(f"Error when try to update last access date for user {user_id}: {e}")
